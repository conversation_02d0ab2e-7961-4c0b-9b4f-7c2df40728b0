package cn.lili.controller.order;

import cn.hutool.json.JSONUtil;
import cn.lili.common.utils.WordToPDFUtil;
import cn.lili.modules.file.plugin.FilePluginFactory;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractService;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dos.OrderItemSettlement;
import cn.lili.modules.order.order.entity.dto.SettlementDataSource;
import cn.lili.modules.order.order.entity.vo.OrderItemSettlementVo;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.order.order.wrapper.OrderItemSettlementWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年05月08日16:39
 */
@RestController
@Api(tags = "买家端,结算单")
@RequestMapping("/buyer/test")
public class TestController {

    @Autowired
    private IContractService contractService;
    @Autowired
    private OrderService orderService;
    @Autowired
    private OrderItemService orderItemService;
    @Autowired
    private FilePluginFactory filePluginFactory;

    @GetMapping("/test01")
    public void test01() throws Exception {
        OrderItem orderItem = orderItemService.getBySn("OI202505071919996264764280832");
        Contract contract = contractService.getOne(Wrappers.<Contract>lambdaQuery().eq(Contract::getContractBizNo, orderItem.getOrderSn()).orderByDesc(Contract::getCreateTime));
        Order order = orderService.getBySn(orderItem.getOrderSn());
        OrderItemSettlement orderItemSettlement = new OrderItemSettlement(order, orderItem, contract.getContractNo());
        String wordUrl = "https://test.jingruiit.com:9002/supplychain/upload/20250508/8ac869299a28661b20d64a177e9ac65e.docx";
        OrderItemSettlementVo orderItemSettlementVo = OrderItemSettlementWrapper.build().entityVO(orderItemSettlement);
        Map<String, Object> backObj = this.getBackObject(order, orderItemSettlementVo);
        // 生成结算单pdf
        File pdfFile = WordToPDFUtil.contractTemplateGenFile(wordUrl, backObj);
        //设置合同其他参数
        //上传合同文件
        InputStream contractStream = new FileInputStream(pdfFile);
        String pdfUrl = filePluginFactory.filePlugin().inputStreamUpload(contractStream, pdfFile.getName());
        System.out.println("pdfUrl = " + pdfUrl);
    }

    private Map<String, Object> getBackObject(Order order, OrderItemSettlementVo orderItemSettlementVo) {
        SettlementDataSource settlementDataSource = new SettlementDataSource();
        settlementDataSource.setMemberName(order.getMemberName());
        settlementDataSource.setStoreName(order.getStoreName());
        settlementDataSource.setMainContractNo(orderItemSettlementVo.getMainContractNo());
        List<OrderItemSettlementVo> list = new ArrayList<>();
        list.add(orderItemSettlementVo);
        list.add(orderItemSettlementVo);
        settlementDataSource.setSettlements(list);
        settlementDataSource.setDate(LocalDate.now().toString());
        return JSONUtil.parseObj(settlementDataSource);
    }

}
