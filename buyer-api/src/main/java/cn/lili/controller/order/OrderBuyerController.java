package cn.lili.controller.order;

import cn.lili.common.aop.annotation.PreventDuplicateSubmissions;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.OperationalJudgment;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderPackage;
import cn.lili.modules.order.order.entity.dto.ChangeOrderItemDTO;
import cn.lili.modules.order.order.entity.dto.OrderPaymentDto;
import cn.lili.modules.order.order.entity.dto.OrderSearchParams;
import cn.lili.modules.order.order.entity.dto.PendingInvoicingPageParams;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderTypeEnum;
import cn.lili.modules.order.order.entity.vo.OrderDetailVO;
import cn.lili.modules.order.order.entity.vo.OrderItemPayableVo;
import cn.lili.modules.order.order.entity.vo.OrderItemVO;
import cn.lili.modules.order.order.entity.vo.OrderSimpleVO;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.service.OrderPackageService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.system.entity.vo.Traces;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * 买家端,订单接口
 *
 * <AUTHOR>
 * @since 2020/11/16 10:08 下午
 */
@RestController
@Api(tags = "买家端,订单接口")
@RequestMapping("/buyer/order/order")
public class OrderBuyerController {

    /**
     * 订单
     */
    @Autowired
    private OrderService orderService;

    @Autowired
    private OrderPackageService orderPackageService;

    @Autowired
    private OrderItemService orderItemService;

    @ApiOperation(value = "查询会员订单列表")
    @GetMapping
    public ResultMessage<IPage<OrderSimpleVO>> queryMineOrder(OrderSearchParams orderSearchParams) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        orderSearchParams.setMemberId(currentUser.getId());
        return ResultUtil.data(orderService.queryByParams(orderSearchParams));
    }

    @ApiOperation(value = "订单明细")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单编号", required = true, paramType = "path")
    })
    @GetMapping(value = "/{orderSn}")
    public ResultMessage<OrderDetailVO> detail(@NotNull(message = "订单编号不能为空") @PathVariable("orderSn") String orderSn) {
        OrderDetailVO orderDetailVO = orderService.queryDetail(orderSn);
        OperationalJudgment.judgment(orderDetailVO.getOrder());
        return ResultUtil.data(orderDetailVO);
    }

    @PreventDuplicateSubmissions
    @ApiOperation(value = "确认收货")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单编号", required = true, paramType = "path")
    })
    @PostMapping(value = "/{orderSn}/receiving")
    public ResultMessage<Object> receiving(@NotNull(message = "订单编号不能为空") @PathVariable("orderSn") String orderSn) {
        Order order = orderService.getBySn(orderSn);
        if (order == null) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        //判定是否是待收货状态
        if (!order.getOrderStatus().equals(OrderStatusEnum.DELIVERED.name())) {
            throw new ServiceException(ResultCode.ORDER_DELIVERED_ERROR);
        }
        orderService.complete(orderSn);
        return ResultUtil.success();
    }

    @PreventDuplicateSubmissions
    @ApiOperation(value = "取消订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单编号", required = true, dataType = "String", paramType = "path"),
            @ApiImplicitParam(name = "reason", value = "取消原因", required = true, dataType = "String", paramType = "query")
    })
    @PostMapping(value = "/{orderSn}/cancel")
    public ResultMessage<Object> cancel(@ApiIgnore @PathVariable String orderSn, @RequestParam String reason) {
        orderService.cancel(orderSn, reason);
        return ResultUtil.success();
    }

    @PreventDuplicateSubmissions
    @ApiOperation(value = "删除订单")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单编号", required = true, dataType = "String", paramType = "path")
    })
    @DeleteMapping(value = "/{orderSn}")
    public ResultMessage<Object> deleteOrder(@PathVariable String orderSn) {
        OperationalJudgment.judgment(orderService.getBySn(orderSn));
        orderService.deleteOrder(orderSn);
        return ResultUtil.success();
    }

    @ApiOperation(value = "查询物流踪迹")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单编号", required = true, dataType = "String", paramType = "path")
    })
    @PostMapping(value = "/getTraces/{orderSn}")
    public ResultMessage<Object> getTraces(@NotBlank(message = "订单编号不能为空") @PathVariable String orderSn) {
        OperationalJudgment.judgment(orderService.getBySn(orderSn));
        return ResultUtil.data(orderService.getTraces(orderSn));
    }

    @ApiOperation(value = "查询地图版物流踪迹")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单编号", required = true, dataType = "String", paramType = "path")
    })
    @PostMapping(value = "/getMapTraces/{orderSn}")
    public ResultMessage<Object> getMapTraces(@NotBlank(message = "订单编号不能为空") @PathVariable String orderSn) {
        OperationalJudgment.judgment(orderService.getBySn(orderSn));
        return ResultUtil.data(orderService.getMapTraces(orderSn));
    }


    @PreventDuplicateSubmissions
    @ApiOperation(value = "开票")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单编号", required = true, dataType = "String", paramType = "path")
    })
    @PostMapping(value = "/receipt/{orderSn}")
    public ResultMessage<Object> invoice(@NotBlank(message = "订单编号不能为空") @PathVariable String orderSn) {
        OperationalJudgment.judgment(orderService.getBySn(orderSn));
        return ResultUtil.data(orderService.invoice(orderSn));
    }

    @ApiOperation(value = "查询物流踪迹")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单编号", required = true, dataType = "String", paramType = "path")
    })
    @GetMapping(value = "/getTracesList/{orderSn}")
    public ResultMessage<Object> getTracesList(@NotBlank(message = "订单编号不能为空") @PathVariable String orderSn) {
        return ResultUtil.data(orderPackageService.getOrderPackageVOList(orderSn));
    }

    @ApiOperation(value = "查看包裹列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderSn", value = "订单编号", required = true, dataType = "String", paramType = "path")
    })
    @GetMapping(value = "/getPackage/{orderSn}")
    public ResultMessage<Object> getPackage(@NotBlank(message = "订单编号不能为空") @PathVariable String orderSn) {
        return ResultUtil.data(orderPackageService.getOrderPackageVOList(orderSn));
    }

    @ApiOperation(value = "查询会员报价订单列表")
    @GetMapping("/queryBaoJiaOrder")
    public ResultMessage<IPage<OrderSimpleVO>> queryBaoJiaOrder(OrderSearchParams orderSearchParams) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        orderSearchParams.setMemberId(currentUser.getId());
        orderSearchParams.setOrderType(OrderTypeEnum.BAOJIA.name());
        return ResultUtil.data(orderService.queryByParams(orderSearchParams));
    }

    /**
     * 上传主订单支付凭证
     * 双方签署合同后上传支付凭证
     */
    @PostMapping("/uploadPaymentVoucher")
    public ResultMessage<Boolean> uploadPaymentVoucher(String orderSn, String paymentVoucherUrl) {
        return ResultUtil.data(orderService.uploadPaymentVoucher(orderSn, paymentVoucherUrl));
    }

    /**
     * 买家调整确认收货数量
     * flowPrice
     */
    @PostMapping("/changeOrderItem")
    public ResultMessage<Boolean> changeOrderItem(@Valid ChangeOrderItemDTO dto) {
        return ResultUtil.data(orderService.changeOrderItem(dto));
    }

    /**
     * 买家 确认/拒绝 卖家调整的数量
     */
    @PreventDuplicateSubmissions
    @PostMapping("/{orderItemSn}/{changeConfirmStatus}")
    public ResultMessage<Boolean> updateOrderItemChangeStatus(@PathVariable String orderItemSn, @PathVariable String changeConfirmStatus) {
        return ResultUtil.data(orderService.updateOrderItemChangeStatus(orderItemSn, changeConfirmStatus));
    }

    /**
     * 上传子订单一次支付凭证
     */
    @PostMapping("/uploadFirstPaymentVoucherUrl")
    public ResultMessage<Boolean> uploadFirstPaymentVoucherUrl(String orderItemSn, String firstPaymentVoucherUrl) {
        return ResultUtil.data(orderService.uploadFirstPaymentVoucherUrl(orderItemSn, firstPaymentVoucherUrl));
    }

    /**
     * 上传子订单尾款支付凭证
     */
    @PostMapping("/uploadBalancePaymentVoucherUrl")
    public ResultMessage<Boolean> uploadBalancePaymentVoucherUrl(String orderItemSn, String balancePaymentVoucherUrl) {
        return ResultUtil.data(orderService.uploadBalancePaymentVoucherUrl(orderItemSn, balancePaymentVoucherUrl));
    }

    /**
     * 获取子订单应付金额等
     */
    @GetMapping("/payableByItemSn/{orderItemSn}")
    public ResultMessage<OrderItemPayableVo> payableByItemSn(@PathVariable("orderItemSn") String orderItemSn) {
        return ResultUtil.data(orderItemService.payableByItemSn(orderItemSn));
    }

    /**
     * 子订单支付
     * @return
     */
    @PostMapping("/orderItemPayment/{orderItemSn}")
    public ResultMessage<Boolean> orderItemPayment(@PathVariable("orderItemSn") String orderItemSn, @RequestBody List<String> fileUrls) {
        return ResultUtil.data(orderService.orderItemPayment(orderItemSn, fileUrls));
    }

    /**
     * 待开票子订单列表
     */
    @GetMapping("/pendingInvoicingPage")
    public ResultMessage<IPage<OrderItemVO>> pendingInvoicingPage(PendingInvoicingPageParams pageParams) {
        return ResultUtil.data(orderItemService.pendingInvoicingPage(pageParams));
    }

}
