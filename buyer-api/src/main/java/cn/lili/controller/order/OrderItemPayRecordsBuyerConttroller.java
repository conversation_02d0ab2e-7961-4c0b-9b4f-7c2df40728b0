package cn.lili.controller.order;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.order.order.entity.dos.OrderItemPayRecords;
import cn.lili.modules.order.order.entity.vo.OrderItemPayRecordsVo;
import cn.lili.modules.order.order.service.OrderItemPayRecordsService;
import cn.lili.modules.order.order.wrapper.OrderItemPayRecordsWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月06日17:32
 */
@RestController
@Api(tags = "店铺端,订单支付记录接口")
@RequestMapping("/buyer/order-item/pay-records")
@RequiredArgsConstructor
public class OrderItemPayRecordsBuyerConttroller {

    private final OrderItemPayRecordsService orderItemPayRecordsService;

    /**
     * 根据子订单编号查询待确认或已确认的支付凭证记录
     */
    @GetMapping("/list/{itemSn}/{paymentStatus}")
    public ResultMessage<List<OrderItemPayRecordsVo>> getByItemSnAndPaymentStatus(@PathVariable("itemSn") String itemSn, @PathVariable("paymentStatus") String paymentStatus) {
        List<OrderItemPayRecords> list = orderItemPayRecordsService.list(Wrappers.<OrderItemPayRecords>lambdaQuery()
                .eq(OrderItemPayRecords::getOrderItemSn, itemSn)
                .eq(OrderItemPayRecords::getPaymentStatus, paymentStatus)
        );
        return ResultUtil.data(OrderItemPayRecordsWrapper.build().listVO(list));
    }

}
