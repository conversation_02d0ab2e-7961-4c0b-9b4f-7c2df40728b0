package cn.lili.controller.goods;

import cn.lili.common.aop.annotation.PreventDuplicateSubmissions;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquiryQueryDto;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquirySubmitDto;
import cn.lili.modules.goods.entity.vos.CommodityPriceInquiryVo;
import cn.lili.modules.goods.entity.vos.CommodityPriceQuoteDetailVo;
import cn.lili.modules.goods.service.ICommodityPriceInquiryService;
import cn.lili.modules.goods.service.ICommodityPriceQuoteService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 询价融资端功能
 * <AUTHOR>
 * @date 2025/3/5
 */
@RestController
@AllArgsConstructor
@RequestMapping("/buyer/commodity/priceInquiry")
public class CommodityPriceInquiryFrontController {

    private final ICommodityPriceInquiryService commodityPriceInquiryService;
    private final ICommodityPriceQuoteService commodityPriceQuoteService;

    /**
     * 融资端用户发起询价
     */
    @PreventDuplicateSubmissions
    @PostMapping("/submit")
    public ResultMessage<Boolean> submit(@RequestBody CommodityPriceInquirySubmitDto dto) {
        return ResultUtil.data(commodityPriceInquiryService.submit(dto));
    }

    /**
     * 融资端用户确认报价
     * @param commodityPriceQuoteIds 报价ids
     * @return
     */
    @PostMapping("/confirmQuote")
    public ResultMessage<Boolean> confirmQuote(@RequestBody List<Long> commodityPriceQuoteIds) {
        AuthUser currentUser = UserContext.getCurrentUser();
        String userId = currentUser.getId();
        return ResultUtil.data(commodityPriceQuoteService.confirmQuote(commodityPriceQuoteIds, userId));
    }

    /**
     * 融资端用户我的询价记录
     */
    @PostMapping("/page")
    public ResultMessage<IPage<CommodityPriceInquiryVo>> page(PageVO query, CommodityPriceInquiryQueryDto dto) {
        dto.setCustomerId(UserContext.getCurrentUser().getId());
        IPage<CommodityPriceInquiryVo> page = commodityPriceQuoteService.pageByQueryDto(query, dto);
        return ResultUtil.data(page);
    }

    /**
     * 报价详情
     */
    @GetMapping("/priceQuoteDetail")
    public ResultMessage<List<CommodityPriceQuoteDetailVo>> priceQuoteDetail(String commodityPriceInquiryId) {
        return ResultUtil.data(commodityPriceQuoteService.priceQuoteDetail(commodityPriceInquiryId));
    }

    /**
     * 根据订单号查询
     */
    @GetMapping("/getByOrderSn")
    public ResultMessage<CommodityPriceInquiryVo> getByOrderSn(String orderSn) {
        return ResultUtil.data(commodityPriceQuoteService.getByOrderSn(orderSn));
    }

}
