package cn.lili.controller.contract;

import cn.hutool.core.bean.BeanUtil;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_contract.contract_api.dto.ContractParamDTO;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractService;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/buyer/contract/contractlist")
@AllArgsConstructor
@Api(value = "客户查看合同")
public class ContractListBuyerController {
    private final IContractService contractService;
//    private final IContractBackUserService contractBackUserService;

    /**
     * 获取合同列表
     */
    @PostMapping("/getContractDetails")
    @ApiOperation(value = "获取合同列表")
    public ResultMessage<IPage<Contract>> getContractDetails(ContractParamDTO dto) {
        AuthUser authUser = UserContext.getCurrentUser();
        dto.setBuyerId(Long.valueOf(authUser.getId()));
        PageVO pageVO = BeanUtil.copyProperties(dto, PageVO.class);
        Contract contract = BeanUtil.copyProperties(dto, Contract.class);
        QueryWrapper<Contract> queryWrapper = PageUtil.initWrapper(contract);
        IPage<Contract> pages = contractService.page(PageUtil.initPage(pageVO), queryWrapper.orderByDesc("create_time"));
        return ResultUtil.data(pages);
    }
    /**
     * 获取合同详情
     */
    @GetMapping("/getDetails")
    @ApiOperation(value = "获取合同详情")
    public ResultMessage getDetails(@RequestParam String contractId) {
        return ResultUtil.data(contractService.getDetails(contractId));
    }
//
//    @SneakyThrows
//    @PostMapping("/contractRemind")
//    public ResultMessage contractRemind(@RequestBody String requestData) {
//        return ResultUtil.data("提醒成功");
//    }
//
//    @PostMapping("/resend")
//    public ResultMessage resend(@RequestParam String contractId) {
//        contractService.resend(contractId);
//        return ResultUtil.data(true);
//    }
//
//    @PostMapping("/delay")
//    public ResultMessage delay(@RequestParam String contractId) {
//        contractService.delay(contractId);
//        return ResultUtil.data(true);
//    }
    /**
     * 后台 使用当前用户进行签署 (自动获取部门id)
     *
     * @return
     */
    /*@PostMapping("/skipToSignByDeptId")
    public ResultMessage skipToSignByDeptId(@RequestBody ContractSignVO contractSignVO) {
        SmsCode code = BeanUtil.copyProperties(contractSignVO.getCode(), SmsCode.class);
        String deptId = AuthUtil.getDeptId();
        Assert.isTrue(StrUtil.isNotBlank(deptId),"账户部门配置不能为空");
        List<Long> deptIdList = Func.toLongList(deptId);
        contractSignVO.setDeptId(deptIdList.get(0));
        //当前用户签署信息
        ContractSignParam signParam = ContractSignParam.builder()
                .contractIds(contractSignVO.getContractId())
                .code(code)
                .customizeWriteBase64(contractSignVO.getCustomizeWriteBase64())
                .verifyType(contractSignVO.getVerifyType())
                .autoLock(contractSignVO.getAutoLock())
                .deptId(contractSignVO.getDeptId())
                .build();
        contractBackUserService.signByCurrentUserBack(signParam);
        return ResultUtil.data("");
    }*/
    /**
     * 合同撤销
     *
     * @return
     */
//    @SneakyThrows
//    @PostMapping("/contractRevoke")
//    public ResultMessage contractRevoke(@RequestBody Map<String, String> param) {
//        contractService.contractRevoke(param.get("contractId"));
//        return ResultUtil.data(Boolean.TRUE);
//    }
//
//    @PostMapping("/contractDownload")
//    public ResultMessage contractDownload(@RequestParam String contractId) {
//        return ResultUtil.data(contractService.downLoadPDF(contractId));
//    }
//
//    /**
//     * 获取预览合同链接
//     *
//     * @param contractId
//     * @return
//     */
//    @PostMapping("skipToPreview")
//    public ResultMessage skipToPreview(@RequestParam String contractId) {
//        return ResultUtil.data(contractService.preViewWithDevAccount(contractId));
//    }
//
//    /**
//     * 使用当前用户进行签署
//     *
//     * @return
//     */
//    @PostMapping("/skipToSign")
//    public ResultMessage skipToSign(@RequestBody ContractSignVO contractSignVO) {
//        SmsCode code = BeanUtil.copyProperties(contractSignVO.getCode(), SmsCode.class);
//        //当前用户签署信息
//        ContractSignParam signParam = ContractSignParam.builder()
//                .contractIds(contractSignVO.getContractId())
//                .code(code)
//                .customizeWriteBase64(contractSignVO.getCustomizeWriteBase64())
//                .verifyType(contractSignVO.getVerifyType())
//                .autoLock(contractSignVO.getAutoLock())
//                .deptId(contractSignVO.getDeptId())
//                .build();
//        contractBackUserService.signByCurrentUserBack(signParam);
//        return ResultUtil.data(true);
//    }
//
//
//    @GetMapping("/listByFinanceApplyId")
//    @ApiOperation("根据融资申请id查询合同")
//    public ResultMessage<List<Contract>> listByFinanceApplyId(@RequestParam Long financeApplyId) {
//        return ResultUtil.data(contractService.lambdaQuery().eq(Contract::getFinanceApplyId, financeApplyId)
//                .orderByDesc(Contract::getCreateTime).list());
//    }
//    @GetMapping("/getContract")
//    @ApiOperationSupport(order = 11)
//    @ApiOperation(value = "合同列表", notes = "传入companyId")
//    public ResultMessage<IPage<ContractVO>> getContract(Long  companyId, Query query) {
//        return ResultUtil.data(contractService.getContract(companyId, query));
//    }
}
