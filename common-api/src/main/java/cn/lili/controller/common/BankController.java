package cn.lili.controller.common;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.system.entity.dos.Bank;
import cn.lili.modules.system.service.IBankService;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping({"common/bank"})
@Api(
        value = "银行列表",
        tags = {"银行列表接口"}
)
public class BankController {
    @Autowired
    private IBankService bankService;
//    private final IPayBankCodeService bankCodeService;

    @GetMapping({"/search"})
    @ApiOperation("搜索银行")
    public ResultMessage<List<Bank>> search(@RequestParam String bank, @RequestParam String city) {
//        List<Bank> list = this.bankService.list((Wrapper) ((LambdaQueryWrapper) Wrappers.lambdaQuery().like(Bank::getName, bank)).like(Bank::getName, city));
        List<Bank> list = this.bankService.list(Wrappers.<Bank>lambdaQuery().like(Bank::getName, bank).like(Bank::getName, city));
        return ResultUtil.data(list);
    }
}
