package cn.lili.controller.common;

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.service.ICustomerInfoService;
import cn.lili.modules.otherapi.huawei.entity.BusinessLicense;
import cn.lili.modules.otherapi.huawei.service.IBusinessLicenseService;
import cn.lili.modules.otherapi.huawei.service.IHuaweiApiService;
import com.huaweicloud.sdk.ocr.v1.model.IdCardResult;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 营业执照识别表 控制器
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Slf4j
@RestController
@Api(value = "ocr识别", tags = "营业执照,身份证识别表接口")
@RequestMapping("/common/businessLicense")
public class BusinessLicenseController {

    @Autowired
    private ICustomerInfoService sky;

    @Autowired
    private IHuaweiApiService iHuaweiApiService;

    @Autowired
    private  IBusinessLicenseService businessLicenseService;


    /**
     * 营业执照识别
     * @param picUrl
     * @return
     */
    @PostMapping("/recognizeBusinessLicense")
    public ResultMessage<BusinessLicense> recognizeBusinessLicense(@RequestParam String picUrl) {
        return ResultUtil.data(businessLicenseService.recognizeBusinessLicense(picUrl));
    }

    /**
     * 身份证识别
     * @param picUrl
     * @return
     */
    @PostMapping("/revognizeIdentity")
    public ResultMessage<IdCardResult> revognizeIdentity(@RequestParam String picUrl) {

        return ResultUtil.data(iHuaweiApiService.revognizeIdentity(picUrl));

    }

    /**
     * 营业执照三要素验证
     * @param creditCode
     * @param companyName
     * @param legalPersonName
     * @return
     */
    @PostMapping("/threeElementsVertify")
    public ResultMessage<Boolean> threeElementsVertify(@RequestParam String creditCode, @RequestParam String companyName, @RequestParam String legalPersonName) {


        boolean b = sky.companyThreeElementsIsTure(creditCode, companyName, legalPersonName);
//
//        skyEyeService.companyThreeElementsIsTure(threeElementsVertifyDTO.getCreditCode(),
//                threeElementsVertif
//                yDTO.getCompanyName(),
//                threeElementsVertifyDTO.getLegalPersonName());

        return ResultUtil.data(b);


    }
}
