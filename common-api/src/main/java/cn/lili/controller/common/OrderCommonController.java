package cn.lili.controller.common;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.order.order.entity.vo.OrderConfirmedVO;
import cn.lili.modules.order.order.service.OrderItemService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公共的订单数据
 * <AUTHOR>
 * @date 2025年05月20日10:17
 */
@RestController
@RequestMapping("/common/common/order")
@RequiredArgsConstructor
public class OrderCommonController {

    private final OrderItemService orderItemService;

    /**
     * 获取成交动态
     * @param pageVO
     * @return
     */
    @ApiOperation(value = "获取成交动态")
    @GetMapping("/orderConfirmedPage")
    public ResultMessage<IPage<OrderConfirmedVO>> orderConfirmedPage(PageVO pageVO) {
        return ResultUtil.data(orderItemService.orderConfirmedPage(pageVO));
    }

}
