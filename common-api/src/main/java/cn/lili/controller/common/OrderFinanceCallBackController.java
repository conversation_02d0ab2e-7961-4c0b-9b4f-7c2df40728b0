package cn.lili.controller.common;

import cn.lili.common.constant.INotifyConstant;
import cn.lili.common.enums.ICallBackServiceEnum;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.handler.PaidBackHandler;
import cn.lili.common.utils.SecureUtil;
import cn.lili.common.utils.SignUtil;
import cn.lili.common.vo.ResultMessage;
import lombok.RequiredArgsConstructor;
import org.springblade.cipher.enums.SeriTypeEnum;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 自定义回调
 * <AUTHOR>
 * @date 2025年06月09日17:18
 */
@RestController
@RequestMapping("/common/common/order")
@RequiredArgsConstructor
public class OrderFinanceCallBackController {

    private final Map<String, PaidBackHandler> paidBackHandlerMap;

    /**
     * 订单融资后回调的接口
     * @return
     */
    @PostMapping("/order-financing-paid-back")
    public String paidBack(@RequestParam Map<String, Object> param) {
        String service = ICallBackServiceEnum.getBusinessServiceBySecretKey(INotifyConstant.ORDER_FINANCING_PAID_NOTIFY);
        // 根据header判断来源
        PaidBackHandler paidBackHandler = paidBackHandlerMap.get(service);
        return paidBackHandler.handler(param);
    }

}
