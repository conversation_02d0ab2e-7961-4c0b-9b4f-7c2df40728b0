package cn.lili.controller.common;


import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.file.plugin.FilePluginFactory;
import cn.lili.modules.jrzh_bases.BladeFile;
import cn.lili.modules.jrzh_bases.IAttachService;
import cn.lili.modules.jrzh_contract.contract_api.entity.Attach;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

@RestController
@Api(
        value = "对象存储端点",
        tags = {"对象存储端点"}
)
@RequestMapping({"/common/oss/endpoint"})
public class OssEndpoint {

    @Autowired
    private IAttachService attachService;
    @Autowired
    private FilePluginFactory filePluginFactory;


    @PostMapping({"/put-file-attach"})
    public ResultMessage<BladeFile> putFileAttach(@RequestParam MultipartFile file) throws IOException {
        try {
            String fileName = IdWorker.getId() + "/" + file.getOriginalFilename();
            String result = filePluginFactory.filePlugin().inputStreamUpload(file.getInputStream(), fileName);
            String attachId = this.buildAttach(fileName, file.getSize(), result);
            BladeFile bladeFile = new BladeFile();
//            bladeFile.setAttachId(Long.valueOf(attachId));
            bladeFile.setAttachId(attachId);
            bladeFile.setLink(result);
            bladeFile.setName(fileName);
            return ResultUtil.data(bladeFile);
        } catch (Throwable $ex) {
            throw $ex;
        }
    }

    private String buildAttach(String fileName, Long fileSize, String result) {
        Attach attach = new Attach();
        attach.setLink(result);
        attach.setName(fileName);
        attach.setOriginalName(fileName);
        attach.setAttachSize(fileSize);
        this.attachService.save(attach);
        return attach.getId();
    }
}
