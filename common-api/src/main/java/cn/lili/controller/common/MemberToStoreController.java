package cn.lili.controller.common;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.token.Token;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.member.service.MemberService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 将会员token转化为店铺token
 * <AUTHOR>
 * @date 2025年06月16日10:03
 */
@RestController
@RequestMapping("/common/member/store")
@RequiredArgsConstructor
public class MemberToStoreController {

    /**
     * 买家
     */
    private final MemberService memberService;

    /**
     * 使用买家端token置换卖家端token
     * @return
     */
    @PostMapping("/replacementStore")
    public ResultMessage<Token> replacementStore() {
        return ResultUtil.data(memberService.replacementStore());
    }

}
