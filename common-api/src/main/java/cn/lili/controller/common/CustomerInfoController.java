/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.controller.common;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.entity.AuthStatus;
import cn.lili.modules.connect.entity.CustomerPersonInfo;
import cn.lili.modules.connect.entity.dto.CustomerAndPersonInfoDTO;
import cn.lili.modules.connect.entity.vo.CustomerPersonInfoVO;
import cn.lili.modules.connect.service.ICustomerInfoService;
import cn.lili.modules.connect.service.ICustomerPersonInfoService;
import com.aliyun.oss.ServiceException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 企业信息名称 控制器
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@RestController
@AllArgsConstructor
@Api(value = "企业信息名称", tags = "企业信息名称接口")
@RequestMapping("/common/customerInfo")
public class CustomerInfoController {

    private final ICustomerInfoService customerInfoService;
    private final ICustomerPersonInfoService customerPersonInfoService;

    @PostMapping("/openPerson")
    @ApiOperation(value = "ceshi")
    public ResultMessage openPerson(String id) {
        CustomerPersonInfo personInfoOld = customerPersonInfoService.getByCustomerId(Long.valueOf(id));
        CustomerPersonInfoVO customerPersonInfoVO = BeanUtil.copyProperties(personInfoOld, CustomerPersonInfoVO.class);
        customerInfoService.commitPersonAuthInfo(customerPersonInfoVO);
        return ResultUtil.data(customerPersonInfoVO);
    }

    /**
     * 开通企业
     *
     * @param companyInfo
     * @return
     */
    @PostMapping("/openCompany")
    @ApiOperation(value = "开通企业")
    public ResultMessage openCompany(@RequestBody CustomerAndPersonInfoDTO companyInfo, String returnUrl) throws Throwable {
        if (ObjectUtil.isNotEmpty(companyInfo.getId())) {
            throw new ServiceException("id必须为空");
        }
        AuthUser currentUser = UserContext.getCurrentUser();
        if (ObjectUtil.isNull(currentUser) || ObjectUtil.isEmpty(currentUser.getId())) {
            throw new ServiceException("登录已过期");
        }
        Long id = Long.valueOf(currentUser.getId());

        //测试阶段设置以下两值
        companyInfo.setLegalPersonFlag(AuthStatus.LegalPersonFlag.LEGAL_PERSON.getStatus());
        return customerInfoService.commitEntAuthInfo(companyInfo, id, returnUrl);
    }

    /**
     * 提交实名企业认证资料
     *
     * @param request 请求体
     * @param type    实名方式
     */
    @PostMapping("/commitEntAuthInfoByType")
    @ApiOperation(value = "提交实名企业认证资料")
    public ResultMessage commitEntAuthInfoByType(@RequestBody(required = false) Map<String, Object> request, @RequestParam Integer type) {
        AuthUser currentUser = UserContext.getCurrentUser();
        if (ObjectUtil.isNull(currentUser) || ObjectUtil.isEmpty(currentUser.getId())) {
            throw new ServiceException("登录已过期");
        }
        Long userId = Long.valueOf(currentUser.getId());
        request.put("userId", userId);
        return customerInfoService.commitEntAuthInfoByType(request, type);
    }

    /**
     * 主动查询更新公司实名信息
     */
    @GetMapping("/queryEntAuth")
    @ApiOperation(value = "", notes = "根据公司名称查询实名情况")
    public ResultMessage queryEntAuth(Long id) {
        AuthUser currentUser = UserContext.getCurrentUser();
        if (ObjectUtil.isNull(currentUser) || ObjectUtil.isEmpty(currentUser.getId())) {
            throw new ServiceException("登录已过期");
        }
        Long customerId = Long.valueOf(currentUser.getId());
        return customerInfoService.queryEntAuthByCustomerId(customerId, id);
    }

    /**
     * 验真实名认证资料
     *
     * @param request 请求体
     * @param type    实名方式
     */
    @PostMapping("/verifyEntAuthInfoByType")
    public ResultMessage verifyEntAuthInfoByType(@RequestBody(required = false) Map<String, Object> request, @RequestParam Integer type) {
        return customerInfoService.verifyEntAuthInfoByType(request, Long.valueOf(UserContext.getCurrentUser().getId()), type);
    }


//    /**
//     * 查询是否需要进行签署授权书
//     */
//    @GetMapping("/checkNeedSign")
//    public ResultMessage checkNeedSign(Long id) {
//        AuthUser currentUser = UserContext.getCurrentUser();
//        if (ObjectUtil.isNull(currentUser) || ObjectUtil.isEmpty(currentUser.getId())) {
//            throw new ServiceException("登录已过期");
//        }
//        Long customerId = Long.valueOf(currentUser.getId());
//        return customerInfoService.checkNeedSignByCustomerId(customerId, id);
//    }
//
//    /**
//     * 获取企业授权书合同
//     *
//     * @return
//     */
//    @GetMapping("/getContractByBizNo")
//    public ResultMessage getContractByBizNo(@RequestParam Long id) {
//        AuthUser currentUser = UserContext.getCurrentUser();
//        if (ObjectUtil.isNull(currentUser) || ObjectUtil.isEmpty(currentUser.getId())) {
//            throw new ServiceException("登录已过期");
//        }
//        Long customerId = Long.valueOf(currentUser.getId());
//        //获取正在开通的企业实名信息
//        CustomerInfo customerInfo = customerInfoService.getById(id);
//        if (2 == customerInfo.getStartSign()) {
//            return ResultUtil.error(400, "无需签署该授权书");
//        }
//        //标识
//        String bizNo = customerId + customerInfo.getId();
//        if (ObjectUtil.isEmpty(customerInfo)) {
//            throw new ServiceException("企业实名信息不存在");
//        }
//        CustomerInfoVO vo = BeanUtil.copyProperties(customerInfo, CustomerInfoVO.class);
//        String contractId = customerInfoService.generateContractByNameAndDataSource(redisCache.getString("AUTH_CONTRACT_TEMPLATE_NAME"), JSONObject.parseObject(JSONUtil.toJsonStr(vo), Map.class), bizNo, customerId.toString(), customerInfo);
//        return ResultUtil.data(contractId);
//    }


}
