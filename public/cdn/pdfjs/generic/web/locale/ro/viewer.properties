# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Pagina precedentÄ
previous_label=Ãnapoi
next.title=Pagina urmÄtoare
next_label=Ãnainte

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=PaginÄ:
page_of=din {{pageCount}}

zoom_out.title=MicÈoreazÄ
zoom_out_label=MicÈoreazÄ
zoom_in.title=MÄreÈte
zoom_in_label=MÄreÈte
zoom.title=Scalare
presentation_mode.title=SchimbÄ la modul de prezentare
presentation_mode_label=Mod de prezentare
open_file.title=Deschide un fiÈier
open_file_label=Deschide
print.title=TipÄreÈte
print_label=TipÄreÈte
download.title=DescarcÄ
download_label=DescarcÄ
bookmark.title=Vizualizare curentÄ (copiaÈi sau deschideÈi Ã®ntr-o fereastrÄ nouÄ)
bookmark_label=Vizualizare curentÄ

# Secondary toolbar and context menu
tools.title=Unelte
tools_label=Unelte
first_page.title=Mergi la prima paginÄ
first_page.label=MergeÈi la prima paginÄ
first_page_label=Mergi la prima paginÄ
last_page.title=Mergi la ultima paginÄ
last_page.label=Mergi la ultima paginÄ
last_page_label=Mergi la ultima paginÄ
page_rotate_cw.title=RoteÈte Ã®n sensul acelor de ceasornic
page_rotate_cw.label=RoteÈte Ã®n sensul acelor de ceasornic
page_rotate_cw_label=RoteÈte Ã®n sensul acelor de ceasornic
page_rotate_ccw.title=RoteÈte Ã®n sens invers al acelor de ceasornic
page_rotate_ccw.label=Rotate Counter-Clockwise
page_rotate_ccw_label=RoteÈte Ã®n sens invers acelor de ceasornic

hand_tool_enable.title=ActiveazÄ instrumentul mÃ¢nÄ
hand_tool_enable_label=ActiveazÄ instrumentul mÃ¢nÄ
hand_tool_disable.title=DezactiveazÄ instrumentul mÃ¢nÄ
hand_tool_disable_label=DezactiveazÄ instrumentul mÃ¢nÄ

# Document properties dialog box
document_properties.title=ProprietÄÈile documentuluiâ¦
document_properties_label=ProprietÄÈile documentuluiâ¦
document_properties_file_name=Nume fiÈier:
document_properties_file_size=Dimensiune fiÈier:
document_properties_kb={{size_kb}} KB ({{size_b}} biÈi)
document_properties_mb={{size_mb}} MB ({{size_b}} biÈi)
document_properties_title=Titlu:
document_properties_author=Autor:
document_properties_subject=Subiect:
document_properties_keywords=Cuvinte cheie:
document_properties_creation_date=Data creÄrii:
document_properties_modification_date=Data modificÄrii:
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Autor:
document_properties_producer=ProducÄtor PDF:
document_properties_version=Versiune PDF:
document_properties_page_count=NumÄr de pagini:
document_properties_close=Ãnchide

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ComutÄ bara lateralÄ
toggle_sidebar_label=ComutÄ bara lateralÄ
outline.title=AratÄ schiÈa documentului
outline_label=SchiÈÄ document
attachments.title=AfiÈeazÄ ataÈamentele
attachments_label=AtaÈamente
thumbs.title=AratÄ miniaturi
thumbs_label=Miniaturi
findbar.title=CautÄ Ã®n document
findbar_label=CÄutaÈi

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Pagina {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Miniatura paginii {{page}}

# Find panel button title and messages
find_label=CautÄ:
find_previous.title=GÄseÈte instanÈa anterioarÄ Ã®n frazÄ
find_previous_label=Anterior
find_next.title=GÄsteÈte urmÄtoarea instanÈÄ Ã®n frazÄ
find_next_label=UrmÄtor
find_highlight=EvidenÈiazÄ apariÈiile
find_match_case_label=Potrivire litere
find_reached_top=Am ajuns la Ã®nceputul documentului, continuÄ de la sfÃ¢rÈit
find_reached_bottom=Am ajuns la sfÃ¢rÈitul documentului, continuÄ de la Ã®nceput
find_not_found=Nu s-a gÄsit textul

# Error panel labels
error_more_info=Mai multe informaÈii
error_less_info=Mai puÈinÄ informaÈie
error_close=Ãnchide
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (varianta: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Mesaj: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=StivÄ: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=FiÈier: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Linie: {{line}}
rendering_error=A intervenit o eroare la afiÈarea paginii.

# Predefined zoom values
page_scale_width=LÄÈime paginÄ
page_scale_fit=Potrivire la paginÄ
page_scale_auto=Dimensiune automatÄ
page_scale_actual=Dimensiune realÄ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Eroare
loading_error=A intervenit o eroare la Ã®ncÄrcarea fiÈierului PDF.
invalid_file_error=FiÈier PDF invalid sau deteriorat.
missing_file_error=FiÈier PDF lipsÄ.
unexpected_response_error=RÄspuns neaÈteptat de la server.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Adnotare]
password_label=IntroduceÈi parola pentru a deschide acest fiÅier PDF.
password_invalid=ParolÄ greÈitÄ. VÄ rugÄm Ã®ncercaÈi din nou.
password_ok=Ok
password_cancel=RenunÈÄ

printing_not_supported=AtenÈie: TipÄrirea nu este suportatÄ Ã®n totalitate de acest navigator.
printing_not_ready=AtenÈie: FiÈierul PDF nu este Ã®ncÄrcat complet pentru tipÄrire.
web_fonts_disabled=Fonturile web sunt dezactivate: nu pot utiliza fonturile PDF Ã®ncorporate.
document_colors_disabled=Documentele PDF nu sunt autorizate sÄ foloseascÄ propriile culori: 'Permite paginilor sÄ aleagÄ propriile culori' este dezactivatÄ Ã®n navigator.
