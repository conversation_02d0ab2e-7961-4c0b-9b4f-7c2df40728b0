# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=á¬ááá áááá áá
previous_label=á¬ááá
next.title=á¨áááááá áááá áá
next_label=á¨áááááá

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=áááá áá:
page_of=of {{pageCount}}

zoom_out.title=á¨áááªáá ááá
zoom_out_label=áááá áá
zoom_in.title=á¨áááªáá ááá
zoom_in_label=á¨áááªáá ááá
zoom.title=ááá¡á¨á¢ááá
print.title=áááááá­ááá
print_label=áááááá­ááá
presentation_mode.title=ááááá ááá áá ááááá¢ááªááá¡ á áááááá
presentation_mode_label=áá ááááá¢ááªááá¡ á ááááá
open_file.title=á¤ááááá¡ ááá®á¡áá
open_file_label=ááá®á¡áá
download.title=á©áááá¢ááá ááá
download_label=á©áááá¢ááá ááá
bookmark.title=áááááááá á á®ááá (áá¡áá áá ááá®á¡áá áá®áá á¡áá ááááá¨á)
bookmark_label=áááááááá á á®ááá

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=á¡á¢ááááá¡ á©áááááá/ááááááá
toggle_sidebar_label=á¡á¢ááááá¡ á©áááááá/ááááááá
outline.title=áááá£áááá¢áá¡ á¡á¥áááá¡ á©áááááá
outline_label=áááá£áááá¢áá¡ á¡á¥ááá
thumbs.title=áááááá¢á£á áááá¡ á©áááááá
thumbs_label=áááááá¢á£á ááá
findbar.title=ááááá áááá£áááá¢á¨á
findbar_label=ááááá

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=áááá áá {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=áááááá¢á£á á áááá ááá¡áááá¡ {{page}}

# Context menu
first_page.label=ááááá¡ááá ááá ááá áááá ááá
last_page.label=ááááá¡ááá áááá áááá ááá
page_rotate_cw.label=ááá¢á áááááá
page_rotate_ccw.label=á£áá£ááá¢á áááááá

# Find panel button title and messages
find_label=ááááá:
find_previous.title=áááá¢áá¥á¡á¢áá¡ á¬ááá áááá®áááá áá¡ ááááá
find_previous_label=á¬ááá
find_next.title=áááá¢áá¥á¡á¢áá¡ á¨áááááá áááá®áááá áá¡ ááááá
find_next_label=á¨áááááá
find_highlight=á§ááááá¡ ááááá§áá¤á
find_match_case_label=ááááá á£ááá¡ áááááááá¡á¬áááááá
find_reached_top=áááá£áááá¢áá¡ áááá, áá á«áááááá áááá£áááá¢áá¡ ááááááá
find_reached_bottom=áááá£áááá¢áá¡ áááá, áá á«áááááá áááá£áááá¢áá¡ ááááááá
find_not_found=áááá¢áá¥á¡á¢á ááá  áááá«áááá

# Error panel labels
error_more_info=ááá¢áááááá¡ á©áááááá
error_less_info=ááá¢áááááá¡ ááááááá
error_close=ááá®á£á áá
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (ááááá: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=áááááááá: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=áá­ááá: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=á¤áááá: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=á¡á¢á áá¥ááá: {{line}}
rendering_error=á¨ááªáááá áááá ááá¡ áá¡áá®ááá¡áá¡.

# Predefined zoom values
page_scale_width=áááá ááá¡ á¡ááááááá
page_scale_fit=áááá ááá¡ á¨ááá¡ááá
page_scale_auto=ááááááá¡á¨á¢ááá
page_scale_actual=á áááá£á á áááá

# Loading indicator messages
loading_error_indicator=á¨ááªáááá
loading_error=á¨ááªáááá PDF á¤ááááá¡ á©áá¢ááá áááá¡áá¡.
invalid_file_error=á£ááá ááááá áá ááááááááá£áá PDF á¤áááá.
missing_file_error=ááªááá á PDF á¤áááá.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} áááá¢ááªáá]
request_password=PDF áááªá£ááá ááá áááá:
invalid_password=ááá ááá ááªááá áá.

printing_not_supported=ááá¤á áá®ááááá: áá áá áá£ááá á¨á áááááá­áááá¡ áá®áá ááá­áá á áá áá¡á á£ááá .
printing_not_ready=ááá¤á áá®ááááá: PDF á¤áááá áááá¡áááá­ááá á¡á á£ááá áá  á©áá¢ááá áá£áá.
web_fonts_disabled=ááá á¨á áá¤á¢ááá áááá áá£ááá: á©ááááá£áá PDF á¨á áá¤á¢áááá á¡áá ááááááá ááá  á®áá á®áááá.
document_colors_disabled=PDF áááá£áááá¢ááá¡ ááá á«áááááá á¡ááá£ááá á á¤áá áááá¡ ááááá§ááááá: áá áá£ááá á¨á áááá áá£ááá ááá áááá¢á á - Â«áááá ááááá¡áááá¡ á¡ááá£ááá á á¤áá áááá á¡áá áááááááá¡ á£á¤ááááÂ».
