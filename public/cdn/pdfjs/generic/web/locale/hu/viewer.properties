# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ElÅzÅ oldal
previous_label=ElÅzÅ
next.title=KÃ¶vetkezÅ oldal
next_label=TovÃ¡bb

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=Oldal:
page_of=Ã¶sszesen: {{pageCount}}

zoom_out.title=KicsinyÃ­tÃ©s
zoom_out_label=KicsinyÃ­tÃ©s
zoom_in.title=NagyÃ­tÃ¡s
zoom_in_label=NagyÃ­tÃ¡s
zoom.title=NagyÃ­tÃ¡s
presentation_mode.title=VÃ¡ltÃ¡s bemutatÃ³ mÃ³dba
presentation_mode_label=BemutatÃ³ mÃ³d
open_file.title=FÃ¡jl megnyitÃ¡sa
open_file_label=MegnyitÃ¡s
print.title=NyomtatÃ¡s
print_label=NyomtatÃ¡s
download.title=LetÃ¶ltÃ©s
download_label=LetÃ¶ltÃ©s
bookmark.title=Jelenlegi nÃ©zet (mÃ¡solÃ¡s vagy megnyitÃ¡s Ãºj ablakban)
bookmark_label=AktuÃ¡lis nÃ©zet

# Secondary toolbar and context menu
tools.title=EszkÃ¶zÃ¶k
tools_label=EszkÃ¶zÃ¶k
first_page.title=UgrÃ¡s az elsÅ oldalra
first_page.label=UgrÃ¡s az elsÅ oldalra
first_page_label=UgrÃ¡s az elsÅ oldalra
last_page.title=UgrÃ¡s az utolsÃ³ oldalra
last_page.label=UgrÃ¡s az utolsÃ³ oldalra
last_page_label=UgrÃ¡s az utolsÃ³ oldalra
page_rotate_cw.title=ForgatÃ¡s az Ã³ramutatÃ³ jÃ¡rÃ¡sÃ¡val egyezÅen
page_rotate_cw.label=ForgatÃ¡s az Ã³ramutatÃ³ jÃ¡rÃ¡sÃ¡val egyezÅen
page_rotate_cw_label=ForgatÃ¡s az Ã³ramutatÃ³ jÃ¡rÃ¡sÃ¡val egyezÅen
page_rotate_ccw.title=ForgatÃ¡s az Ã³ramutatÃ³ jÃ¡rÃ¡sÃ¡val ellentÃ©tesen
page_rotate_ccw.label=ForgatÃ¡s az Ã³ramutatÃ³ jÃ¡rÃ¡sÃ¡val ellentÃ©tesen
page_rotate_ccw_label=ForgatÃ¡s az Ã³ramutatÃ³ jÃ¡rÃ¡sÃ¡val ellentÃ©tesen

hand_tool_enable.title=KÃ©z eszkÃ¶z bekapcsolÃ¡sa
hand_tool_enable_label=KÃ©z eszkÃ¶z bekapcsolÃ¡sa
hand_tool_disable.title=KÃ©z eszkÃ¶z kikapcsolÃ¡sa
hand_tool_disable_label=KÃ©z eszkÃ¶z kikapcsolÃ¡sa

# Document properties dialog box
document_properties.title=Dokumentum tulajdonsÃ¡gaiâ¦
document_properties_label=Dokumentum tulajdonsÃ¡gaiâ¦
document_properties_file_name=FÃ¡jlnÃ©v:
document_properties_file_size=FÃ¡jlmÃ©ret:
document_properties_kb={{size_kb}} KB ({{size_b}} bÃ¡jt)
document_properties_mb={{size_mb}} MB ({{size_b}} bÃ¡jt)
document_properties_title=CÃ­m:
document_properties_author=SzerzÅ:
document_properties_subject=TÃ¡rgy:
document_properties_keywords=Kulcsszavak:
document_properties_creation_date=LÃ©trehozÃ¡s dÃ¡tuma:
document_properties_modification_date=MÃ³dosÃ­tÃ¡s dÃ¡tuma:
document_properties_date_string={{date}}, {{time}}
document_properties_creator=LÃ©trehozta:
document_properties_producer=PDF elÅÃ¡llÃ­tÃ³:
document_properties_version=PDF verziÃ³:
document_properties_page_count=OldalszÃ¡m:
document_properties_close=BezÃ¡rÃ¡s

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=OldalsÃ¡v be/ki
toggle_sidebar_label=OldalsÃ¡v be/ki
outline.title=DokumentumvÃ¡zlat megjelenÃ­tÃ©se
outline_label=DokumentumvÃ¡zlat
attachments.title=MellÃ©kletek megjelenÃ­tÃ©se
attachments_label=Van mellÃ©klet
thumbs.title=BÃ©lyegkÃ©pek megjelenÃ­tÃ©se
thumbs_label=BÃ©lyegkÃ©pek
findbar.title=KeresÃ©s a dokumentumban
findbar_label=KeresÃ©s

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}}. oldal
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}}. oldal bÃ©lyegkÃ©pe

# Find panel button title and messages
find_label=KeresÃ©s:
find_previous.title=A kifejezÃ©s elÅzÅ elÅfordulÃ¡sÃ¡nak keresÃ©se
find_previous_label=ElÅzÅ
find_next.title=A kifejezÃ©s kÃ¶vetkezÅ elÅfordulÃ¡sÃ¡nak keresÃ©se
find_next_label=TovÃ¡bb
find_highlight=Ãsszes kiemelÃ©se
find_match_case_label=Kis- Ã©s nagybetÅ±k megkÃ¼lÃ¶nbÃ¶ztetÃ©se
find_reached_top=A dokumentum eleje elÃ©rve, folytatÃ¡s a vÃ©gÃ©tÅl
find_reached_bottom=A dokumentum vÃ©ge elÃ©rve, folytatÃ¡s az elejÃ©tÅl
find_not_found=A kifejezÃ©s nem talÃ¡lhatÃ³

# Error panel labels
error_more_info=TovÃ¡bbi tudnivalÃ³k
error_less_info=Kevesebb informÃ¡ciÃ³
error_close=BezÃ¡rÃ¡s
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ãzenet: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=NyomkÃ¶vetÃ©s: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=FÃ¡jl: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Sor: {{line}}
rendering_error=Hiba tÃ¶rtÃ©nt az oldal feldolgozÃ¡sa kÃ¶zben.

# Predefined zoom values
page_scale_width=OldalszÃ©lessÃ©g
page_scale_fit=Teljes oldal
page_scale_auto=Automatikus nagyÃ­tÃ¡s
page_scale_actual=ValÃ³di mÃ©ret
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Hiba
loading_error=Hiba tÃ¶rtÃ©nt a PDF betÃ¶ltÃ©sekor.
invalid_file_error=ÃrvÃ©nytelen vagy sÃ©rÃ¼lt PDF fÃ¡jl.
missing_file_error=HiÃ¡nyzÃ³ PDF fÃ¡jl.
unexpected_response_error=VÃ¡ratlan kiszolgÃ¡lÃ³vÃ¡lasz.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} megjegyzÃ©s]
password_label=Adja meg a jelszÃ³t a PDF fÃ¡jl megnyitÃ¡sÃ¡hoz.
password_invalid=Helytelen jelszÃ³. PrÃ³bÃ¡lja Ãºjra.
password_ok=OK
password_cancel=MÃ©gse

printing_not_supported=FigyelmeztetÃ©s: Ez a bÃ¶ngÃ©szÅ nem teljesen tÃ¡mogatja a nyomtatÃ¡st.
printing_not_ready=FigyelmeztetÃ©s: A PDF nincs teljesen betÃ¶ltve a nyomtatÃ¡shoz.
web_fonts_disabled=Webes betÅ±kÃ©szletek letiltva: nem hasznÃ¡lhatÃ³k a beÃ¡gyazott PDF betÅ±kÃ©szletek.
document_colors_disabled=A PDF dokumentumok nem hasznÃ¡lhatjÃ¡k sajÃ¡t szÃ­neiket: âAz oldalak a sajÃ¡t maguk Ã¡ltal kivÃ¡lasztott szÃ­neket hasznÃ¡lhatjÃ¡kâ beÃ¡llÃ­tÃ¡s ki van kapcsolva a bÃ¶ngÃ©szÅben.
