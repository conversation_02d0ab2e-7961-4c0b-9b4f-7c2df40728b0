# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Ø§ÙØµÙØ­Ø© Ø§ÙØ³Ø§Ø¨ÙØ©
previous_label=Ø§ÙØ³Ø§Ø¨ÙØ©
next.title=Ø§ÙØµÙØ­Ø© Ø§ÙØªØ§ÙÙØ©
next_label=Ø§ÙØªØ§ÙÙØ©

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=ØµÙØ­Ø©:
page_of=ÙÙ {{pageCount}}

zoom_out.title=Ø¨Ø¹ÙØ¯
zoom_out_label=Ø¨Ø¹ÙØ¯
zoom_in.title=ÙØ±ÙØ¨
zoom_in_label=ÙØ±ÙØ¨
zoom.title=Ø§ÙØªÙØ±ÙØ¨
presentation_mode.title=Ø§ÙØªÙÙ ÙÙØ¶Ø¹ Ø§ÙØ¹Ø±Ø¶ Ø§ÙØªÙØ¯ÙÙÙ
presentation_mode_label=ÙØ¶Ø¹ Ø§ÙØ¹Ø±Ø¶ Ø§ÙØªÙØ¯ÙÙÙ
open_file.title=Ø§ÙØªØ­ ÙÙÙÙØ§
open_file_label=Ø§ÙØªØ­
print.title=Ø§Ø·Ø¨Ø¹
print_label=Ø§Ø·Ø¨Ø¹
download.title=ÙØ²ÙÙ
download_label=ÙØ²ÙÙ
bookmark.title=Ø§ÙÙÙØ¸ÙØ± Ø§ÙØ­Ø§ÙÙ (Ø§ÙØ³Ø® Ø£Ù Ø§ÙØªØ­ ÙÙ ÙØ§ÙØ°Ø© Ø¬Ø¯ÙØ¯Ø©)
bookmark_label=Ø§ÙÙÙØ¸ÙØ± Ø§ÙØ­Ø§ÙÙ

# Secondary toolbar and context menu
tools.title=Ø§ÙØ£Ø¯ÙØ§Øª
tools_label=Ø§ÙØ£Ø¯ÙØ§Øª
first_page.title=Ø§Ø°ÙØ¨ Ø¥ÙÙ Ø§ÙØµÙØ­Ø© Ø§ÙØ£ÙÙÙ
first_page.label=Ø§Ø°ÙØ¨ Ø¥ÙÙ Ø§ÙØµÙØ­Ø© Ø§ÙØ£ÙÙÙ
first_page_label=Ø§Ø°ÙØ¨ Ø¥ÙÙ Ø§ÙØµÙØ­Ø© Ø§ÙØ£ÙÙÙ
last_page.title=Ø§Ø°ÙØ¨ Ø¥ÙÙ Ø§ÙØµÙØ­Ø© Ø§ÙØ£Ø®ÙØ±Ø©
last_page.label=Ø§Ø°ÙØ¨ Ø¥ÙÙ Ø§ÙØµÙØ­Ø© Ø§ÙØ£Ø®ÙØ±Ø©
last_page_label=Ø§Ø°ÙØ¨ Ø¥ÙÙ Ø§ÙØµÙØ­Ø© Ø§ÙØ£Ø®ÙØ±Ø©
page_rotate_cw.title=Ø£Ø¯Ø± Ø¨Ø§ØªØ¬Ø§Ù Ø¹ÙØ§Ø±Ø¨ Ø§ÙØ³Ø§Ø¹Ø©
page_rotate_cw.label=Ø£Ø¯Ø± Ø¨Ø§ØªØ¬Ø§Ù Ø¹ÙØ§Ø±Ø¨ Ø§ÙØ³Ø§Ø¹Ø©
page_rotate_cw_label=Ø£Ø¯Ø± Ø¨Ø§ØªØ¬Ø§Ù Ø¹ÙØ§Ø±Ø¨ Ø§ÙØ³Ø§Ø¹Ø©
page_rotate_ccw.title=Ø£Ø¯Ø± Ø¨Ø¹ÙØ³ Ø§ØªØ¬Ø§Ù Ø¹ÙØ§Ø±Ø¨ Ø§ÙØ³Ø§Ø¹Ø©
page_rotate_ccw.label=Ø£Ø¯Ø± Ø¨Ø¹ÙØ³ Ø§ØªØ¬Ø§Ù Ø¹ÙØ§Ø±Ø¨ Ø§ÙØ³Ø§Ø¹Ø©
page_rotate_ccw_label=Ø£Ø¯Ø± Ø¨Ø¹ÙØ³ Ø§ØªØ¬Ø§Ù Ø¹ÙØ§Ø±Ø¨ Ø§ÙØ³Ø§Ø¹Ø©

hand_tool_enable.title=ÙØ¹ÙÙ Ø£Ø¯Ø§Ø© Ø§ÙÙØ¯
hand_tool_enable_label=ÙØ¹ÙÙ Ø£Ø¯Ø§Ø© Ø§ÙÙØ¯
hand_tool_disable.title=Ø¹Ø·ÙÙ Ø£Ø¯Ø§Ø© Ø§ÙÙØ¯
hand_tool_disable_label=Ø¹Ø·ÙÙ Ø£Ø¯Ø§Ø© Ø§ÙÙØ¯

# Document properties dialog box
document_properties.title=Ø®ØµØ§Ø¦Øµ Ø§ÙÙØ³ØªÙØ¯â¦
document_properties_label=Ø®ØµØ§Ø¦Øµ Ø§ÙÙØ³ØªÙØ¯â¦
document_properties_file_name=Ø§Ø³Ù Ø§ÙÙÙÙ:
document_properties_file_size=Ø­Ø¬Ù Ø§ÙÙÙÙ:
document_properties_kb={{size_kb}} Ù.Ø¨Ø§ÙØª ({{size_b}} Ø¨Ø§ÙØª)
document_properties_mb={{size_mb}} Ù.Ø¨Ø§ÙØª ({{size_b}} Ø¨Ø§ÙØª)
document_properties_title=Ø§ÙØ¹ÙÙØ§Ù:
document_properties_author=Ø§ÙÙØ¤ÙÙ:
document_properties_subject=Ø§ÙÙÙØ¶ÙØ¹:
document_properties_keywords=Ø§ÙÙÙÙØ§Øª Ø§ÙØ£Ø³Ø§Ø³ÙØ©:
document_properties_creation_date=ØªØ§Ø±ÙØ® Ø§ÙØ¥ÙØ´Ø§Ø¡:
document_properties_modification_date=ØªØ§Ø±ÙØ® Ø§ÙØªØ¹Ø¯ÙÙ:
document_properties_date_string={{date}}Ø {{time}}
document_properties_creator=Ø§ÙÙÙØ´Ø¦:
document_properties_producer=ÙÙØªØ¬ PDF:
document_properties_version=Ø¥ØµØ¯Ø§Ø±Ø© PDF:
document_properties_page_count=Ø¹Ø¯Ø¯ Ø§ÙØµÙØ­Ø§Øª:
document_properties_close=Ø£ØºÙÙ

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Ø¨Ø¯ÙÙ Ø§ÙØ´Ø±ÙØ· Ø§ÙØ¬Ø§ÙØ¨Ù
toggle_sidebar_label=Ø¨Ø¯ÙÙ Ø§ÙØ´Ø±ÙØ· Ø§ÙØ¬Ø§ÙØ¨Ù
outline.title=Ø§Ø¹Ø±Ø¶ ÙØ®Ø·Ø· Ø§ÙÙØ³ØªÙØ¯
outline_label=ÙØ®Ø·Ø· Ø§ÙÙØ³ØªÙØ¯
attachments.title=Ø§Ø¹Ø±Ø¶ Ø§ÙÙØ±ÙÙØ§Øª
attachments_label=Ø§ÙÙÙØ±ÙÙØ§Øª
thumbs.title=Ø§Ø¹Ø±Ø¶ ÙÙØµØºØ±Ø§Øª
thumbs_label=ÙÙØµØºÙØ±Ø§Øª
findbar.title=Ø§Ø¨Ø­Ø« ÙÙ Ø§ÙÙØ³ØªÙØ¯
findbar_label=Ø§Ø¨Ø­Ø«

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=ØµÙØ­Ø© {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ÙØµØºÙØ±Ø© ØµÙØ­Ø© {{page}}

# Find panel button title and messages
find_label=Ø§Ø¨Ø­Ø«:
find_previous.title=Ø§Ø¨Ø­Ø« Ø¹Ù Ø§ÙØªÙÙØ§Ø¬Ø¯ Ø§ÙØ³ÙØ§Ø¨Ù ÙÙØ¹Ø¨Ø§Ø±Ø©
find_previous_label=Ø§ÙØ³Ø§Ø¨Ù
find_next.title=Ø§Ø¨Ø­Ø« Ø¹Ù Ø§ÙØªÙÙØ§Ø¬Ø¯ Ø§ÙØªÙØ§ÙÙ ÙÙØ¹Ø¨Ø§Ø±Ø©
find_next_label=Ø§ÙØªØ§ÙÙ
find_highlight=Ø£Ø¨Ø±ÙØ² Ø§ÙÙÙ
find_match_case_label=Ø·Ø§Ø¨Ù Ø­Ø§ÙØ© Ø§ÙØ£Ø­Ø±Ù
find_reached_top=ØªØ§Ø¨Ø¹Øª ÙÙ Ø§ÙØ£Ø³ÙÙ Ø¨Ø¹Ø¯ÙØ§ ÙØµÙØª Ø¥ÙÙ Ø¨Ø¯Ø§ÙØ© Ø§ÙÙØ³ØªÙØ¯
find_reached_bottom=ØªØ§Ø¨Ø¹Øª ÙÙ Ø§ÙØ£Ø¹ÙÙ Ø¨Ø¹Ø¯ÙØ§ ÙØµÙØª Ø¥ÙÙ ÙÙØ§ÙØ© Ø§ÙÙØ³ØªÙØ¯
find_not_found=ÙØ§ ÙØ¬ÙØ¯ ÙÙØ¹Ø¨Ø§Ø±Ø©

# Error panel labels
error_more_info=ÙØ¹ÙÙÙØ§Øª Ø£ÙØ«Ø±
error_less_info=ÙØ¹ÙÙÙØ§Øª Ø£ÙÙ
error_close=Ø£ØºÙÙ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=âPDF.js Ù{{version}} â(Ø¨ÙØ§Ø¡: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ø§ÙØ±Ø³Ø§ÙØ©: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Ø§ÙØ±ØµÙØ©: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Ø§ÙÙÙÙ: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Ø§ÙØ³Ø·Ø±: {{line}}
rendering_error=Ø­Ø¯Ø« Ø®Ø·Ø£ Ø£Ø«ÙØ§Ø¡ Ø¹Ø±Ø¶ Ø§ÙØµÙØ­Ø©.

# Predefined zoom values
page_scale_width=Ø¹Ø±Ø¶ Ø§ÙØµÙØ­Ø©
page_scale_fit=ÙÙØ§Ø¦ÙØ© Ø§ÙØµÙØ­Ø©
page_scale_auto=ØªÙØ±ÙØ¨ ØªÙÙØ§Ø¦Ù
page_scale_actual=Ø§ÙØ­Ø¬Ù Ø§ÙØ­ÙÙÙÙ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}Ùª

# Loading indicator messages
loading_error_indicator=Ø¹Ø·Ù
loading_error=Ø­Ø¯Ø« Ø¹Ø·Ù Ø£Ø«ÙØ§Ø¡ ØªØ­ÙÙÙ ÙÙÙ PDF.
invalid_file_error=ÙÙÙ PDF ØªØ§ÙÙ Ø£Ù ØºÙØ± ØµØ­ÙØ­.
missing_file_error=ÙÙÙ PDF ØºÙØ± ÙÙØ¬ÙØ¯.
unexpected_response_error=Ø§Ø³ØªØ¬Ø§Ø¨Ø© Ø®Ø§Ø¯ÙÙ ØºÙØ± ÙØªÙÙØ¹Ø©.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[ØªØ¹ÙÙÙ {{type}}]
password_label=Ø£Ø¯Ø®Ù ÙÙÙÙØ© Ø§ÙØ³Ø± ÙÙØªØ­ ÙØ°Ø§ Ø§ÙÙÙÙ.
password_invalid=ÙÙÙØ© Ø³Ø± Ø®Ø·Ø£. ÙÙ ÙØ¶ÙÙ Ø£Ø¹Ø¯ Ø§ÙÙØ­Ø§ÙÙØ©.
password_ok=Ø­Ø³ÙØ§
password_cancel=Ø£ÙØºÙ

printing_not_supported=ØªØ­Ø°ÙØ±: ÙØ§ ÙØ¯Ø¹Ù ÙØ°Ø§ Ø§ÙÙØªØµÙØ­ Ø§ÙØ·Ø¨Ø§Ø¹Ø© Ø¨Ø´ÙÙ ÙØ§ÙÙ.
printing_not_ready=ØªØ­Ø°ÙØ±: ÙÙÙ PDF ÙÙ ÙÙØ­ÙÙÙ ÙØ§ÙÙÙØ§ ÙÙØ·Ø¨Ø§Ø¹Ø©.
web_fonts_disabled=Ø®Ø·ÙØ· Ø§ÙÙØ¨ ÙÙØ¹Ø·ÙÙØ©: ØªØ¹Ø°ÙØ± Ø§Ø³ØªØ®Ø¯Ø§Ù Ø®Ø·ÙØ· PDF Ø§ÙÙÙØ¶ÙÙÙØ©.
document_colors_disabled=ÙÙØ³ ÙØ³ÙÙØ­ÙØ§ ÙÙÙÙØ§Øª PDF Ø¨Ø§Ø³ØªØ®Ø¯Ø§Ù Ø£ÙÙØ§ÙÙØ§ Ø§ÙØ®Ø§ØµØ©: Ø®ÙØ§Ø± 'Ø§Ø³ÙØ­ ÙÙØµÙØ­Ø§Øª Ø¨Ø§Ø®ØªÙØ§Ø± Ø£ÙÙØ§ÙÙØ§ Ø§ÙØ®Ø§ØµØ©' ÙÙØ³ ÙÙÙØ¹ÙÙÙØ§ ÙÙ Ø§ÙÙØªØµÙØ­.
