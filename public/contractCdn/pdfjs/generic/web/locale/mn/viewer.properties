# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.

zoom.title=Ð¢ÑÐ»ÑÐ»Ñ
open_file.title=Ð¤Ð°Ð¹Ð» Ð½ÑÑ
open_file_label=ÐÑÑ

# Secondary toolbar and context menu

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
findbar_label=ÐÐ»

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.

# Find panel button title and messages
find_previous.title=Ð¥Ð°Ð¹Ð»ÑÑÐ½ Ó©Ð¼Ð½Ó©Ñ Ð¾Ð»Ð´ÑÑÐ³ ÑÐ°ÑÑÑÐ»Ð½Ð°
find_next.title=Ð¥Ð°Ð¹Ð»ÑÑÐ½ Ð´Ð°ÑÐ°Ð°Ð³Ð¸Ð¹Ð½ Ð¾Ð»Ð´ÑÑÐ³ ÑÐ°ÑÑÑÐ»Ð½Ð°
find_not_found=ÐÐ»Ð´ÑÐ¾Ð½Ð³Ò¯Ð¹

# Error panel labels
error_more_info=ÐÑÐ¼ÑÐ»Ñ Ð¼ÑÐ´ÑÑÐ»ÑÐ»
error_close=Ð¥Ð°Ð°
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number

# Predefined zoom values

# Loading indicator messages
loading_error_indicator=ÐÐ»Ð´Ð°Ð°

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"

