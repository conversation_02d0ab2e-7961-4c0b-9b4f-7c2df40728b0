# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=à¦ªà§à¦°à§à¦¬à¦¬à¦°à§à¦¤à§ à¦ªà§à¦·à§à¦ à¦¾
previous_label=à¦ªà§à¦°à§à¦¬à¦¬à¦°à§à¦¤à§
next.title=à¦ªà¦°à¦¬à¦°à§à¦¤à§ à¦ªà§à¦·à§à¦ à¦¾
next_label=à¦ªà¦°à¦¬à¦°à§à¦¤à§

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=à¦ªà§à¦·à§à¦ à¦¾:
page_of={{pageCount}} à¦à¦°

zoom_out.title=à¦à§à¦ à¦à¦à¦¾à¦°à§ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
zoom_out_label=à¦à§à¦ à¦à¦à¦¾à¦°à§ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
zoom_in.title=à¦¬à§ à¦à¦à¦¾à¦°à§ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
zoom_in_label=à¦¬à§ à¦à¦à¦¾à¦°à§ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
zoom.title=à¦¬à§ à¦à¦à¦¾à¦°à§ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨
presentation_mode.title=à¦à¦ªà¦¸à§à¦¥à¦¾à¦ªà¦¨à¦¾ à¦®à§à¦¡à§ à¦¸à§à¦¯à§à¦à¦ à¦à¦°à§à¦¨
presentation_mode_label=à¦à¦ªà¦¸à§à¦¥à¦¾à¦ªà¦¨à¦¾ à¦®à§à¦¡
open_file.title=à¦«à¦¾à¦à¦² à¦à§à¦²à§à¦¨
open_file_label=à¦à§à¦²à§à¦¨
print.title=à¦®à§à¦¦à§à¦°à¦£
print_label=à¦®à§à¦¦à§à¦°à¦£
download.title=à¦¡à¦¾à¦à¦¨à¦²à§à¦¡
download_label=à¦¡à¦¾à¦à¦¨à¦²à§à¦¡
bookmark.title=à¦¬à¦°à§à¦¤à¦®à¦¾à¦¨ à¦à¦¬à¦¸à§à¦¥à¦¾ (à¦à¦¨à§à¦²à¦¿à¦ªà¦¿ à¦à¦¥à¦¬à¦¾ à¦¨à¦¤à§à¦¨ à¦à¦à¦¨à§à¦¡à§ à¦¤à§ à¦à§à¦²à§à¦¨)
bookmark_label=à¦¬à¦°à§à¦¤à¦®à¦¾à¦¨ à¦à¦¬à¦¸à§à¦¥à¦¾

# Secondary toolbar and context menu
tools.title=à¦à§à¦²
tools_label=à¦à§à¦²
first_page.title=à¦ªà§à¦°à¦¥à¦® à¦ªà¦¾à¦¤à¦¾à§ à¦¯à¦¾à¦
first_page.label=à¦ªà§à¦°à¦¥à¦® à¦ªà¦¾à¦¤à¦¾à§ à¦¯à¦¾à¦
first_page_label=à¦ªà§à¦°à¦¥à¦® à¦ªà¦¾à¦¤à¦¾à§ à¦¯à¦¾à¦
last_page.title=à¦¶à§à¦· à¦ªà¦¾à¦¤à¦¾à§ à¦¯à¦¾à¦
last_page.label=à¦¶à§à¦· à¦ªà¦¾à¦¤à¦¾à§ à¦¯à¦¾à¦
last_page_label=à¦¶à§à¦· à¦ªà¦¾à¦¤à¦¾à§ à¦¯à¦¾à¦
page_rotate_cw.title=à¦à§à¦¿à¦° à¦à¦¾à¦à¦à¦¾à¦° à¦¦à¦¿à¦à§ à¦à§à¦°à¦¾à¦
page_rotate_cw.label=à¦à§à¦¿à¦° à¦à¦¾à¦à¦à¦¾à¦° à¦¦à¦¿à¦à§ à¦à§à¦°à¦¾à¦
page_rotate_cw_label=à¦à§à¦¿à¦° à¦à¦¾à¦à¦à¦¾à¦° à¦¦à¦¿à¦à§ à¦à§à¦°à¦¾à¦
page_rotate_ccw.title=à¦à¦¡à¦¼à¦¿à¦° à¦à¦¾à¦à¦à¦¾à¦° à¦¬à¦¿à¦ªà¦°à§à¦¤à§ à¦à§à¦°à¦¾à¦
page_rotate_ccw.label=à¦à¦¡à¦¼à¦¿à¦° à¦à¦¾à¦à¦à¦¾à¦° à¦¬à¦¿à¦ªà¦°à§à¦¤à§ à¦à§à¦°à¦¾à¦
page_rotate_ccw_label=à¦à¦¡à¦¼à¦¿à¦° à¦à¦¾à¦à¦à¦¾à¦° à¦¬à¦¿à¦ªà¦°à§à¦¤à§ à¦à§à¦°à¦¾à¦


# Document properties dialog box
document_properties_title=à¦¶à¦¿à¦°à§à¦¨à¦¾à¦®:

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=à¦¸à¦¾à¦à¦¡à¦¬à¦¾à¦° à¦à¦à¦² à¦à¦°à§à¦¨
toggle_sidebar_label=à¦¸à¦¾à¦à¦¡à¦¬à¦¾à¦° à¦à¦à¦² à¦à¦°à§à¦¨
outline.title=à¦¨à¦¥à¦¿à¦° à¦°à§à¦ªà¦°à§à¦à¦¾ à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨ à¦à¦°à§à¦¨
outline_label=à¦¨à¦¥à¦¿à¦° à¦°à§à¦ªà¦°à§à¦à¦¾
thumbs.title=à¦¥à¦¾à¦®à§à¦¬à¦¨à§à¦à¦² à¦¸à¦®à§à¦¹  à¦ªà§à¦°à¦¦à¦°à§à¦¶à¦¨ à¦à¦°à§à¦¨
thumbs_label=à¦¥à¦¾à¦®à§à¦¬à¦¨à§à¦à¦² à¦¸à¦®à§à¦¹
findbar.title=à¦¨à¦¥à¦¿à¦° à¦®à¦§à§à¦¯à§ à¦à§à¦à¦à§à¦¨
findbar_label=à¦à¦¨à§à¦¸à¦¨à§à¦§à¦¾à¦¨

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=à¦ªà§à¦·à§à¦ à¦¾ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas={{page}} à¦ªà§à¦·à§à¦ à¦¾à¦° à¦¥à¦¾à¦®à§à¦¬à¦¨à§à¦à¦²

# Find panel button title and messages
find_label=à¦à¦¨à§à¦¸à¦¨à§à¦§à¦¾à¦¨:
find_previous.title=à¦¬à¦¾à¦à§à¦¯à¦¾à¦à¦¶à§à¦° à¦ªà§à¦°à§à¦¬à¦¬à¦°à§à¦¤à§ à¦à¦ªà¦¸à§à¦¥à¦¿à¦¤à¦¿ à¦à¦¨à§à¦¸à¦¨à§à¦§à¦¾à¦¨
find_previous_label=à¦ªà§à¦°à§à¦¬à¦¬à¦°à§à¦¤à§
find_next.title=à¦¬à¦¾à¦à§à¦¯à¦¾à¦à¦¶à§à¦° à¦ªà¦°à¦¬à¦°à§à¦¤à§ à¦à¦ªà¦¸à§à¦¥à¦¿à¦¤à¦¿ à¦à¦¨à§à¦¸à¦¨à§à¦§à¦¾à¦¨
find_next_label=à¦ªà¦°à¦¬à¦°à§à¦¤à§
find_highlight=à¦¸à¦¬ à¦¹à¦¾à¦à¦²à¦¾à¦à¦ à¦à¦°à¦¾ à¦¹à¦¬à§
find_match_case_label=à¦à¦à§à¦·à¦°à§à¦° à¦à¦¾à¦à¦¦ à¦®à§à¦²à¦¾à¦¨à§
find_reached_top=à¦ªà§à¦·à§à¦ à¦¾à¦° à¦¶à§à¦°à§à¦¤à§ à¦ªà§à¦à§ à¦à§à¦à§, à¦¨à§à¦ à¦¥à§à¦à§ à¦à¦°à¦®à§à¦­ à¦à¦°à¦¾ à¦¹à§à§à¦à§
find_reached_bottom=à¦ªà§à¦·à§à¦ à¦¾à¦° à¦¶à§à¦·à§ à¦ªà§à¦à§ à¦à§à¦à§, à¦à¦ªà¦° à¦¥à§à¦à§ à¦à¦°à¦®à§à¦­ à¦à¦°à¦¾ à¦¹à§à§à¦à§
find_not_found=à¦¬à¦¾à¦à§à¦¯à¦¾à¦à¦¶ à¦ªà¦¾à¦à§à¦¾ à¦¯à¦¾à§à¦¨à¦¿

# Error panel labels
error_more_info=à¦à¦°à¦ à¦¤à¦¥à§à¦¯
error_less_info=à¦à¦® à¦¤à¦¥à§à¦¯
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=à¦¬à¦¾à¦°à§à¦¤à¦¾: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=à¦¨à¦¥à¦¿: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=à¦²à¦¾à¦à¦¨: {{line}}
rendering_error=à¦ªà§à¦·à§à¦ à¦¾ à¦à¦ªà¦¸à§à¦¥à¦¾à¦ªà¦¨à¦¾à¦° à¦¸à¦®à§ à¦¤à§à¦°à§à¦à¦¿ à¦¦à§à¦à¦¾ à¦¦à¦¿à§à§à¦à§à¥¤

# Predefined zoom values
page_scale_width=à¦ªà§à¦·à§à¦ à¦¾à¦° à¦ªà§à¦°à¦¸à§à¦¥
page_scale_fit=à¦ªà§à¦·à§à¦ à¦¾ à¦«à¦¿à¦ à¦à¦°à§à¦¨
page_scale_auto=à¦¸à§à¦¬à§à¦à¦à§à¦°à¦¿à§ à¦à§à¦®
page_scale_actual=à¦ªà§à¦°à¦à§à¦¤ à¦à¦à¦¾à¦°

# Loading indicator messages
loading_error_indicator=à¦¤à§à¦°à§à¦à¦¿
loading_error=à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦²à§à¦¡ à¦à¦°à¦¾à¦° à¦¸à¦®à§ à¦¤à§à¦°à§à¦à¦¿ à¦¦à§à¦à¦¾ à¦¦à¦¿à§à§à¦à§à¥¤
invalid_file_error=à¦à¦à¦¾à¦°à§à¦¯à¦à¦° à¦à¦¥à¦¬à¦¾ à¦à§à¦·à¦¤à¦¿à¦à§à¦°à¦¸à§à¦¤ à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦«à¦¾à¦à¦²à¥¤
missing_file_error=à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦«à¦¾à¦à¦² à¦ªà¦¾à¦à§à¦¾ à¦¯à¦¾à¦à§à¦à§ à¦¨à¦¾à¥¤

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} à¦à§à¦à¦¾]
password_label=à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦«à¦¾à¦à¦²à¦à¦¿ à¦à¦ªà§à¦¨ à¦à¦°à¦¤à§ à¦ªà¦¾à¦¸à¦à§à¦¾à¦°à§à¦¡ à¦¦à¦¿à¦¨à¥¤
password_invalid=à¦­à§à¦² à¦ªà¦¾à¦¸à¦à§à¦¾à¦°à§à¦¡à¥¤ à¦à¦¨à§à¦à§à¦°à¦¹ à¦à¦°à§ à¦à¦¬à¦¾à¦° à¦à§à¦·à§à¦à¦¾ à¦à¦°à§à¦¨à¥¤
password_ok=à¦ à¦¿à¦ à¦à¦à§
password_cancel=à¦¬à¦¾à¦¤à¦¿à¦²

printing_not_supported=à¦¸à¦¤à¦°à§à¦à¦¤à¦¾: à¦à¦ à¦¬à§à¦°à¦¾à¦à¦à¦¾à¦°à§ à¦®à§à¦¦à§à¦°à¦£ à¦¸à¦®à§à¦ªà§à¦°à§à¦£à¦­à¦¾à¦¬à§ à¦¸à¦®à¦°à§à¦¥à¦¿à¦¤ à¦¨à§à¥¤
printing_not_ready=à¦¸à¦¤à¦°à§à¦à§à¦à¦°à¦£: à¦ªà¦¿à¦¡à¦¿à¦à¦«à¦à¦¿ à¦®à§à¦¦à§à¦°à¦£à§à¦° à¦à¦¨à§à¦¯ à¦¸à¦®à§à¦ªà§à¦°à§à¦£ à¦²à§à¦¡ à¦¹à§à¦¨à¦¿à¥¤
web_fonts_disabled=à¦à§à§à¦¬ à¦«à¦¨à§à¦ à¦¨à¦¿à¦·à§à¦à§à¦°à¦¿à§: à¦¸à¦à¦¯à§à¦à§à¦¤ à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦«à¦¨à§à¦ à¦¬à§à¦¯à¦¬à¦¹à¦¾à¦° à¦à¦°à¦¾ à¦¯à¦¾à¦à§à¦à§ à¦¨à¦¾à¥¤
document_colors_disabled=à¦ªà¦¿à¦¡à¦¿à¦à¦« à¦¡à¦à§à¦®à§à¦¨à§à¦à¦à§ à¦¤à¦¾à¦¦à§à¦° à¦¨à¦¿à¦à¦¸à§à¦¬ à¦°à¦ à¦¬à§à¦¯à¦¬à¦¹à¦¾à¦°à§ à¦à¦¨à§à¦®à¦¤à¦¿ à¦¨à§à¦: 'à¦ªà¦¾à¦¤à¦¾ à¦¤à¦¾à¦¦à§à¦° à¦¨à¦¿à¦à§à¦¸à§à¦¬ à¦°à¦ à¦¨à¦¿à¦°à§à¦¬à¦¾à¦à¦¨ à¦à¦°à¦¤à§ à¦à¦¨à§à¦®à¦¤à¦¿ à¦¦à¦¿à¦¨' à¦à¦ à¦¬à§à¦°à¦¾à¦à¦à¦¾à¦°à§ à¦¨à¦¿à¦·à§à¦à§à¦°à¦¿à§ à¦°à§à§à¦à§à¥¤
