# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=RÃ»pela berÃª
previous_label=PaÅve
next.title=RÃ»pela pÃªÅ
next_label=PÃªÅ

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=RÃ»pel:
page_of=/ {{pageCount}}

zoom_out.title=DÃ»r bike
zoom_out_label=DÃ»r bike
zoom_in.title=NÃªzÃ®k bike
zoom_in_label=NÃªzÃ®k bike
zoom.title=NÃªzÃ®k Bike
presentation_mode.title=DerbasÃ® mÃ»da pÃªÅkÃªÅkariyÃª bibe
presentation_mode_label=Moda PÃªÅkÃªÅkariyÃª
open_file.title=PelÃ® veke
open_file_label=Veke
print.title=Ãap bike
print_label=Ãap bike
download.title=JÃªbar bike
download_label=JÃªbar bike
bookmark.title=Xuyakirina niha (kopÃ® yan jÃ® di pencereyeke nÃ» de veke)
bookmark_label=Xuyakirina niha

# Secondary toolbar and context menu
tools.title=AmÃ»r
tools_label=AmÃ»r
first_page.title=Here rÃ»pela yekemÃ®n
first_page.label=Here rÃ»pela yekemÃ®n
first_page_label=Here rÃ»pela yekemÃ®n
last_page.title=Here rÃ»pela dawÃ®n
last_page.label=Here rÃ»pela dawÃ®n
last_page_label=Here rÃ»pela dawÃ®n
page_rotate_cw.title=Bi aliyÃª saetÃª ve bizivirÃ®ne
page_rotate_cw.label=Bi aliyÃª saetÃª ve bizivirÃ®ne
page_rotate_cw_label=Bi aliyÃª saetÃª ve bizivirÃ®ne
page_rotate_ccw.title=BerevajÃ® aliyÃª saetÃª ve bizivirÃ®ne
page_rotate_ccw.label=BerevajÃ® aliyÃª saetÃª ve bizivirÃ®ne
page_rotate_ccw_label=BerevajÃ® aliyÃª saetÃª ve bizivirÃ®ne


# Document properties dialog box
document_properties_title=Sernav:

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=DarikÃª kÃªlekÃª veke/bigire
toggle_sidebar_label=DarikÃª kÃªlekÃª veke/bigire
outline.title=Åemaya belgeyÃª nÃ®Åan bide
outline_label=Åemaya belgeyÃª
thumbs.title=WÃªnekokan nÃ®Åan bide
thumbs_label=WÃªnekok
findbar.title=Di belgeyÃª de bibÃ®ne
findbar_label=BibÃ®ne

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=RÃ»pel {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=WÃªnekoka rÃ»pelÃª {{page}}

# Find panel button title and messages
find_label=BibÃ®ne:
find_previous.title=Peyva berÃª bibÃ®ne
find_previous_label=PaÅve
find_next.title=Peyya pÃªÅ bibÃ®ne
find_next_label=PÃªÅve
find_highlight=TevÃ® beloq bike
find_match_case_label=Ji bo tÃ®pÃªn hÃ»rdek-girdek bihÃ®styar
find_reached_top=GihÃ®Åt serÃª rÃ»pelÃª, ji dawiya rÃ»pelÃª bidomÃ®ne
find_reached_bottom=GihÃ®Åt dawiya rÃ»pelÃª, ji serÃª rÃ»pelÃª bidomÃ®ne
find_not_found=Peyv nehat dÃ®tin

# Error panel labels
error_more_info=ZÃªdetir agahÃ®
error_less_info=ZÃªdetir agahÃ®
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js versiyon {{version}} (avanÃ®: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Peyam: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Komik: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Pel: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=RÃªzik: {{line}}
rendering_error=Di vehÃ»randina rÃ»pelÃª de Ã§ewtÃ® Ã§ÃªbÃ».

# Predefined zoom values
page_scale_width=Firehiya rÃ»pelÃª
page_scale_fit=Di rÃ»pelÃª de bicÃ® bike
page_scale_auto=Xweber nÃªzÃ®k bike
page_scale_actual=Mezinahiya rastÃ®n

# Loading indicator messages
loading_error_indicator=XeletÃ®
loading_error=Dema ku PDF dihat barkirin Ã§ewtiyek Ã§ÃªbÃ».
invalid_file_error=PelÃª PDFÃª nederbasdar yan jÃ® xirabe ye.
missing_file_error=PelÃª PDFÃª kÃªm e.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[NÃ®Åaneya {{type}}Ãª]
password_label=Ji bo PDFÃª vekÃ® ÅÃ®freyÃª binivÃ®se.
password_invalid=ÅÃ®fre Ã§ewt e. Tika ye dÃ®sa biceribÃ®ne.
password_ok=Temam
password_cancel=Betal

printing_not_supported=HiÅyarÃ®: Ãapkirin ji hÃªla vÃª gerokÃª ve bi temamÃ® nayÃª destekirin.
printing_not_ready=HiÅyarÃ®: PDF bi temamÃ® nehat barkirin Ã» ji bo Ã§apÃª ne amade ye.
web_fonts_disabled=FontÃªn WebÃª neÃ§alak in: FontÃªn PDFÃª yÃªn veÅartÃ® nayÃªn bikaranÃ®n.
document_colors_disabled=DestÃ»r tune ye ku belgeyÃªn PDFÃª rengÃªn xwe bi kar bÃ®nin: Di gerokÃª de 'destÃ»rÃª bide rÃ»pelan ku rengÃªn xwe bi kar bÃ®nin' nehatiye Ã§alakirin.
