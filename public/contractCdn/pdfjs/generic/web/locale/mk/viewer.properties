# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ÐÑÐµÑÑÐ¾Ð´Ð½Ð° ÑÑÑÐ°Ð½Ð¸ÑÐ°
previous_label=ÐÑÐµÑÑÐ¾Ð´Ð½Ð°
next.title=Ð¡Ð»ÐµÐ´Ð½Ð° ÑÑÑÐ°Ð½Ð¸ÑÐ°
next_label=Ð¡Ð»ÐµÐ´Ð½Ð°

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ°:
page_of=Ð¾Ð´ {{pageCount}}

zoom_out.title=ÐÐ°Ð¼Ð°Ð»ÑÐ²Ð°ÑÐµ
zoom_out_label=ÐÐ°Ð¼Ð°Ð»Ð¸
zoom_in.title=ÐÐ³Ð¾Ð»ÐµÐ¼ÑÐ²Ð°ÑÐµ
zoom_in_label=ÐÐ³Ð¾Ð»ÐµÐ¼Ð¸
zoom.title=ÐÑÐ¾Ð¼ÐµÐ½ÑÐ²Ð°ÑÐµ Ð½Ð° Ð³Ð¾Ð»ÐµÐ¼Ð¸Ð½Ð°
print.title=ÐÐµÑÐ°ÑÐµÑÐµ
print_label=ÐÐµÑÐ°ÑÐ¸
open_file.title=ÐÑÐ²Ð°ÑÐ°ÑÐµ  Ð´Ð°ÑÐ¾ÑÐµÐºÐ°
open_file_label=ÐÑÐ²Ð¾ÑÐ¸
download.title=ÐÑÐµÐ·ÐµÐ¼Ð°ÑÐµ
download_label=ÐÑÐµÐ·ÐµÐ¼Ð¸
bookmark.title=ÐÐ²Ð¾Ñ Ð¿ÑÐµÐ³Ð»ÐµÐ´ (ÐºÐ¾Ð¿Ð¸ÑÐ°Ñ Ð¸Ð»Ð¸ Ð¾ÑÐ²Ð¾ÑÐ¸ Ð²Ð¾ Ð½Ð¾Ð² Ð¿ÑÐ¾Ð·Ð¾ÑÐµÑ)
bookmark_label=ÐÐ²Ð¾Ñ Ð¿ÑÐµÐ³Ð»ÐµÐ´

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_slider.title=ÐÐºÐ»ÑÑÑÐ²Ð°ÑÐµ Ð½Ð° Ð»Ð¸Ð·Ð³Ð°Ñ
toggle_slider_label=ÐÐºÐ»ÑÑÐ¸ Ð»Ð¸Ð·Ð³Ð°Ñ
outline.title=ÐÑÐ¸ÐºÐ°Ð¶ÑÐ²Ð°ÑÐµ Ð½Ð° ÑÐ¾Ð´ÑÐ¶Ð¸Ð½Ð° Ð½Ð° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ¾Ñ
outline_label=Ð¡Ð¾Ð´ÑÐ¶Ð¸Ð½Ð° Ð½Ð° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ¾Ñ
thumbs.title=ÐÑÐ¸ÐºÐ°Ð¶ÑÐ²Ð°ÑÐµ Ð½Ð° Ð¸ÐºÐ¾Ð½Ð¸
thumbs_label=ÐÐºÐ¾Ð½Ð¸

# Document outline messages
no_outline=ÐÐµÐ¼Ð° ÑÐ¾Ð´ÑÐ¶Ð¸Ð½Ð°

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Ð¡ÑÑÐ°Ð½Ð¸ÑÐ° {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ÐÐºÐ¾Ð½Ð° Ð¾Ð´ ÑÑÑÐ°Ð½Ð¸ÑÐ° {{page}}

# Error panel labels
error_more_info=ÐÐ¾Ð²ÐµÑÐµ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸
error_less_info=ÐÐ¾Ð¼Ð°Ð»ÐºÑ Ð¸Ð½ÑÐ¾ÑÐ¼Ð°ÑÐ¸Ð¸
error_close=ÐÐ°ÑÐ²Ð¾ÑÐ¸
# LOCALIZATION NOTE (error_build): "{{build}}" will be replaced by the PDF.JS
# build ID.
error_build=PDF.JS Build: {{build}}
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ÐÐ¾ÑÐ°ÐºÐ°: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ÐÐ°ÑÐ¾ÑÐµÐºÐ°: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ÐÐ¸Ð½Ð¸ÑÐ°: {{line}}
rendering_error=ÐÐ°ÑÑÐ°Ð½Ð° Ð³ÑÐµÑÐºÐ° Ð¿ÑÐ¸ Ð¿ÑÐ¸ÐºÐ°Ð¶ÑÐ²Ð°ÑÐµÑÐ¾ Ð½Ð° ÑÑÑÐ°Ð½Ð¸ÑÐ°ÑÐ°.

# Predefined zoom values
page_scale_width=Ð¨Ð¸ÑÐ¸Ð½Ð° Ð½Ð° ÑÑÑÐ°Ð½Ð¸ÑÐ°
page_scale_fit=Ð¦ÐµÐ»Ð° ÑÑÑÐ°Ð½Ð¸ÑÐ°
page_scale_auto=ÐÐ²ÑÐ¾Ð¼Ð°ÑÑÐºÐ° Ð³Ð¾Ð»ÐµÐ¼Ð¸Ð½Ð°
page_scale_actual=ÐÐ¸ÑÑÐ¸Ð½ÑÐºÐ° Ð³Ð¾Ð»ÐµÐ¼Ð¸Ð½Ð°

loading_error_indicator=ÐÑÐµÑÐºÐ°
loading_error=ÐÐ°ÑÑÐ°Ð½Ð° Ð³ÑÐµÑÐºÐ° Ð¿ÑÐ¸ Ð²ÑÐ¸ÑÑÐ²Ð°ÑÐµÑÐ¾ Ð½Ð° PDF-Ð¾Ñ.

# LOCALIZATION NOTE (text_annotation_type): This is used as a tooltip.
# "{{[type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type=[{{type}} ÐÐ°Ð±ÐµÐ»ÐµÑÐºÐ°]
request_password=PDF-Ð¾Ñ Ðµ Ð·Ð°ÑÑÐ¸ÑÐµÐ½ ÑÐ¾ Ð»Ð¾Ð·Ð¸Ð½ÐºÐ°:


printing_not_supported=ÐÑÐµÐ´ÑÐ¿ÑÐµÐ´ÑÐ²Ð°ÑÐµ: ÐÐµÑÐ°ÑÐµÑÐµÑÐ¾ Ð½Ðµ Ðµ ÑÐµÐ»Ð¾ÑÐ½Ð¾ Ð¿Ð¾Ð´Ð´ÑÐ¶Ð°Ð½Ð¾ Ð²Ð¾ Ð¾Ð²Ð¾Ñ Ð¿ÑÐµÐ»Ð¸ÑÑÑÐ²Ð°Ñ.

find_highlight=ÐÐ·Ð½Ð°ÑÐ¸ ÑÑ

# Find panel button title and messages
find_label=ÐÐ°ÑÐ´Ð¸:
find_match_case_label=Ð¢Ð¾ÐºÐ¼Ñ ÑÐ°ÐºÐ°
find_next.title=ÐÐ°ÑÐ´Ð¸ ÑÐ° ÑÐ»ÐµÐ´Ð½Ð°ÑÐ° Ð¿Ð¾ÑÐ°Ð²Ð° Ð½Ð° ÑÑÐ°Ð·Ð°ÑÐ°
find_next_label=Ð¡Ð»ÐµÐ´Ð½Ð¾
find_not_found=Ð¤ÑÐ°Ð·Ð°ÑÐ° Ð½Ðµ Ðµ Ð¿ÑÐ¾Ð½Ð°ÑÐ´ÐµÐ½Ð°
find_previous.title=ÐÐ°ÑÐ´Ð¸ ÑÐ° Ð¿ÑÐµÐ´ÑÐ¾Ð´Ð½Ð°ÑÐ° Ð¿Ð¾ÑÐ°Ð²Ð° Ð½Ð° ÑÑÐ°Ð·Ð°ÑÐ°
find_previous_label=ÐÑÐµÑÑÐ¾Ð´Ð½Ð¾
find_reached_bottom=ÐÐ°ÑÐ°ÑÐµÑÐ¾ ÑÑÐ¸Ð³Ð½Ð° Ð´Ð¾ ÐºÑÐ°ÑÐ¾Ñ Ð½Ð° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ¾Ñ Ð¸ Ð¿Ð¾ÑÐ½ÑÐ²Ð° Ð¾Ð´ Ð¿Ð¾ÑÐµÑÐ¾Ðº
find_reached_top=ÐÐ°ÑÐ°ÑÐµÑÐ¾ ÑÑÐ¸Ð³Ð½Ð° Ð´Ð¾ Ð¿Ð¾ÑÐµÑÐ¾ÐºÐ¾Ñ Ð½Ð° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ¾Ñ Ð¸ Ð¿Ð¾ÑÐ½ÑÐ²Ð° Ð¾Ð´ ÐºÑÐ°ÑÐ¾Ñ
findbar.title=ÐÐ°ÑÐ´Ð¸ Ð²Ð¾ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ¾Ñ
findbar_label=ÐÐ°ÑÐ´Ð¸

# Context menu
first_page.label=ÐÐ´Ð¸ Ð´Ð¾ Ð¿ÑÐ²Ð°ÑÐ° ÑÑÑÐ°Ð½Ð¸ÑÐ°
invalid_file_error=ÐÐµÐ²Ð°Ð»Ð¸Ð´Ð½Ð° Ð¸Ð»Ð¸ ÐºÐ¾ÑÑÐ¼Ð¿Ð¸ÑÐ°Ð½Ð° PDF Ð´Ð°ÑÐ¾ÑÐµÐºÐ°.
last_page.label=ÐÐ´Ð¸ Ð´Ð¾ Ð¿Ð¾ÑÐ»ÐµÐ´Ð½Ð°ÑÐ° ÑÑÑÐ°Ð½Ð¸ÑÐ°
page_rotate_ccw.label=Ð Ð¾ÑÐ¸ÑÐ°Ñ ÑÐ¿ÑÐ¾ÑÐ¸Ð²Ð½Ð¾ Ð¾Ð´ ÑÑÑÐµÐ»ÐºÐ¸ÑÐµ Ð½Ð° ÑÐ°ÑÐ¾Ð²Ð½Ð¸ÐºÐ¾Ñ
page_rotate_cw.label=Ð Ð¾ÑÐ¸ÑÐ°Ñ Ð¿Ð¾ ÑÑÑÐµÐ»ÐºÐ¸ÑÐµ Ð½Ð° ÑÐ°ÑÐ¾Ð²Ð½Ð¸ÐºÐ¾Ñ
presentation_mode.title=ÐÑÐµÐ¼Ð¸Ð½Ð¸ Ð²Ð¾ Ð¿ÑÐµÐ·ÐµÐ½ÑÐ°ÑÐ¸ÑÐºÐ¸ ÑÐµÐ¶Ð¸Ð¼
presentation_mode_label=ÐÑÐµÐ·ÐµÐ½ÑÐ°ÑÐ¸ÑÐºÐ¸ ÑÐµÐ¶Ð¸Ð¼

# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
missing_file_error=ÐÐµÐ´Ð¾ÑÑÐ°ÑÑÐ²Ð° PDF Ð´Ð¾ÐºÑÐ¼ÐµÐ½Ñ.
printing_not_ready=ÐÑÐµÐ´ÑÐ¿ÑÐµÐ´ÑÐ²Ð°ÑÐµ: PDF Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ¾Ñ Ð½Ðµ Ðµ ÑÐµÐ»Ð¾ÑÐ½Ð¾ Ð²ÑÐ¸ÑÐ°Ð½ Ð·Ð° Ð¿ÐµÑÐ°ÑÐµÑÐµ.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ÐÐºÐ»ÑÑÐ¸ ÑÑÑÐ°Ð½Ð¸ÑÐ½Ð° Ð»ÐµÐ½ÑÐ°
toggle_sidebar_label=ÐÐºÐ»ÑÑÐ¸ ÑÑÑÐ°Ð½Ð¸ÑÐ½Ð° Ð»ÐµÐ½ÑÐ°
web_fonts_disabled=ÐÐ½ÑÐµÑÐ½ÐµÑ ÑÐ¾Ð½ÑÐ¾Ð²Ð¸ÑÐµ ÑÐµ Ð¾Ð½ÐµÐ²Ð¾Ð·Ð¼Ð¾Ð¶ÐµÐ½Ð¸: Ð½Ðµ Ð¼Ð¾Ð¶Ðµ Ð´Ð° ÑÐµ ÐºÐ¾ÑÐ¸ÑÑÐ°Ñ Ð²Ð³ÑÐ°Ð´ÐµÐ½Ð¸ÑÐµ PDF ÑÐ¾Ð½ÑÐ¾Ð²Ð¸.
