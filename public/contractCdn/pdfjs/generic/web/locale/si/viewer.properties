# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=à¶¸à·à¶§ à¶´à·à¶» à¶´à·à¶§à·à·
previous_label=à¶´à·à¶»
next.title=à¶¸à·à·à¶ à¶´à·à¶§à·à·
next_label=à¶¸à·à·à¶

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=à¶´à·à¶§à·à·:
page_of={{pageCount}} à¶à·à¶±à·

zoom_out.title=à¶à·à¶©à· à¶à¶»à¶±à·à¶±
zoom_out_label=à¶à·à¶©à· à¶à¶»à¶±à·à¶±
zoom_in.title=à·à·à·à·à¶½ à¶à¶»à¶±à·à¶±
zoom_in_label=à·à·à·à·à¶½ à¶à¶»à¶±à·à¶±
zoom.title=à·à·à·à·à¶½à¶«à¶º
presentation_mode.title=à¶à¶¯à·à¶»à·à¶´à¶­à·à¶à·à¶»à·à¶¸à· à¶´à·âà¶»à¶à·à¶»à¶º à·à·à¶­ à¶¸à·à¶»à·à·à¶±à·à¶±
presentation_mode_label=à¶à¶¯à·à¶»à·à¶´à¶­à·à¶à·à¶»à·à¶¸à· à¶´à·âà¶»à¶à·à¶»à¶º
open_file.title=à¶à·à¶±à·à· à·à·à·à·à¶­ à¶à¶»à¶±à·à¶±
open_file_label=à·à·à·à·à¶­ à¶à¶»à¶±à·à¶±
print.title=à¶¸à·à¶¯à·âà¶»à¶«à¶º
print_label=à¶¸à·à¶¯à·âà¶»à¶«à¶º
download.title=à¶¶à·à¶à¶±à·à¶±
download_label=à¶¶à·à¶à¶±à·à¶±
bookmark.title=à¶¯à·à¶±à¶§ à¶à¶­à· à¶¯à·à·à¶± (à¶´à·à¶§à¶´à¶­à· à¶à¶»à¶±à·à¶± à·à· à¶±à· à¶à·à·à·à·à·à¶ à·à·à·à·à¶­ à¶à¶»à¶±à·à¶±)
bookmark_label=à¶¯à·à¶±à¶§ à¶à¶­à· à¶¯à·à·à¶±

# Secondary toolbar and context menu
tools.title=à¶¸à·à·à¶½à¶¸à·
tools_label=à¶¸à·à·à¶½à¶¸à·
first_page.title=à¶¸à·à¶½à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±
first_page.label=à¶¸à·à¶½à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±
first_page_label=à¶¸à·à¶½à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±
last_page.title=à¶à·à·à¶±à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±
last_page.label=à¶à·à·à¶±à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±
last_page_label=à¶à·à·à¶±à· à¶´à·à¶§à·à·à¶§ à¶ºà¶±à·à¶±
page_rotate_cw.title=à¶¯à¶à·à·à·à¶«à·à·à¶»à·à¶­à· à¶·à·âà¶»à¶¸à¶«à¶º
page_rotate_cw.label=à¶¯à¶à·à·à·à¶«à·à·à¶»à·à¶­à· à¶·à·âà¶»à¶¸à¶«à¶º
page_rotate_cw_label=à¶¯à¶à·à·à·à¶«à·à·à¶»à·à¶­à· à¶·à·âà¶»à¶¸à¶«à¶º
page_rotate_ccw.title=à·à·à¶¸à·à·à¶»à·à¶­à· à¶·à·âà¶»à¶¸à¶«à¶º
page_rotate_ccw.label=à·à·à¶¸à·à·à¶»à·à¶­à· à¶·à·âà¶»à¶¸à¶«à¶º
page_rotate_ccw_label=à·à·à¶¸à·à·à¶»à·à¶­à· à¶·à·âà¶»à¶¸à¶«à¶º

hand_tool_enable.title=à·à·à·à¶­ à¶¸à·à·à¶½à¶¸ à·à¶à·âà¶»à·à¶º
hand_tool_enable_label=à·à·à·à¶­ à¶¸à·à·à¶½à¶¸ à·à¶à·âà¶»à·à¶º
hand_tool_disable.title=à·à·à·à¶­ à¶¸à·à·à¶½à¶¸ à¶à¶à·âà¶»à·à¶º
hand_tool_disable_label=à·à·à·à¶­ à¶¸à·à·à¶½à¶¸ à¶à¶à·âà¶»à·à¶º

# Document properties dialog box
document_properties.title=à¶½à·à¶à¶± à·à¶­à·à¶à¶¸à·...
document_properties_label=à¶½à·à¶à¶± à·à¶­à·à¶à¶¸à·...
document_properties_file_name=à¶à·à¶±à· à¶±à¶¸:
document_properties_file_size=à¶à·à¶±à· à¶´à·âà¶»à¶¸à·à¶«à¶º:
document_properties_kb={{size_kb}} KB ({{size_b}} à¶¶à¶ºà·à¶§)
document_properties_mb={{size_mb}} MB ({{size_b}} à¶¶à¶ºà·à¶§)
document_properties_title=à·à·à¶»à·à·à¶­à¶½à¶º:
document_properties_author=à¶à¶­à·²
document_properties_subject=à¶¸à·à¶­à·à¶à·à·:
document_properties_keywords=à¶ºà¶­à·à¶»à· à·à¶¯à¶±à·:
document_properties_creation_date=à¶±à·à¶»à·à¶¸à·à¶­ à¶¯à·à¶±à¶º:
document_properties_modification_date=à·à·à¶±à·à·à¶à¶½ à¶¯à·à¶±à¶º:
document_properties_date_string={{date}}, {{time}}
document_properties_creator=à¶±à·à¶»à·à¶¸à·à¶´à¶:
document_properties_producer=PDF à¶±à·à·à·à¶´à·à¶¯à¶:
document_properties_version=PDF à¶±à·à¶à·à¶­à·à·:
document_properties_page_count=à¶´à·à¶§à· à¶à¶«à¶±:
document_properties_close=à·à·à¶±à·à¶±

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=à¶´à·à¶­à· à¶­à·à¶»à·à·à¶§ à¶¸à·à¶»à·à·à¶±à·à¶±
toggle_sidebar_label=à¶´à·à¶­à· à¶­à·à¶»à·à·à¶§ à¶¸à·à¶»à·à·à¶±à·à¶±
outline.title=à¶½à·à¶à¶±à¶ºà· à¶´à·à¶§ à¶¸à·à¶ºà·à¶¸ à¶´à·à¶±à·à·à¶±à·à¶±
outline_label=à¶½à·à¶à¶±à¶ºà· à¶´à·à¶§ à¶¸à·à¶ºà·à¶¸
attachments.title=à¶à¶¸à·à¶«à·à¶¸à· à¶´à·à¶±à·à·à¶±à·à¶±
attachments_label=à¶à¶¸à·à¶«à·à¶¸à·
thumbs.title=à·à·à¶à·à¶­à· à¶»à· à¶´à·à¶±à·à·à¶±à·à¶±
thumbs_label=à·à·à¶à·à¶­à· à¶»à·
findbar.title=à¶½à·à¶à¶±à¶º à¶­à·à· à·à·à¶ºà¶±à·à¶±
findbar_label=à·à·à¶ºà¶±à·à¶±

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=à¶´à·à¶§à·à· {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=à¶´à·à¶§à·à·à· à·à·à¶à·à¶­ à¶»à·à· {{page}}

# Find panel button title and messages
find_label=à·à·à¶ºà¶±à·à¶±:
find_previous.title=à¶¸à· à·à·à¶à·âà¶º à¶à¶«à·à¶©à¶º à¶¸à·à¶§ à¶´à·à¶» à¶ºà·à¶¯à·à¶«à· à·à·à¶®à·à¶±à¶º à·à·à¶ºà¶±à·à¶±
find_previous_label=à¶´à·à¶»:
find_next.title=à¶¸à· à·à·à¶à·âà¶º à¶à¶«à·à¶©à¶º à¶¸à·à·à¶à¶§ à¶ºà·à¶¯à·à¶± à·à·à¶®à·à¶±à¶º à·à·à¶ºà¶±à·à¶±
find_next_label=à¶¸à·à·à¶
find_highlight=à·à·à¶ºà¶½à·à¶½ à¶à¶¯à·à¶¯à·à¶´à¶±à¶º
find_match_case_label=à¶à¶à·à¶»à· à¶à·à¶´à¶±à·à¶±
find_reached_top=à¶´à·à¶§à·à·à· à¶à·à· à¶à·à·à·à¶»à¶§ à¶½à¶à·à·à·à¶º, à¶´à·à· à·à·à¶§ à¶à¶¯à·à¶»à·à¶ºà¶§ à¶ºà¶¸à·à¶±à·
find_reached_bottom=à¶´à·à¶§à·à·à· à¶´à·à· à¶à·à·à·à¶»à¶§ à¶½à¶à·à·à·à¶º, à¶à·à· à·à·à¶§ à¶à¶¯à·à¶»à·à¶ºà¶§ à¶ºà¶¸à·à¶±à·
find_not_found=à¶à¶¶ à·à·à·à· à·à¶ à¶± à·à¶¸à· à¶±à·à·à·à¶º

# Error panel labels
error_more_info=à¶¶à·à·à· à¶­à·à¶»à¶­à·à¶»à·
error_less_info=à¶à·à¶¸ à¶­à·à¶»à¶­à·à¶»à·
error_close=à·à·à¶±à·à¶±
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (à¶±à·à¶à·à¶­à·à·: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=à¶´à¶«à·à·à·à¶©à¶º: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=à¶à·à¶±à·à·: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=à¶´à·à·à·à¶º: {{line}}
rendering_error=à¶´à·à¶§à·à· à¶»à·à¶±à·à¶©à¶»à· à·à·à¶¸à·à¶¯à· à¶à·à¶§à¶½à·à·à¶à· à·à¶§ à¶à·à¶±à·à¶«à·.

# Predefined zoom values
page_scale_width=à¶´à·à¶§à·à·à· à¶´à·à¶½
page_scale_fit=à¶´à·à¶§à·à·à¶§ à·à·à¶¯à·à·à· à¶½à·à·
page_scale_auto=à·à·à·à¶ºà¶à¶à·âà¶»à·à¶º à·à·à·à·à¶½à¶«à¶º
page_scale_actual=à¶±à·à¶ºà¶¸à·à¶­ à¶´à·âà¶»à¶¸à·à¶«à¶º
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=à¶¯à·à·à¶º
loading_error=PDF à¶´à·à¶»à¶«à¶º à·à·à¶¸à·à¶¯à· à¶¯à·à·à¶ºà¶à· à·à¶§ à¶à·à¶±à·à¶«à·.
invalid_file_error=à¶¯à·à·à·à¶­ à·à· à·à·à·à¶¯à·âà¶º PDF à¶à·à¶±à·à·.
missing_file_error=à¶±à·à¶­à·à·à· PDF à¶à·à¶±à·à·.
unexpected_response_error=à¶¶à¶½à·à¶´à·à¶»à·à¶­à·à¶­à· à¶±à·à·à· à·à·à·à·à¶¯à·à¶ºà¶ à¶´à·âà¶»à¶­à·à¶ à·à¶»à¶º.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} à·à·à·à·à¶­à¶»à¶º]
password_label=à¶¸à·à¶¸ PDF à¶à·à¶±à·à· à·à·à·à·à¶­ à¶à·à¶»à·à¶¸à¶§ à¶¸à·à¶»à¶´à¶¯à¶º à¶à¶­à·à·à¶­à· à¶à¶»à¶±à·à¶±.
password_invalid=à·à·à¶»à¶¯à· à¶¸à·à¶»à¶´à¶¯à¶ºà¶à·. à¶à¶»à·à¶«à·à¶à¶» à¶±à·à·à¶­ à¶à¶­à·à·à· à¶à¶»à¶±à·à¶±.
password_ok=à·à¶»à·
password_cancel=à¶à¶´à·

printing_not_supported=à¶à·à·à·à¶¯à¶ºà¶ºà·: à¶¸à·à¶¸ à¶à·à·à·à¶à¶º à¶¸à·à¶¯à·âà¶»à¶«à¶º à·à¶³à·à· à·à¶¸à·à¶´à·à¶»à·à¶«à¶ºà·à¶±à· à·à·à¶º à¶±à·à¶¯à¶à·à·à¶ºà·.
printing_not_ready=à¶à·à·à·à¶¯à¶ºà¶ºà·: à¶¸à·à¶¯à·âà¶»à¶«à¶º à·à¶³à·à· PDF à·à¶¸à·à¶´à·à¶»à·à¶«à¶ºà·à¶±à· à¶´à·à¶»à·à¶«à¶º à·à· à¶±à·à¶¸à·à¶­.
web_fonts_disabled=à¶¢à·à¶½ à¶à¶à·à¶»à· à¶à¶à·âà¶»à·à¶ºà¶ºà·: à¶­à·à·à·à¶½à· PDF à¶à¶à·à¶»à· à¶·à·à·à·à¶­ à¶à· à¶±à·à·à·à¶.
document_colors_disabled=PDF à¶½à·à¶à¶±à¶ºà¶§ à¶à·à·à¶±à·à¶à·à¶¸ à·à¶»à·à¶« à¶·à·à·à·à¶­à¶ºà¶§ à¶à¶© à¶±à·à¶½à·à¶¶à·: 'à¶´à·à¶§à· à·à·à¶­ à¶à·à·à¶±à·à¶à·à¶¸ à·à¶»à·à¶« à¶·à·à·à·à¶­à¶ºà¶§ à¶à¶©à¶¯à·à¶±à·à¶±' à¶à·à·à·à¶à¶º à¶¸à¶­ à¶à¶à·âà¶»à·à¶º à¶à¶» à¶à¶­.
