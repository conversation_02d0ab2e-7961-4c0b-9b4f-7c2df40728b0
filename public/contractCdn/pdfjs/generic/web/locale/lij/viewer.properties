# This Source Code Form is subject to the terms of the Mozilla Public
# License, v. 2.0. If a copy of the MPL was not distributed with this
# file, You can obtain one at http://mozilla.org/MPL/2.0/.

previous.title = PÃ gina precedÃ©nte
previous_label = PrecedÃ©nte
next.title = PÃ gina dÃ²ppo
next_label = PrÃ²scima
page_label = PÃ gina:
page_of = de {{pageCount}}
zoom_out.title = DiminoÃ¬sci zoom
zoom_out_label = DiminoÃ¬sci zoom
zoom_in.title = AomÃ©nta zoom
zoom_in_label = AomÃ©nta zoom
zoom.title = Zoom
print.title = StÃ npa
print_label = StÃ npa
open_file.title = Ãrvi file
open_file_label = Ãrvi
download.title = DescaregamÃ©nto
download_label = DescaregamÃ©nto
bookmark.title = VixÃ³n corÃ©nte (cÃ²pia Ã² Ã rvi inte 'n nÃªuvo barcÃ³n)
bookmark_label = VixÃ³n corÃ©nte
outline.title = VÃ©ddi strutÃ»a documÃ©nto
outline_label = StrutÃ»a documÃ©nto
thumbs.title = MÃ³stra miniatÃ»e
thumbs_label = MiniatÃ»e
thumb_page_title = PÃ gina {{page}}
thumb_page_canvas = MiniatÃ»a da pÃ gina {{page}}
error_more_info = CiÃ¹ informaÃ§ioÃ¬n
error_less_info = MÃªno informaÃ§ioÃ¬n
error_version_info = PDF.js v{{version}} (build: {{build}})
error_close = SÃ¦ra
missing_file_error = O file PDF o no gh'Ã©.
toggle_sidebar.title = AtÃ®va/dizatÃ®va bÃ¢ra de sciÃ nco
toggle_sidebar_label = AtÃ®va/dizatÃ®va bÃ¢ra de sciÃ nco
error_message = MesÃ ggio: {{message}}
error_stack = Stack: {{stack}}
error_file = File: {{file}}
error_line = LÃ¬nia: {{line}}
rendering_error = Gh'Ã© stÃ¦to 'n'erÃ´ itno rendering da pÃ gina.
page_scale_width = LarghÃ©ssa pÃ gina
page_scale_fit = AdÃ tta a una pÃ gina
page_scale_auto = Zoom aotomÃ tico
page_scale_actual = DimenscioÃ¬n efetÃ®ve
loading_error_indicator = ErÃ´
loading_error = S'Ã© verificÃ²u 'n'erÃ´ itno caregamÃ©nto do PDF.
printing_not_supported = AtenÃ§iÃ³n: a stÃ npa a no l'Ã© conpletamÃ©nte soportÃ¢ da sto navegatÃ´.

# Context menu
page_rotate_cw.label=GÃ®a in sÃ©nso do relÃ©uio
page_rotate_ccw.label=GÃ®a in sÃ©nso do relÃ©uio a-a revÃ¨rsa

presentation_mode.title=VÃ nni into mÃ²ddo de prezentaÃ§iÃ³n
presentation_mode_label=MÃ²ddo de prezentaÃ§iÃ³n

find_label = TrÃªuva:
find_previous.title = TrÃªuva a ripetiÃ§iÃ³n precedÃ©nte do tÃ¨sto da Ã§ercÃ¢
find_previous_label = PrecedÃ©nte
find_next.title = TrÃªuva a ripetiÃ§iÃ³n dÃ²ppo do tÃ¨sto da Ã§ercÃ¢
find_next_label = SegoÃ©nte
find_highlight = EvidÃ©nÃ§ia
find_match_case_label = MaiÃ³scole/minÃ³scole
find_reached_bottom = RazÃ³nto l'inÃ¬Ã§io da pÃ gina, contÃ¬noa da-a fÃ¬n
find_reached_top = RazÃ³nto a fÃ¬n da pÃ gina, contÃ¬noa da l'inÃ¬Ã§io
find_not_found = TÃ¨sto no trovÃ²u
findbar.title = TrÃªuva into documÃ©nto
findbar_label = TrÃªuva
first_page.label = VÃ nni a-a prÃ¬mma pÃ gina
last_page.label = VÃ nni a l'Ã¹rtima pÃ gina
invalid_file_error = O file PDF o l'Ã© no vÃ lido Ã² aroinÃ²u.

web_fonts_disabled = I font do web Ã©n dizativÃ¦: inposcÃ¬bile adÃªuviÃ¢ i carÃ teri do PDF.
printing_not_ready = AtenÃ§iÃ³n: o PDF o no l'Ã© ancÃ³n caregÃ²u conpletamÃ©nte pe-a stÃ npa.

document_colors_disabled = No l'Ã© poscÃ¬bile adÃªuviÃ¢ i prÃ²pi coÃ® pe-i documÃ©nti PDF: l'opÃ§iÃ³n do navegatÃ´ 'PermÃ¨tti a-e pÃ gine de Ã§Ã¨rne i prÃ²pi coÃ® in cÃ ngio de quÃ©lli inpostÃ¦' a l'Ã© dizativÃ¢.
text_annotation_type.alt = [AnotaÃ§iÃ³n: {{type}}]

first_page.title = VÃ nni a-a prÃ¬mma pÃ gina
first_page_label = VÃ nni a-a prÃ¬mma pÃ gina
last_page.title = VÃ nni a l'Ã¹rtima pÃ gina
last_page_label = VÃ nni a l'Ã¹rtima pÃ gina
page_rotate_ccw.title = GÃ®a into vÃ¨rso antiorÃ¢io
page_rotate_ccw_label = GÃ®a into vÃ¨rso antiorÃ¢io
page_rotate_cw.title = GÃ®a into vÃ¨rso orÃ¢io
page_rotate_cw_label = GÃ®a into vÃ¨rso orÃ¢io
tools.title = StrumÃ©nti
tools_label = StrumÃ©nti
password_label = DÃ¬mme a parÃ²lla segrÃªta pe arvÃ® sto file PDF.
password_invalid = ParÃ²lla segrÃªta sbaliÃ¢. PrÃªuva tÃ³rna.
password_ok = Va bÃ©n
password_cancel = AnÃ¹lla

document_properties.title = PropietÃ¦ do documÃ©ntoâ¦
document_properties_label = PropietÃ¦ do documÃ©ntoâ¦
document_properties_file_name = NÃ³mme file:
document_properties_file_size = DimensciÃ³n file:
document_properties_kb = {{size_kb}} kB ({{size_b}} byte)
document_properties_mb = {{size_kb}} MB ({{size_b}} byte)
document_properties_title = TÃ¬tolo:
document_properties_author = AotÃ´:
document_properties_subject = OgÃ©tto:
document_properties_keywords = ParÃ²lle ciÃ¢ve:
document_properties_creation_date = DÃ¦ta creaÃ§iÃ³n:
document_properties_modification_date = DÃ¦ta cangiamÃ©nto:
document_properties_date_string = {{date}}, {{time}}
document_properties_creator = AotÃ´ originÃ¢le:
document_properties_producer = ProdutÃ´ PDF:
document_properties_version = VersciÃ³n PDF:
document_properties_page_count = ContÃ©zzo pÃ gine:
document_properties_close = SÃ¦ra

hand_tool_enable.title = AtÃ®va strumÃ©nto mÃ n
hand_tool_enable_label = AtÃ®va strumÃ©nto mÃ n
hand_tool_disable.title = DizatÃ®va strumÃ©nto mÃ n
hand_tool_disable_label = DizatÃ®va strumÃ©nto mÃ n
