# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=åã®ãã¼ã¸ã¸æ»ãã¾ã
previous_label=åã¸
next.title=æ¬¡ã®ãã¼ã¸ã¸é²ã¿ã¾ã
next_label=æ¬¡ã¸

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=ãã¼ã¸:
page_of=/ {{pageCount}}

zoom_out.title=è¡¨ç¤ºãç¸®å°ãã¾ã
zoom_out_label=ç¸®å°
zoom_in.title=è¡¨ç¤ºãæ¡å¤§ãã¾ã
zoom_in_label=æ¡å¤§
zoom.title=æ¡å¤§/ç¸®å°
presentation_mode.title=ãã¬ã¼ã³ãã¼ã·ã§ã³ã¢ã¼ãã«åãæ¿ãã¾ã
presentation_mode_label=ãã¬ã¼ã³ãã¼ã·ã§ã³ã¢ã¼ã
open_file.title=ãã¡ã¤ã«ãæå®ãã¦éãã¾ã
open_file_label=éã
print.title=å°å·ãã¾ã
print_label=å°å·
download.title=ãã¦ã³ã­ã¼ããã¾ã
download_label=ãã¦ã³ã­ã¼ã
bookmark.title=ç¾å¨ã®ãã¥ã¼ã® URL ã§ã (ã³ãã¼ã¾ãã¯æ°ããã¦ã£ã³ãã¦ã«éã)
bookmark_label=ç¾å¨ã®ãã¥ã¼

# Secondary toolbar and context menu
tools.title=ãã¼ã«
tools_label=ãã¼ã«
first_page.title=æåã®ãã¼ã¸ã¸ç§»åãã¾ã
first_page.label=æåã®ãã¼ã¸ã¸ç§»å
first_page_label=æåã®ãã¼ã¸ã¸ç§»å
last_page.title=æå¾ã®ãã¼ã¸ã¸ç§»åãã¾ã
last_page.label=æå¾ã®ãã¼ã¸ã¸ç§»å
last_page_label=æå¾ã®ãã¼ã¸ã¸ç§»å
page_rotate_cw.title=ãã¼ã¸ãå³ã¸åè»¢ãã¾ã
page_rotate_cw.label=å³åè»¢
page_rotate_cw_label=å³åè»¢
page_rotate_ccw.title=ãã¼ã¸ãå·¦ã¸åè»¢ãã¾ã
page_rotate_ccw.label=å·¦åè»¢
page_rotate_ccw_label=å·¦åè»¢

hand_tool_enable.title=æã®ã²ããã¼ã«ãæå¹ã«ãã¾ã
hand_tool_enable_label=æã®ã²ããã¼ã«ãæå¹ã«ãã
hand_tool_disable.title=æã®ã²ããã¼ã«ãç¡å¹ã«ãã¾ã
hand_tool_disable_label=æã®ã²ããã¼ã«ãç¡å¹ã«ãã

# Document properties dialog box
document_properties.title=ææ¸ã®ãã­ããã£...
document_properties_label=ææ¸ã®ãã­ããã£...
document_properties_file_name=ãã¡ã¤ã«å:
document_properties_file_size=ãã¡ã¤ã«ãµã¤ãº:
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=ã¿ã¤ãã«:
document_properties_author=ä½æè:
document_properties_subject=ä»¶å:
document_properties_keywords=ã­ã¼ã¯ã¼ã:
document_properties_creation_date=ä½ææ¥:
document_properties_modification_date=æ´æ°æ¥:
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ã¢ããªã±ã¼ã·ã§ã³:
document_properties_producer=PDF ä½æ:
document_properties_version=PDF ã®ãã¼ã¸ã§ã³:
document_properties_page_count=ãã¼ã¸æ°:
document_properties_close=éãã

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ãµã¤ããã¼è¡¨ç¤ºãåãæ¿ãã¾ã
toggle_sidebar_label=ãµã¤ããã¼ã®åãæ¿ã
outline.title=ææ¸ã®ç®æ¬¡ãè¡¨ç¤ºãã¾ã
outline_label=ææ¸ã®ç®æ¬¡
attachments.title=æ·»ä»ãã¡ã¤ã«ãè¡¨ç¤ºãã¾ã
attachments_label=æ·»ä»ãã¡ã¤ã«
thumbs.title=ç¸®å°çãè¡¨ç¤ºãã¾ã
thumbs_label=ç¸®å°ç
findbar.title=ææ¸åãæ¤ç´¢ãã¾ã
findbar_label=æ¤ç´¢

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title={{page}} ãã¼ã¸
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ãã¼ã¸ã®ç¸®å°ç {{page}}

# Find panel button title and messages
find_label=æ¤ç´¢:
find_previous.title=æå®æå­åã«ä¸è´ãã 1 ã¤åã®é¨åãæ¤ç´¢ãã¾ã
find_previous_label=åã¸
find_next.title=æå®æå­åã«ä¸è´ããæ¬¡ã®é¨åãæ¤ç´¢ãã¾ã
find_next_label=æ¬¡ã¸
find_highlight=ãã¹ã¦å¼·èª¿è¡¨ç¤º
find_match_case_label=å¤§æå­/å°æå­ãåºå¥
find_reached_top=ææ¸åé ­ã«å°éããã®ã§æ«å°¾ã«æ»ã£ã¦æ¤ç´¢ãã¾ããã
find_reached_bottom=ææ¸æ«å°¾ã«å°éããã®ã§åé ­ã«æ»ã£ã¦æ¤ç´¢ãã¾ããã
find_not_found=è¦ã¤ããã¾ããã§ããã

# Error panel labels
error_more_info=è©³ç´°æå ±
error_less_info=è©³ç´°æå ±ã®éè¡¨ç¤º
error_close=éãã
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (ãã«ã: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ã¡ãã»ã¼ã¸: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ã¹ã¿ãã¯: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=ãã¡ã¤ã«: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=è¡: {{line}}
rendering_error=ãã¼ã¸ã®ã¬ã³ããªã³ã°ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã

# Predefined zoom values
page_scale_width=å¹ã«åããã
page_scale_fit=ãã¼ã¸ã®ãµã¤ãºã«åããã
page_scale_auto=èªåãºã¼ã 
page_scale_actual=å®éã®ãµã¤ãº
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ã¨ã©ã¼
loading_error=PDF ã®èª­ã¿è¾¼ã¿ä¸­ã«ã¨ã©ã¼ãçºçãã¾ãã
invalid_file_error=ç¡å¹ã¾ãã¯ç ´æãã PDF ãã¡ã¤ã«
missing_file_error=PDF ãã¡ã¤ã«ãè¦ã¤ããã¾ããã
unexpected_response_error=ãµã¼ãããäºæãã¬å¿ç­ãããã¾ããã

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} æ³¨é]
password_label=ãã® PDF ãã¡ã¤ã«ãéãããã®ãã¹ã¯ã¼ããå¥åãã¦ãã ããã
password_invalid=ç¡å¹ãªãã¹ã¯ã¼ãã§ããããä¸åº¦ããç´ãã¦ãã ããã
password_ok=OK
password_cancel=ã­ã£ã³ã»ã«

printing_not_supported=è­¦å: ãã®ãã©ã¦ã¶ã§ã¯å°å·ãå®å¨ã«ãµãã¼ãããã¦ãã¾ãã
printing_not_ready=è­¦å: PDF ãå°å·ããããã®èª­ã¿è¾¼ã¿ãçµäºãã¦ãã¾ãã
web_fonts_disabled=Web ãã©ã³ããç¡å¹ã«ãªã£ã¦ãã¾ã: åãè¾¼ã¾ãã PDF ã®ãã©ã³ããä½¿ç¨ã§ãã¾ãã
document_colors_disabled=PDF ææ¸ã¯ãWeb ãã¼ã¸ãæå®ããéè²ãä½¿ç¨ãããã¨ãã§ãã¾ãã: \u0027Web ãã¼ã¸ãæå®ããéè²\u0027 ã¯ãã©ã¦ã¶ã§ç¡å¹ã«ãªã£ã¦ãã¾ãã
