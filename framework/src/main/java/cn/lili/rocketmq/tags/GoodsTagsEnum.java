package cn.lili.rocketmq.tags;

/**
 * <AUTHOR>
 * @since 2020/12/9
 **/
public enum GoodsTagsEnum {

    /**
     * "生成商品索引"
     */
    GENERATOR_GOODS_INDEX("生成商品索引"),
    /**
     * "生成店铺商品索引"
     */
    GENERATOR_STORE_GOODS_INDEX("生成店铺商品索引"),
    /**
     * "更新商品索引"
     */
    UPDATE_GOODS_INDEX("更新商品索引"),
    /**
     * "更新商品索引促销信息"
     */
    UPDATE_GOODS_INDEX_PROMOTIONS("更新商品索引促销信息"),
    /**
     * "更新商品索引促销信息"
     */
    DELETE_GOODS_INDEX_PROMOTIONS("更新商品索引促销信息"),

    /**
     * "更新商品索引部分字段"
     */
    UPDATE_GOODS_INDEX_FIELD("更新商品索引部分字段"),
    /**
     * "重置商品索引"
     */
    RESET_GOODS_INDEX("重置商品索引"),
    /**
     * "删除店铺商品索引"
     */
    STORE_GOODS_DELETE("删除店铺商品索引"),
    /**
     * 下架商品"
     */
    DOWN("下架商品"),
    /**
     * "删除商品"
     */
    GOODS_DELETE("删除商品"),
    /**
     * "审核商品"
     */
    GOODS_AUDIT("审核商品"),
    /**
     * "收藏商品"
     */
    GOODS_COLLECTION("收藏商品"),
    /**
     * 同步商品分类名称
     */
    CATEGORY_GOODS_NAME("同步商品分类名称"),
    /**
     * "购买商品完成"
     */
    BUY_GOODS_COMPLETE("购买商品完成"),
    /**
     * "删除商品SKU"
     */
    SKU_DELETE("删除商品SKU"),
    /**
     * "查看商品"
     */
    VIEW_GOODS("查看商品"),
    /**
     * "商品评价"
     */
    GOODS_COMMENT_COMPLETE("商品评价");

    private final String description;

    GoodsTagsEnum(String description) {
        this.description = description;
    }

    public String description() {
        return description;
    }


}
