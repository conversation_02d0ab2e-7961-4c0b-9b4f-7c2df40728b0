package cn.lili.common.exception;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.utils.SecureUtil;

/**
 * 自定义回调通知错误异常
 */
public class IBackServiceException extends RuntimeException {

    private final ResultCode resultCode;

    private final String clientIdFromHeader;

    public IBackServiceException(String message) {
        super(message);
        this.clientIdFromHeader = SecureUtil.getClientIdFromHeader();
        this.resultCode = ResultCode.SERVICE_FAIL;
    }

    public IBackServiceException(ResultCode resultCode) {
        super(resultCode.message());
        this.resultCode = resultCode;
        this.clientIdFromHeader = SecureUtil.getClientIdFromHeader();
    }

    public IBackServiceException(ResultCode resultCode, Throwable cause) {
        super(cause);
        this.resultCode = resultCode;
        this.clientIdFromHeader = SecureUtil.getClientIdFromHeader();
    }

    @Override
    public Throwable fillInStackTrace() {
        return this;
    }

    public Throwable doFillInStackTrace() {
        return super.fillInStackTrace();
    }

    public ResultCode getResultCode() {
        return resultCode;
    }

    public String getClientIdFromHeader() {
        return clientIdFromHeader;
    }
}