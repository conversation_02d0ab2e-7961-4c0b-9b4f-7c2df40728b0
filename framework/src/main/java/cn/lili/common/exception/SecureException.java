package cn.lili.common.exception;

import cn.lili.common.enums.ResultCode;

/**
 * <AUTHOR>
 * @date 2025年06月10日15:01
 */
public class SecureException extends RuntimeException {
    private static final long serialVersionUID = 2359767895161832954L;
    private final ResultCode resultCode;

    public SecureException(String message) {
        super(message);
        this.resultCode = ResultCode.UN_AUTHORIZED;
    }

    public SecureException(ResultCode resultCode) {
        super(resultCode.message());
        this.resultCode = resultCode;
    }

    public SecureException(ResultCode resultCode, Throwable cause) {
        super(cause);
        this.resultCode = resultCode;
    }

    public ResultCode getResultCode() {
        return this.resultCode;
    }
}
