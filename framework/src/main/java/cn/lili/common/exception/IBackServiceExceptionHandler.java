package cn.lili.common.exception;

import cn.hutool.json.JSONUtil;
import cn.lili.common.dto.CallBackResultDto;
import cn.lili.common.utils.Func;
import cn.lili.common.utils.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @date 2025年06月16日19:14
 */
@Slf4j
@Order(1)
@RestControllerAdvice
public class IBackServiceExceptionHandler {

    /**
     * 统一拦截自定义回调错误，加密返回串
     * @param e
     * @return
     */
    @ExceptionHandler({IBackServiceException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public String handleError(IBackServiceException e) {
        CallBackResultDto result = CallBackResultDto.fail(Func.toStr(e.getResultCode().code())
                ,e.getResultCode().message());
        String resultStr = JSONUtil.toJsonStr(result);
        log.info("统一返回错误明文：{}",resultStr);
        return SignUtil.requestEncrypt(resultStr,e.getClientIdFromHeader());
    }

}
