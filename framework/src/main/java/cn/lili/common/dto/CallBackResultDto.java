package cn.lili.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 回调返回dto
 * <AUTHOR>
 * @date 2025/6/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CallBackResultDto<T> implements Serializable {

    /**
     * 成功响应code
     */
    private final static String SUCCESS = "000000";

    /**
     * 失败响应code
     */

    /**
     * 处理结果错误码
     */
    private String resultcode;
    /**
     * 处理结果描述
     */
    private String resultdesc;
    /**
     * 返回实体
     */
    private T data;

    public static CallBackResultDto success() {
        return new CallBackResultDto(SUCCESS, "成功");
    }

    public static <T> CallBackResultDto<T>success(T data) {
        return new CallBackResultDto(SUCCESS, "成功", data);
    }

    public static <T> CallBackResultDto<T>success(String message) {
        return new CallBackResultDto(SUCCESS, message);
    }


    /**
     * 错误返回对象
     * @param code
     * @param message
     * @return
     */
    public static CallBackResultDto fail(String code ,String message){
        return new CallBackResultDto(code,message);
    }

    public CallBackResultDto(String resultcode, String resultdesc) {
        this.resultcode = resultcode;
        this.resultdesc = resultdesc;
    }
}