package cn.lili.common.enums;

import cn.lili.common.constant.INotifyConstant;
import cn.lili.common.exception.IBackServiceException;
import cn.lili.common.utils.SecureUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025年06月17日10:28
 */
@Slf4j
@Getter
@AllArgsConstructor
public enum ICallBackServiceEnum {

    CREDIT_APPLY_NOTIFY(INotifyConstant.ORDER_FINANCING_PAID_NOTIFY,"订单融资支付通知"),
    ;

    /**
     * 业务类型
     */
    private final String type;

    /**
     * 说明
     */
    private final String desc;

    /**
     * 根据header头应用key获取对应
     * @param type
     * @return
     */
    public static String getBusinessServiceBySecretKey(String type){
        String header = SecureUtil.getClientIdFromHeader();
        StringBuilder builder = new StringBuilder();
        for (ICallBackServiceEnum service : ICallBackServiceEnum.values()) {
            if(service.getType().equals(type)){
                return builder.append(header).append(service.getType()).toString();
            }
        }
        log.info("公共请求头应用来源不存在：{}",type);
        throw new IBackServiceException(ResultCode.ILLEGAL_SOURCE);
    }

}
