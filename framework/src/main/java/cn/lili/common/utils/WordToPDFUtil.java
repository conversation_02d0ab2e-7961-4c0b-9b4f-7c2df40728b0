package cn.lili.common.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.jrzh_bases.PoiTiUtils;
import lombok.SneakyThrows;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Map;

/**
 * 将word按照关键字填充转成pdf的工具类
 * <AUTHOR>
 * @date 2025年05月08日11:08
 */
public class WordToPDFUtil {

    @SneakyThrows
    public static File contractTemplateGenFile(String templateWordUrl, Map<String, Object> backObj) {
        FileInputStream contractStream = null;
        InputStream templateInputStream = null;
        try {
            //获取合同模板文件
            if (cn.hutool.core.util.ObjectUtil.isEmpty(templateWordUrl)) {
                throw new ServiceException("模板文件不存在");
            }
            templateInputStream = PoiTiUtils.getFileInputStream(templateWordUrl);
            File file = PoiTiUtils.genPdfFile(backObj, templateInputStream);
            return file;
        } finally {
            if (cn.hutool.core.util.ObjectUtil.isNotEmpty(templateInputStream)) {
                templateInputStream.close();
            }
            if (ObjectUtil.isNotEmpty(contractStream)) {
                contractStream.close();
            }
        }
    }

}
