package cn.lili.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.lili.common.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.function.Supplier;

/**
 * 通用校验工具类
 * 用于简化参数校验和异常处理逻辑
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Slf4j
public class ValidationUtils {

    /**
     * 校验表达式为true，否则抛出异常
     *
     * @param expression 校验表达式
     * @param message    异常信息
     */
    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            log.error("校验失败：{}", message);
            throw new ServiceException(message);
        }
    }

    /**
     * 校验表达式为true，否则抛出异常（支持格式化消息）
     *
     * @param expression 校验表达式
     * @param message    异常信息模板
     * @param args       格式化参数
     */
    public static void isTrue(boolean expression, String message, Object... args) {
        if (!expression) {
            String formattedMessage = String.format(message, args);
            log.error("校验失败：{}", formattedMessage);
            throw new ServiceException(formattedMessage);
        }
    }

    /**
     * 校验表达式为false，否则抛出异常
     *
     * @param expression 校验表达式
     * @param message    异常信息
     */
    public static void isFalse(boolean expression, String message) {
        isTrue(!expression, message);
    }

    /**
     * 校验表达式为false，否则抛出异常（支持格式化消息）
     *
     * @param expression 校验表达式
     * @param message    异常信息模板
     * @param args       格式化参数
     */
    public static void isFalse(boolean expression, String message, Object... args) {
        isTrue(!expression, message, args);
    }

    /**
     * 校验对象不为空，否则抛出异常
     *
     * @param object  校验对象
     * @param message 异常信息
     */
    public static void notNull(Object object, String message) {
        isTrue(ObjectUtil.isNotEmpty(object), message);
    }

    /**
     * 校验对象不为空，否则抛出异常（支持格式化消息）
     *
     * @param object  校验对象
     * @param message 异常信息模板
     * @param args    格式化参数
     */
    public static void notNull(Object object, String message, Object... args) {
        isTrue(ObjectUtil.isNotEmpty(object), message, args);
    }

    /**
     * 校验字符串不为空，否则抛出异常
     *
     * @param str     校验字符串
     * @param message 异常信息
     */
    public static void notEmpty(String str, String message) {
        isTrue(StrUtil.isNotEmpty(str), message);
    }

    /**
     * 校验字符串不为空，否则抛出异常（支持格式化消息）
     *
     * @param str     校验字符串
     * @param message 异常信息模板
     * @param args    格式化参数
     */
    public static void notEmpty(String str, String message, Object... args) {
        isTrue(StrUtil.isNotEmpty(str), message, args);
    }

    /**
     * 校验集合不为空，否则抛出异常
     *
     * @param collection 校验集合
     * @param message    异常信息
     */
    public static void notEmpty(Collection<?> collection, String message) {
        isTrue(CollUtil.isNotEmpty(collection), message);
    }

    /**
     * 校验集合不为空，否则抛出异常（支持格式化消息）
     *
     * @param collection 校验集合
     * @param message    异常信息模板
     * @param args       格式化参数
     */
    public static void notEmpty(Collection<?> collection, String message, Object... args) {
        isTrue(CollUtil.isNotEmpty(collection), message, args);
    }

    /**
     * 校验数据库操作结果，否则抛出异常
     *
     * @param result     数据库操作返回的影响行数
     * @param message    异常信息
     * @param logMessage 日志信息
     * @param logArgs    日志参数
     */
    public static void checkDbResult(int result, String message, String logMessage, Object... logArgs) {
        if (result > 0) {
            log.info(logMessage, logArgs);
        } else {
            log.error("数据库操作失败，影响行数：{}，{}", result, String.format(logMessage, logArgs));
            throw new ServiceException(message);
        }
    }

    /**
     * 校验业务条件，支持自定义异常供应商
     *
     * @param expression        校验表达式
     * @param exceptionSupplier 异常供应商
     */
    public static void isTrue(boolean expression, Supplier<RuntimeException> exceptionSupplier) {
        if (!expression) {
            RuntimeException exception = exceptionSupplier.get();
            log.error("校验失败：{}", exception.getMessage());
            throw exception;
        }
    }

    /**
     * 执行数据库操作并校验结果
     *
     * @param operation    数据库操作
     * @param successMsg   成功日志信息
     * @param errorMsg     失败异常信息
     * @param logArgs      日志参数
     * @return 操作结果
     */
    public static boolean executeAndValidate(Supplier<Integer> operation, String successMsg, String errorMsg, Object... logArgs) {
        try {
            int result = operation.get();
            checkDbResult(result, errorMsg, successMsg, logArgs);
            return true;
        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("数据库操作异常：{}，参数：{}", e.getMessage(), logArgs, e);
            throw new ServiceException(errorMsg + "：" + e.getMessage());
        }
    }

    /**
     * 校验重复性，如果已存在则抛出异常
     *
     * @param count   查询到的记录数
     * @param message 异常信息
     * @param logArgs 日志参数
     */
    public static void checkDuplicate(long count, String message, Object... logArgs) {
        if (count > 0) {
            log.warn("数据重复：{}，参数：{}", message, logArgs);
            throw new ServiceException(message);
        }
    }

    /**
     * 校验数据是否存在，如果不存在则抛出异常
     *
     * @param data    查询到的数据
     * @param message 异常信息
     * @param logArgs 日志参数
     */
    public static void checkExists(Object data, String message, Object... logArgs) {
        if (ObjectUtil.isEmpty(data)) {
            log.warn("数据不存在：{}，参数：{}", message, logArgs);
            throw new ServiceException(message);
        }
    }
}
