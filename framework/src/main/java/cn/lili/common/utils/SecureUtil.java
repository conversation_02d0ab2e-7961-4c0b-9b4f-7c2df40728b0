package cn.lili.common.utils;

import cn.lili.common.exception.ServiceException;

import javax.servlet.http.HttpServletRequest;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年06月10日14:52
 */
public class SecureUtil {

    public static String getClientIdFromHeader() {
        String[] tokens = extractAndDecodeHeader();

        assert tokens.length == 2;

        return tokens[0];
    }

    public static String[] extractAndDecodeHeader() {
        try {
            String header = ((HttpServletRequest) Objects.requireNonNull(WebUtil.getRequest())).getHeader("Authorization");
            header = Func.toStr(header).replace("Basic%20", "Basic ");
            if (!header.startsWith("Basic ")) {
                throw new ServiceException("no client information in request header");
            } else {
                byte[] base64Token = header.substring(6).getBytes(StandardCharsets.UTF_8);

                byte[] decoded;
                try {
                    decoded = Base64.getDecoder().decode(base64Token);
                } catch (IllegalArgumentException var5) {
                    throw new RuntimeException("failed to decode basic authentication token");
                }

                String token = new String(decoded, StandardCharsets.UTF_8);
                int index = token.indexOf(":");
                if (index == -1) {
                    throw new RuntimeException("invalid basic authentication token");
                } else {
                    return new String[]{token.substring(0, index), token.substring(index + 1)};
                }
            }
        } catch (Throwable var6) {
            throw var6;
        }
    }

}
