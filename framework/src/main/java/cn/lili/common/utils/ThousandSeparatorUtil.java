package cn.lili.common.utils;

import cn.hutool.core.util.ObjectUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

/**
 * 千分位工具类
 */
public class ThousandSeparatorUtil {

    private static final DecimalFormat DECIMAL_FORMAT = new DecimalFormat("#,##0.00");

    /**
     * 将BigDecimal格式化为带千分位的字符串
     * @param decimal
     * @return
     */
    public static String format(BigDecimal decimal) {
        if (ObjectUtil.isEmpty(decimal)) {
            return format(decimal, "0.00");
        }
        return DECIMAL_FORMAT.format(decimal);
    }

    /**
     * 将BigDecimal格式化为带千分位的字符串
     * @param decimal
     * @param defaultStr decimao为null时返回值
     * @return
     */
    public static String format(BigDecimal decimal, String defaultStr) {
        if (ObjectUtil.isEmpty(decimal)) {
            return defaultStr;
        }
        return DECIMAL_FORMAT.format(decimal);
    }

}
