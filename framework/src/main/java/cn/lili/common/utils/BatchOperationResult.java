package cn.lili.common.utils;

import cn.lili.common.exception.ServiceException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 批量操作结果统计类
 * 用于统计批量操作的成功、跳过、失败数量
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Data
@Slf4j
public class BatchOperationResult {

    /**
     * 成功数量
     */
    private int successCount = 0;

    /**
     * 跳过数量
     */
    private int skipCount = 0;

    /**
     * 失败数量
     */
    private int errorCount = 0;

    /**
     * 操作名称（用于日志）
     */
    private String operationName;

    public BatchOperationResult(String operationName) {
        this.operationName = operationName;
    }

    /**
     * 增加成功数量
     */
    public void addSuccess() {
        this.successCount++;
    }

    /**
     * 增加成功数量
     *
     * @param count 增加的数量
     */
    public void addSuccess(int count) {
        this.successCount += count;
    }

    /**
     * 增加跳过数量
     */
    public void addSkip() {
        this.skipCount++;
    }

    /**
     * 增加跳过数量
     *
     * @param count 增加的数量
     */
    public void addSkip(int count) {
        this.skipCount += count;
    }

    /**
     * 增加失败数量
     */
    public void addError() {
        this.errorCount++;
    }

    /**
     * 增加失败数量
     *
     * @param count 增加的数量
     */
    public void addError(int count) {
        this.errorCount += count;
    }

    /**
     * 获取总数量
     *
     * @return 总数量
     */
    public int getTotalCount() {
        return successCount + skipCount + errorCount;
    }

    /**
     * 是否有成功的操作
     *
     * @return 是否有成功
     */
    public boolean hasSuccess() {
        return successCount > 0;
    }

    /**
     * 是否有失败的操作
     *
     * @return 是否有失败
     */
    public boolean hasError() {
        return errorCount > 0;
    }

    /**
     * 是否全部成功
     *
     * @return 是否全部成功
     */
    public boolean isAllSuccess() {
        return successCount > 0 && errorCount == 0;
    }

    /**
     * 记录开始日志
     *
     * @param totalItems 总项目数
     * @param params     额外参数
     */
    public void logStart(int totalItems, Object... params) {
        log.info("开始{}，总数量：{}，参数：{}", operationName, totalItems, params);
    }

    /**
     * 记录完成日志
     */
    public void logComplete() {
        log.info("{}完成，成功：{}，跳过：{}，失败：{}", operationName, successCount, skipCount, errorCount);
    }

    /**
     * 记录跳过日志
     *
     * @param reason 跳过原因
     * @param params 参数
     */
    public void logSkip(String reason, Object... params) {
        log.warn("{}跳过：{}，参数：{}", operationName, reason, params);
        addSkip();
    }

    /**
     * 记录错误日志
     *
     * @param reason 错误原因
     * @param params 参数
     */
    public void logError(String reason, Object... params) {
        log.error("{}失败：{}，参数：{}", operationName, reason, params);
        addError();
    }

    /**
     * 记录错误日志（带异常）
     *
     * @param reason 错误原因
     * @param e      异常
     * @param params 参数
     */
    public void logError(String reason, Exception e, Object... params) {
        log.error("{}异常：{}，参数：{}，错误：{}", operationName, reason, params, e.getMessage(), e);
        addError();
    }

    /**
     * 检查结果并抛出异常（如果有失败）
     */
    public void checkAndThrowIfError() {
        if (hasError()) {
            String message = String.format("%s部分失败，成功：%d，跳过：%d，失败：%d", 
                operationName, successCount, skipCount, errorCount);
            throw new ServiceException(message);
        }
    }

    /**
     * 获取结果描述
     *
     * @return 结果描述
     */
    public String getResultDescription() {
        if (isAllSuccess()) {
            return String.format("%s全部成功，成功：%d", operationName, successCount);
        } else if (hasSuccess() && hasError()) {
            return String.format("%s部分成功，成功：%d，跳过：%d，失败：%d", 
                operationName, successCount, skipCount, errorCount);
        } else if (!hasSuccess() && hasError()) {
            return String.format("%s全部失败，跳过：%d，失败：%d", operationName, skipCount, errorCount);
        } else {
            return String.format("%s无有效操作，跳过：%d", operationName, skipCount);
        }
    }

    /**
     * 返回操作是否成功（至少有一个成功且没有失败）
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return hasSuccess() && !hasError();
    }
}
