package cn.lili.common.utils;

import cn.hutool.extra.spring.SpringUtil;
import cn.lili.cache.impl.RedisCache;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.cipher.CipherManager;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;

/**
 * <AUTHOR>
 * @date 2025年06月10日18:16
 */
@Slf4j
public class SignUtil {

    // 平台私钥
    private final static String PLAT_PRI_KEY;
    // 平台公钥
    private final static String PLAT_PUB_KEY;

    // 合作方公钥
    private static String PARTNER_PUB_KEY;

    // 合作方私钥
    private final static String PARTNER_PRI_KEY;

    private final static String CACHE_PUB_KEY = "sign:capital:";

    static {
        String profiles = getActiveProfiles();
        if ("prod".equals(profiles)) {
            PLAT_PRI_KEY = loadFile("conf/prod/plat_private_key.pem");
            PLAT_PUB_KEY = loadFile("conf/prod/plat_public_key.pem");
            PARTNER_PRI_KEY = loadFile("conf/prod/partner-pri-key.pem");
        } else if("test".equals(profiles)) {
            PLAT_PRI_KEY = loadFile("conf/test/plat_private_key.pem");
            PLAT_PUB_KEY = loadFile("conf/test/gongJin/plat_public_key.pem");
            PARTNER_PRI_KEY = loadFile("conf/test/partner-pri-key.pem");
        }else  {
            PLAT_PRI_KEY = loadFile("conf/dev/plat_private_key.pem");
            PLAT_PUB_KEY = loadFile("conf/dev/plat_public_key.pem");
            PARTNER_PRI_KEY = loadFile("conf/dev/partner-pri-key.pem");
        }
    }

    /**
     * 请求三方接口的参数进行加密
     * @param paramJson 需加密的参数json
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/26 15:29
     */
    public static String requestEncrypt(String paramJson) {
        PARTNER_PUB_KEY = loadPartnerPublicKey(null);
        return CipherManager.stdCipherManager.secData(paramJson, PARTNER_PUB_KEY, PLAT_PRI_KEY);
    }

    /**
     * 请求三方接口的参数进行加密
     * @param paramJson 需加密的参数json
     * @return java.lang.String
     * <AUTHOR>
     * @date 2024/1/26 15:29
     */
    public static String requestEncrypt(String paramJson,String header) {
        PARTNER_PUB_KEY = loadPartnerPublicKey(header);
        return CipherManager.stdCipherManager.secData(paramJson, PARTNER_PUB_KEY, PLAT_PRI_KEY);
    }

    /**
     * 合作方接受解密
     * @param paramJson 加密参数字符串
     * @return java.lang.String 返回原参数json
     * <AUTHOR>
     * @date 2024/1/26 15:34
     */
    public static String responseLocalDecrypt(String paramJson) {
        //PARTNER_PUB_KEY = loadPartnerPublicKey(null);
        return CipherManager.stdCipherManager.analyzeData(paramJson, PLAT_PUB_KEY, PARTNER_PRI_KEY);
    }


    /**
     * 合作方响应解密
     * @param paramJson 加密参数字符串
     * @return java.lang.String 返回原参数json
     * <AUTHOR>
     * @date 2024/1/26 15:34
     */
    public static String responseDecrypt(String paramJson) {
        PARTNER_PUB_KEY = loadPartnerPublicKey(null);
        return CipherManager.stdCipherManager.analyzeData(paramJson, PARTNER_PUB_KEY, PLAT_PRI_KEY);
    }

    /**
     * 合作方响应解密
     * @param paramJson 加密参数字符串
     * @return java.lang.String 返回原参数json
     * <AUTHOR>
     * @date 2024/1/26 15:34
     */
    public static String responseDecrypt(String paramJson,String header) {
        PARTNER_PUB_KEY = loadPartnerPublicKey(header);
        return CipherManager.stdCipherManager.analyzeData(paramJson, PARTNER_PUB_KEY, PLAT_PRI_KEY);
    }

    /**
     * 加载对应外部请求加密key
     * <AUTHOR>
     * @date 2024/8/9 18:41
     * @return java.lang.String
     */
    public static String loadPartnerPublicKey(String header) {
        if(StringUtil.isBlank(header)){
            header = SecureUtil.getClientIdFromHeader();
        }
        RedisCache bladeRedis = SpringUtil.getBean(RedisCache.class);
        String pubKey = Func.toStr(bladeRedis.get(CACHE_PUB_KEY + Func.toStr(header)));
        if (StringUtil.isBlank(pubKey)) {
            String dev = getActiveProfiles();
            String filePath = "conf/" + dev + "/" + Func.toStr(header);
            pubKey = loadFile(filePath + "/partner-pub-key.pem");
            bladeRedis.put(CACHE_PUB_KEY + header, pubKey);
        }
        return pubKey;
    }

    /**
     * 加载文件内容
     *
     * @param filePath 配置文件路迳
     * @return 密钥字符串
     */
    private static String loadFile(String filePath) {
        BufferedReader bufferedReader = null;
        try {
            Resource resource = new ClassPathResource(filePath);
            bufferedReader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
            StringBuilder builder = new StringBuilder();
            String line = null;
            while ((line = bufferedReader.readLine()) != null) {
                if (line.isEmpty() || line.charAt(0) == '-') {
                    continue;
                }
                builder.append(line).append("\r");
            }
            return builder.toString();
        } catch (Exception e) {
            log.error("钥匙文件处理错误");
        } finally {
            if (null != bufferedReader) {
                try {
                    bufferedReader.close();
                } catch (IOException e) {
                    log.error("钥匙文件处理错误");
                }
            }
        }
        return null;
    }

    private static String getActiveProfiles() {
        String profiles = "dev";
        String[] activeProfiles = SpringUtil.getBean(Environment.class).getActiveProfiles();
        if (null != activeProfiles && activeProfiles.length > 0) {
            profiles = activeProfiles[0];
        }
        return profiles;
    }

}
