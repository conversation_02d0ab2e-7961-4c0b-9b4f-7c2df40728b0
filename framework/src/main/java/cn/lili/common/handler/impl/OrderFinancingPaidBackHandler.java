package cn.lili.common.handler.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.constant.INotifyConstant;
import cn.lili.common.dto.CallBackResultDto;
import cn.lili.common.dto.OrderFinancingPaidNotify;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.IBackServiceException;
import cn.lili.common.handler.AbstractPaidBackHandler;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dos.OrderItemPayRecords;
import cn.lili.modules.order.order.entity.enums.PayRecordsPaymentStatusEnum;
import cn.lili.modules.order.order.service.OrderItemPayRecordsService;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.service.OrderService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * 订单融资支付回调处理器
 * <AUTHOR>
 * @date 2025年06月16日19:17
 */
@Slf4j
@Service(INotifyConstant.GONG_JING_HEADER + INotifyConstant.ORDER_FINANCING_PAID_NOTIFY)
@RequiredArgsConstructor
public class OrderFinancingPaidBackHandler extends AbstractPaidBackHandler<OrderFinancingPaidNotify> {

    private final OrderService orderService;
    private final OrderItemService orderItemService;
    private final OrderItemPayRecordsService orderItemPayRecordsService;

    @Override
    public OrderFinancingPaidNotify prepareParams(String param) {
        log.info("订单融资支付回调解密参数：{}", param);
        return JSONUtil.toBean(param, OrderFinancingPaidNotify.class);
    }

    @Override
    public CallBackResultDto businessProcessing(OrderFinancingPaidNotify orderFinancingPaidNotify) {
        try {
            OrderItem orderItem = orderItemService.getBySn(orderFinancingPaidNotify.getOrderItemSn());
            if (ObjectUtil.isEmpty(orderItem)) {
                throw new IBackServiceException(ResultCode.ORDER_ITEM_NOT_EXIST);
            }
            Order order = orderService.getBySn(orderItem.getOrderSn());
            if (ObjectUtil.isEmpty(order)) {
                throw new IBackServiceException(ResultCode.ORDER_NOT_EXIST);
            }
            List<String> paymentStatus = Arrays.asList(PayRecordsPaymentStatusEnum.PAYMENT_UN_CONFIRM.name(), PayRecordsPaymentStatusEnum.PAYMENT_CONFIRM.name());
            OrderItemPayRecords payRecords = orderItemPayRecordsService.getOne(Wrappers.<OrderItemPayRecords>lambdaQuery()
                    .eq(OrderItemPayRecords::getBackPaidSn, orderFinancingPaidNotify.getFinanceNo())
                    .in(OrderItemPayRecords::getPaymentStatus, paymentStatus)
            );
            if (ObjectUtil.isNotEmpty(payRecords)) {
                throw new IBackServiceException("该订单融资已有待确认或已确认的支付记录");
            }
            Boolean financingPayment = orderItemPayRecordsService.orderItemFinancingPayment(orderItem, orderFinancingPaidNotify, order.getMemberId());
            return CallBackResultDto.success(financingPayment);
        } catch (Exception e) {
            log.info("订单融资支付回调业务处理失败:", e);
            throw new IBackServiceException(e.getMessage());
        }
    }
}
