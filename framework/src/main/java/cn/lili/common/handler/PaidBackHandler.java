package cn.lili.common.handler;

import cn.lili.common.dto.CallBackResultDto;

import java.util.Map;

/**
 * 付款通知回调
 * <AUTHOR>
 * @date 2025年06月16日17:38
 */
public interface PaidBackHandler<T> {

    /**
     * 解密参数
     * @param param
     * @return
     */
    String decrypt(Map<String,Object> param);

    /**
     * 参数转换，json转对象
     * @param param
     * @return
     */
    T prepareParams(String param);

    /**
     * 业务处理前置操作
     * @param t
     */
    void before(T t);

    /**
     * 业务处理后置操作
     * @param t
     */
    void after(T t);

    /**
     * 通知业务处理
     * @param t
     * @return
     */
    CallBackResultDto businessProcessing(T t);

    /**
     * 业务封装
     * @param param
     * @return
     */
    String handler(Map<String,Object> param);
}
