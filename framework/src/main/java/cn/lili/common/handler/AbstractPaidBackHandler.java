package cn.lili.common.handler;

import cn.hutool.json.JSONUtil;
import cn.lili.common.dto.CallBackResultDto;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.IBackServiceException;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.SignUtil;
import lombok.extern.slf4j.Slf4j;
import org.springblade.cipher.enums.SeriTypeEnum;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025年06月16日18:51
 */
@Slf4j
public abstract class AbstractPaidBackHandler<T> implements PaidBackHandler<T> {
    @Override
    public String decrypt(Map<String, Object> param) {
        try{
            log.info("自定义付款回调通知加密参数：{}", JSONUtil.toJsonStr(param));
            return SignUtil.responseDecrypt(SeriTypeEnum.SPLIC.serialize(param));
        }catch (Exception e){
            throw new IBackServiceException(ResultCode.REQUEST_BODY_EXCEPTION);
        }
    }

    @Override
    public void before(T t) {

    }

    @Override
    public void after(T t) {

    }

    @Override
    public String handler(Map<String, Object> param) {
        //处理解密
        String decryptStr = decrypt(param);
        log.info("自定义付款回调通知解密参数：{}",decryptStr);
        T t = prepareParams(decryptStr);

        before(t);

        CallBackResultDto<T> dto = businessProcessing(t);

        after(t);
        String resultStr =JSONUtil.toJsonStr(dto);
        log.info("资方接口请求加密前参数：{}",resultStr);
        return SignUtil.requestEncrypt(resultStr);
    }
}
