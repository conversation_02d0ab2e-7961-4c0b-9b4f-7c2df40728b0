package cn.lili.modules.audit.entity.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核类型枚举
 */
@Getter
@AllArgsConstructor
public enum AuditTypeEnum {
    /**
     * 店铺审核
     */
    STORE_AUDIT("店铺审核"),
    /**
     * 商品审核
     */
    GOODS_AUDIT("商品审核"),
    /**
     * 银行变更审核
     */
    BANK_CHANGE_AUDIT("银行变更审核");

    private final String description;

    public String getDescription() {
        return description;
    }

    public String getValue() {
        return name();
    }
}
