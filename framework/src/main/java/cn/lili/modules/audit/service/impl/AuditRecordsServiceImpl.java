package cn.lili.modules.audit.service.impl;

import cn.lili.modules.audit.entity.AuditRecords;
import cn.lili.modules.audit.mapper.AuditRecordsMapper;
import cn.lili.modules.audit.service.IAuditRecordsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 审核记录接口业务层实现
 *
 * <AUTHOR>
 */
@Service
public class AuditRecordsServiceImpl extends ServiceImpl<AuditRecordsMapper, AuditRecords> implements IAuditRecordsService {


}
