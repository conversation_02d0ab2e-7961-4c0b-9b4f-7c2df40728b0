package cn.lili.modules.audit.entity;


import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 审核记录
 *
 * <AUTHOR>
 */
@Data
@TableName("li_audit_records")
@ApiModel(value = "会员")
@NoArgsConstructor
@AllArgsConstructor
public class AuditRecords extends BaseEntity {

    /**
     * 审核类型
     */
    @ApiModelProperty("审核类型")
    private String auditType;
    /**
     * 审核目标id
     */
    @ApiModelProperty("审核目标id")
    private String targetId;
    /**
     * 审核变量
     */
    @ApiModelProperty("审核变量")
    private String auditVariables;

    /**
     * 审核状态
     */
    @ApiModelProperty("审核状态")
    private String auditStatus;
    /**
     * 审核备注
     */
    @ApiModelProperty("审核备注")
    private String auditComment;
    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    private Date auditTime;


}
