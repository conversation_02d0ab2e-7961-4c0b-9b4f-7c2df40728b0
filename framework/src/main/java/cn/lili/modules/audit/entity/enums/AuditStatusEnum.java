package cn.lili.modules.audit.entity.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审核状态枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AuditStatusEnum {
    /**
     * 审核中
     */
    APPLYING("审核中"),

    /**
     * 已通过
     */
    SUCCESS("已通过"),

    /**
     * 审核拒绝
     */
    REFUSED("审核拒绝"),

    ;

    private final String description;


    public String getDescription() {
        return description;
    }

    public String getValue() {
        return name();
    }
}
