package cn.lili.modules.otherapi.huawei.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Description:
 * @date 2025年04月17日 17:51
 */
@Getter
@AllArgsConstructor
@Slf4j
public enum InvoiceExceptionEnum {


    /***
     * ocr  识别异常
     */
    OVER_TIME("1001", "验证次数次数过多,请隔一段时间后再试"),


    NOT_FOUND_TAX("1002", "被查验发票信息有误，请核对发票信息是否填写正确"),

    NOT_COMMON_WITH_TAX_AUTH("1003", "发票验证信息与税务机关信息不一致"),


    OVER_FIVE_YEARS("1004", "超过5年的发票不能查验"),


    UNSUPPORT_INVOICE_TYPE("1005", "被查验发票信息有误，请核对发票信息是否填写正确"),


    ABNORMAL_INVOICE_INFO("1006", "被查验发票信息有误，请核对发票信息是否填写正确"),


    BEING_VERTIFY("1007", "该批次发票正在核验中，请第二天再查验"),


    REQUEST_OVER_TIME("1008", "（国税局）发票查询服务请求超时，请提交工单咨询"),


    NOT_SUPPORT_REGION("1009", "（国税局）查验请求被拒绝，此区域暂未开通线上查验通道"),


    PARAMETER_ERROR("1010", "被查验发票信息有误，请核对发票信息是否填写正确"),


    INVALID_REQUEST("1011", "被查验发票信息有误，请核对发票信息是否填写正确"),


    INVALID_PARAMETER_LENGTH("1012", "请检查服务请求参数，参数长度有要求，是否超过限制"),


    PARAMETER_LEFT_BLANK("1013", "请检查服务请求参数，必要参数不能为空"),


    PARAMS_NOT_STANDARD("AIS.0101", "输入参数不符合规范"),

    PIC_NOT_SUPPORT("AIS.0102", "图片格式不支持"),


    PIC_SIZE_NOT_SUPPORT("AIS.0103", "图片大小不支持"),


    PIC_TYPE_NOT_SUPPORT("AIS.0104", "非支持的图片类型或图片质量差"),

    PIC_ALG_NOT_SUCCESS("AIS.0105", "算法计算失败"),


    API_NOT_EXSIT("APIG.0101", "访问的API不存在或尚未在环境中发布"),


    BACKEND_OVER_TIME("APIG.0201", "请求超时"),


    AUTH_FAIL("APIG.0301", "身份验证信息不正确"),


    OVER_RATE_LIMIT("APIG.0308", "发送请求超过了服务的默认配置限流"),


    INVALID_TOKEN("ModelArts.0203", "非法Token"),


    BLANK_TOKEN("ModelArts.4101", "token为空"),


    PARSE_TOKEN_ERROR("ModelArts.4102", "token解析失败"),


    INVALID_TOKEN_HEADER("ModelArts.4103", "非法token"),


    INVALID_BODY_CONTENT("ModelArts.4104", "请求body体长度不合法"),


    INVALD_BODY_JSON_STRU("ModelArts.4105", "请求body体json格式不对"),


    INVALID_AUTH_REQUEST("ModelArts.4106", "用户账号受限"),


    GET_AK_SK_ERROR("ModelArts.4107", "获取用户临时AK，SK异常"),


    NOT_SERVICE_ID("ModelArts.4201", "请求url中需要包含服务ID"),


    NOT_VALID_URL("ModelArts.4202", "请求url格式不合法"),


    NOT_HAS_PERMIT("ModelArts.4203", "没有权限访问"),


    NOT_SUBCRIBE_API("ModelArts.4204", "没有订阅该api"),


    FAIL_TO_GET_AFMIN_TOKEN("ModelArts.4301", "获取服务管理员token失败"),


    GATEWAY_ERROR("ModelArts.4302", "访问服务失败"),


    MAX_CONCUREENCY_ERROR("ModelArts.4401", "最大并发错误"),


    BACK_SERVICE_TIME_ERROR("ModelArts.4402", "服务处理超时"),


    SERVIE_NOT_USEABLE("ModelArts.4403", "服务不可用"),


    RESPONSE_ERROR("ModelArts.4502", "服务响应失败"),


    SERVICE_NOT_FONUD("ModelArts.4503", "服务找不到"),


    CHILDREN_API_SERVICE_NOT_FOUND("ModelArts.4504", "子服务不存在"),


    INTERAL_ERROR("ModelArts.4505", "内部服务错误"),


    SUBSERVICE_FOUND_ERROR("ModelArts.4506", "子服务不存在"),


    SERVICE_NOT_START("ModelArts.4508", "服务未启动"),


    URL_NOT_ALLOW("ModelArts.4601", "下载地址不合法,请输入正确地址"),


    OBTAIN_FILE_URL_ERROR("ModelArts.4603", "外网下载地址不合法"),


    QUERY_OBS_FAIL("ModelArts.4702", "查询OBS委托失败"),


    OBS_URL_INVALID("ModelArts.4703", "OBS地址不合法"),


    GET_FILE_OBS_FAIL("ModelArts.4704", "获取OBS文件失败"),


    OBS_SIZE_OVER("ModelArts.4705", "OBS大小超标"),


    OBS_FILE_NOT_EXSIT("ModelArts.4706", "OBS文件不存在");


    /**
     * 异常类型
     */
    private final String exceptionType;

    /**
     * 异常描述
     */
    private final String desc;

    /***
     *
     * @param exceptionType
     * @return
     */
    public static InvoiceExceptionEnum getInvoiceDescByType(String exceptionType) {
        for (InvoiceExceptionEnum invoiceExceptionEnum : InvoiceExceptionEnum.values()) {
            if (invoiceExceptionEnum.getExceptionType().equals(exceptionType)) {
                return invoiceExceptionEnum;
            }
        }
        return null;
    }


}