//package cn.lili.modules.otherapi.huawei.serviceimpl;
//
//import cn.lili.modules.otherapi.huawei.entity.Attach;
//import cn.lili.modules.otherapi.huawei.mapper.AttachMapper;
//import cn.lili.modules.otherapi.huawei.service.IAttachService;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import org.springframework.stereotype.Service;
//
//@Service
//public class AttachServiceImpl extends ServiceImpl<AttachMapper, Attach> implements IAttachService {
//}
