package cn.lili.modules.otherapi.sky.dto;

import java.io.Serializable;
import java.util.List;

public class SupplierBusinessInfo implements Serializable {
//    List<SupplierOwnerShipStruct> supplierOwnerShipStructList;
    private CompanyBasicInfo companyBasicInfo;
//
//    public SupplierBusinessInfo() {
//    }
//
//    public List<SupplierOwnerShipStruct> getSupplierOwnerShipStructList() {
//        return this.supplierOwnerShipStructList;
//    }
//
    public CompanyBasicInfo getCompanyBasicInfo() {
        return this.companyBasicInfo;
    }
//
//    public void setSupplierOwnerShipStructList(List<SupplierOwnerShipStruct> supplierOwnerShipStructList) {
//        this.supplierOwnerShipStructList = supplierOwnerShipStructList;
//    }

    public void setCompanyBasicInfo(CompanyBasicInfo companyBasicInfo) {
        this.companyBasicInfo = companyBasicInfo;
    }
}