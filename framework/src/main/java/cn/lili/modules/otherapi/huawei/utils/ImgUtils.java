package cn.lili.modules.otherapi.huawei.utils;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.ObjectUtil;
import lombok.SneakyThrows;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 图片工具类
 */
public class ImgUtils {
	/**
	 * 从网络Url中下载文件
	 *
	 * @param urlStr =
	 */
	@SneakyThrows
	public static byte[] downLoadFromUrl(String urlStr) {

		URL url = new URL(urlStr);
		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		//设置超时间为20秒
		conn.setConnectTimeout(20 * 1000);
		//防止屏蔽程序抓取而返回403错误
		conn.setRequestProperty("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

		//得到输入流
		InputStream inputStream = conn.getInputStream();
		//获取自己数组
		byte[] getData = readInputStream(inputStream);
		Assert.isFalse(ObjectUtil.isEmpty(getData) || getData.length == 0, "该图片url有误");

		return getData;

	}

	public static long getImgHash(String imgUrl) {
		byte[] imgBytes = downLoadFromUrl(imgUrl);
		return HashUtil.murmur64(imgBytes);
	}

	public static long[] getImgHashArray(String imgUrl) {
		byte[] imgBytes = downLoadFromUrl(imgUrl);
		return HashUtil.murmur128(imgBytes);
	}

	/**
	 * 获取图片hash
	 *
	 * @param imgBytes
	 * @return
	 */
	public static long getImgHashByImgBytes(byte[] imgBytes) {
		return HashUtil.murmur64(imgBytes);
	}

	/**
	 * 从输入流中获取字节数组
	 *
	 * @param inputStream
	 * @return
	 */
	@SneakyThrows
	public static byte[] readInputStream(InputStream inputStream) {
		byte[] buffer = new byte[1024];
		int len = 0;
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		while ((len = inputStream.read(buffer)) != -1) {
			bos.write(buffer, 0, len);
		}
		bos.close();
		return bos.toByteArray();
	}

}
