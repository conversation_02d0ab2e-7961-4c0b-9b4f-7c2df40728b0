/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.otherapi.huawei.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 营业执照识别表实体类
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Data
@TableName("jrzh_business_license")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "BusinessLicense对象", description = "营业执照识别表")
public class BusinessLicense extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 注册码
	*/
		@ApiModelProperty(value = "注册码")
		private String registrationNumber;
	/**
	* 企业名称
	*/
		@ApiModelProperty(value = "企业名称")
		private String name;
	/**
	* 公司/企业类型/主体类型
	*/
		@ApiModelProperty(value = "公司/企业类型/主体类型")
		private String type;
	/**
	* 住所/营业场所/企业住所
	*/
		@ApiModelProperty(value = "住所/营业场所/企业住所")
		private String address;
	/**
	* 法定代表人/负责人
	*/
		@ApiModelProperty(value = "法定代表人/负责人")
		private String legalRepresentative;
	/**
	* 注册资本
	*/
		@ApiModelProperty(value = "注册资本")
		private String registeredCapital;
	/**
	* 成立日期
	*/
		@ApiModelProperty(value = "成立日期")
		private String foundDate;
	/**
	* 营业期限
	*/
		@ApiModelProperty(value = "营业期限")
		private String businessTerm;
	/**
	* 经营范围
	*/
		@ApiModelProperty(value = "经营范围")
		private String businessScope;
	/**
	* 发照日期
	*/
		@ApiModelProperty(value = "发照日期")
		private String issueDate;
	/**
	* 相关字段的置信度信息，置信度越大，表示本次识别的对应字段的可靠性越高，在统计意义上，置信度越大，准确率越高。 置信度由算法给出，不直接等价于对应字段的准确率
	*/
		@ApiModelProperty(value = "相关字段的置信度信息，置信度越大，表示本次识别的对应字段的可靠性越高，在统计意义上，置信度越大，准确率越高。 置信度由算法给出，不直接等价于对应字段的准确率")
		private String confidence;

	/**
	 * 客户id
	 */
	@ApiModelProperty(value = "客户id")
	private  Long customerId;




}
