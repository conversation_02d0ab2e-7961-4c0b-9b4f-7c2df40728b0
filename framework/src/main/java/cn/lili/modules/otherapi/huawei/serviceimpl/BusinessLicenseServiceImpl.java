/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.otherapi.huawei.serviceimpl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.modules.otherapi.huawei.entity.BusinessLicense;
import cn.lili.modules.otherapi.huawei.mapper.BusinessLicenseMapper;
import cn.lili.modules.otherapi.huawei.service.IBusinessLicenseService;
import cn.lili.modules.otherapi.huawei.service.IHuaweiApiService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huaweicloud.sdk.ocr.v1.model.BusinessLicenseResult;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 营业执照识别表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
@Service
@RequiredArgsConstructor
public class BusinessLicenseServiceImpl extends ServiceImpl<BusinessLicenseMapper, BusinessLicense> implements IBusinessLicenseService {
    private final IHuaweiApiService huaweiApiService;

//    @Override
//    public IPage<BusinessLicenseVO> selectBusinessLicensePage(IPage<BusinessLicenseVO> page, BusinessLicenseVO businessLicense) {
//        return page.setRecords(baseMapper.selectBusinessLicensePage(page, businessLicense));
//    }·

    @Override
    public BusinessLicense recognizeBusinessLicense(String picUrl) {
        BusinessLicenseResult businessLicenseResult = huaweiApiService.recognizeBusinessLicense(picUrl);
        //保存营业执照
        return saveBusinessLicense(businessLicenseResult);
    }

    private BusinessLicense saveBusinessLicense(BusinessLicenseResult businessLicenseResult) {
        AuthUser user = UserContext.getCurrentUser();
        BusinessLicense businessLicense = BeanUtil.toBean(businessLicenseResult, BusinessLicense.class);
        businessLicense.setCustomerId(Long.valueOf(user.getId()));
        BusinessLicense businessLicenseOld = lambdaQuery().eq(BusinessLicense::getRegistrationNumber, businessLicense.getRegistrationNumber())
                .eq(BusinessLicense::getName, businessLicense.getName())
                .eq(BusinessLicense::getCustomerId, user.getId())
                .eq(BusinessLicense::getLegalRepresentative, businessLicense.getLegalRepresentative()).orderByDesc(BusinessLicense::getCreateTime)
                .last("limit 1").one();
        if (ObjectUtil.isNotEmpty(businessLicenseOld)) {
            businessLicense.setId(businessLicenseOld.getId());
        }
        saveOrUpdate(businessLicense);
        return businessLicense;
    }
}
