package cn.lili.modules.otherapi.huawei.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.jrzh_bases.IAttachService;
import cn.lili.modules.otherapi.huawei.config.HuaweiOcrConfiguration;
import cn.lili.modules.otherapi.huawei.constant.HuaWeiOcrResultType;
import cn.lili.modules.otherapi.huawei.dto.OcrExceptionDTO;
import cn.lili.modules.otherapi.huawei.enums.InvoiceExceptionEnum;
import cn.lili.modules.otherapi.huawei.service.IHuaweiApiService;
import cn.lili.modules.otherapi.huawei.service.IHuaweiOcrResultService;
import cn.lili.modules.otherapi.huawei.utils.ImgUtils;
import com.huaweicloud.sdk.core.exception.ClientRequestException;
import com.huaweicloud.sdk.ocr.v1.OcrClient;
import com.huaweicloud.sdk.ocr.v1.model.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Map;
import java.util.TreeMap;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023/1/29 16:25
 * @Description: 华为云银行信息服务
 * @Version: 1.0
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class HuaweiApiServiceImpl implements IHuaweiApiService {

    private final IHuaweiOcrResultService huaweiOcrResultService;
    private final HuaweiOcrConfiguration huaweiOcrConfiguration;
    private final IAttachService attachService;
    //产品key与产品密钥
    private final String appKey = "c397a1beef5249129596e330a6728fd9";
    private final String appSecret = "9549e726403a4b9195afd2093ec4beb7";


    @Override
    public BusinessLicenseResult recognizeBusinessLicense(String picUrl) {
        long imgHash = ImgUtils.getImgHash(picUrl);
        BusinessLicenseResult sysRecord = huaweiOcrResultService.getByHashVal(imgHash, HuaWeiOcrResultType.BUSINESS_LICENSE.getCode(), BusinessLicenseResult.class);
        if (ObjectUtil.isNotEmpty(sysRecord)) {
            return sysRecord;
        }
        //进行华为云营业执照识别
        Integer resultSuccess = null;
        String failReason = "";
        BusinessLicenseResult businessLicenseResult = null;
        try {
            businessLicenseResult = getOrcBusinessLicenseResult(picUrl);
            resultSuccess = 1;
        } catch (ClientRequestException e) {
            OcrExceptionDTO ocrExceptionDTO = cn.hutool.core.bean.BeanUtil.toBean(e, OcrExceptionDTO.class);
            String errorMsg = getBusinessLicenseErrorMsg(ocrExceptionDTO);
            resultSuccess = 0;
            failReason = errorMsg;
            throw new ServiceException(errorMsg);
        } finally {
            //保存附件

            //系统留底
            huaweiOcrResultService.saveOcrResult(imgHash, JSONUtil.toJsonStr(businessLicenseResult), picUrl, HuaWeiOcrResultType.BUSINESS_LICENSE.getCode(), resultSuccess, failReason);
        }
        return businessLicenseResult;
    }

    @Override
    public IdCardResult revognizeIdentity(String picUrl) {
        long imgHash = ImgUtils.getImgHash(picUrl);
        IdCardResult sysRecord = huaweiOcrResultService.getByHashVal(imgHash, HuaWeiOcrResultType.IDENTITY.getCode(), IdCardResult.class);
        if (ObjectUtil.isNotEmpty(sysRecord)) {
            return sysRecord;
        }
        //进行华为云身份证识别
        IdCardResult result = null;
        Integer resultSuccess = null;
        String failReason = "";
        try {
            OcrClient client = huaweiOcrConfiguration.getDefaultOcrClient();
            RecognizeIdCardRequest request = new RecognizeIdCardRequest();
            IdCardRequestBody idCardRequestBody = new IdCardRequestBody();
            idCardRequestBody.setUrl(picUrl);
            request.withBody(idCardRequestBody);
            RecognizeIdCardResponse response = client.recognizeIdCard(request);
            log.info("身份证识别结果" + response.toString());
            result = response.getResult();
            resultSuccess = 1;
        } catch (Exception e) {
            resultSuccess = 0;
            failReason = e.getMessage();
        } finally {
            //系统留底
            huaweiOcrResultService.saveOcrResult(imgHash, JSONUtil.toJsonStr(result), picUrl, HuaWeiOcrResultType.IDENTITY.getCode(), resultSuccess, failReason);
        }
        return result;
    }

    private BusinessLicenseResult getOrcBusinessLicenseResult(String picUrl) {
        OcrClient ocrClient = huaweiOcrConfiguration.getDefaultOcrClient();
        RecognizeBusinessLicenseRequest recognizeBusinessLicenseRequest = new RecognizeBusinessLicenseRequest();
        BusinessLicenseRequestBody businessLicenseRequestBody = new BusinessLicenseRequestBody();
        businessLicenseRequestBody.setUrl(picUrl);
        recognizeBusinessLicenseRequest.withBody(businessLicenseRequestBody);
        RecognizeBusinessLicenseResponse recognizeBusinessLicenseResponse = ocrClient.recognizeBusinessLicense(recognizeBusinessLicenseRequest);
        BusinessLicenseResult businessLicenseResult = recognizeBusinessLicenseResponse.getResult();
//        LogInterface logInterface = new LogInterface();
//        //记录
//        logInterface.setRequestUrl(recognizeBusinessLicenseRequest.getBody().getUrl())
//                .setInterfaceParty(InvoiceEnum.API_INTERFACE_PARTY)
//                .setCaller(LogTypeEnum.ACTIVE_CALL.getCode())
//                .setRequestInfo(String.valueOf(recognizeBusinessLicenseRequest.getBody()))
//                .setResponseInfo(String.valueOf(businessLicenseResult))
//                .setResult(ObjectUtil.isNotEmpty(businessLicenseResult.getName()) ? CommonConstant.OPENSTATUS : CommonConstant.CLOSESTATUS)
//                .setName(HuaweiYunOcrEnum.BUSINESS_LICENSE_RECONGINIZE.getName());
//        LogInterfaceUtil.save(logInterface);
        return businessLicenseResult;
    }

    public String getBusinessLicenseErrorMsg(OcrExceptionDTO ocrExceptionDTO) {
        String msg;
        InvoiceExceptionEnum invoiceExceptionEnum = InvoiceExceptionEnum.getInvoiceDescByType(ocrExceptionDTO.getErrorCode());
        if (ObjectUtil.isNotEmpty(invoiceExceptionEnum)) {
            msg = invoiceExceptionEnum.getDesc();
        } else {
            msg = "营业执照识别失败,请上传正确且清晰的图片";
        }
        return msg;

    }

}
