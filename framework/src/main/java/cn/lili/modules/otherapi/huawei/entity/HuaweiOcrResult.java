/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.otherapi.huawei.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 华为云orc识别附件实体类
 * 租戶共享
 *
 * <AUTHOR>
 * @since 2022-07-24
 */
@Data
@TableName("li_huaweiocrresult")
//@TableName("li_huawei_ocr_result")
@ApiModel(value = "HuaweiOcrResult对象", description = "华为云orc识别附件")
@NoArgsConstructor
@AllArgsConstructor
public class HuaweiOcrResult extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 附加id
	 */
	@ApiModelProperty(value = "附加id")
	private Long attachId;
	/**
	 * 识别类型 1身份证 2营业执照
	 */
	@ApiModelProperty(value = "识别类型 1身份证 2营业执照 3发票识别")
	private Integer ocrType;
	/**
	 * 图片hash
	 */
	@ApiModelProperty(value = "图片hash")
	private Long hashVal;
	/**
	 * 图片地址
	 */
	@ApiModelProperty(value = "图片地址")
	private String proof;
	/**
	 * 识别结果
	 */
	@ApiModelProperty(value = "识别结果")
	private String ocrResult;
	/**
	 * 识别是否成功 0 失败 1成功
	 */
	private Integer resultSuccess;
	/**
	 * 失败原因
	 */
	private String failReason;
}
