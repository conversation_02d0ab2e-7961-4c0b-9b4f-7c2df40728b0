/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.otherapi.huawei.service;

import cn.lili.modules.otherapi.huawei.entity.BusinessLicense;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 营业执照识别表 服务类
 *
 * <AUTHOR>
 * @since 2022-04-21
 */
public interface IBusinessLicenseService extends IService<BusinessLicense> {

    /**
     * 自定义分页
     *
     * @param page
     * @param businessLicense
     * @return
     */
//    IPage<BusinessLicenseVO> selectBusinessLicensePage(IPage<BusinessLicenseVO> page, BusinessLicenseVO businessLicense);

    /**
     * 识别营业执照
     *
     * @param picUrl
     */
    BusinessLicense recognizeBusinessLicense(String picUrl);
}
