package cn.lili.modules.otherapi.sky.dto;


import java.io.Serializable;

public class SkyEyeIndustryAll implements Serializable {
    private String categoryMiddle;
    private String categoryBig;
    private String category;
    private String categorySmall;

    public SkyEyeIndustryAll() {
    }

    public String getCategoryMiddle() {
        return this.categoryMiddle;
    }

    public String getCategoryBig() {
        return this.categoryBig;
    }

    public String getCategory() {
        return this.category;
    }

    public String getCategorySmall() {
        return this.categorySmall;
    }

    public void setCategoryMiddle(String categoryMiddle) {
        this.categoryMiddle = categoryMiddle;
    }

    public void setCategoryBig(String categoryBig) {
        this.categoryBig = categoryBig;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public void setCategorySmall(String categorySmall) {
        this.categorySmall = categorySmall;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SkyEyeIndustryAll)) {
            return false;
        } else {
            SkyEyeIndustryAll other = (SkyEyeIndustryAll)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$categoryMiddle = this.getCategoryMiddle();
                Object other$categoryMiddle = other.getCategoryMiddle();
                if (this$categoryMiddle == null) {
                    if (other$categoryMiddle != null) {
                        return false;
                    }
                } else if (!this$categoryMiddle.equals(other$categoryMiddle)) {
                    return false;
                }

                Object this$categoryBig = this.getCategoryBig();
                Object other$categoryBig = other.getCategoryBig();
                if (this$categoryBig == null) {
                    if (other$categoryBig != null) {
                        return false;
                    }
                } else if (!this$categoryBig.equals(other$categoryBig)) {
                    return false;
                }

                Object this$category = this.getCategory();
                Object other$category = other.getCategory();
                if (this$category == null) {
                    if (other$category != null) {
                        return false;
                    }
                } else if (!this$category.equals(other$category)) {
                    return false;
                }

                Object this$categorySmall = this.getCategorySmall();
                Object other$categorySmall = other.getCategorySmall();
                if (this$categorySmall == null) {
                    if (other$categorySmall != null) {
                        return false;
                    }
                } else if (!this$categorySmall.equals(other$categorySmall)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SkyEyeIndustryAll;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $categoryMiddle = this.getCategoryMiddle();
        result = result * 59 + ($categoryMiddle == null ? 43 : $categoryMiddle.hashCode());
        Object $categoryBig = this.getCategoryBig();
        result = result * 59 + ($categoryBig == null ? 43 : $categoryBig.hashCode());
        Object $category = this.getCategory();
        result = result * 59 + ($category == null ? 43 : $category.hashCode());
        Object $categorySmall = this.getCategorySmall();
        result = result * 59 + ($categorySmall == null ? 43 : $categorySmall.hashCode());
        return result;
    }

    public String toString() {
        return "SkyEyeIndustryAll(categoryMiddle=" + this.getCategoryMiddle() + ", categoryBig=" + this.getCategoryBig() + ", category=" + this.getCategory() + ", categorySmall=" + this.getCategorySmall() + ")";
    }
}

