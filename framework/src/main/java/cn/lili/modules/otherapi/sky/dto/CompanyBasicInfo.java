package cn.lili.modules.otherapi.sky.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;

public class CompanyBasicInfo implements Serializable {
    private String name;
    private String legalPersonName;
    private String phoneNumber;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date estiblishTime;
    private String creditCode;
    private String orgNumber;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date regDate;
    private String regNumber;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date approvedTime;
    private String regCapital;
    private String totalAssets;
    private String regInstitute;
    private String companyOrgType;
    private String industry;
    private String employeeNum;
    private String medicalInsurance;
    private String regLocation;
    private String businessScope;
    private String paidAmount;
    private String base;

    public CompanyBasicInfo() {
    }

    public String getName() {
        return this.name;
    }

    public String getLegalPersonName() {
        return this.legalPersonName;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public Date getEstiblishTime() {
        return this.estiblishTime;
    }

    public String getCreditCode() {
        return this.creditCode;
    }

    public String getOrgNumber() {
        return this.orgNumber;
    }

    public Date getRegDate() {
        return this.regDate;
    }

    public String getRegNumber() {
        return this.regNumber;
    }

    public Date getApprovedTime() {
        return this.approvedTime;
    }

    public String getRegCapital() {
        return this.regCapital;
    }

    public String getTotalAssets() {
        return this.totalAssets;
    }

    public String getRegInstitute() {
        return this.regInstitute;
    }

    public String getCompanyOrgType() {
        return this.companyOrgType;
    }

    public String getIndustry() {
        return this.industry;
    }

    public String getEmployeeNum() {
        return this.employeeNum;
    }

    public String getMedicalInsurance() {
        return this.medicalInsurance;
    }

    public String getRegLocation() {
        return this.regLocation;
    }

    public String getBusinessScope() {
        return this.businessScope;
    }

    public String getPaidAmount() {
        return this.paidAmount;
    }

    public String getBase() {
        return this.base;
    }

    public CompanyBasicInfo setName(String name) {
        this.name = name;
        return this;
    }

    public CompanyBasicInfo setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
        return this;
    }

    public CompanyBasicInfo setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public CompanyBasicInfo setEstiblishTime(Date estiblishTime) {
        this.estiblishTime = estiblishTime;
        return this;
    }

    public CompanyBasicInfo setCreditCode(String creditCode) {
        this.creditCode = creditCode;
        return this;
    }

    public CompanyBasicInfo setOrgNumber(String orgNumber) {
        this.orgNumber = orgNumber;
        return this;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public CompanyBasicInfo setRegDate(Date regDate) {
        this.regDate = regDate;
        return this;
    }

    public CompanyBasicInfo setRegNumber(String regNumber) {
        this.regNumber = regNumber;
        return this;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public CompanyBasicInfo setApprovedTime(Date approvedTime) {
        this.approvedTime = approvedTime;
        return this;
    }

    public CompanyBasicInfo setRegCapital(String regCapital) {
        this.regCapital = regCapital;
        return this;
    }

    public CompanyBasicInfo setTotalAssets(String totalAssets) {
        this.totalAssets = totalAssets;
        return this;
    }

    public CompanyBasicInfo setRegInstitute(String regInstitute) {
        this.regInstitute = regInstitute;
        return this;
    }

    public CompanyBasicInfo setCompanyOrgType(String companyOrgType) {
        this.companyOrgType = companyOrgType;
        return this;
    }

    public CompanyBasicInfo setIndustry(String industry) {
        this.industry = industry;
        return this;
    }

    public CompanyBasicInfo setEmployeeNum(String employeeNum) {
        this.employeeNum = employeeNum;
        return this;
    }

    public CompanyBasicInfo setMedicalInsurance(String medicalInsurance) {
        this.medicalInsurance = medicalInsurance;
        return this;
    }

    public CompanyBasicInfo setRegLocation(String regLocation) {
        this.regLocation = regLocation;
        return this;
    }

    public CompanyBasicInfo setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
        return this;
    }

    public CompanyBasicInfo setPaidAmount(String paidAmount) {
        this.paidAmount = paidAmount;
        return this;
    }

    public CompanyBasicInfo setBase(String base) {
        this.base = base;
        return this;
    }
}
