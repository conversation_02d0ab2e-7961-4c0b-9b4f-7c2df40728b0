package cn.lili.modules.otherapi.sky.dto;


import cn.hutool.json.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;

public class SkyEyeBaseInfo implements Serializable {
    private String historyNames;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date cancelDate;
    private String regStatus;
    private String regCapital;
    private String city;
    private String staffNumRange;
    private String bondNum;
    private JSONArray historyNameList;
    private String industry;
    private String bondName;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date revokeDate;
    private Integer type;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date updateTimes;
    private String legalPersonName;
    private String revokeReason;
    private Integer compForm;
    private String regNumber;
    private String creditCode;
    private String property3;
    private String usedBondName;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date approvedTime;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date fromTime;
    private String socialStaffNum;
    private String actualCapitalCurrency;
    private String alias;
    private String companyOrgType;
    private String cancelReason;
    private String orgNumber;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date toTime;
    private String actualCapital;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date estiblishTime;
    private String regInstitute;
    private String businessScope;
    private String taxNumber;
    private String regLocation;
    private String regCapitalCurrency;
    private String tags;
    private String district;
    private String bondType;
    private String name;
    private Integer percentileScore;
    private SkyEyeIndustryAll industryAll;
    private Integer isMicroEnt;
    private String base;
    private String phoneNumber;

    public SkyEyeBaseInfo() {
    }

    public String getHistoryNames() {
        return this.historyNames;
    }

    public Date getCancelDate() {
        return this.cancelDate;
    }

    public String getRegStatus() {
        return this.regStatus;
    }

    public String getRegCapital() {
        return this.regCapital;
    }

    public String getCity() {
        return this.city;
    }

    public String getStaffNumRange() {
        return this.staffNumRange;
    }

    public String getBondNum() {
        return this.bondNum;
    }

    public JSONArray getHistoryNameList() {
        return this.historyNameList;
    }

    public String getIndustry() {
        return this.industry;
    }

    public String getBondName() {
        return this.bondName;
    }

    public Date getRevokeDate() {
        return this.revokeDate;
    }

    public Integer getType() {
        return this.type;
    }

    public Date getUpdateTimes() {
        return this.updateTimes;
    }

    public String getLegalPersonName() {
        return this.legalPersonName;
    }

    public String getRevokeReason() {
        return this.revokeReason;
    }

    public Integer getCompForm() {
        return this.compForm;
    }

    public String getRegNumber() {
        return this.regNumber;
    }

    public String getCreditCode() {
        return this.creditCode;
    }

    public String getProperty3() {
        return this.property3;
    }

    public String getUsedBondName() {
        return this.usedBondName;
    }

    public Date getApprovedTime() {
        return this.approvedTime;
    }

    public Date getFromTime() {
        return this.fromTime;
    }

    public String getSocialStaffNum() {
        return this.socialStaffNum;
    }

    public String getActualCapitalCurrency() {
        return this.actualCapitalCurrency;
    }

    public String getAlias() {
        return this.alias;
    }

    public String getCompanyOrgType() {
        return this.companyOrgType;
    }

    public String getCancelReason() {
        return this.cancelReason;
    }

    public String getOrgNumber() {
        return this.orgNumber;
    }

    public Date getToTime() {
        return this.toTime;
    }

    public String getActualCapital() {
        return this.actualCapital;
    }

    public Date getEstiblishTime() {
        return this.estiblishTime;
    }

    public String getRegInstitute() {
        return this.regInstitute;
    }

    public String getBusinessScope() {
        return this.businessScope;
    }

    public String getTaxNumber() {
        return this.taxNumber;
    }

    public String getRegLocation() {
        return this.regLocation;
    }

    public String getRegCapitalCurrency() {
        return this.regCapitalCurrency;
    }

    public String getTags() {
        return this.tags;
    }

    public String getDistrict() {
        return this.district;
    }

    public String getBondType() {
        return this.bondType;
    }

    public String getName() {
        return this.name;
    }

    public Integer getPercentileScore() {
        return this.percentileScore;
    }

    public SkyEyeIndustryAll getIndustryAll() {
        return this.industryAll;
    }

    public Integer getIsMicroEnt() {
        return this.isMicroEnt;
    }

    public String getBase() {
        return this.base;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public void setHistoryNames(String historyNames) {
        this.historyNames = historyNames;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public void setCancelDate(Date cancelDate) {
        this.cancelDate = cancelDate;
    }

    public void setRegStatus(String regStatus) {
        this.regStatus = regStatus;
    }

    public void setRegCapital(String regCapital) {
        this.regCapital = regCapital;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setStaffNumRange(String staffNumRange) {
        this.staffNumRange = staffNumRange;
    }

    public void setBondNum(String bondNum) {
        this.bondNum = bondNum;
    }

    public void setHistoryNameList(JSONArray historyNameList) {
        this.historyNameList = historyNameList;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public void setBondName(String bondName) {
        this.bondName = bondName;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public void setRevokeDate(Date revokeDate) {
        this.revokeDate = revokeDate;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public void setUpdateTimes(Date updateTimes) {
        this.updateTimes = updateTimes;
    }

    public void setLegalPersonName(String legalPersonName) {
        this.legalPersonName = legalPersonName;
    }

    public void setRevokeReason(String revokeReason) {
        this.revokeReason = revokeReason;
    }

    public void setCompForm(Integer compForm) {
        this.compForm = compForm;
    }

    public void setRegNumber(String regNumber) {
        this.regNumber = regNumber;
    }

    public void setCreditCode(String creditCode) {
        this.creditCode = creditCode;
    }

    public void setProperty3(String property3) {
        this.property3 = property3;
    }

    public void setUsedBondName(String usedBondName) {
        this.usedBondName = usedBondName;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public void setApprovedTime(Date approvedTime) {
        this.approvedTime = approvedTime;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public void setFromTime(Date fromTime) {
        this.fromTime = fromTime;
    }

    public void setSocialStaffNum(String socialStaffNum) {
        this.socialStaffNum = socialStaffNum;
    }

    public void setActualCapitalCurrency(String actualCapitalCurrency) {
        this.actualCapitalCurrency = actualCapitalCurrency;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public void setCompanyOrgType(String companyOrgType) {
        this.companyOrgType = companyOrgType;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }

    public void setOrgNumber(String orgNumber) {
        this.orgNumber = orgNumber;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public void setToTime(Date toTime) {
        this.toTime = toTime;
    }

    public void setActualCapital(String actualCapital) {
        this.actualCapital = actualCapital;
    }

    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    public void setEstiblishTime(Date estiblishTime) {
        this.estiblishTime = estiblishTime;
    }

    public void setRegInstitute(String regInstitute) {
        this.regInstitute = regInstitute;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public void setRegLocation(String regLocation) {
        this.regLocation = regLocation;
    }

    public void setRegCapitalCurrency(String regCapitalCurrency) {
        this.regCapitalCurrency = regCapitalCurrency;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public void setBondType(String bondType) {
        this.bondType = bondType;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setPercentileScore(Integer percentileScore) {
        this.percentileScore = percentileScore;
    }

    public void setIndustryAll(SkyEyeIndustryAll industryAll) {
        this.industryAll = industryAll;
    }

    public void setIsMicroEnt(Integer isMicroEnt) {
        this.isMicroEnt = isMicroEnt;
    }

    public void setBase(String base) {
        this.base = base;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SkyEyeBaseInfo)) {
            return false;
        } else {
            SkyEyeBaseInfo other = (SkyEyeBaseInfo)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$type = this.getType();
                Object other$type = other.getType();
                if (this$type == null) {
                    if (other$type != null) {
                        return false;
                    }
                } else if (!this$type.equals(other$type)) {
                    return false;
                }

                Object this$compForm = this.getCompForm();
                Object other$compForm = other.getCompForm();
                if (this$compForm == null) {
                    if (other$compForm != null) {
                        return false;
                    }
                } else if (!this$compForm.equals(other$compForm)) {
                    return false;
                }

                Object this$percentileScore = this.getPercentileScore();
                Object other$percentileScore = other.getPercentileScore();
                if (this$percentileScore == null) {
                    if (other$percentileScore != null) {
                        return false;
                    }
                } else if (!this$percentileScore.equals(other$percentileScore)) {
                    return false;
                }

                Object this$isMicroEnt = this.getIsMicroEnt();
                Object other$isMicroEnt = other.getIsMicroEnt();
                if (this$isMicroEnt == null) {
                    if (other$isMicroEnt != null) {
                        return false;
                    }
                } else if (!this$isMicroEnt.equals(other$isMicroEnt)) {
                    return false;
                }

                Object this$historyNames = this.getHistoryNames();
                Object other$historyNames = other.getHistoryNames();
                if (this$historyNames == null) {
                    if (other$historyNames != null) {
                        return false;
                    }
                } else if (!this$historyNames.equals(other$historyNames)) {
                    return false;
                }

                Object this$cancelDate = this.getCancelDate();
                Object other$cancelDate = other.getCancelDate();
                if (this$cancelDate == null) {
                    if (other$cancelDate != null) {
                        return false;
                    }
                } else if (!this$cancelDate.equals(other$cancelDate)) {
                    return false;
                }

                Object this$regStatus = this.getRegStatus();
                Object other$regStatus = other.getRegStatus();
                if (this$regStatus == null) {
                    if (other$regStatus != null) {
                        return false;
                    }
                } else if (!this$regStatus.equals(other$regStatus)) {
                    return false;
                }

                Object this$regCapital = this.getRegCapital();
                Object other$regCapital = other.getRegCapital();
                if (this$regCapital == null) {
                    if (other$regCapital != null) {
                        return false;
                    }
                } else if (!this$regCapital.equals(other$regCapital)) {
                    return false;
                }

                Object this$city = this.getCity();
                Object other$city = other.getCity();
                if (this$city == null) {
                    if (other$city != null) {
                        return false;
                    }
                } else if (!this$city.equals(other$city)) {
                    return false;
                }

                Object this$staffNumRange = this.getStaffNumRange();
                Object other$staffNumRange = other.getStaffNumRange();
                if (this$staffNumRange == null) {
                    if (other$staffNumRange != null) {
                        return false;
                    }
                } else if (!this$staffNumRange.equals(other$staffNumRange)) {
                    return false;
                }

                Object this$bondNum = this.getBondNum();
                Object other$bondNum = other.getBondNum();
                if (this$bondNum == null) {
                    if (other$bondNum != null) {
                        return false;
                    }
                } else if (!this$bondNum.equals(other$bondNum)) {
                    return false;
                }

                Object this$historyNameList = this.getHistoryNameList();
                Object other$historyNameList = other.getHistoryNameList();
                if (this$historyNameList == null) {
                    if (other$historyNameList != null) {
                        return false;
                    }
                } else if (!this$historyNameList.equals(other$historyNameList)) {
                    return false;
                }

                Object this$industry = this.getIndustry();
                Object other$industry = other.getIndustry();
                if (this$industry == null) {
                    if (other$industry != null) {
                        return false;
                    }
                } else if (!this$industry.equals(other$industry)) {
                    return false;
                }

                Object this$bondName = this.getBondName();
                Object other$bondName = other.getBondName();
                if (this$bondName == null) {
                    if (other$bondName != null) {
                        return false;
                    }
                } else if (!this$bondName.equals(other$bondName)) {
                    return false;
                }

                Object this$revokeDate = this.getRevokeDate();
                Object other$revokeDate = other.getRevokeDate();
                if (this$revokeDate == null) {
                    if (other$revokeDate != null) {
                        return false;
                    }
                } else if (!this$revokeDate.equals(other$revokeDate)) {
                    return false;
                }

                Object this$updateTimes = this.getUpdateTimes();
                Object other$updateTimes = other.getUpdateTimes();
                if (this$updateTimes == null) {
                    if (other$updateTimes != null) {
                        return false;
                    }
                } else if (!this$updateTimes.equals(other$updateTimes)) {
                    return false;
                }

                Object this$legalPersonName = this.getLegalPersonName();
                Object other$legalPersonName = other.getLegalPersonName();
                if (this$legalPersonName == null) {
                    if (other$legalPersonName != null) {
                        return false;
                    }
                } else if (!this$legalPersonName.equals(other$legalPersonName)) {
                    return false;
                }

                Object this$revokeReason = this.getRevokeReason();
                Object other$revokeReason = other.getRevokeReason();
                if (this$revokeReason == null) {
                    if (other$revokeReason != null) {
                        return false;
                    }
                } else if (!this$revokeReason.equals(other$revokeReason)) {
                    return false;
                }

                Object this$regNumber = this.getRegNumber();
                Object other$regNumber = other.getRegNumber();
                if (this$regNumber == null) {
                    if (other$regNumber != null) {
                        return false;
                    }
                } else if (!this$regNumber.equals(other$regNumber)) {
                    return false;
                }

                Object this$creditCode = this.getCreditCode();
                Object other$creditCode = other.getCreditCode();
                if (this$creditCode == null) {
                    if (other$creditCode != null) {
                        return false;
                    }
                } else if (!this$creditCode.equals(other$creditCode)) {
                    return false;
                }

                Object this$property3 = this.getProperty3();
                Object other$property3 = other.getProperty3();
                if (this$property3 == null) {
                    if (other$property3 != null) {
                        return false;
                    }
                } else if (!this$property3.equals(other$property3)) {
                    return false;
                }

                Object this$usedBondName = this.getUsedBondName();
                Object other$usedBondName = other.getUsedBondName();
                if (this$usedBondName == null) {
                    if (other$usedBondName != null) {
                        return false;
                    }
                } else if (!this$usedBondName.equals(other$usedBondName)) {
                    return false;
                }

                Object this$approvedTime = this.getApprovedTime();
                Object other$approvedTime = other.getApprovedTime();
                if (this$approvedTime == null) {
                    if (other$approvedTime != null) {
                        return false;
                    }
                } else if (!this$approvedTime.equals(other$approvedTime)) {
                    return false;
                }

                Object this$fromTime = this.getFromTime();
                Object other$fromTime = other.getFromTime();
                if (this$fromTime == null) {
                    if (other$fromTime != null) {
                        return false;
                    }
                } else if (!this$fromTime.equals(other$fromTime)) {
                    return false;
                }

                Object this$socialStaffNum = this.getSocialStaffNum();
                Object other$socialStaffNum = other.getSocialStaffNum();
                if (this$socialStaffNum == null) {
                    if (other$socialStaffNum != null) {
                        return false;
                    }
                } else if (!this$socialStaffNum.equals(other$socialStaffNum)) {
                    return false;
                }

                Object this$actualCapitalCurrency = this.getActualCapitalCurrency();
                Object other$actualCapitalCurrency = other.getActualCapitalCurrency();
                if (this$actualCapitalCurrency == null) {
                    if (other$actualCapitalCurrency != null) {
                        return false;
                    }
                } else if (!this$actualCapitalCurrency.equals(other$actualCapitalCurrency)) {
                    return false;
                }

                Object this$alias = this.getAlias();
                Object other$alias = other.getAlias();
                if (this$alias == null) {
                    if (other$alias != null) {
                        return false;
                    }
                } else if (!this$alias.equals(other$alias)) {
                    return false;
                }

                Object this$companyOrgType = this.getCompanyOrgType();
                Object other$companyOrgType = other.getCompanyOrgType();
                if (this$companyOrgType == null) {
                    if (other$companyOrgType != null) {
                        return false;
                    }
                } else if (!this$companyOrgType.equals(other$companyOrgType)) {
                    return false;
                }

                Object this$cancelReason = this.getCancelReason();
                Object other$cancelReason = other.getCancelReason();
                if (this$cancelReason == null) {
                    if (other$cancelReason != null) {
                        return false;
                    }
                } else if (!this$cancelReason.equals(other$cancelReason)) {
                    return false;
                }

                Object this$orgNumber = this.getOrgNumber();
                Object other$orgNumber = other.getOrgNumber();
                if (this$orgNumber == null) {
                    if (other$orgNumber != null) {
                        return false;
                    }
                } else if (!this$orgNumber.equals(other$orgNumber)) {
                    return false;
                }

                Object this$toTime = this.getToTime();
                Object other$toTime = other.getToTime();
                if (this$toTime == null) {
                    if (other$toTime != null) {
                        return false;
                    }
                } else if (!this$toTime.equals(other$toTime)) {
                    return false;
                }

                Object this$actualCapital = this.getActualCapital();
                Object other$actualCapital = other.getActualCapital();
                if (this$actualCapital == null) {
                    if (other$actualCapital != null) {
                        return false;
                    }
                } else if (!this$actualCapital.equals(other$actualCapital)) {
                    return false;
                }

                Object this$estiblishTime = this.getEstiblishTime();
                Object other$estiblishTime = other.getEstiblishTime();
                if (this$estiblishTime == null) {
                    if (other$estiblishTime != null) {
                        return false;
                    }
                } else if (!this$estiblishTime.equals(other$estiblishTime)) {
                    return false;
                }

                Object this$regInstitute = this.getRegInstitute();
                Object other$regInstitute = other.getRegInstitute();
                if (this$regInstitute == null) {
                    if (other$regInstitute != null) {
                        return false;
                    }
                } else if (!this$regInstitute.equals(other$regInstitute)) {
                    return false;
                }

                Object this$businessScope = this.getBusinessScope();
                Object other$businessScope = other.getBusinessScope();
                if (this$businessScope == null) {
                    if (other$businessScope != null) {
                        return false;
                    }
                } else if (!this$businessScope.equals(other$businessScope)) {
                    return false;
                }

                Object this$taxNumber = this.getTaxNumber();
                Object other$taxNumber = other.getTaxNumber();
                if (this$taxNumber == null) {
                    if (other$taxNumber != null) {
                        return false;
                    }
                } else if (!this$taxNumber.equals(other$taxNumber)) {
                    return false;
                }

                Object this$regLocation = this.getRegLocation();
                Object other$regLocation = other.getRegLocation();
                if (this$regLocation == null) {
                    if (other$regLocation != null) {
                        return false;
                    }
                } else if (!this$regLocation.equals(other$regLocation)) {
                    return false;
                }

                Object this$regCapitalCurrency = this.getRegCapitalCurrency();
                Object other$regCapitalCurrency = other.getRegCapitalCurrency();
                if (this$regCapitalCurrency == null) {
                    if (other$regCapitalCurrency != null) {
                        return false;
                    }
                } else if (!this$regCapitalCurrency.equals(other$regCapitalCurrency)) {
                    return false;
                }

                Object this$tags = this.getTags();
                Object other$tags = other.getTags();
                if (this$tags == null) {
                    if (other$tags != null) {
                        return false;
                    }
                } else if (!this$tags.equals(other$tags)) {
                    return false;
                }

                Object this$district = this.getDistrict();
                Object other$district = other.getDistrict();
                if (this$district == null) {
                    if (other$district != null) {
                        return false;
                    }
                } else if (!this$district.equals(other$district)) {
                    return false;
                }

                Object this$bondType = this.getBondType();
                Object other$bondType = other.getBondType();
                if (this$bondType == null) {
                    if (other$bondType != null) {
                        return false;
                    }
                } else if (!this$bondType.equals(other$bondType)) {
                    return false;
                }

                Object this$name = this.getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }

                Object this$industryAll = this.getIndustryAll();
                Object other$industryAll = other.getIndustryAll();
                if (this$industryAll == null) {
                    if (other$industryAll != null) {
                        return false;
                    }
                } else if (!this$industryAll.equals(other$industryAll)) {
                    return false;
                }

                Object this$base = this.getBase();
                Object other$base = other.getBase();
                if (this$base == null) {
                    if (other$base != null) {
                        return false;
                    }
                } else if (!this$base.equals(other$base)) {
                    return false;
                }

                Object this$phoneNumber = this.getPhoneNumber();
                Object other$phoneNumber = other.getPhoneNumber();
                if (this$phoneNumber == null) {
                    if (other$phoneNumber != null) {
                        return false;
                    }
                } else if (!this$phoneNumber.equals(other$phoneNumber)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SkyEyeBaseInfo;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        Object $compForm = this.getCompForm();
        result = result * 59 + ($compForm == null ? 43 : $compForm.hashCode());
        Object $percentileScore = this.getPercentileScore();
        result = result * 59 + ($percentileScore == null ? 43 : $percentileScore.hashCode());
        Object $isMicroEnt = this.getIsMicroEnt();
        result = result * 59 + ($isMicroEnt == null ? 43 : $isMicroEnt.hashCode());
        Object $historyNames = this.getHistoryNames();
        result = result * 59 + ($historyNames == null ? 43 : $historyNames.hashCode());
        Object $cancelDate = this.getCancelDate();
        result = result * 59 + ($cancelDate == null ? 43 : $cancelDate.hashCode());
        Object $regStatus = this.getRegStatus();
        result = result * 59 + ($regStatus == null ? 43 : $regStatus.hashCode());
        Object $regCapital = this.getRegCapital();
        result = result * 59 + ($regCapital == null ? 43 : $regCapital.hashCode());
        Object $city = this.getCity();
        result = result * 59 + ($city == null ? 43 : $city.hashCode());
        Object $staffNumRange = this.getStaffNumRange();
        result = result * 59 + ($staffNumRange == null ? 43 : $staffNumRange.hashCode());
        Object $bondNum = this.getBondNum();
        result = result * 59 + ($bondNum == null ? 43 : $bondNum.hashCode());
        Object $historyNameList = this.getHistoryNameList();
        result = result * 59 + ($historyNameList == null ? 43 : $historyNameList.hashCode());
        Object $industry = this.getIndustry();
        result = result * 59 + ($industry == null ? 43 : $industry.hashCode());
        Object $bondName = this.getBondName();
        result = result * 59 + ($bondName == null ? 43 : $bondName.hashCode());
        Object $revokeDate = this.getRevokeDate();
        result = result * 59 + ($revokeDate == null ? 43 : $revokeDate.hashCode());
        Object $updateTimes = this.getUpdateTimes();
        result = result * 59 + ($updateTimes == null ? 43 : $updateTimes.hashCode());
        Object $legalPersonName = this.getLegalPersonName();
        result = result * 59 + ($legalPersonName == null ? 43 : $legalPersonName.hashCode());
        Object $revokeReason = this.getRevokeReason();
        result = result * 59 + ($revokeReason == null ? 43 : $revokeReason.hashCode());
        Object $regNumber = this.getRegNumber();
        result = result * 59 + ($regNumber == null ? 43 : $regNumber.hashCode());
        Object $creditCode = this.getCreditCode();
        result = result * 59 + ($creditCode == null ? 43 : $creditCode.hashCode());
        Object $property3 = this.getProperty3();
        result = result * 59 + ($property3 == null ? 43 : $property3.hashCode());
        Object $usedBondName = this.getUsedBondName();
        result = result * 59 + ($usedBondName == null ? 43 : $usedBondName.hashCode());
        Object $approvedTime = this.getApprovedTime();
        result = result * 59 + ($approvedTime == null ? 43 : $approvedTime.hashCode());
        Object $fromTime = this.getFromTime();
        result = result * 59 + ($fromTime == null ? 43 : $fromTime.hashCode());
        Object $socialStaffNum = this.getSocialStaffNum();
        result = result * 59 + ($socialStaffNum == null ? 43 : $socialStaffNum.hashCode());
        Object $actualCapitalCurrency = this.getActualCapitalCurrency();
        result = result * 59 + ($actualCapitalCurrency == null ? 43 : $actualCapitalCurrency.hashCode());
        Object $alias = this.getAlias();
        result = result * 59 + ($alias == null ? 43 : $alias.hashCode());
        Object $companyOrgType = this.getCompanyOrgType();
        result = result * 59 + ($companyOrgType == null ? 43 : $companyOrgType.hashCode());
        Object $cancelReason = this.getCancelReason();
        result = result * 59 + ($cancelReason == null ? 43 : $cancelReason.hashCode());
        Object $orgNumber = this.getOrgNumber();
        result = result * 59 + ($orgNumber == null ? 43 : $orgNumber.hashCode());
        Object $toTime = this.getToTime();
        result = result * 59 + ($toTime == null ? 43 : $toTime.hashCode());
        Object $actualCapital = this.getActualCapital();
        result = result * 59 + ($actualCapital == null ? 43 : $actualCapital.hashCode());
        Object $estiblishTime = this.getEstiblishTime();
        result = result * 59 + ($estiblishTime == null ? 43 : $estiblishTime.hashCode());
        Object $regInstitute = this.getRegInstitute();
        result = result * 59 + ($regInstitute == null ? 43 : $regInstitute.hashCode());
        Object $businessScope = this.getBusinessScope();
        result = result * 59 + ($businessScope == null ? 43 : $businessScope.hashCode());
        Object $taxNumber = this.getTaxNumber();
        result = result * 59 + ($taxNumber == null ? 43 : $taxNumber.hashCode());
        Object $regLocation = this.getRegLocation();
        result = result * 59 + ($regLocation == null ? 43 : $regLocation.hashCode());
        Object $regCapitalCurrency = this.getRegCapitalCurrency();
        result = result * 59 + ($regCapitalCurrency == null ? 43 : $regCapitalCurrency.hashCode());
        Object $tags = this.getTags();
        result = result * 59 + ($tags == null ? 43 : $tags.hashCode());
        Object $district = this.getDistrict();
        result = result * 59 + ($district == null ? 43 : $district.hashCode());
        Object $bondType = this.getBondType();
        result = result * 59 + ($bondType == null ? 43 : $bondType.hashCode());
        Object $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Object $industryAll = this.getIndustryAll();
        result = result * 59 + ($industryAll == null ? 43 : $industryAll.hashCode());
        Object $base = this.getBase();
        result = result * 59 + ($base == null ? 43 : $base.hashCode());
        Object $phoneNumber = this.getPhoneNumber();
        result = result * 59 + ($phoneNumber == null ? 43 : $phoneNumber.hashCode());
        return result;
    }

    public String toString() {
        return "SkyEyeBaseInfo(historyNames=" + this.getHistoryNames() + ", cancelDate=" + this.getCancelDate() + ", regStatus=" + this.getRegStatus() + ", regCapital=" + this.getRegCapital() + ", city=" + this.getCity() + ", staffNumRange=" + this.getStaffNumRange() + ", bondNum=" + this.getBondNum() + ", historyNameList=" + this.getHistoryNameList() + ", industry=" + this.getIndustry() + ", bondName=" + this.getBondName() + ", revokeDate=" + this.getRevokeDate() + ", type=" + this.getType() + ", updateTimes=" + this.getUpdateTimes() + ", legalPersonName=" + this.getLegalPersonName() + ", revokeReason=" + this.getRevokeReason() + ", compForm=" + this.getCompForm() + ", regNumber=" + this.getRegNumber() + ", creditCode=" + this.getCreditCode() + ", property3=" + this.getProperty3() + ", usedBondName=" + this.getUsedBondName() + ", approvedTime=" + this.getApprovedTime() + ", fromTime=" + this.getFromTime() + ", socialStaffNum=" + this.getSocialStaffNum() + ", actualCapitalCurrency=" + this.getActualCapitalCurrency() + ", alias=" + this.getAlias() + ", companyOrgType=" + this.getCompanyOrgType() + ", cancelReason=" + this.getCancelReason() + ", orgNumber=" + this.getOrgNumber() + ", toTime=" + this.getToTime() + ", actualCapital=" + this.getActualCapital() + ", estiblishTime=" + this.getEstiblishTime() + ", regInstitute=" + this.getRegInstitute() + ", businessScope=" + this.getBusinessScope() + ", taxNumber=" + this.getTaxNumber() + ", regLocation=" + this.getRegLocation() + ", regCapitalCurrency=" + this.getRegCapitalCurrency() + ", tags=" + this.getTags() + ", district=" + this.getDistrict() + ", bondType=" + this.getBondType() + ", name=" + this.getName() + ", percentileScore=" + this.getPercentileScore() + ", industryAll=" + this.getIndustryAll() + ", isMicroEnt=" + this.getIsMicroEnt() + ", base=" + this.getBase() + ", phoneNumber=" + this.getPhoneNumber() + ")";
    }
}
