package cn.lili.modules.otherapi.sky.constant;


public enum SkyEyeApiPath {
    GET_COMPANY_INFO("/services/open/cb/ic/2.0", "获取公司详情信息"),
    GET_COMPANY_JUDICIAL_INFO("/services/open/cb/judicial/2.0", "获取公司司法信息"),
    THREE_ELEMENTS_VERTIFY("/services/open/ic/verify/2.0", "企业三要素验证"),
    TOW_ELEMENTS_VERTIFY("https://enterprisetwo.apistore.huaweicloud.com/enterprise/two/validate-detail", "企业二要素验证"),
    COMPANY_BASE_INFO("/services/open/ic/baseinfoV2/2.0", "企业基本信息（含企业联系方式）");

    private final String path;
    private final String desc;

    public static String getDescByPath(String path) {
        for(SkyEyeApiPath pathEnum : values()) {
            if (pathEnum.getPath().equals(path)) {
                return pathEnum.getDesc();
            }
        }

        return null;
    }

    public String getPath() {
        return this.path;
    }

    public String getDesc() {
        return this.desc;
    }

    private SkyEyeApiPath(String path, String desc) {
        this.path = path;
        this.desc = desc;
    }
}

