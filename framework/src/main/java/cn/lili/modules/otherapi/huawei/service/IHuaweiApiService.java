package cn.lili.modules.otherapi.huawei.service;

import com.huaweicloud.sdk.ocr.v1.model.BusinessLicenseResult;
import com.huaweicloud.sdk.ocr.v1.model.IdCardResult;

/**
 * @Author: z<PERSON>gchuangkai
 * @CreateTime: 2023/1/29 16:24
 * @Description: 银行信息服务
 * @Version: 1.0
 */
public interface IHuaweiApiService {

    /**
     * 通过图片识别营业执照
     *
     * @param picUrl
     * @return
     */
    BusinessLicenseResult recognizeBusinessLicense(String picUrl);

    /**
     * 身份证识别
     *
     * @param picUrl
     * @return
     */
    IdCardResult revognizeIdentity(String picUrl);
}
