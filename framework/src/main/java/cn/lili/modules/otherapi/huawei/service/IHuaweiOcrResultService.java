/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.otherapi.huawei.service;

import cn.lili.modules.otherapi.huawei.entity.HuaweiOcrResult;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 华为云orc识别附件 服务类
 *
 * <AUTHOR>
 * @since 2022-07-24
 */
public interface  IHuaweiOcrResultService extends IService<HuaweiOcrResult> {
	/**
	 * 根据hashVal获取对应实体类
	 *
	 * @param hashVal 图片hash
	 * @param ocrType 识别类型
	 * @param <T>
	 * @return
	 */
	<T> T getByHashVal(Long hashVal, Integer ocrType, Class<T> type);

	/**
	 * 保存识别结果
	 *
	 * @param imgHash    图片hash
	 * @param result     识别结果
	 * @param picUrl     图片地址
	 * @param type       识别类型
	 * @param success    是否成功 0失败 1成功
	 * @param failReason 失败理由
	 */
	void saveOcrResult(long imgHash, String result, String picUrl, Integer type, Integer success, String failReason);
}
