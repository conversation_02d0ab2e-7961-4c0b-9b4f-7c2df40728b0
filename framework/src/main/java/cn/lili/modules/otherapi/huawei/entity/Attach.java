//package cn.lili.modules.otherapi.huawei.entity;
//
////
//// Source code recreated from a .class file by IntelliJ IDEA
//// (powered by FernFlower decompiler)
////
//
//
//
//import cn.lili.mybatis.BaseEntity;
//import com.baomidou.mybatisplus.annotation.TableName;
//import io.swagger.annotations.ApiModel;
//
//
//@TableName("blade_attach")
//@ApiModel(
//        value = "Attach对象",
//        description = "附件表"
//)
//public class Attach extends BaseEntity {
//    private static final long serialVersionUID = 1L;
//    private String link;
//    private String domain;
//    private String name;
//    private String originalName;
//    private String extension;
//    private Long attachSize;
//
//    public Attach() {
//    }
//
//    public String getLink() {
//        return this.link;
//    }
//
//    public String getDomain() {
//        return this.domain;
//    }
//
//    public String getName() {
//        return this.name;
//    }
//
//    public String getOriginalName() {
//        return this.originalName;
//    }
//
//    public String getExtension() {
//        return this.extension;
//    }
//
//    public Long getAttachSize() {
//        return this.attachSize;
//    }
//
//    public void setLink(String link) {
//        this.link = link;
//    }
//
//    public void setDomain(String domain) {
//        this.domain = domain;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }
//
//    public void setOriginalName(String originalName) {
//        this.originalName = originalName;
//    }
//
//    public void setExtension(String extension) {
//        this.extension = extension;
//    }
//
//    public void setAttachSize(Long attachSize) {
//        this.attachSize = attachSize;
//    }
//
//    public String toString() {
//        return "Attach(link=" + this.getLink() + ", domain=" + this.getDomain() + ", name=" + this.getName() + ", originalName=" + this.getOriginalName() + ", extension=" + this.getExtension() + ", attachSize=" + this.getAttachSize() + ")";
//    }
//
//    public boolean equals(Object o) {
//        if (o == this) {
//            return true;
//        } else if (!(o instanceof Attach)) {
//            return false;
//        } else {
//            Attach other = (Attach)o;
//            if (!other.canEqual(this)) {
//                return false;
//            } else if (!super.equals(o)) {
//                return false;
//            } else {
//                Object this$attachSize = this.getAttachSize();
//                Object other$attachSize = other.getAttachSize();
//                if (this$attachSize == null) {
//                    if (other$attachSize != null) {
//                        return false;
//                    }
//                } else if (!this$attachSize.equals(other$attachSize)) {
//                    return false;
//                }
//
//                Object this$link = this.getLink();
//                Object other$link = other.getLink();
//                if (this$link == null) {
//                    if (other$link != null) {
//                        return false;
//                    }
//                } else if (!this$link.equals(other$link)) {
//                    return false;
//                }
//
//                label71: {
//                    Object this$domain = this.getDomain();
//                    Object other$domain = other.getDomain();
//                    if (this$domain == null) {
//                        if (other$domain == null) {
//                            break label71;
//                        }
//                    } else if (this$domain.equals(other$domain)) {
//                        break label71;
//                    }
//
//                    return false;
//                }
//
//                label64: {
//                    Object this$name = this.getName();
//                    Object other$name = other.getName();
//                    if (this$name == null) {
//                        if (other$name == null) {
//                            break label64;
//                        }
//                    } else if (this$name.equals(other$name)) {
//                        break label64;
//                    }
//
//                    return false;
//                }
//
//                Object this$originalName = this.getOriginalName();
//                Object other$originalName = other.getOriginalName();
//                if (this$originalName == null) {
//                    if (other$originalName != null) {
//                        return false;
//                    }
//                } else if (!this$originalName.equals(other$originalName)) {
//                    return false;
//                }
//
//                Object this$extension = this.getExtension();
//                Object other$extension = other.getExtension();
//                if (this$extension == null) {
//                    if (other$extension != null) {
//                        return false;
//                    }
//                } else if (!this$extension.equals(other$extension)) {
//                    return false;
//                }
//
//                return true;
//            }
//        }
//    }
//
//    protected boolean canEqual(Object other) {
//        return other instanceof Attach;
//    }
//
//    public int hashCode() {
//        boolean PRIME = true;
//        int result = super.hashCode();
//        Object $attachSize = this.getAttachSize();
//        result = result * 59 + ($attachSize == null ? 43 : $attachSize.hashCode());
//        Object $link = this.getLink();
//        result = result * 59 + ($link == null ? 43 : $link.hashCode());
//        Object $domain = this.getDomain();
//        result = result * 59 + ($domain == null ? 43 : $domain.hashCode());
//        Object $name = this.getName();
//        result = result * 59 + ($name == null ? 43 : $name.hashCode());
//        Object $originalName = this.getOriginalName();
//        result = result * 59 + ($originalName == null ? 43 : $originalName.hashCode());
//        Object $extension = this.getExtension();
//        result = result * 59 + ($extension == null ? 43 : $extension.hashCode());
//        return result;
//    }
//}
