package cn.lili.modules.otherapi.huawei.config;

import com.huaweicloud.sdk.core.auth.BasicCredentials;
import com.huaweicloud.sdk.core.auth.ICredential;
import com.huaweicloud.sdk.ocr.v1.OcrClient;
import com.huaweicloud.sdk.ocr.v1.region.OcrRegion;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 *
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "huawei.auth")
public  class HuaweiOcrConfiguration {
	/**
	 * 访问秘钥
	 */
	private String ak;
	/**
	 * 加密秘钥
	 */
	private String sk;

	@Bean
	public OcrClient getDefaultOcrClient(){
		ICredential auth = new BasicCredentials()
			.withAk(ak)
			.withSk(sk);
		OcrClient client = OcrClient.newBuilder()
			.withCredential(auth)
			.withRegion(OcrRegion.valueOf("cn-north-4"))
			.build();
		return  client;
	}
}

