/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.otherapi.huawei.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.otherapi.huawei.entity.HuaweiOcrResult;
import cn.lili.modules.otherapi.huawei.mapper.HuaweiOcrResultMapper;
import cn.lili.modules.otherapi.huawei.service.IHuaweiOcrResultService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;

import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * 华为云orc识别附件 服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-24
 */
@Service
@RequiredArgsConstructor
public class HuaweiOcrResultServiceImpl extends ServiceImpl<HuaweiOcrResultMapper, HuaweiOcrResult> implements IHuaweiOcrResultService {
    //private final IAttachService attachService;

    @Override
    public <T> T getByHashVal(Long hashVal, Integer ocrType, Class<T> type) {
        HuaweiOcrResult result = getOne(Wrappers.<HuaweiOcrResult>lambdaQuery().eq(HuaweiOcrResult::getHashVal, hashVal)
                .eq(HuaweiOcrResult::getOcrType, ocrType)
                .orderByDesc(HuaweiOcrResult::getCreateTime)
                .last("limit 1"));
        if (ObjectUtil.isNotEmpty(result)) {
            //失败记录抛出失败理由
            if (result.getResultSuccess() == 0) {
                throw new ServiceException(result.getFailReason());
            }
            String orcResultStr = result.getOcrResult();
            if (StrUtil.isNotEmpty(orcResultStr)) {
                return JSONUtil.toBean(orcResultStr, type);
            }
        }
        return null;
    }

    @Override
    @Async
    public void saveOcrResult(long imgHash, String result, String picUrl, Integer type, Integer success, String failReason) {
        //Attach attach = attachService.getByLink(picUrl);
        HuaweiOcrResult ocrResult =  new HuaweiOcrResult();

        ocrResult.setOcrType(type);
        ocrResult.setProof(picUrl);
        ocrResult.setOcrResult(result);
        ocrResult.setHashVal(imgHash);
        ocrResult.setResultSuccess(success);
        ocrResult.setFailReason(failReason);

        save(ocrResult);
    }

}
