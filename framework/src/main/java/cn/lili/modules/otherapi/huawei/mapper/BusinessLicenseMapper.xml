<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.BusinessLicenseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessLicenseResultMap" type="org.springblade.customer.entity.BusinessLicense">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="registration_number" property="registrationNumber"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="address" property="address"/>
        <result column="legal_representative" property="legalRepresentative"/>
        <result column="registered_capital" property="registeredCapital"/>
        <result column="found_date" property="foundDate"/>
        <result column="business_term" property="businessTerm"/>
        <result column="business_scope" property="businessScope"/>
        <result column="issue_date" property="issueDate"/>
        <result column="confidence" property="confidence"/>
        <result column="customer_id" property="customerId"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


    <select id="selectBusinessLicensePage" resultMap="businessLicenseResultMap">
        select * from jrzh_business_license where is_deleted = 0
    </select>

</mapper>
