package cn.lili.modules.jrzh_bases;

import cn.lili.modules.jrzh_contract.contract_api.entity.Attach;
import com.baomidou.mybatisplus.extension.service.IService;

public interface IAttachService extends IService<Attach> {
//    IPage<AttachVO> selectAttachPage(IPage<AttachVO> var1, AttachVO var2);

//    List<Attach> listByLinks(List<String> var1);

//    Attach getByLink(String var1);

//    List<Attach> getAttachListByIds(List<Long> var1);
}
