package cn.lili.modules.jrzh_bases;


import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * poiti Word渲染实体类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PoiTiDataVO {
	@ApiModelProperty(value = "普通对象,接受word中字段及实体变量")
	private Map<String, Object> commonObj;

	@ApiModelProperty(value = "行表格循环对象,接收需要循环的行对象 输出以行")
	private Map<String, List<?>> rowDyObj;

	@ApiModelProperty(value = "列表格循环对象,接收需要循环的列对象 输出以列")
	private Map<String, List<?>> colDyObj;

	@ApiModelProperty(value = "行表格循环对象（自动进行填充下标，）,接收需要循环的列对象 输出以列")
	private Map<String, List<?>> rowDyHasIndexObj;

}
