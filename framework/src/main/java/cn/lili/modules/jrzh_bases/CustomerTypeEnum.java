package cn.lili.modules.jrzh_bases;

public enum CustomerTypeEnum {
    PERSONAL("个人", 1),
    ENTERPRISE("企业", 2),
    UNDER_ENTERPRISE("企业下人员", 3);

    private final String name;
    private final Integer code;

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    private CustomerTypeEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }
}
