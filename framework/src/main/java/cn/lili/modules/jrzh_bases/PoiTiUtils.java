package cn.lili.modules.jrzh_bases;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.data.PictureRenderData;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;
import com.deepoove.poi.util.BytePictureUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


import org.apache.tomcat.util.http.fileupload.FileItemFactory;
import org.apache.tomcat.util.http.fileupload.disk.DiskFileItemFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

public final class PoiTiUtils {
    private static final Logger log = LoggerFactory.getLogger(PoiTiUtils.class);
    private static final String PARSE_TIME_FORMAT = "yyyy-MM-dd";
    private static final String INDEX_LIST_PRE_INDEX = "index";
    private static final String INDEX_SPLIT = "_";
    private static final String realPath = System.getProperty("user.dir");

//    public static MultipartFile uploadImgUrlToMultipartFile(String url) {
//        byte[] bytes = downloadPicture(url);
//        String name = IdWorker.getIdStr() + url.substring(url.lastIndexOf("."));
//        MultipartFile multipartFile = getMultipartFile(name, bytes);
//        return multipartFile;
//    }

//    private static byte[] downloadPicture(String url) {
//        URL urlConnection = null;
//        HttpURLConnection httpURLConnection = null;
//
//        try {
//            urlConnection = new URL(url);
//            httpURLConnection = (HttpURLConnection)urlConnection.openConnection();
//            InputStream in = httpURLConnection.getInputStream();
//            byte[] buffer = new byte[1024];
//            int len = 0;
//            ByteArrayOutputStream out = new ByteArrayOutputStream();
//
//            while((len = in.read(buffer)) != -1) {
//                out.write(buffer, 0, len);
//            }
//
//            in.close();
//            out.close();
//            byte[] var7 = out.toByteArray();
//            return var7;
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            httpURLConnection.disconnect();
//        }
//
//        return null;
//    }

//    public static MultipartFile getMultipartFile(String name, byte[] bytes) {
//        MultipartFile mfile = null;
//        ByteArrayInputStream in = null;
//
//        try {
//            in = new ByteArrayInputStream(bytes);
//            FileItemFactory factory = new DiskFileItemFactory(16, (File)null);
//            FileItem fileItem = factory.createItem("mainFile", "text/plain", false, name);
//            IOUtils.copy(new ByteArrayInputStream(bytes), fileItem.getOutputStream());
//            mfile = new CommonsMultipartFile(fileItem);
//            in.close();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return mfile;
//    }

    public static File genPdfFile(Object obj, InputStream fileInputStream) throws IOException {
        try {
            File wordFile = null;
            File pdfFile = null;
            FileInputStream inputStream = null;
            FileOutputStream fos = null;
            boolean var16 = false;

            File var10;
            try {
                var16 = true;
                Map<String, Object> params = objectToMap(obj);
                HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy();
                ConfigureBuilder config = Configure.newBuilder();
                params.keySet().forEach((key) -> {
                    Object param = params.get(key);
                    if (param instanceof Collection) {
                        try {
                            matchIndexListAndTransfer(params, key);
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                        config.bind(key, policy);
                    }

                });
                XWPFTemplate template = XWPFTemplate.compile(fileInputStream, config.build()).render(params);
                wordFile = new File(CommonConstant.TEMP_FILE_PATH, IdWorker.getIdStr() + ".docx");
                fos = new FileOutputStream(wordFile);
                template.write(fos);
                template.close();
                inputStream = new FileInputStream(wordFile);
                pdfFile = wordToPdf(inputStream);
                var10 = pdfFile;
                var16 = false;
            } finally {
                if (var16) {
                    if (ObjectUtil.isNotEmpty(fos)) {
                        fos.close();
                    }

                    if (ObjectUtil.isNotEmpty(inputStream)) {
                        inputStream.close();
                    }

                    if (wordFile != null && wordFile.exists()) {
                        boolean delete = wordFile.delete();
                        System.out.println("word已经" + delete);
                    }

                }
            }

            if (ObjectUtil.isNotEmpty(fos)) {
                fos.close();
            }

            if (ObjectUtil.isNotEmpty(inputStream)) {
                inputStream.close();
            }

            if (wordFile != null && wordFile.exists()) {
                boolean delete = wordFile.delete();
                System.out.println("word已经" + delete);
            }

            return var10;
        } catch (Throwable $ex) {
            throw $ex;
        }
    }

    private static void matchIndexListAndTransfer(Map<String, Object> params, String key) throws Exception {
        JSONArray objectsArray = JSONUtil.parseArray(params.get(key));
        JSONArray newArrays = new JSONArray(objectsArray.size());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

        for (int i = 0; i < objectsArray.size(); ++i) {
            JSONObject newObj = JSONUtil.parseObj(objectsArray.get(i));
            boolean isIndexList = key.contains("index") && key.contains("_") && "index".equals(key.split("_")[0]);
            if (isIndexList) {
                newObj.putOnce("index", i + 1);
            }

            for (String objKey : newObj.keySet()) {
                Object obj = newObj.get(objKey);
                dealTimeLocal(newObj, simpleDateFormat, objKey, obj);
                Class<?> objClass = obj.getClass();
                if (!ClassUtil.isSimpleTypeOrArray(objClass)) {
                    dealObjNoContainCollection(newObj, simpleDateFormat, key, obj);
                }
            }

            newArrays.add(newObj);
        }

        params.put(key, newArrays);
    }


    private static Map<String, Object> dealPoiDataVO(PoiTiDataVO poiTiData) throws Exception {
        Map<String, Object> commonObjMap = dealMapObj(poiTiData.getCommonObj());
        Map<String, List<Map<String, Object>>> rowListMap = dealCommonTableData(poiTiData.getRowDyObj(), false);
        Map<String, List<Map<String, Object>>> colDyObj = dealCommonTableData(poiTiData.getColDyObj(), false);
        Map<String, List<Map<String, Object>>> rowDyHahIndexMap = dealCommonTableData(poiTiData.getRowDyHasIndexObj(), true);
        Map<String, Object> poiData = new HashMap();
        poiData.putAll(commonObjMap);
        poiData.putAll(rowListMap);
        poiData.putAll(colDyObj);
        poiData.putAll(rowDyHahIndexMap);
        return poiData;
    }

    private static Map<String, List<Map<String, Object>>> dealCommonTableData(Map<String, List<?>> rowDyObj, Boolean hasIndex) throws Exception {
        try {
            if (CollectionUtil.isEmpty(rowDyObj)) {
                return new HashMap();
            } else {
                Map<String, List<Map<String, Object>>> newRowDyObj = new HashMap();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

                for (String rowObjKey : rowDyObj.keySet()) {
                    List<?> objectList = (List) rowDyObj.get(rowObjKey);
                    if (CollectionUtil.isEmpty(objectList)) {
                        newRowDyObj.put(rowObjKey, new ArrayList());
                    } else {
                        List<Map<String, Object>> obj2 = new ArrayList(objectList.size());

                        for (int i = 0; i < objectList.size(); ++i) {
                            Map<String, Object> newObj = object2Map(objectList.get(i), false);
                            if (hasIndex) {
                                newObj.put("index", i + 1);
                            }

                            for (String objKey : newObj.keySet()) {
                                Object obj = newObj.get(objKey);
                                if (!ObjectUtil.isEmpty(obj)) {
                                    dealTimeLocal(newObj, simpleDateFormat, objKey, obj);
                                    Class<?> objClass = obj.getClass();
                                    if (!ClassUtil.isSimpleTypeOrArray(objClass)) {
                                        dealObjNoContainCollection(newObj, simpleDateFormat, objKey, obj);
                                    }
                                }
                            }

                            obj2.add(newObj);
                        }

                        newRowDyObj.put(rowObjKey, obj2);
                    }
                }

                return newRowDyObj;
            }
        } catch (Throwable $ex) {
            throw $ex;
        }
    }

//    public static File genPdfFile(Object obj, InputStream fileInputStream) throws IOException {
//        try {
//            File wordFile = null;
//            File pdfFile = null;
//            FileInputStream inputStream = null;
//            FileOutputStream fos = null;
//            boolean var16 = false;
//
//            File var10;
//            try {
//                var16 = true;
//                Map<String, Object> params = objectToMap(obj);
//                HackLoopTableRenderPolicy policy = new HackLoopTableRenderPolicy();
//                ConfigureBuilder config = Configure.newBuilder();
//                params.keySet().forEach((key) -> {
//                    Object param = params.get(key);
//                    if (param instanceof Collection) {
//                        try {
//                            matchIndexListAndTransfer(params, key);
//                        } catch (Exception e) {
//                            throw new RuntimeException(e);
//                        }
//                        config.bind(key, policy);
//                    }
//
//                });
//                XWPFTemplate template = XWPFTemplate.compile(fileInputStream, config.build()).render(params);
//                wordFile = new File(CommonConstant.TEMP_FILE_PATH, IdWorker.getIdStr() + ".docx");
//                fos = new FileOutputStream(wordFile);
//                template.write(fos);
//                template.close();
//                inputStream = new FileInputStream(wordFile);
//                pdfFile = wordToPdf(inputStream);
//                var10 = pdfFile;
//                var16 = false;
//            } finally {
//                if (var16) {
//                    if (ObjectUtil.isNotEmpty(fos)) {
//                        fos.close();
//                    }
//
//                    if (ObjectUtil.isNotEmpty(inputStream)) {
//                        inputStream.close();
//                    }
//
//                    if (wordFile != null && wordFile.exists()) {
//                        boolean delete = wordFile.delete();
//                        System.out.println("word已经" + delete);
//                    }
//
//                }
//            }
//
//            if (ObjectUtil.isNotEmpty(fos)) {
//                fos.close();
//            }
//
//            if (ObjectUtil.isNotEmpty(inputStream)) {
//                inputStream.close();
//            }
//
//            if (wordFile != null && wordFile.exists()) {
//                boolean delete = wordFile.delete();
//                System.out.println("word已经" + delete);
//            }
//
//            return var10;
//        } catch (Throwable $ex) {
//            throw $ex;
//        }
//    }

//    private static void matchIndexListAndTransfer(Map<String, Object> params, String key) throws Exception {
//        JSONArray objectsArray = JSONUtil.parseArray(params.get(key));
//        JSONArray newArrays = new JSONArray(objectsArray.size());
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//
//        for(int i = 0; i < objectsArray.size(); ++i) {
//            JSONObject newObj = JSONUtil.parseObj(objectsArray.get(i));
//            boolean isIndexList = key.contains("index") && key.contains("_") && "index".equals(key.split("_")[0]);
//            if (isIndexList) {
//                newObj.putOnce("index", i + 1);
//            }
//
//            for(String objKey : newObj.keySet()) {
//                Object obj = newObj.get(objKey);
//                dealTimeLocal(newObj, simpleDateFormat, objKey, obj);
//                Class<?> objClass = obj.getClass();
//                if (!ClassUtil.isSimpleTypeOrArray(objClass)) {
//                    dealObjNoContainCollection(newObj, simpleDateFormat, key, obj);
//                }
//            }
//
//            newArrays.add(newObj);
//        }
//
//        params.put(key, newArrays);
//    }

    public static Map<String, Object> object2Map(Object obj, boolean isFilterNullAttr) throws Exception {
        Map<String, Object> reMap = new HashMap(16);
        if (Objects.isNull(obj)) {
            return null;
        } else if (obj instanceof Map) {
            return (Map) obj;
        } else {
            for (Class<?> objClass = obj.getClass(); null != objClass; objClass = objClass.getSuperclass()) {
                Field[] fields = objClass.getDeclaredFields();

                for (int i = 0; i < fields.length; ++i) {
                    Field field = objClass.getDeclaredField(fields[i].getName());
                    field.setAccessible(true);
                    Object o = field.get(obj);
                    if (isFilterNullAttr) {
                        if (!ObjectUtil.isEmpty(o)) {
                            reMap.put(fields[i].getName(), o);
                        }
                    } else {
                        reMap.put(fields[i].getName(), o);
                    }
                }
            }

            return reMap;
        }
    }

    public static InputStream pictureUrlToInputStream(String url) {
        try {
            InputStream urlPictureStream = BytePictureUtils.getUrlPictureStream(url);
            return urlPictureStream;
        } catch (IOException e) {
            log.error("提示:", e);
            return null;
        }
    }

    public static InputStream getFileInputStream(String path) {
        URL url = null;

        try {
            url = new URL(path);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setConnectTimeout(10000);
            return connection.getInputStream();
        } catch (Exception var3) {
            log.error("文件读取失败" + path);
            return null;
        }
    }

    public static File wordToPdf(InputStream inputStream) {
        try {
            return Word2PdfUtil.word2Pdf(inputStream, (realPath + IdWorker.getIdStr() + ".pdf"));
        } catch (Throwable $ex) {
            throw $ex;
        }
    }

//    public static String wordToPdfBase64(InputStream inputStream) {
//        try {
//            File fileTransfer = null;
//
//            String base64;
//            try {
//                String realPath = (new BladeFileProperties()).getRealPath();
//                fileTransfer = Word2PdfUtil.word2Pdf(inputStream, realPath + IdWorker.getIdStr());
//                base64 = Base64.encode(fileTransfer);
//            } finally {
//                if (fileTransfer != null && fileTransfer.exists()) {
//                    fileTransfer.delete();
//                }
//
//            }
//
//            return base64;
//        } catch (Throwable $ex) {
//            throw $ex;
//        }
//    }

    private static Map<String, Object> dealMapObj(Map<String, Object> objMap) {
        if (CollectionUtil.isEmpty(objMap)) {
            return objMap;
        } else {
            try {
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

                for (String key : objMap.keySet()) {
                    Object o = objMap.get(key);
                    Class<?> objClass = o.getClass();
                    if (!ClassUtil.isSimpleTypeOrArray(objClass)) {
                        dealObjNoContainCollection(objMap, simpleDateFormat, key, o);
                    } else {
                        dealTimeLocal(objMap, simpleDateFormat, key, o);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            return objMap;
        }
    }

    private static Map<String, Object> objectToMap(Object obj) {
        if (ObjectUtil.isEmpty(obj)) {
            return new HashMap();
        } else {
            Map<String, Object> map = null;

            try {
                map = object2Map(obj, true);
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

                for (String key : map.keySet()) {
                    Object o = map.get(key);
                    Class<?> objClass = o.getClass();
                    if (!ClassUtil.isSimpleTypeOrArray(objClass)) {
                        dealObjNoContainCollection(map, simpleDateFormat, key, o);
                    } else {
                        dealTimeLocal(map, simpleDateFormat, key, o);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }

            return map;
        }
    }

    private static void dealObjNoContainCollection(Map<String, Object> map, SimpleDateFormat simpleDateFormat, String key, Object o) throws Exception {
        try {
            if (!(o instanceof PictureRenderData) && !(o instanceof Collection)) {
                Map<String, Object> obj1 = object2Map(o, false);
                if (CollectionUtil.isEmpty(obj1)) {
                    return;
                }

                for (String obj1Key : obj1.keySet()) {
                    dealTimeLocal(obj1, simpleDateFormat, obj1Key, obj1.get(obj1Key));
                }

                map.put(key, obj1);
            }

        } catch (Throwable $ex) {
            throw $ex;
        }
    }

    private static void dealTimeLocal(Map<String, Object> map, SimpleDateFormat simpleDateFormat, String key, Object o) {
        if (ObjectUtil.isNotEmpty(o)) {
            if (o instanceof LocalDateTime) {
                map.replace(key, LocalDateTimeUtil.format((LocalDateTime) o, "yyyy-MM-dd"));
                return;
            }

            if (o instanceof LocalDate) {
                map.replace(key, LocalDateTimeUtil.format((LocalDate) o, "yyyy-MM-dd"));
                return;
            }

            if (o instanceof Date) {
                map.replace(key, simpleDateFormat.format((Date) o));
            }
        }

    }

    private PoiTiUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }
}

