package cn.lili.modules.jrzh_bases;

import cn.hutool.system.OsInfo;
import cn.hutool.system.SystemUtil;
import cn.lili.common.exception.ServiceException;
import com.aspose.words.FontSettings;
import com.aspose.words.SaveFormat;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import com.aspose.words.Document;
import com.aspose.words.License;

import java.io.*;

/**
 * <AUTHOR>
 */
@UtilityClass
@Slf4j
public class Word2PdfUtil {

	/**
	 * 获取license
	 *
	 * @return
	 */
	private static boolean getLicense() {
		boolean result = false;
		try {
			// 凭证
			String licenseStr =
				"<License>\n" +
					"  <Data>\n" +
					"    <Products>\n" +
					"      <Product>Aspose.Total for Java</Product>\n" +
					"      <Product>Aspose.Words for Java</Product>\n" +
					"    </Products>\n" +
					"    <EditionType>Enterprise</EditionType>\n" +
					"    <SubscriptionExpiry>20991231</SubscriptionExpiry>\n" +
					"    <LicenseExpiry>20991231</LicenseExpiry>\n" +
					"    <SerialNumber>8bfe198c-7f0c-4ef8-8ff0-acc3237bf0d7</SerialNumber>\n" +
					"  </Data>\n" +
					"  <Signature>sNLLKGMUdF0r8O1kKilWAGdgfs2BvJb/2Xp8p5iuDVfZXmhppo+d0Ran1P9TKdjV4ABwAgKXxJ3jcQTqE/2IRfqwnPf8itN8aFZlV3TJPYeD3yWE7IT55Gz6EijUpC7aKeoohTb4w2fpox58wWoF3SNp6sK6jDfiAUGEHYJ9pjU=</Signature>\n" +
					"</License>";
			InputStream license = new ByteArrayInputStream(licenseStr.getBytes("UTF-8"));
			License asposeLic = new License();
			asposeLic.setLicense(license);
			result = true;
		} catch (Exception e) {
			log.error("error:", e);
		}
		return result;
	}

	/**
	 * Word 2 pdf.
	 *
	 * @param inputStream the multipart file
	 * @param pdfFilePath the pdf file path
	 */
	public static File word2Pdf(InputStream inputStream,String pdfFilePath) {
		FileOutputStream fileOS = null;
		File file = null;
		if (!getLicense()) {
			log.error("验证License失败！");
			throw new ServiceException("验证License失败！");
		} else {
			try {
				Document doc = new Document(inputStream);
				file = new File(pdfFilePath);
				fileOS = new FileOutputStream(file);
				OsInfo osInfo = SystemUtil.getOsInfo();
				if (osInfo.isLinux()) {
					FontSettings.getDefaultInstance().setFontsFolder("/usr/share/fonts/win", true);
				}

				doc.save(fileOS, 40);
			} catch (Exception e) {
				log.error("error:", e);
				throw new ServiceException("转化pdf失败");
			} finally {
				try {
					if (fileOS != null) {
						fileOS.close();
					}
				} catch (IOException e) {
					log.error("error:", e);
				}

			}

			return file;
		}
	}
}
