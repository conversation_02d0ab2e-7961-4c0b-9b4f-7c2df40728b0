package cn.lili.modules.jrzh_bases;


import java.math.BigDecimal;

public interface CommonConstant {
    String APPLICATION_NAME = "blade-api";
    String SWORD_NAME = "sword";
    String SABER_NAME = "saber";
    Long TOP_PARENT_ID = 0L;
    Long NO_EXIST = -1L;
    Integer ENABLE = 0;
    Integer QUOTASTATUS = 2;
    Integer DIAABLE = 1;
    String TOP_PARENT_NAME = "顶级";
    Integer NOT_SEALED_ID = 0;
    String DEFAULT_PASSWORD = "123456";
    String DEFAULT_PARAM_PASSWORD = "account.initPassword";
    String SORT_FIELD = "sort";
    Integer DATA_SCOPE_CATEGORY = 1;
    Integer API_SCOPE_CATEGORY = 2;
    Integer OPENSTATUS = 1;
    Integer CLOSESTATUS = 0;
    Integer OPEN_RELEASE = 2;
    Integer OFF_RELEASE = 1;
    String WEB_FRONT = "web-front";
    String WEB_BACK = "web-back";
    String WEB_APPLET = "web-applet";
    String WEB_CORE = "web-core";
    String CUSTOMER_WEB_BACK = "blade-customer/";
    String BLADE_GOODS = "blade-goods/";
    String BLADE_HELP = "blade-help/";
    String BLADE_BUSINESS = "blade-business/";
    String BLADE_FINANCE = "blade-finance/";
    String BLADE_JOB = "blade-job/";
    String BLADE_CONTRACT = "blade-contract/";
    String BLADE_RISKMANA = "blade-riskmana/";
    String BLADE_RISK_SYSTEM = "blade-risk-system/";
    String BLADE_COMMODITY = "blade-commodity/";
    String BLADE_OTHER_API = "blade-otherApi/";
    String BLADE_FRONT = "blade-front/";
    String BLADE_BILL = "blade-bill/";
    String BLADE_ACCOUNT = "blade-account/";
    String BLADE_CLOUD = "blade-cloud/";
    String BLADE_PURCHASE = "blade-purchase/";
    String BLADE_WAREHOUSE = "blade-warehouse/";
    String BLADE_REDEMPTION = "blade-redemption/";
    String BLADE_LOAN = "blade-loan/";
    String BLADE_ADJUST = "blade_adjust/";
    String BLADE_FOLLOW = "blade-follow/";
    String BLADE_REDEEM = "blade-redeem/";
    String BLADE_COMPANY_INFO = "blade-companyInfo/";
    String DROOLS = "blade-drools/";
    String BLADE_MESSAGE = "blade-message/";
    String BLADE_REPORT = "blade-report/";
    String BLADE_NEGOTIATE = "blade_negotiate/";
    String BLADE_FEIGN = "blade_feign/";
    Integer NUMBER_STRATEGY = 6;
    Integer YEAR_DAY_COUNT = 360;
    String WEL_HOME = "wel-home/";
    String BLADE_PAY = "blade-pay/";
    String BLADE_OVERDUE_CONSULT = "blade-overdue-consult/";
    String BLADE_BLOCK_CHAIN = "blade-block-chain/";
    String BLADE_VISUAL = "blade-visual/";
    String EXCLUDE_MENU_NAME = "外部接口,用户管理";
    String PERMIT_OPERA_EXCLUDE_MENU_ACCOUNT = "admin";
    String USERNAME = "username";
    String ACCOUNT = "account";
    String OVERDUEOVERDUEFOUR = "M4";
    Integer ONEHUNDREDANDTWENTY = 120;
    Long DEFAULTCOSTRULE = 1555096877559558145L;
    String TASK_BILL_DAY = "1";
    String TASK_CLOUD_DAY = "2";
    String TASK_PURCHASE_PENDING_DAY = "3";
    Integer SPONSOR_TYPE = 1;
    String NO_BODY_CLAIM = "1";
    Integer YES = 1;
    Integer NO = 0;
    String B2B = "B2B";
    String B2C = "B2C";
    String TRANSFER = "TRANSFER";
    String BIND_ACCOUNT = "BIND_ACCOUNT";
    String BLOCK_ENABLED = "blockEnabled";
    String TEMP_FILE_PATH = System.getProperty("java.io.tmpdir");
    String BLADE_EXPENSE = "blade_expense/";
    String BLADE_PRODUCT = "blade_product/";
    String SYSTEM_SERVICE = "system_service";
    String APP_SERVICE = "app_service";
    String OTHER_SERVICE = "other_service";
    String SYSTEM_FINANCE_SERVICE = "system_finance_service";
    String CUSTOMER_CORE_SERVICE = "customer_core_service";
    String CUSTOMER_SERVICE = "customer_service";
    String BLADE_CLIENT = "client";
    String BLADE_PLEDGE = "blade-pledge/";
    BigDecimal ONE_HUNDRED = new BigDecimal("100");
    Integer NORM_REJECT = 2;
    Integer NORM_NORMAL = 1;
    String RISK_DRL_TEMPLATE = "/templates/01_test2.vm";
}
