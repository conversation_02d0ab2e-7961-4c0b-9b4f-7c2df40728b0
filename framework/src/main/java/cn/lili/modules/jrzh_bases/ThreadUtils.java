package cn.lili.modules.jrzh_bases;


import com.alibaba.ttl.threadpool.TtlExecutors;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public final class ThreadUtils {
    public static ExecutorService asyncPool;

    public static ExecutorService getPool() {
        return asyncPool;
    }

    public static <U> CompletableFuture<U> supplyAsync(Supplier<U> supplier) {
        return CompletableFuture.supplyAsync(supplier, asyncPool);
    }

    public static CompletableFuture<Void> runAsync(Runnable runnable) {
        return CompletableFuture.runAsync(runnable, asyncPool);
    }

    public static CompletableFuture<List<?>> allOf(CompletableFuture<?>... completableFutures) {
        return CompletableFuture.allOf(completableFutures).thenApply((ignore) -> (List)Stream.of(completableFutures).map(CompletableFuture::join).collect(Collectors.toList()));
    }

    public static <T> T get(CompletableFuture<T> completableFuture) throws InterruptedException, ExecutionException, TimeoutException {
        return (T)completableFuture.get(3L, TimeUnit.SECONDS);
    }

    public static <T> T get(CompletableFuture<T> completableFuture, long timeout) throws InterruptedException, ExecutionException, TimeoutException {
        return (T)completableFuture.get(timeout, TimeUnit.SECONDS);
    }

    private ThreadUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    static {
        asyncPool = TtlExecutors.getTtlExecutorService(new ThreadPoolExecutor(50, 300, 300L, TimeUnit.SECONDS, new LinkedBlockingQueue(), new ThreadPoolExecutor.CallerRunsPolicy()));
    }
}

