package cn.lili.modules.jrzh_bases;


import com.fasterxml.jackson.annotation.JsonIgnore;
import java.io.Serializable;

public class SmsCode implements Serializable {
    private static final long serialVersionUID = 1L;
    private boolean success;
    private String phone;
    private String id;
    @JsonIgnore
    private String value;

    public SmsCode() {
        this.success = Boolean.TRUE;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public String getPhone() {
        return this.phone;
    }

    public String getId() {
        return this.id;
    }

    public String getValue() {
        return this.value;
    }

    public SmsCode setSuccess(final boolean success) {
        this.success = success;
        return this;
    }

    public SmsCode setPhone(final String phone) {
        this.phone = phone;
        return this;
    }

    public SmsCode setId(final String id) {
        this.id = id;
        return this;
    }

    @JsonIgnore
    public SmsCode setValue(final String value) {
        this.value = value;
        return this;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SmsCode)) {
            return false;
        } else {
            SmsCode other = (SmsCode)o;
            if (!other.canEqual(this)) {
                return false;
            } else if (this.isSuccess() != other.isSuccess()) {
                return false;
            } else {
                Object this$phone = this.getPhone();
                Object other$phone = other.getPhone();
                if (this$phone == null) {
                    if (other$phone != null) {
                        return false;
                    }
                } else if (!this$phone.equals(other$phone)) {
                    return false;
                }

                Object this$id = this.getId();
                Object other$id = other.getId();
                if (this$id == null) {
                    if (other$id != null) {
                        return false;
                    }
                } else if (!this$id.equals(other$id)) {
                    return false;
                }

                Object this$value = this.getValue();
                Object other$value = other.getValue();
                if (this$value == null) {
                    if (other$value != null) {
                        return false;
                    }
                } else if (!this$value.equals(other$value)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof SmsCode;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        result = result * 59 + (this.isSuccess() ? 79 : 97);
        Object $phone = this.getPhone();
        result = result * 59 + ($phone == null ? 43 : $phone.hashCode());
        Object $id = this.getId();
        result = result * 59 + ($id == null ? 43 : $id.hashCode());
        Object $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : $value.hashCode());
        return result;
    }

    public String toString() {
        return "SmsCode(success=" + this.isSuccess() + ", phone=" + this.getPhone() + ", id=" + this.getId() + ", value=" + this.getValue() + ")";
    }
}

