package cn.lili.modules.jrzh_bases;


import cn.hutool.extra.spring.SpringUtil;
import cn.lili.modules.member.entity.dos.Member;
import cn.lili.modules.member.service.MemberService;
import lombok.experimental.UtilityClass;

@UtilityClass
public class AuthUtil {
    static MemberService memberService;
    public Long getUserId(){
        Member member = memberService.getUserInfo();
        return null == member ? -1L : Long.parseLong(member.getId());
    }
}

