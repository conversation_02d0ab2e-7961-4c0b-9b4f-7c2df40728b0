package cn.lili.modules.jrzh_bases;


import java.util.Arrays;
import java.util.List;

import com.xkzhangsan.time.utils.StringUtil;
import org.springframework.lang.Nullable;

public class Func {
    public Func() {
    }

    public static List<String> toStrList(String str) {
        return Arrays.asList(toStrArray(str));
    }
    public static String[] toStrArray(String str) {
        return toStrArray(",", str);
    }
    public static String[] toStrArray(String split, String str) {
        return isBlank(str) ? new String[0] : str.split(split);
    }
    public static boolean isBlank(@Nullable final String cs) {
        return StringUtil.isEmpty(cs);
    }

    public static List<Integer> toIntList(String str) {
        return Arrays.asList(toIntArray(str));
    }
    public static Integer[] toIntArray(String str) {
        return toIntArray(",", str);
    }
    public static Integer[] toIntArray(String split, String str) {
        if (StringUtil.isEmpty(str)) {
            return new Integer[0];
        } else {
            String[] arr = str.split(split);
            Integer[] ints = new Integer[arr.length];

            for(int i = 0; i < arr.length; ++i) {
                Integer v = toInt(arr[i], 0);
                ints[i] = v;
            }

            return ints;
        }
    }
    public static int toInt(@Nullable final Object str, final int defaultValue) {
        if (str == null) {
            return defaultValue;
        } else {
            try {
                return Integer.valueOf(String.valueOf(str));
            } catch (NumberFormatException var3) {
                return defaultValue;
            }
        }
    }


    public static long toLong(final String str) {
        return toLong(str, 0L);
    }
    public static long toLong(@Nullable final String str, final long defaultValue) {
        if (str == null) {
            return defaultValue;
        } else {
            try {
                return Long.valueOf(str);
            } catch (NumberFormatException var4) {
                return defaultValue;
            }
        }
    }
}