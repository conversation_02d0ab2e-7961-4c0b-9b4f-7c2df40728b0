package cn.lili.modules.member.entity.enums;

import lombok.Getter;

@Getter
public enum MemberRegisterStatusEnum {

    OPEN_COMPANY(1,"进行开通企业"),

    ENTAUTH_NO(2,"未进行企业实名"),

    ENTAUTH_ON(3,"企业实名认证中"),

    ENTAUTH_YES(4,"已完成实名"),


    ;

    private final Integer code;

    private final String desc;


    MemberRegisterStatusEnum(Integer code,String desc){
        this.code = code;
        this.desc = desc;
    }
}
