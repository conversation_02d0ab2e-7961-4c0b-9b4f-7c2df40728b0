package cn.lili.modules.jrzh_other.bestsign.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;


public interface ContractEnum {
	@Getter
	@AllArgsConstructor
	enum contract implements ContractEnum {

		/*
		 * 启用与禁用
		 */
		CONTRACT_TEMPLATE_STATE_ABLE("USING", 1, "启用"),
		CONTRACT_TEMPLATE_STATE_DISABLE("DISABLE", 0, "禁用");
		/*
		 * 上上签编码
		 */
		private String bestSignCode;
		/*
		 * 系统状态
		 */
		private Integer status;
		/*
		 * 描述
		 */
		private String desc;
	}
	/**
	 * 用户合同签署状态
	 * 上上签状态整合
	 */
	@AllArgsConstructor
	@Getter
	enum CONTRACT_OPERATOR_STATUS implements ContractEnum {
		/**
		 * 待签署
		 */
		NOT_START(1, "1", "待签署"),
		/**
		 * 拒绝签署,已取消
		 */
		REJECT(2, "4", "拒绝签署,已取消"),
		/**
		 * 已过期
		 */
		INVALID(4, "9", "已过期"),
		/**
		 * 已签署
		 */
		SIGN_COMPLETE(5, "2", "已签署"),
		/**
		 * 签署异常
		 */
		SYSTEM_ERROR(20, "400", "签署异常"),
		/**
		 * 拒签
		 */
		REFUSE_SIGN(7, "", "拒签"),
		/**
		 * 作废
		 */
		CANCEL(8, "", "作废"),
		;

		private final Integer status;
		private final String ssqStatus;
		private final String desc;

		/**
		 * 根据传入上上签状态进行状态匹配
		 * 找不到则为待签署
		 *
		 * @param code
		 * @return
		 */
		public static Integer matchEnum(String code) {
			for (CONTRACT_OPERATOR_STATUS statusEnum : CONTRACT_OPERATOR_STATUS.values()) {
				if (statusEnum.getSsqStatus().equals(code)) {
					return statusEnum.getStatus();
				}
			}
			return NOT_START.getStatus();
		}

		/**
		 * 是否为结束节点 （已完成、 拒签、审批不通过、逾期未签）
		 *
		 * @param status 当前合同状态
		 * @return
		 */
		public static Boolean isFinished(Integer status) {
			return !CONTRACT_OPERATOR_STATUS.NOT_START.getStatus().equals(status);
		}
	}

	/**
	 * 合同签署状态
	 */
	@AllArgsConstructor
	@Getter
	enum SSQ_CONTRACT_STATUS implements ContractEnum {
		/**
		 * 已创建
		 */
		CREATE(1, "2", "已创建"),
		/**
		 * 拒绝签署,已取消
		 */
		REJECT(2, "4", "拒绝签署,已取消"),
		/**
		 * 已签署
		 */
		SINGED(3, "10", "已签署"),
		/**
		 * 已过期
		 */
		INVALID(4, "9", "已过期"),
		/**
		 * 已完成
		 */
		COMPLETE(5, "5", "已完成"),
		/**
		 * 已发送，正在签署中
		 */
		SEND(6, "3", "已发送，正在签署中"),
		/**
		 * 拒签
		 */
		REFUSE_SIGN(7, "", "拒签"),
		/**
		 * 作废
		 */
		CANCEL(8, "", "作废"),
		;

		/**
		 * 根据传入上上签状态进行状态匹配
		 * 找不到则为待签署
		 *
		 * @param code
		 * @return
		 */
		public static Integer matchEnum(String code) {
			for (SSQ_CONTRACT_STATUS statusEnum : SSQ_CONTRACT_STATUS.values()) {
				if (statusEnum.getSsqStatus().equals(code)) {
					return statusEnum.getStatus();
				}
			}
			return 1;
		}

		/**
		 * 是否为结束节点
		 *
		 * @param status 当前合同状态
		 * @return
		 */
		public static Boolean isFinished(Integer status) {
			return !SSQ_CONTRACT_STATUS.SEND.getStatus().equals(status) &&
				!SSQ_CONTRACT_STATUS.CREATE.getStatus().equals(status) &&
				!SSQ_CONTRACT_STATUS.SINGED.getStatus().equals(status);
		}

		private final Integer status;
		private final String ssqStatus;
		private final String desc;
	}

	@AllArgsConstructor
	@Getter
	enum ContractGenType implements ContractEnum {
		TEMPLATE(1, "合同模板"),
		CUSTOMIZE_TEMPLATE(2, "自定义模板引擎"),
		;
		private final Integer type;
		private final String desc;
	}

	@AllArgsConstructor
	@Getter
	enum signType implements ContractEnum {
		SIGN(1, "签名"),
		STAMP(2, "印章"),
		TIME(3, "签署日期"),
		;
		private final Integer type;
		private final String desc;
	}

	@AllArgsConstructor
	@Getter
	enum ContractVerifyType implements ContractEnum {
		V_CODE(1, "手机号校验"),

		V_FACE(2, "人脸校验"),

		;
		private final Integer type;
		private final String desc;
	}
}
