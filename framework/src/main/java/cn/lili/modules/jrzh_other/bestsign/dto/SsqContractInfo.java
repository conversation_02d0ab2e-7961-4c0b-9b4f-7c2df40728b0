package cn.lili.modules.jrzh_other.bestsign.dto;

import cn.hutool.json.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 合同签署信息
 */
@Data
public class SsqContractInfo implements Serializable {
	/**
	 *
	 */
	private String fid;
	/**
	 * 合同创建者唯一标识
	 */
	private String senderAccount;
	/**
	 * 合同的完成时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private LocalDateTime finishTime;
	/**
	 * 合同内容描述
	 */
	private String description;
	/**
	 * 合同标题
	 */
	private String title;
	/**
	 * 合同的发送时间
	 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private LocalDateTime sendTime;
	/**
	 * 合同签署者account集合
	 */
	private JSONArray signers;
	/**
	 * 开发者编号
	 */
	private String developerId;
	/**
	 * 合同签署的到期时间
	 */
	private String expireTime;
	/**
	 * 合同页数
	 */
	private String pages;
	/**
	 * 合同编号
	 */
	private String contractId;
	/**
	 * 合同状态
	 */
	private String status;
	/**
	 * 用户id
	 */
	private String userId;
}
