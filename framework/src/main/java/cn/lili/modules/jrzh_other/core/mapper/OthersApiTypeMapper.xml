<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.othersapi.core.mapper.OthersApiTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="othersApiTypeResultMap" type="org.springblade.otherapi.core.entity.OthersApiType">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="parent_id" property="parentId"/>
        <result column="name" property="name"/>
        <result column="code" property="code"/>
        <result column="sort" property="sort"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springblade.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>


    <select id="selectOthersApiTypePage" resultMap="othersApiTypeResultMap">
        select * from jrzh_others_api_type where is_deleted = 0
    </select>
    <select id="tree" resultMap="treeNodeResultMap">
        select id, parent_id, name as title, id as "value", id as "key" from   jrzh_others_api_type where is_deleted = 0 ORDER BY sort
    </select>

</mapper>
