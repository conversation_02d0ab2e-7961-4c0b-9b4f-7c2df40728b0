package cn.lili.modules.jrzh_other.remote.handler.order.impl;

import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_other.remote.handler.order.OrderHandler;

/**
 * 订单相关业务
 *
 * @param <T>
 * <AUTHOR>
 * @since 2025-6-17
 */
public class AbstractOrderHandler<T> implements OrderHandler<T> {
    @Override
    public T prepareParams(String memberId, String sn) {
        return null;
    }

    @Override
    public void before(T t, String memberId) {

    }

    @Override
    public void after(T t, String memberId) {

    }

    @Override
    public ResultMessage orderPush(T t) {
        return null;
    }

    @Override
    public Boolean orderDel(String sn) {
        return null;
    }

    @Override
    public void success(T t, String memberId) {

    }

    @Override
    public void fail(T t, String memberId) {

    }

    @Override
    public ResultMessage handler(String memberId, String sn) {
        //订单相关参数处理
        T t = prepareParams(memberId, sn);
        //操作前
        before(t, memberId);
        //处理中
        ResultMessage result = orderPush(t);
        //判断
        if (null!=result&&result.isSuccess()) {
            //执行成功处理逻辑
            success(t, memberId);
        } else {
            //执行失败处理逻辑
            fail(t, memberId);
        }
        //操作后
        after(t, memberId);
        return result;
    }
}
