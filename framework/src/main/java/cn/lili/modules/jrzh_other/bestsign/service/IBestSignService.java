package cn.lili.modules.jrzh_other.bestsign.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.lili.modules.connect.entity.dto.SSQAccountAudit;
import cn.lili.modules.file.entity.File;
import cn.lili.modules.jrzh_bases.BladeFile;
import cn.lili.modules.jrzh_contract.contract_api.dto.SsqContractGenParam;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractReturnData;
import cn.lili.modules.jrzh_other.bestsign.dto.ContractTemplateFileDTO;
import cn.lili.modules.jrzh_other.bestsign.dto.SSQResult;
import cn.lili.modules.jrzh_other.bestsign.dto.SsqContractInfo;
import cn.lili.modules.jrzh_other.bestsign.dto.SsqSignaturePositions;

import java.util.List;

/**
 * @Author: zhengchuangkai
 * @CreateTime: 2023-04-06  10:15
 * @Description: TODO
 * @Version: 1.0
 */
public interface IBestSignService {
//    /**
//     * 对公账户认证
//     *
//     * @param ssqAccountAudit
//     * @return
//     */
//    Boolean entBankVerify(SSQAccountAudit ssqAccountAudit);
//
//    /**
//     * 个人银行验证
//     *
//     * @param bankcard
//     * @param personName
//     * @return
//     */
//    Boolean personBankVerify(String bankcard, String personName);
//
    /**
     * 创建签名/印章图片
     *
     * @param account   账户id
     * @param imageData 图片base64字符串
     * @param imageName 传空或default表示更新默认的签名/印章图片。 企业用户如果有多个印章，可以指定印章名称，签署时用指定的印章名称
     * @return 是否成功
     */
    boolean uploadSignImg(String account, String imageData, String imageName);

    /**
     * 生成签章
     *
     * @param accountId    账户id
     * @param customerType 企业类型 1个人 2企业
     * @return
     */
    boolean generateSignImg(String accountId, Integer customerType);

    /**
     * 下载印章
     *
     * @param imageName 印章名称
     * @param accountId 账户id
     * @return
     */
    BladeFile downloadSignImg(String imageName, String accountId);
//
//    /**
//     * 在线验签(通过合同ID和哈希值)
//     *
//     * @param contractId 合同id
//     * @param fhash      文件hash
//     */
//    boolean verifySignature(String contractId, String fhash);
//
//    /**
//     * 通过合同模板生成合同
//     *
//     * @param ssqContractGenParam 生成合同字段
//     * @return 合同id
//     */
//    ContractReturnData templateContractCreate(SsqContractGenParam ssqContractGenParam);
//
//    /**
//     * 自动签署
//     *
//     * @param varsJSON   签署参数
//     * @param contractId 合同id
//     * @param templateId 模板id
//     * @return
//     */
//    void contractAutoSign(String contractId, String templateId, JSONObject varsJSON);
//
//    /**
//     * @param contractId
//     * @param templateId
//     * @param account
//     * @param genType
//     * @return
//     */
//    String contractSign(String contractId, String templateId, String account, Integer genType);
//
//    /**
//     * 获取合同模板所有字段
//     *
//     * @param templateId
//     * @return
//     */
//    JSONArray getTemplateFields(String templateId);
//
//
//    /**
//     * 跳转到创建模板页面
//     *
//     * @return 跳转地址
//     */
//    String skipTemplatePage();
//
//    /**
//     * 根据模板id预览
//     *
//     * @param templateId
//     * @return
//     */
//    String preview(String templateId);
//
//    /**
//     * 获取开发者模板列表
//     *
//     * @param query
//     * @return
//     */
//    List<SsqTemplate> listContractTemplate(Query query);
//
    /**
     * 查询合同签署者状态
     *
     * @param contractId 合同
     */
    JSONObject getContractStatus(String contractId);

    /**
     * 下载合同
     *
     * @param contractId 合同
     * @return
     */
    BladeFile downLoadContract(String contractId, String contractTitle);

    /**
     * 撤销合同
     *
     * @param contractId 合同id
     */
    void contractRevoke(String contractId);

    /**
     * 撤销合同
     *
     * @param contractIds 合同id
     */
    void contractRevoke(List<String> contractIds);
//
//    /**
//     * 预览合同
//     *
//     * @param contractId
//     * @param accountId
//     * @return
//     */
//    String previewContract(String contractId, String accountId);
//
//    /**
//     * 跳转签署
//     *
//     * @param skipSignParam
//     * @return
//     */
//    String skipToSign(SsqSkipSignParam skipSignParam);
//
    /**
     * 合同锁定
     *
     * @param contractIds 合同ids
     */
    void contractLock(String contractIds);

    /**
     * 查找关键字坐标
     *
     * @param fileBase64 pdf文件
     * @param signAlign  1 左上角 2 右上角 3 左下角 4 右下角
     * @param keywords   关键字
     */
    SsqSignaturePositions getSignaturePositions(String fileBase64, String signAlign, String keywords);
//
    /**
     * 通过Pol引擎创建合同
     *
     * @param contractTemplateFile
     */
    String createContractByReport(ContractTemplateFileDTO contractTemplateFile);
//
    /**
     * 合同最新信息
     *
     * @param contractId
     * @return
     */
    SsqContractInfo getContractInfo(String contractId);
//
//    /**
//     * 合同文件签署
//     */
//    void contractFileSign();
//
//    /**
//     * 自定义模板签署
//     *
//     * @param ssqAutoSignParamNew
//     */
//    void customizeAutoSign(SsqCustomerAutoSignParamNew ssqAutoSignParamNew);
//
//    /**
//     * 模板自动签署
//     *
//     * @param signParam
//     */
//    void templateAutoSign(SsqCustomerAutoSignParamNew signParam);
//
//    /**
//     * 查询用户基本资料
//     *
//     * @param account
//     * @return
//     */
//    SsqUserBaseInfo userBaseInfo(String account);
//
//    /**
//     * 检查安全证书
//     *
//     * @param account
//     */
//    SsqCertInfo checkCerInfo(String account);
//
//    /**
//     * 重新申请证书
//     *
//     * @param account
//     */
//    void reApplyCert(String account);
//
//    /**
//     * 通过现存变量重新发送合同
//     *
//     * @param template       合同模板信息
//     * @param oldContractVar 旧合同变量
//     * @return
//     */
//    ContractReturnData contractResend(SsqContractGenParam template, String oldContractVar);
//
    /**
     * 延长合同时间
     *
     * @param contractIds
     * @param expireTime
     * @return
     */
    boolean delayExpireTime(List<String> contractIds, String expireTime);
//
//    /**
//     * 注册个人
//     *
//     * @param personAuth
//     * @return 任务id
//     */
//    String regPersonUser(SSqPersonAuth personAuth);
//
//    /**
//     * 个人手机三要数验证提交
//     *
//     * @param phoneParam
//     * @return
//     */
//    SSQResult commitPersonAuthInfoOnPhone(SSqPersonAuthPhoneParam phoneParam);
//
//    /**
//     * 个人提交人脸资料提交
//     *
//     * @param faceParam
//     * @return
//     */
//    SSQResult commitPersonAuthInfoOnFace(SSqPersonAuthFaceParam faceParam);
//
//    /**
//     * 个人提交银行资料
//     *
//     * @param bankParam
//     * @return
//     */
//    SSQResult commitPersonAuthInfoOnBank(SSqPersonAuthBankParam bankParam);
//
//    /**
//     * 个人提交微信人脸资料
//     *
//     * @param faceParam
//     * @return
//     */
//    SSQResult commitPersonAuthInfoOnWxAppletFace(SSqPersonAuthFaceParam faceParam);
//
//    /**
//     * 个人人脸对比验证提交
//     *
//     * @param faceContrastParam
//     * @return
//     */
//    SSQResult commitPersonAuthInfoOnFaceContrast(SSqPersonAuthFaceContrastParam faceContrastParam);
//
//    /**
//     * h5腾讯云人脸识别
//     *
//     * @param param
//     * @return
//     */
//    SSQResult commitPersonAuthInfoOnH5FaceContrast(SSqPersonAuthFaceH5Param param);
//
//    /**
//     * 个人手机号三要素验证码校验
//     *
//     * @param identity3Key
//     * @param vcode
//     * @return
//     */
//    SSQResult verifyPersonAuthInfoOnPhone(String identity3Key, String vcode);
//
//    /**
//     * 个人银行卡校验
//     *
//     * @param identity3Key
//     * @param vcode
//     * @return
//     */
//    SSQResult verifyPersonAuthInfoOnBank(String identity3Key, String vcode);
//
//    /**
//     * 企业注册电子签账号
//     *
//     * @param sSqEntReg
//     * @return
//     */
//    String regEntUser(SSqEntReg sSqEntReg);
//
    /**
     * 企业打款认证状态查询
     *
     * @param bizNo
     * @return
     */
    SSQResult entPayAuthQuery(String bizNo);
//
    /**
     * 发起企业打款认证
     *
     * @param accountAudit
     * @return
     */
    SSQResult entCorporateAccountAudit(SSQAccountAudit accountAudit);

    /**
     * 企业打款认证验证
     *
     * @param bankCard
     * @param transactionAmount
     * @return
     */
    SSQResult payAuthVerify(String bankCard, String transactionAmount);
//
//    /**
//     * 查询个人用户证件信息
//     *
//     * @param account
//     * @return
//     */
//    PersonEAuthInfoDTO getPersonalCredential(String account);
//
//    /**
//     * 查询企业用户证件信息
//     *
//     * @param account
//     * @return
//     */
//    EntEAuthInfoDTO getEntCredential(String account);
}
