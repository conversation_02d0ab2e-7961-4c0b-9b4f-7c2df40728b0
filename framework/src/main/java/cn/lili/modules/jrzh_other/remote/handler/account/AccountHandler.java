package cn.lili.modules.jrzh_other.remote.handler.account;

import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.promotion.entity.dto.MemberDTO;

/**
 * 账户相关业务
 *
 * <AUTHOR>
 * @since 2025-6-17
 * @param <T>
 */
public interface AccountHandler<T> {
    /**
     * 参数处理
     */
    T prepareParams(MemberDTO memberDto);

    /**
     * 账户相关业务前操作
     */
    void before(T t,String memberId);
    /**
     * 账户相关业务后操作
     */
    void after(T t,String memberId);

    /**
     * 账户同步业务处理
     */
    ResultMessage accountConnection(T t);

    /**
     * 查询产品和额度
     */
    ResultMessage getGoodsAndQuota(String memberId);

    /**
     * 执行成功
     */
    void success(T t,String memberId);

    /**
     * 执行失败
     */
    void fail(T t,String memberId);

    /**
     * 账户相关业务处理类
     */
    ResultMessage handler(MemberDTO memberDto);
}
