package cn.lili.modules.jrzh_other.bestsign.constant;

import okhttp3.MediaType;

/**
 * 上上签请求常量
 */
public class BestSignConstant {
	/***
	 *上上签数据请求格式
	 */
	public static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");
	/**
	 * 上上签数据请求格式
	 */
	public static final String APPLICATION_TYPE = "application/json";
	/**
	 * 上上签签名类型
	 */
	public static final String SIGNATURE_TYPE = "rsa";
	/***
	 * 上上签GET请求路径格式
	 */
	public static String BEST_URI = "bestsign-client-id=%sbestsign-sign-timestamp=%sbestsign-signature-type=%srequest-body=%suri=%s";
	public static String BEST_POST_URI = "developerId=%srtick=%ssignType=%s%s%s";
	public static String URL_FORMAT = "?developerId=%s&rtick=%s&signType=rsa&sign=%s";
}
