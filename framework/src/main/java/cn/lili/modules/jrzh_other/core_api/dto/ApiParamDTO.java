package cn.lili.modules.jrzh_other.core_api.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class ApiParamDTO implements Serializable {
    /**
     * 子类api编号
     */
    private final String code;
    /**
     * 参数列表
     */
    private final Map<String, String> paramMap;
    /**
     * k:请求地址 v:接口信息
     */
//    private final Map<String, OthersApiOpen> openApi;
}
