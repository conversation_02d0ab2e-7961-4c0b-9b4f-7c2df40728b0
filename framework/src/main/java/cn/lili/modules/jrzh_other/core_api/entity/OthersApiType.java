/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_other.core_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Data
@TableName("jrzh_others_api_type")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OthersApiType对象", description = "OthersApiType对象")
public class OthersApiType extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 父级菜单
	 */
	@ApiModelProperty(value = "父级菜单")
	private Long parentId;
	/**
	 * 分类名称
	 */
	@NotBlank
	@ApiModelProperty(value = "分类名称")
	private String name;
	/**
	 * 分类code
	 */
	@NotBlank
	@ApiModelProperty(value = "分类code")
	private String code;
	/**
	 * 排序
	 */
	//@NotNull
	@ApiModelProperty(value = "排序")
	private Integer sort;
	/**
	 *是否支持多开启 0不支持 1支持
	 */
	private Integer supportMultiOpen;

}
