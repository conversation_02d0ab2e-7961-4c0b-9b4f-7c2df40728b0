package cn.lili.modules.jrzh_other.bestsign.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Component
public class BestSignConfig {
    /**
     * 客户端id
     */
    @Value("${apiConfig.bestSign.clientId}")
    private String clientId;
    /**
     * 客户端秘钥
     */
    @Value("${apiConfig.bestSign.clientSecret}")
    private String clientSecret;
    /**
     * rsa私钥
     */
    @Value("${apiConfig.bestSign.rsaPrivateKey}")
    private String rsaPrivateKey;
    /**
     * 主机域名
     */
    @Value("${apiConfig.bestSign.host}")
    private String host;
    /**
     * 开发者id
     */
    @Value("${apiConfig.bestSign.developerId}")
    private String developerId;
    /**
     * 地址后缀
     */
    @Value("${apiConfig.bestSign.hostSuffix}")
    private String hostSuffix;
    /**
     * 账号
     */
    @Value("${apiConfig.bestSign.account}")
    private String account;

}
