package cn.lili.modules.jrzh_other.bestsign.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 合同签署签名位置参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "SsqSignaturePositions对象", description = "SsqSignaturePositions")
public class SsqSignaturePositions {

	@ApiModelProperty(value = "签名页码")
	private String pageNum;

	@ApiModelProperty(value = "签名x坐标")
	private String x;

	@ApiModelProperty(value = "签名y坐标")
	private String y;

	@ApiModelProperty(value = "签名复制页码列表")
	private String rptPageNums;

}
