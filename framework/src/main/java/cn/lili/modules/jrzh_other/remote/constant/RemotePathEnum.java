package cn.lili.modules.jrzh_other.remote.constant;

import cn.lili.modules.jrzh_other.bestsign.constant.BestSignPathEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum RemotePathEnum {
    /**
     * 新增系统账号,账号打通
     */
    ACCOUNT_CONNECTION("/external/front/customerAccountConnection/accountConnection",  "账号打通"),
    ORDER_PUSH("/external/front/customerAccountConnection/orderPush",  "订单推送"),
    ORDER_DEL("/external/front/customerAccountConnection/orderDel",  "订单删除"),
    GET_GOODS_AND_QUOTA("/external/front/customerAccountConnection/getGoodsAndQuota",  "查询产品和额度"),
    ;


    private final String path;
    private final String desc;

    public static String getDescByPath(String path) {
        for (BestSignPathEnum pathEnum : BestSignPathEnum.values()) {
            if (pathEnum.getPath().equals(path)) {
                return pathEnum.getDesc();
            }
        }
        return null;
    }
}
