package cn.lili.modules.jrzh_other.bestsign.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description: 合同配置 枚举
 * @author: cx
 * @date: 2022/8/16 9:54
 * @version：1.0
 */
public interface ContractConfigEnum {


    Integer getCode();

    String getDesc();

    /**
     * 类型枚举
     */
    @AllArgsConstructor
    @Getter
    enum CATEGORY implements ContractConfigEnum {
        /**
         * 签名
         */
        SIGN(0, "签名"),
        /**
         * 印章
         */
        SEAL(1, "印章"),
        ;

        private final Integer code;
        private final String desc;
    }

    /**
     * 图片类型 枚举
     */
    @AllArgsConstructor
    @Getter
    enum IMGTYPE implements ContractConfigEnum {
        /**
         * 上传
         */
        UPLOAD(0, "上传"),
        /**
         * 生成
         */
        GENERATE(1, "生成"),
        ;

        private final Integer code;
        private final String desc;
    }
}
