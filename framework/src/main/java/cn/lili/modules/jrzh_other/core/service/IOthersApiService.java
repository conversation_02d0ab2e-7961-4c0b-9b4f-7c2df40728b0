/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_other.core.service;


import cn.lili.modules.jrzh_other.core_api.dto.ApiParamDTO;
import cn.lili.modules.jrzh_other.core_api.entity.OthersApi;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
public interface IOthersApiService extends IService<OthersApi> {
//
//	/**
//	 * 自定义分页
//	 */
//	IPage<OthersApiVO> selectOthersApiPage(IPage<OthersApiVO> page, OthersApiVO othersApi);
//
//	/**
//	 * 三方api保存
//	 */
//	Boolean othersApiSave(OthersApiDTO othersApiDTO);
//
//
//	/**
//	 * 三方api删除
//	 */
//	Boolean otherApiDelete(List<Long> ids);
//
//	/**
//	 * 查询是否存在
//	 *
//	 * @return
//	 */
//	Boolean existOtherApi(OthersApi othersApi);
//
//	/**
//	 * 状态变更
//	 *
//	 * @param ids
//	 * @param status
//	 * @return
//	 */
//	Boolean changeApiStatus(List<Long> ids, Integer status);
//
	/**
	 * 单开启情况下根据分类code获取配置code
	 *
	 * @param typeCode 分类code
	 * @return
	 */
	String getSingleByTypeCode(String typeCode);
//
	/**
	 * 单开启情况下根据分类code获取配置
	 *
	 * @param typeCode 分类code
	 * @return
	 */
	ApiParamDTO getSingleParamByTypeCode(String typeCode);
//
//	/**
//	 * 多开启情况下根据分类code获取配置code
//	 *
//	 * @param typeCode 分类code
//	 * @param apiNo    三方编号
//	 * @return
//	 */
//	String getMultiByTypeCode(String typeCode, String apiNo);
//
//	/**
//	 * 根据类别查询所有开启的供应商
//	 *
//	 * @param typeCode
//	 * @return
//	 */
//	Map<String, String> mapMultiByTypeCode(String typeCode);
//
//	/**
//	 * 多开启情况下根据分类code获取配置
//	 *
//	 * @param typeCode 分类code
//	 * @param apiNo    三方编号
//	 * @return
//	 */
//	ApiParamDTO getMultiParamByTypeCode(String typeCode, String apiNo);
//
//	/**
//	 * @param typeCode 子类code
//	 * @param keyName  key名称
//	 * @return
//	 */
//	Map<String, String> getParamsValueByApiNoAndKeyName(String typeCode, String keyName);
//
//	/**
//	 * 程序启动时配置池放入所有已启用的配置
//	 */
//	void putPoolEnable();
//
//	/**
//	 * 资方
//	 *
//	 * @param capitalCreditCode
//	 * @return
//	 */
//	String getApiNoByCapitalCreditCode(String capitalCreditCode);
//
//	/**
//	 * 接口是否开启
//	 *
//	 * @param apiNo
//	 * @return
//	 */
//	Boolean isOpenByApiNo(String apiNo);
}
