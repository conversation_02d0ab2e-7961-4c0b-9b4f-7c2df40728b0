/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_other.core.mapper;

import cn.lili.modules.jrzh_other.core_api.entity.OthersApiType;
import cn.lili.modules.jrzh_other.core_api.vo.OthersApiTypeVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
public interface OthersApiTypeMapper extends BaseMapper<OthersApiType> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param othersApiType
	 * @return
	 */
	List<OthersApiTypeVO> selectOthersApiTypePage(IPage page, OthersApiTypeVO othersApiType);


	/**
	 * 三方api分类
	 * @return
	 */
	List<OthersApiTypeVO> tree();

}
