/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_other.core.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.jrzh_bases.CommonConstant;
import cn.lili.modules.jrzh_other.core.mapper.OthersApiMapper;
import cn.lili.modules.jrzh_other.core.service.IOthersApiService;
import cn.lili.modules.jrzh_other.core.service.IOthersApiTypeService;
import cn.lili.modules.jrzh_other.core_api.constant.ApiSupplier;
import cn.lili.modules.jrzh_other.core_api.dto.ApiParamDTO;
import cn.lili.modules.jrzh_other.core_api.entity.OthersApi;
import cn.lili.modules.jrzh_other.core_api.entity.OthersApiParams;
import cn.lili.modules.jrzh_other.core_api.entity.OthersApiType;
import cn.lili.modules.jrzh_other.core_api.vo.OthersApiVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Service
@RequiredArgsConstructor
public class OthersApiServiceImpl extends ServiceImpl<OthersApiMapper, OthersApi> implements IOthersApiService {
//    private final IOthersApiParamsService othersApiParamsService;
    private final IOthersApiTypeService othersApiTypeService;
//    private final IOthersApiOpenService othersApiOpenService;
//	private final IDictService dictService;
    /**
     * 配置缓冲池
     */
    private final Map<String, Map<String, ApiParamDTO>> apiParamPool = new ConcurrentHashMap<>();
//
//    @Override
//    public IPage<OthersApiVO> selectOthersApiPage(IPage<OthersApiVO> page, OthersApiVO othersApi) {
//        return page.setRecords(baseMapper.selectOthersApiPage(page, othersApi));
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean othersApiSave(OthersApiDTO othersApi) {
//        Long apiId = Long.valueOf(othersApi.getId());
//        if (ObjectUtil.isNotEmpty(apiId)) {
//            //先删除
//            othersApiParamsService.deleteByApiId(apiId);
//            othersApiOpenService.deleteByApiId(apiId);
//        }
//        saveOrUpdate(othersApi);
//        //获取新的apiId
//        apiId = Long.valueOf(othersApi.getId());
//        //再保存
//        List<OthersApiParams> paramsList = othersApi.getOthersApiParamsList();
//        if (CollUtil.isNotEmpty(paramsList)) {
//            for (OthersApiParams params : paramsList) {
//                params.setId(null);
//                params.setApiId(apiId);
//            }
//            othersApiParamsService.saveOrUpdateBatch(paramsList);
//        }
//
//        //再保存
//        List<OthersApiOpen> openList = othersApi.getOthersApiOpenList();
//        if (CollUtil.isNotEmpty(openList)) {
//            for (OthersApiOpen open : openList) {
//                open.setId(null);
//                open.setApiId(apiId);
//            }
//            othersApiOpenService.saveOrUpdateBatch(openList);
//        }
//        //根据状态
////        putOrRemoveByStatus(othersApi.getApiType(), othersApi.getApiNo(), paramsList, openList, othersApi.getStatus(), apiId, othersApi.getTenantId());
//        putOrRemoveByStatus(othersApi.getApiType(), othersApi.getApiNo(), paramsList, openList, othersApi.getStatus(), apiId, "000000");
//        return true;
//    }
//
//    /**
//     * 关闭其他相同类型的api
//     *
//     * @param apiType
//     * @param id      当前id
//     */
//    private void closeOtherApiByType(Long apiType, Long id) {
//        update(Wrappers.<OthersApi>lambdaUpdate().set(OthersApi::getStatus, CommonConstant.CLOSESTATUS).eq(OthersApi::getApiType, apiType)
//                .ne(OthersApi::getId, id));
//    }
//
//    private void putOrRemoveByStatus(Long apiType, String apiNo, List<OthersApiParams> apiParamsList, List<OthersApiOpen> openList, Integer status, Long id, String tenantId) {
//        //开启时关闭相同分类下启动的api并将api放入配置连接池
//        //获取父类code
//        OthersApiType type = othersApiTypeService.getById(apiType);
//        if (ObjectUtil.isEmpty(type)) {
//            return;
//        }
//        Integer multiOpen = type.getSupportMultiOpen();
//        String code = type.getCode();
//        String key = tenantId + code;
//        if (CommonConstant.OPENSTATUS.equals(status)) {
//            //非多启动时关闭其他相同类型的api
//            if (!CommonConstant.YES.equals(multiOpen)) {
//                closeOtherApiByType(apiType, id);
//            }
//            putInPool(key, apiNo, apiParamsList, openList, multiOpen);
//        } else {
//            //移除
//            if (CommonConstant.YES.equals(multiOpen)) {
//                //移除多开启情况下的api连接池
//                removeMultiApi(apiNo, key);
//            } else {
//                apiParamPool.remove(key);
//            }
//        }
//    }
//
//    private void removeMultiApi(String apiNo, String key) {
//        if (apiParamPool.containsKey(key)) {
//            Map<String, ApiParamDTO> sysApiMap = apiParamPool.get(key);
//            sysApiMap.remove(apiNo);
//            //若此时缓存池中已经参数列表 则移除
//            if (sysApiMap.size() <= 0) {
//                apiParamPool.remove(key);
//            }
//        }
//    }
//
//
//    /**
//     * 放入配置链接池
//     *
//     * @param key           存入的key
//     * @param apiNo         api编号
//     * @param apiParamsList api参数列表
//     * @param multiOpen     是否支持多开启
//     */
//    private void putInPool(String key, String apiNo, List<OthersApiParams> apiParamsList, List<OthersApiOpen> openList, Integer multiOpen) {
//        //创建新的配置
//        ApiParamDTO apiParams = buildApiParams(apiNo, apiParamsList, openList);
//        Map<String, ApiParamDTO> apiParamDTOMap = new HashMap<>(1);
//        apiParamDTOMap.put(apiNo, apiParams);
//        if (CommonConstant.YES.equals(multiOpen)) {
//            //多开启情况下放入连接池
//            putMultiOpen(key, apiNo, apiParams, apiParamDTOMap);
//        } else {
//            //移除整个类别的缓存 再添加
//            apiParamPool.remove(key);
//            apiParamPool.put(key, apiParamDTOMap);
//        }
//    }
//
//    /**
//     * 多开启情况下放入连接池
//     *
//     * @param key            标识
//     * @param apiNo          编号
//     * @param apiParams      参数
//     * @param apiParamDTOMap 参数Map
//     */
//    private void putMultiOpen(String key, String apiNo, ApiParamDTO apiParams, Map<String, ApiParamDTO> apiParamDTOMap) {
//        //已存在相同类型的情况下 移除再添加 否则直接添加
//        if (apiParamPool.containsKey(key)) {
//            Map<String, ApiParamDTO> sysApiMap = apiParamPool.get(key);
//            sysApiMap.remove(apiNo);
//            sysApiMap.put(apiNo, apiParams);
//        } else {
//            apiParamPool.put(key, apiParamDTOMap);
//        }
//    }
//
//    /**
//     * 创建新的配置
//     *
//     * @param code                子类编号
//     * @param othersApiParamsList 配置参数列表
//     */
//    private ApiParamDTO buildApiParams(String code, List<OthersApiParams> othersApiParamsList, List<OthersApiOpen> openList) {
//        //k:参数key v：参数值
//        Map<String, String> paramMap = othersApiParamsList.stream()
//                .collect(HashMap::new, (m, v) -> m.put(v.getApiKeyName(), v.getApiKeyValue()), HashMap::putAll);
//        Map<String, OthersApiOpen> openMap = new HashMap<>();
//        if (CollUtil.isNotEmpty(openList)) {
//            openMap = openList.stream().collect(Collectors.toMap(OthersApiOpen::getOpenApiUrl, e -> e));
//        }
//        ApiParamDTO apiParam = new ApiParamDTO(code, paramMap, openMap);
//        return apiParam;
//    }
//
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public Boolean otherApiDelete(List<Long> ids) {
//        //清除配置
//        List<OthersApi> othersApis = listByIds(ids);
//        for (OthersApi othersApi : othersApis) {
//            Long id = Long.valueOf(othersApi.getId());
//            //删除
//            othersApiParamsService.deleteByApiId(id);
//        }
//        return removeByIds(ids);
//    }
//
//    @Override
//    public Boolean existOtherApi(OthersApi othersApi) {
//        OthersApiType apiType = othersApiTypeService.getById(othersApi.getApiType());
//        return count(Wrappers.<OthersApi>lambdaQuery()
//                .eq(OthersApi::getApiNo, othersApi.getApiNo())
//                .eq(CommonConstant.NO.equals(apiType.getSupportMultiOpen()), OthersApi::getApiName, othersApi.getApiName())
//                .ne(ObjectUtil.isNotEmpty(othersApi.getId()), OthersApi::getId, othersApi.getId())) > 0;
//    }
//
//    @Override
//    public Boolean changeApiStatus(List<Long> ids, Integer status) {
//        //变更状态
//        this.changeStatus(ids, status);
//        //操作配置连接池
//        List<OthersApi> othersApis = listByIds(ids);
//        for (OthersApi othersApi : othersApis) {
//            Long id = Long.valueOf(othersApi.getId());
//            List<OthersApiParams> othersApiParams = othersApiParamsService.listByApiId(id);
//            List<OthersApiOpen> openList = othersApiOpenService.listByApiId(id);
////            putOrRemoveByStatus(othersApi.getApiType(), othersApi.getApiNo(), othersApiParams, openList, status, id, othersApi.getTenantId());
//            putOrRemoveByStatus(othersApi.getApiType(), othersApi.getApiNo(), othersApiParams, openList, status, id, "000000");
//        }
//        return true;
//    }
//
//    private void changeStatus(List<Long> ids, Integer status) {
//        OthersApiServiceImpl service = SpringUtil.getBean(OthersApiServiceImpl.class);
//        List<OthersApi> list = new ArrayList<>();
//        ids.forEach((id) -> {
//            OthersApi entity = new OthersApi();
//            entity.setUpdateTime(DateUtil.now());
//            entity.setId(String.valueOf(id));
//            entity.setStatus(status);
//            list.add(entity);
//        });
//        service.updateBatchById(list);
//    }
//
    @Override
    public String getSingleByTypeCode(String typeCode) {
        ApiParamDTO apiParam = getSingleParamByTypeCode(typeCode);
        return ObjectUtil.isEmpty(apiParam) ? "" : apiParam.getCode();
    }
//
    @Override
    public ApiParamDTO getSingleParamByTypeCode(String typeCode) {
//        String key = Func.toStr(TenantContextHolder.getTenantId(), Func.toStr(AuthUtil.getTenantId(), "000000")) + typeCode;
        String key = "000000"+ typeCode;
        ApiParamDTO poolApiParam = getFirstElem(apiParamPool.get(key));
        //若查不到 从数据库中查询后放入缓存池
        if (ObjectUtil.isEmpty(poolApiParam)) {
            OthersApiType dataSourceType = othersApiTypeService.getByTypeCode(typeCode);
            OthersApi type = getEnableApiByType(Long.valueOf(dataSourceType.getId()));
            if (ObjectUtil.isNotEmpty(type)) {//TODO服务启动注释
//                List<OthersApiParams> othersApiParams = othersApiParamsService.listByApiId(Long.valueOf(type.getId()));
//                List<OthersApiOpen> openList = othersApiOpenService.listByApiId(Long.valueOf(type.getId()));
//                putInPool(key, type.getApiNo(), othersApiParams, openList, dataSourceType.getSupportMultiOpen());
                return getFirstElem(apiParamPool.get(key));
            }
            throw new ServiceException("配置未开启，请联系管理员");
        }
        return poolApiParam;
    }
//
//    @Override
//    public String getMultiByTypeCode(String typeCode, String apiNo) {
//        ApiParamDTO apiParam = getSingleParamByTypeCode(typeCode);
//        return ObjectUtil.isEmpty(apiParam) ? "" : apiParam.getCode();
//    }
//
//    @Override
//    public Map<String, String> mapMultiByTypeCode(String typeCode) {
//        String key = Func.toStr("000000") + typeCode;
//        Map<String, String> supplierMap = new HashMap<>();
//        if (!apiParamPool.containsKey(key)) {
//            return supplierMap;
//        }
//        Map<String, ApiParamDTO> apiParamMap = apiParamPool.get(key);
//        apiParamMap.forEach((k, v) -> {
//            supplierMap.put(k, ApiSupplier.getDescByCode(k));
//        });
//
//        return supplierMap;
//    }
//
//    @Override
//    public ApiParamDTO getMultiParamByTypeCode(String typeCode, String apiNo) {
////        String key = Func.toStr(TenantContextHolder.getTenantId(), Func.toStr(AuthUtil.getTenantId(), "000000")) + typeCode;
//        String key = Func.toStr("000000") + typeCode;
//        if (!apiParamPool.containsKey(key)) {
//            throw new ServiceException("配置未开启，请联系管理员");
//        }
//        ApiParamDTO apiParamDTO = apiParamPool.get(key).get(apiNo);
//        //若查不到 从数据库中查询后放入缓存池
//        if (ObjectUtil.isEmpty(apiParamDTO)) {
//            OthersApiType dataSourceType = othersApiTypeService.getByTypeCode(typeCode);
//            OthersApi type = getEnableApiByType(Long.valueOf(dataSourceType.getId()));
//            if (ObjectUtil.isNotEmpty(type)) {
//                List<OthersApiParams> othersApiParams = othersApiParamsService.listByApiId(Long.valueOf(type.getId()));
//                List<OthersApiOpen> openList = othersApiOpenService.listByApiId(Long.valueOf(type.getId()));
//                putInPool(key, type.getApiNo(), othersApiParams, openList, dataSourceType.getSupportMultiOpen());
//                return apiParamPool.get(key).get(apiNo);
//            }
//            throw new ServiceException("配置未开启，请联系管理员");
//        }
//        return apiParamDTO;
//    }
//
    /**
     * 获取该Map集合中第一个元素
     *
     * @param apiParamMap
     */
    private ApiParamDTO getFirstElem(Map<String, ApiParamDTO> apiParamMap) {
        ApiParamDTO apiParamDTO = null;
        //取出第一个元素
        if (CollUtil.isNotEmpty(apiParamMap)) {
            for (Map.Entry<String, ApiParamDTO> entry : apiParamMap.entrySet()) {
                ApiParamDTO value = entry.getValue();
                if (ObjectUtil.isNotEmpty(value)) {
                    apiParamDTO = value;
                    break;
                }
            }
        }
        return apiParamDTO;
    }
//
//    @Override
//    public Map<String, String> getParamsValueByApiNoAndKeyName(String code, String keyName) {
//        return Collections.emptyMap();
//    }
//
//    @Override
//    public void putPoolEnable() {
////        List<OthersApi> enableApi = getAllEnable();
////        for (OthersApi othersApi : enableApi) {
////            String tenantId = othersApi.getTenantId();
////            TenantBroker2.runAs(tenantId, e -> {
////                Long id = othersApi.getId();
////                List<OthersApiParams> apiParams = othersApiParamsService.listByApiId(id);
////                List<OthersApiOpen> openList = othersApiOpenService.listByApiId(id);
////                putOrRemoveByStatus(othersApi.getApiType(), othersApi.getApiNo(), apiParams, openList, othersApi.getStatus(), id, tenantId);
////            });
////        }
//    }
//
//    @Override
//    public String getApiNoByCapitalCreditCode(String capitalCreditCode) {
//        //TODO 待处理
////		String key = dictService.getValue("core_system_reference", capitalCreditCode);
////		return StringUtil.isBlank(key) ? ApiSupplier.JR_CAPITAL_SALE_SYSTEM.getCode() : key;
//        return capitalCreditCode;
//    }
//
//    @Override
//    public Boolean isOpenByApiNo(String apiNo) {
//        return count(Wrappers.<OthersApi>lambdaQuery().eq(OthersApi::getApiNo, apiNo)
//                .eq(OthersApi::getStatus, CommonConstant.OPENSTATUS)) > 0;
//    }
//
//    private List<OthersApi> getAllEnable() {
//        return list(Wrappers.<OthersApi>lambdaQuery().eq(OthersApi::getStatus, CommonConstant.OPENSTATUS));
//    }
//
    private OthersApi getEnableApiByType(Long apiType) {
        return getOne(Wrappers.<OthersApi>lambdaQuery()
                .eq(OthersApi::getStatus, CommonConstant.OPENSTATUS)
                .eq(OthersApi::getApiType, apiType).orderByDesc(OthersApi::getUpdateTime)
                .last("limit 1"));
    }
}
