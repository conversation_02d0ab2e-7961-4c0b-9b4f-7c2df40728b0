/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_other.core.service;

import cn.lili.modules.jrzh_other.core_api.entity.OthersApiType;
import cn.lili.modules.jrzh_other.core_api.vo.OthersApiTypeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
public interface IOthersApiTypeService extends IService<OthersApiType> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param othersApiType
	 * @return
	 */
	IPage<OthersApiTypeVO> selectOthersApiTypePage(IPage<OthersApiTypeVO> page, OthersApiTypeVO othersApiType);


	/**
	 * 新增三方api分类
	 * @param othersApiType
	 * @return
	 */
//	boolean submit(OthersApiType othersApiType);


	/**
	 * 三方api分类
	 *
	 * @return
	 */
//	List<OthersApiTypeVO> tree();


	/**
	 * 根据
	 * @param parentId
	 * @return
	 */
//	List<OthersApiType> getListByParentId(Long parentId);

	/**
	 * 删除商品分类
	 * @param toLongList
	 * @return
	 */
//	Boolean removeOthersType(List<Long> toLongList);

	/**
	 * 是否存在
	 * @param code
	 * @param id
	 * @return
	 */
//	boolean existByCodeAndId(String code, Long id);

	/**
	 * 根据编号查找
	 * @param typeCode
	 * @return
	 */
	OthersApiType getByTypeCode(String typeCode);
	/**
	 * 批量插入
	 * @param entityList 实体类集合
	 * @return 成功的行数
	 */
//	Integer insertBatchSomeColumn(List<OthersApiType> entityList);

}
