package cn.lili.modules.jrzh_other.bestsign.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Component
public class AuthApiConfig {
    /**
     * 客户端id
     */
    @Value("${apiConfig.authApi.clientId}")
    private String clientId;
    /**
     * 客户端秘钥
     */
    @Value("${apiConfig.authApi.clientSecret}")
    private String clientSecret;
    /**
     * rsa私钥
     */
    @Value("${apiConfig.authApi.rsaPrivateKey}")
    private String rsaPrivateKey;
    /**
     * 主机域名
     */
    @Value("${apiConfig.authApi.host}")
    private String host;
    /**
     * 开发者id
     */
    @Value("${apiConfig.authApi.developerId}")
    private String developerId;
    /**
     * 地址后缀
     */
    @Value("${apiConfig.authApi.hostSuffix}")
    private String hostSuffix;

    @Value("${apiConfig.authApi.isTest}")
    private String isTest;
}
