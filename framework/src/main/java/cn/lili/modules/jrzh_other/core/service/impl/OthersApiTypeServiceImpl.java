/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_other.core.service.impl;

import cn.lili.modules.jrzh_other.core.mapper.OthersApiTypeMapper;
import cn.lili.modules.jrzh_other.core.service.IOthersApiTypeService;
import cn.lili.modules.jrzh_other.core_api.entity.OthersApiType;
import cn.lili.modules.jrzh_other.core_api.vo.OthersApiTypeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2021-12-07
 */
@Service
@AllArgsConstructor
@Data
public class OthersApiTypeServiceImpl extends ServiceImpl<OthersApiTypeMapper, OthersApiType> implements IOthersApiTypeService {

	@Override
	public IPage<OthersApiTypeVO> selectOthersApiTypePage(IPage<OthersApiTypeVO> page, OthersApiTypeVO othersApiType) {
		return page.setRecords(baseMapper.selectOthersApiTypePage(page, othersApiType));
	}

//	@Override
//	public boolean submit(OthersApiType othersApiType) {
//		synchronized (OthersApiTypeServiceImpl.class) {
//			String code = null;
//			if (Func.isEmpty(othersApiType.getParentId())) {
//				int counter = this.count(Wrappers.<OthersApiType>lambdaQuery().eq(OthersApiType::getParentId, 0));
//				counter = counter + 1;
//				code = "0" + counter;
//			} else {
//				int countSum = this.count(Wrappers.<OthersApiType>lambdaQuery().eq(OthersApiType::getParentId, othersApiType.getParentId()).eq(OthersApiType::getName, othersApiType.getName()));
//				if (countSum > 0) {
//					throw new ServiceException("同分类下面已存在相同名称!");
//				}
//				OthersApiType commodityTypeParent = this.getOne(Wrappers.<OthersApiType>lambdaQuery().eq(OthersApiType::getId, othersApiType.getParentId()));
//				int count = this.count(Wrappers.<OthersApiType>lambdaQuery().eq(OthersApiType::getParentId, othersApiType.getParentId()));
//				count = count + 1;
//				code = commodityTypeParent.getCode() + "0" + count;
//			}
//			othersApiType.setCode(code);
//			return this.save(othersApiType);
//		}
//	}

//	@Override
//	public List<OthersApiTypeVO> tree() {
//		return ForestNodeMerger.merge(baseMapper.tree());
//	}
//
//	@Override
//	public List<OthersApiType> getListByParentId(Long parentId) {
//		return baseMapper.selectList(Wrappers.<OthersApiType>lambdaQuery().eq(OthersApiType::getParentId, parentId).orderByAsc(OthersApiType::getSort));
//	}

//	@Override
//	public Boolean removeOthersType(List<Long> toLongList) {
//		List<OthersApiType> list = baseMapper.selectList(Wrappers.<OthersApiType>lambdaQuery().in(OthersApiType::getParentId, toLongList));
//		if (!CollectionUtils.isEmpty(list)) {
//			throw new ServiceException("请先删除子节点");
//		}
//		IOthersApiService othersApiService = SpringUtil.getBean(IOthersApiService.class);
//		List<OthersApi> othersApis = othersApiService.lambdaQuery().in(OthersApi::getApiType, toLongList).list();
//		if (!CollectionUtils.isEmpty(othersApis)) {
//			throw new ServiceException("请先删除绑定的分类");
//		}
//		return removeByIds(toLongList);
//	}

//	@Override
//	public boolean existByCodeAndId(String code, Long id) {
//		LambdaQueryWrapper<OthersApiType> queryWrapper = Wrappers.lambdaQuery();
//		return count(queryWrapper.eq(OthersApiType::getCode, code)
//			.ne(ObjectUtil.isNotEmpty(id), OthersApiType::getId, id)) > 0;
//	}

	@Override
	public OthersApiType getByTypeCode(String typeCode) {
		return getOne(Wrappers.<OthersApiType>lambdaQuery().eq(OthersApiType::getCode, typeCode)
			.orderByDesc(OthersApiType::getUpdateTime)
			.last("limit 1"));
	}

	/**
	 * 批量插入
	 *
	 * @param entityList 实体类集合
	 * @return 成功的行数
	 */
//	@Override
//	public Integer insertBatchSomeColumn(List<OthersApiType> entityList) {
//		return baseMapper.insertBatchSomeColumn(entityList);
//	}

}
