package cn.lili.modules.jrzh_other.bestsign.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 合同关键字
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SSqKeyWordsPosition implements Serializable {
	@ApiModelProperty(value = "左上角纵坐标")
	private String y1;

	@ApiModelProperty(value = "右下角纵坐标")
	private String y2;

	@ApiModelProperty(value = "右下角横坐标")
	private String x2;

	@ApiModelProperty(value = "关键字所在页码")
	private String pageNum;

	@ApiModelProperty(value = "左上角横坐标")
	private String x1;
}
