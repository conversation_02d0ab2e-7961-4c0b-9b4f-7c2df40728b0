package cn.lili.modules.jrzh_other.core.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.jrzh_other.core.service.IOthersApiService;
import cn.lili.modules.jrzh_other.core_api.dto.ApiParamDTO;

import java.time.Duration;
import java.util.Map;
import java.util.TreeMap;
import java.util.function.Supplier;

/**
 * 接口列表工具类
 */
public class OtherApiUtils {
	private static final IOthersApiService othersApiService = (IOthersApiService)SpringUtil.getBean(IOthersApiService.class);
//	private static final IOtherApiResultService othersApiResultService = (IOtherApiResultService)SpringUtil.getBean(IOtherApiResultService.class);
//	private static final IOthersApiOpenService otherApiOpenService = (IOthersApiOpenService)SpringUtil.getBean(IOthersApiOpenService.class);
//
	/**
	 * 单开启类型接口
	 * 根据传入的接口类型获取当前开启的接口参数
	 *
	 * @param type
	 * @param clazz
	 * @param <T>
	 * @return
	 */
	public static <T> T getParams(String type, Class<T> clazz) {
		ApiParamDTO typeCode = othersApiService.getSingleParamByTypeCode(type);
		if (ObjectUtil.isEmpty(typeCode)) {
			throw new ServiceException("请求的资源不存在或未开启");
		}
		try {
			Map<String, String> paramMap = typeCode.getParamMap();
			JSONObject obj = JSONUtil.parseObj(paramMap);
			return JSONUtil.toBean(obj, clazz);
		} catch (Exception e) {
			throw new ServiceException("类型转换异常，请检查传入的clazz类");
		}
	}
//
//	/**
//	 * 单开启
//	 * 获取参数
//	 *
//	 * @param type
//	 * @return
//	 */
//	public static ApiParamDTO getMapSingleParams(String type) {
//		return othersApiService.getSingleParamByTypeCode(type);
//	}
//
	/**
	 * 单开启类型接口
	 * 获取接口类型获取当前开启的接口供应商编号
	 *
	 * @param type
	 * @return
	 */
	public static String getCode(String type) {
		String typeCode = othersApiService.getSingleByTypeCode(type);
		if (ObjectUtil.isEmpty(typeCode)) {
			throw new ServiceException("请求的资源不存在或未开启");
		}
		return typeCode;
	}
//
//	/**
//	 * 多开启类型接口
//	 * 获取接口类型获取当前开启的接口供应商编号
//	 *
//	 * @param type
//	 * @return
//	 */
//	public static String getCode(String type, String code) {
//		return othersApiService.getMultiByTypeCode(type, code);
//	}
//
//	/**
//	 * 多开启接口类型
//	 * 根据传入的接口类型，接口编号获取参数
//	 *
//	 * @param type
//	 * @param clazz
//	 * @param <T>
//	 * @return
//	 */
//	public static <T> T getParams(String type, String code, Class<T> clazz) {
//		ApiParamDTO typeCode = othersApiService.getMultiParamByTypeCode(type, code);
//		if (ObjectUtil.isEmpty(typeCode)) {
//			throw new ServiceException("请求的资源不存在或未开启");
//		}
//		try {
//			Map<String, String> paramMap = typeCode.getParamMap();
//			JSONObject obj = JSONUtil.parseObj(paramMap);
//			return JSONUtil.toBean(obj, clazz);
//		} catch (Exception e) {
//			throw new ServiceException("类型转换异常，请检查传入的clazz类");
//		}
//	}
//
//	/**
//	 * 多开启
//	 * 获取参数
//	 *
//	 * @param type
//	 * @return
//	 */
//	public static ApiParamDTO getMapMultiParams(String type, String code) {
//		return othersApiService.getMultiParamByTypeCode(type, code);
//	}
//
//	/**
//	 * 查询是否接口开启
//	 *
//	 * @param code
//	 * @return
//	 */
//	public static Boolean isOpenByApiNo(String code) {
//		return othersApiService.isOpenByApiNo(code);
//	}
//
//	/**
//	 * 查看接口是否开启
//	 *
//	 * @param apiParamDTO
//	 * @param url
//	 * @return
//	 */
//	public static Boolean apiIsOpen(ApiParamDTO apiParamDTO, String url) {
//		return otherApiOpenService.isOpen(apiParamDTO, url);
//	}
//
//	/**
//	 * 保存调用结果
//	 *
//	 * @param apiName      三方接口名
//	 * @param methodName   方法名
//	 * @param requestInfo  请求体
//	 * @param responseInfo 识别类型
//	 * @param result       是否成功 0失败 1成功
//	 * @param failReason   失败理由
//	 */
//	public static void saveOtherApiResult(String apiName, String methodName, TreeMap<String, Object> requestInfo, String responseInfo, Integer result, String failReason) {
//		othersApiResultService.saveApiResult(apiName, methodName, requestInfo, responseInfo, result, failReason);
//	}
//
//	public static void saveOtherApiConsequence(String apiName, String methodName, TreeMap<String, Object> requestInfo, String responseInfo, Integer result, String failReason) {
//		othersApiResultService.saveApiResult(apiName, methodName, requestInfo, responseInfo, result, failReason);
//	}
//
//	/**
//	 * 查询单条未过期的识别结果
//	 *
//	 * @param <T>         返回类型
//	 * @param apiName     三方名称
//	 * @param methodName  方法名称
//	 * @param requestInfo 请求参数
//	 * @return
//	 */
//	public static <T> T getByArg(String apiName, String methodName, TreeMap<String, Object> requestInfo, Class<T> type) {
//		return othersApiResultService.getByArg(apiName, methodName, requestInfo, type, null);
//	}
//
//	/**
//	 * 查询单条未过期的识别结果
//	 *
//	 * @param <T>         返回类型
//	 * @param apiName     三方名称
//	 * @param methodName  方法名称
//	 * @param requestInfo 请求参数
//	 * @param timeout     过期时间
//	 * @return
//	 */
//	public static <T> T getByArg(String apiName, String methodName, TreeMap<String, Object> requestInfo, Class<T> type, Duration timeout) {
//		return othersApiResultService.getByArg(apiName, methodName, requestInfo, type, timeout);
//	}
//
//	public static <T> T selectResultDataExists(String apiName, String methodName, TreeMap<String, Object> requestInfo, Class<T> type, Duration timeout) {
//		return othersApiResultService.getByArg(apiName, methodName, requestInfo, type, timeout);
//	}
//
//	/**
//	 * 查询单条未过期的识别结果
//	 *
//	 * @param <T>         返回类型
//	 * @param apiName     三方名称
//	 * @param methodName  方法名称
//	 * @param requestInfo 请求参数
//	 * @param timeout     过期时间
//	 * @return
//	 */
//	public static <T> T getByArg(String apiName, String methodName, String requestInfo, Class<T> type, Duration timeout) {
//		TreeMap<String, Object> req = new TreeMap<>(JSONUtil.parseObj(requestInfo));
//		IOtherApiResultService othersApiResultService = SpringUtil.getBean(IOtherApiResultService.class);
//		return othersApiResultService.getByArg(apiName, methodName, req, type, timeout);
//	}
//
//	/**
//	 * 执行事件并存入缓存中 若该记录已存在 则取旧数据
//	 *
//	 * @param queryTree    请求参数
//	 * @param supplierName 供应商
//	 * @param methodName   方法名
//	 * @param tClass       类型
//	 * @param timeout      过期时间
//	 * @param action       执行事件
//	 * @param <T>
//	 * @return
//	 */
//	public static <T> T doActionWithRecord(TreeMap<String, Object> queryTree, String supplierName, String methodName, Class<T> tClass, Duration timeout, Supplier<T> action) {
//		T oldResult = OtherApiUtils.getByArg(supplierName, methodName, queryTree, tClass, timeout);
//		if (ObjectUtil.isNotEmpty(oldResult)) {
//			return oldResult;
//		}
//		//接口调用
//		T result = action.get();
//		//记录调用结果
//		OtherApiUtils.saveOtherApiResult(supplierName, methodName, queryTree, JSONUtil.toJsonStr(result), 1, null);
//		return result;
//	}
}
