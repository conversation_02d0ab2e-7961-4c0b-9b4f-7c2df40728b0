package cn.lili.modules.jrzh_other.core_api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;


@AllArgsConstructor
@Getter
public enum OtherApiTypeEnum {
	/**
	 * 电子签
	 */

	ELEC_SIGN("elecSign", "电子签"),
	/**
	 * 实名接口
	 */
	AUTH_API("authApi", "实名认证"),
	/**
	 * 企业工商信息接口
	 */
	COMPANY_INFO("companyInfo", "企业工商信息接口"),
	/**
	 * 风控系统
	 */
	RISK_ORDER_SYSTEM("riskOrderSys", "风控系统"),
	/**
	 * 第三方支付
	 */
	POLYMERIZATION_PAY("pay", "第三方支付"),
	/**
	 * 支付系统
	 */
	paySys("paySys", "支付系统"),
	/**
	 * 银行信息服务
	 */
	BANK_SERVICE("bankService","银行信息服务"),
	/**
	 * 交易系统
	 */
	TRADE_API("tradeApi", "交易系统"),
	/**
	 * warehouse
	 */
	WAREHOUSE_API("warehouseApi", "仓储"),
	/**
	 *
	 */
	INTEGRATION_API("integrationApi", "内部对接"),
	/**
	 * 汉辰e签宝签署信息配置
	 */
	HANCHEN_SIGN("hanchenSign", "电子签"),
	/**
	 * 汉辰身份证识别信息配置
	 */
	HANCHEN_OCR_IDCARD("hanChenOcrIdCard", "汉辰身份证识别"),
	/**
	 * 汉辰营业执照识别配置
	 */
	HANCHEN_OCR_BIZ_LICENSE("hanChenOcrBizLicense", "汉辰营业执照识别"),
	/**
	 * 汉辰活体识别获取token配置
	 */
	HANCHEN_LIVING_BODY_OCR("hanChenLivingBodyOcr", "汉辰活体识别获取令牌"),
	/**
	 * 汉辰活体识别结果查询配置
	 */
	HANCHEN_LIVING_BODY_OCR_RESULT("hanChenLivingBodyOcrResult", "汉辰活体识别获取结果"),
	/**
	 * 合同签署
	 */
	CONTRACT_SIGN("contractSign", "合同签署"),
	BEST_CONTRACT_SIGN("bestContractSign", "原合同签署"),
	HAN_CHEN_CONTRACT_SIGN("hanChenContractSign", "汉臣合同签署"),
	/**
	 * 图片ocr识别
	 */
	OCR("ocr", "图片识别"),
	best_ocr("bestOcr", "华为图片识别"),
	han_chen_ocr("hanChenOcr", "汉辰图片识别")
	;
	private final String code;
	private final String desc;
}
