package cn.lili.modules.jrzh_other.bestsign.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 上上签工具版请求路径
 */
@AllArgsConstructor
@Getter
public enum BestSignPathEnum {
	/**
	 * 更新合同签署截止日期
	 */
	DELAY_EXPIRE_TIME("/contract/delayExpireTime/", "更新合同签署截止日期"),
	/**
	 * 注册用户并申请证书
	 */
	USER_REG("/user/reg/", "注册用户并申请证书"),
	/**
	 * 获取证书详细信息
	 */
	USER_CERT_INFO("/user/cert/info/", "获取证书详细信息"),
	/**
	 * 查询证书编号
	 */
	USER_GET_CERT("/user/getCert/", "查询证书编号"),
	/**
	 * 重新申请数字证书
	 */
	USER_REAPPLY_CERT("/user/reapplyCert/", "重新申请数字证书"),
	/**
	 * 支付宝刷脸认证请求认证url
	 */
	PERSON_IDENTITY2_ALIPAYFACE("/realName/personal/identity2/alipayFace", "支付宝刷脸认证请求认证url"),
	/**
	 * 微信小程序刷脸认证请求认证url
	 */
	PERSON_IDENTITY2_WXAPPLETFACE("/realName/personal/identity2/bestsignFace", "微信小程序刷脸认证请求认证"),
	/**
	 * 个人人脸比对验证
	 */
	PERSON_IDENTITY3_FACE_COMPARISON("/credentialVerify/personal/identity3/face", "个人人脸比对验证"),
	/**
	 * 腾讯云H5刷脸认证
	 */
	PERSON_IDENTITY2_H5_COMPARISON("/realName/personal/identity2/face/", "腾讯云H5刷脸认证"),
	/**
	 * 获取刷脸结果
	 */
	PERSON_IDENTITY2_GETFACEAUTHRESULT("/realName/personal/identity2/getFaceAuthResult/", "获取刷脸结果"),
	/**
	 * 个人手机号三要素验证码获取
	 */
	PERSON_IDENTITY3_VCODE_SENDER("/realName/personal/identity3/vcode/sender/", "个人手机号三要素验证码获取"),
	/**
	 * 个人个人手机号三要素验证码校验手机号三要素验证码校验
	 */
	PERSON_IDENTITY3_VCODE_VERIFY("/realName/personal/identity3/vcode/verify/", "个人手机号三要素验证码校验"),
	/**
	 * 银行卡多要素校验及发送验证码
	 */
	PERSON_IDENTITY4_VCODE_SENDER("/realName/personal/identity4/vcode/sender/", "银行卡多要素校验及发送验证码"),
	/**
	 * 银行卡多要素验证码校验
	 */
	PERSON_IDENTITY4_VCODE_VERIFY("/realName/personal/identity4/vcode/verify/", "银行卡多要素验证码校验"),
	/**
	 * 企业银行打款（关联用户）
	 */
	ENT_CORPORATE_ACCOUNT_AUDIT("/realName/enterprise/corporateAccountAudit/", "企业银行打款（关联用户）"),
	/**
	 * 企业应答验证
	 */
	ENT_PAYAUTH_VERIFY("/credentialVerify/enterprise/payAuthVerify", "企业应答验证"),
	/**
	 * 企业打款状态查询
	 */
	ENT_PAY_AUTH_QUERY("/credentialVerify/enterprise/payAuthQuery", "企业打款状态查询"),
	/**
	 * 异步证书申请状态查询
	 */
	ASYNC_APPLY_CERT_STATUS("/user/async/applyCert/status/", "异步证书申请状态查询"),
	/**
	 * 生成用户签名/印章图片
	 */
	CREATE("/signatureImage/user/create", "生成用户签名/印章图片"),
	/**
	 * 上传用户签名/印章图片
	 */
	UPLOAD("/signatureImage/user/upload", "上传用户签名/印章图片"),
	/**
	 * 下载用户签名/印章图片
	 */
	DOWNLOAD("/signatureImage/user/download", "下载用户签名/印章图片"),
	/**
	 * 生成企业印章图片
	 */
	CREATE_ENT("/dist/signatureImage/ent/create", "生成企业印章图片"),
	/**
	 * PDF文件验签（新）
	 */
	PDF_FILE_NEW("/certification/v2/openapi/pdf/verifySignatureByFile", "PDF文件验签（新）"),
	/**
	 * 获取开发者模版列表
	 */
	LIST_TEMPLATES("/template/getTemplates/", "获取开发者模版列表"),
	/**
	 * 获取创建模版的地址
	 */
	PAGE_TEMPLATE_CREATE("/page/template/create/", "获取创建模版的地址"),
	/**
	 * 预览模版
	 */
	PAGE_TEMPLATE_PREVIEW("/page/template/preview/", "预览模版"),
	/**
	 * 获取模版变量
	 */
	TEMPLATE_GET_TEMPLATE_VARS("/template/getTemplateVars/", "获取模版变量"),
	/**
	 * 通过模版生成合同文件
	 */
	TEMPLATE_CREATE_CONTRACT_PDF("/template/createContractPdf/", "通过模版生成PDF合同文件"),
	/**
	 * 通过模版创建合同
	 */
	CONTRACT_CREATE_BY_TEMPLATE("/contract/createByTemplate/", "通过模版创建合同"),
	/**
	 * 用模版变量的手动签
	 */
	CONTRACT_SEND_BY_TEMPLATE("/contract/sendByTemplate/", "用模版变量的手动签"),
	/**
	 * 用模版变量的自动签
	 */
	CONTRACT_SIGN_TEMPLATE("/contract/sign/template/", "用模版变量签署合同"),
	/**
	 * 下载合同文件
	 */
	CONTRACT_DOWNLOAD("/storage/contract/download/", "下载合同文件"),
	/**
	 * 预览合同文件
	 */
	CONTRACT_PREVIEW_URL("/contract/getPreviewURL/", "预览合同文件"),
	/**
	 * 查看合同信息
	 */
	CONTRACT_GET_INFO("/contract/getInfo/", "查看合同信息"),
	/**
	 * 查询合同签署者状态
	 */
	CONTRACT_GET_SIGNER_STATUS("/contract/getSignerStatus/", "查询合同签署者状态"),
	/**
	 * 锁定并结束合同
	 */
	CONTRACT_LOCK("/storage/contract/lock/", "锁定并结束合同"),
	/**
	 * 合同撤銷
	 */
	CONTRACT_CANCEL("/contract/cancel/", "撤销合同"),
	/**
	 * 上传并创建合同
	 */
	CONTRACT_UPLOAD("/storage/contract/upload/", "上传并创建合同"),
	/**
	 * 关键字定位签署合同
	 */
	CONTRACT_SIGN_KEYWORDS("/contract/sign/keywords/", "关键字定位签署合同"),
	/**
	 * 关键字获取合同位置
	 */
	PDF_FIND_KEYWORD_POSITIONS("/pdf/findKeywordPositions/", "PDF关键字查找位置"),
	/**
	 * 关键字获取合同位置
	 */
	CONTRACT_SIGN_CERT("/contract/sign/cert/", "签署合同（每个位置可用不同的图片）"),
	/**
	 * 带校验的自动签
	 */
	CONTRACT_SIGN_CERT2("/storage/contract/sign/cert2/", "带校验的自动签"),
	/**
	 * 查询证书编号
	 */
	USER_BASE_INFO("/user/baseInfo/", "用户基本资料"),
	/**
	 * 在线验签(通过合同ID和哈希值)
	 */
	ONLINE("/contract/verifyContractFileHash", "在线验签(通过合同ID和哈希值)"),
	/**
	 * 对公账户验证
	 */
	ENT_BANK_VERIFY("/realName/enterprise/corporateAccountAudit/", "对公账户验证"),
	/**
	 * 个人银行卡二要素验证
	 */
	PERSON_BANK_VERIFY("/credentialVerify/personal/bankcard2/", "个人银行卡二要素验证"),
	/**
	 * 查询个人用户证件信息
	 */
	GET_PERSONAL_CREDENTIAL("/user/getPersonalCredential/","查询个人用户证件信息"),
	/**
	 * 查询企业用户证件信息
	 */
	GET_ENT_CREDENTIAL("/user/getEnterpriseCredential/","查询企业用户证件信息"),
	;
	private final String path;
	private final String desc;

	public static String getDescByPath(String path) {
		for (BestSignPathEnum pathEnum : BestSignPathEnum.values()) {
			if (pathEnum.getPath().equals(path)) {
				return pathEnum.getDesc();
			}
		}
		return null;
	}
}
