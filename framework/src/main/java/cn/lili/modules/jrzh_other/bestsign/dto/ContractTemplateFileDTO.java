package cn.lili.modules.jrzh_other.bestsign.dto;/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */


import lombok.Data;

import java.io.File;
import java.io.Serializable;


/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@Data
public class ContractTemplateFileDTO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 发送账号
	 */
	private String account;
	/**
	 * 合同生成文件 最后删除
	 */
	private File pdfFile;
	/**
	 * 文件的md5
	 */
	private String fmd5;
	/**
	 * 文件的base64
	 */
	private String fdata;
	/**
	 * 文件页数
	 */
	private String fpages;
	/**
	 * 文件名
	 */
	private String fname;
	/**
	 * 文件类型
	 */
	private String ftype;
	/**
	 * 文件标题
	 */
	private String title;
	/**
	 * 文件描述
	 */
	private String description;
	/**
	 * 文件过期时间
	 */
	private String expireTime;
	/**
	 * 下载链接
	 */
	private String downLoadUrl;
}
