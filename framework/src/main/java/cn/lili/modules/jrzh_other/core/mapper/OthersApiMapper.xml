<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.othersapi.core.mapper.OthersApiMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="othersApiResultMap" type="org.springblade.otherapi.core.entity.OthersApi">
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="api_no" property="apiNo"/>
        <result column="api_name" property="apiName"/>
        <result column="api_img" property="apiImg"/>
        <result column="api_link" property="apiLink"/>
        <result column="api_secrect_content" property="apiSecrectContent"/>
        <result column="api_type" property="apiType"/>
    </resultMap>

    <select id="selectOthersApiPage" resultMap="othersApiResultMap">
        select * from jrzh_others_api where is_deleted = 0
    </select>

</mapper>
