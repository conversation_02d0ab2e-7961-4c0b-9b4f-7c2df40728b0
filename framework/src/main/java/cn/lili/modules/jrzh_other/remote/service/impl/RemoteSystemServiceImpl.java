package cn.lili.modules.jrzh_other.remote.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_other.remote.connector.RemoteSystemConnector;
import cn.lili.modules.jrzh_other.remote.constant.RemotePathEnum;
import cn.lili.modules.jrzh_other.remote.constant.RemoteSystemConstant;
import cn.lili.modules.jrzh_other.remote.dto.OrderInfoConnectionDTO;
import cn.lili.modules.jrzh_other.remote.handler.account.AccountHandler;
import cn.lili.modules.jrzh_other.remote.handler.order.OrderHandler;
import cn.lili.modules.jrzh_other.remote.service.RemoteSystemService;
import cn.lili.modules.promotion.entity.dto.MemberDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 远程系统调用 服务实现类
 *
 * <AUTHOR>
 * @since 2025-5-30
 */
@Service
@RequiredArgsConstructor
public class RemoteSystemServiceImpl implements RemoteSystemService {
    private final RemoteSystemConnector remoteSystemConnector;
    private final Map<String, OrderHandler> orderHandlerMap;
    private final Map<String, AccountHandler> accountHandlerMap;

    @Override
    public ResultMessage accountConnection(String userName) {
        AuthUser user = UserContext.getCurrentUser();
        MemberDTO memberDto = new MemberDTO();
        memberDto.setId(user.getId());
        memberDto.setUserName(userName);
        AccountHandler accountHandler = accountHandlerMap.get(RemoteSystemConstant.SUPPLY_CHAIN_FINANCE_3 + RemoteSystemConstant.ACCOUNT_HANDLER);
        return accountHandler.handler(memberDto);
    }

    /**
     * 订单推送
     *
     * @param sn
     * @return
     */
    @Override
    public ResultMessage orderPush(String sn) {
        AuthUser user = UserContext.getCurrentUser();
        OrderHandler orderHandler = orderHandlerMap.get(RemoteSystemConstant.SUPPLY_CHAIN_FINANCE_3 + RemoteSystemConstant.ORDER_HANDLER);
        return orderHandler.handler(user.getId(), sn);
    }

    /**
     * 推送订单删除
     *
     * @param sn
     * @return
     */
    @Override
    public Boolean orderDel(String sn) {
        OrderInfoConnectionDTO orderPushDto = new OrderInfoConnectionDTO();
        orderPushDto.setOrderNo(sn);
        ResultMessage result = remoteSystemConnector.post(RemotePathEnum.ORDER_DEL.getPath(), JSONUtil.toJsonStr(orderPushDto), true);
        JSONObject parseObj = JSONUtil.parseObj(result.getResult());
        if ("true" == parseObj.get("orderInFinancing")) {
            throw new ServiceException("订单正在融资中，请先取消融资");
        }
        return true;
    }

    /**
     * 查询产品和额度
     *
     * @return
     */
    @Override
    public ResultMessage getGoodsAndQuota() {
        AuthUser user = UserContext.getCurrentUser();
        AccountHandler accountHandler = accountHandlerMap.get(RemoteSystemConstant.SUPPLY_CHAIN_FINANCE_3 + RemoteSystemConstant.ACCOUNT_HANDLER);
        return accountHandler.getGoodsAndQuota(user.getId());
    }


}
