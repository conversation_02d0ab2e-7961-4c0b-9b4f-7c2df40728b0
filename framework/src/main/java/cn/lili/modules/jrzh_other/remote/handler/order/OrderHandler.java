package cn.lili.modules.jrzh_other.remote.handler.order;

import cn.lili.common.vo.ResultMessage;

/**
 * 订单相关业务
 *
 * <AUTHOR>
 * @since 2025-6-17
 * @param <T>
 */
public interface OrderHandler<T> {
    /**
     * 参数处理
     */
    T prepareParams(String memberId,String sn);

    /**
     * 订单相关业务前操作
     */
    void before(T t,String memberId);
    /**
     * 订单相关业务后操作
     */
    void after(T t,String memberId);

    /**
     * 订单推送业务处理
     */
    ResultMessage orderPush(T t);

    /**
     * 订单删除
     */
    Boolean orderDel(String sn);
    /**
     * 执行成功
     */
    void success(T t,String memberId);

    /**
     * 执行失败
     */
    void fail(T t,String memberId);

    /**
     * 订单相关业务处理类
     */
    ResultMessage handler(String memberId,String sn);
}
