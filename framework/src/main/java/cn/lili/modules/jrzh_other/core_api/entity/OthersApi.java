/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_other.core_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2021-11-26
 */
@Data
@TableName("jrzh_others_api")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OthersApi对象", description = "OthersApi对象")
public class OthersApi extends BaseEntity {

	private static final long serialVersionUID = 1L;

	private Integer status;
	/**
	 * api编号
	 */
	@ApiModelProperty(value = "api编号")
	private String apiNo;
	/**
	 * api名称
	 */
	@ApiModelProperty(value = "api名称")
	private String apiName;
	/**
	 * 请求字段
	 */
	@ApiModelProperty(value = "请求图片")
	private String apiImg;
	/**
	 * api类型
	 */
	@ApiModelProperty(value = "api类型")
	private Long apiType;


}
