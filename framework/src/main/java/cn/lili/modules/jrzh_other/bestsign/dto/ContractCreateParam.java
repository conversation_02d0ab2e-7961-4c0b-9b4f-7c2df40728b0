package cn.lili.modules.jrzh_other.bestsign.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建合同 参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractCreateParam {
	/**
	 * 发送账号
	 */
	private String account;
	/**
	 * 文件的md5
	 */
	private String fmd5;
	/**
	 * 文件的base64
	 */
	private String fdata;
	/**
	 * 文件页数
	 */
	private String fpages;
	/**
	 * 文件名
	 */
	private String fname;
	/**
	 * 文件类型
	 */
	private String ftype;
	/**
	 * 文件标题
	 */
	private String title;
	/**
	 * 文件描述
	 */
	private String description;
	/**
	 * 文件过期时间
	 */
	private String expireTime;
}
