package cn.lili.modules.jrzh_other.remote.controller;

import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_other.remote.service.RemoteSystemService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 远程系统调用 控制器
 *
 */
@RestController
@AllArgsConstructor
@Api(value = "远程调用", tags = "远程调用接口")
@RequestMapping("/common/remoteSystem")
public class RemoteSystemController {

    private final RemoteSystemService remoteSystemService;

    @PostMapping("/accountConnection")
    @ApiOperation(value = "账号打通")
    public ResultMessage accountConnection(String userName) {
        return remoteSystemService.accountConnection(userName);

    }

    @GetMapping("/orderPush")
    @ApiOperation(value = "订单推送")
    public ResultMessage orderPush(String sn){
        return remoteSystemService.orderPush(sn);
    }

    @GetMapping("/getGoodsAndQuota")
    @ApiOperation(value = "查询产品和额度")
    public ResultMessage getGoodsAndQuota(){
        return remoteSystemService.getGoodsAndQuota();
    }

//    @GetMapping("/getToken")
//    @ApiOperation(value = "getToken")
//    public ResultMessage getToken(){
//        AuthUser user = UserContext.getCurrentUser();
//        CustomerAccountConnectionDTO dto = new CustomerAccountConnectionDTO();
//        dto.setMemberId(user.getId());
//        return remoteSystemConnector.post(RemotePathEnum.GET_TOKEN.getPath(), JSONUtil.toJsonStr(dto), true);
//    }

}
