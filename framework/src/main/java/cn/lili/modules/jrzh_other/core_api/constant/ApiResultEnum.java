package cn.lili.modules.jrzh_other.core_api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 三方接口方法
 */
public interface ApiResultEnum {
	@Getter
	@AllArgsConstructor
	enum SkyEyesMethod implements ApiResultEnum {
		/**
		 * 发票验真
		 */
		INVOICE_VERIFICATION("InvoiceVerification", "发票验真"),
		/**
		 * 三要素校验
		 */
		VERIFY_COMPANY_THREE("verifyCompanyThree", "三要素校验"),
		/**
		 * 二要素校验
		 */
		VERIFY_COMPANY_TWO("verifyCompanyTwo", "二要素校验"),
		/**
		 * 工商基础信息
		 */
		BASE_COMPANY_INFO("baseCompanyInfo", "工商基础信息"),
		/**
		 * 银联号查询
		 */
		QUERY_YLH("queryylh", "银联号查询"),
		;
		private String name;
		private String desc;
	}

	@Getter
	@AllArgsConstructor
	enum ApiName implements ApiResultEnum {
		/**
		 * 天眼查
		 */
		SKY_EYES("skyEyes", "天眼查");
		private String name;
		private String desc;
	}
}
