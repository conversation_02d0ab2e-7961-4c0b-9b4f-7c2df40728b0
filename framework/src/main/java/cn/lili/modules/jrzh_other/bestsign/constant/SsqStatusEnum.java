package cn.lili.modules.jrzh_other.bestsign.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface SsqStatusEnum {
	/**
	 * 上上签模板类型变量
	 */
	@Getter
	@AllArgsConstructor
	enum PARAM_TYPE implements SsqStatusEnum {
		/**
		 * 盖章
		 */
		STAMP("50", "盖章"),
		/**
		 * 签名
		 */
		SIGN("40", "签名"),
		/**
		 * 图片
		 */
		IMG("30", "图片"),
		/**
		 * 签署日期
		 */
		SIGN_DATA("20", "签署日期"),
		/**
		 * 多行文本
		 */
		MULTIPLE_LINE_TEXT("11", "多行文本"),
		/**
		 * 单行文本
		 */
		SINGLE_LINE_TEXT("10", "单行文本"),
		;
		private String type;
		private String desc;
	}
}
