/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_other.core_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2021-11-25
 */
@Data
@TableName("jrzh_others_api_params")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "OthersApiParams对象", description = "OthersApiParams对象")
public class OthersApiParams extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * api_id
	 */
	@ApiModelProperty(value = "api_id")
	private Long apiId;
	/**
	 * api请求key
	 */
	@ApiModelProperty(value = "api请求key")
	private String apiKeyName;
	/**
	 * api请求value
	 */
	@ApiModelProperty(value = "api请求value")
	private String apiKeyValue;

	@ApiModelProperty(value = "备注")
	private String remark;
}
