package cn.lili.modules.jrzh_other.remote.config;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Data
@Accessors(chain = true)
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Component
public class RemoteApiConfig {

    /**
     * 主机域名
     */
    @Value("${apiConfig.remoteApi.host}")
    private String host;


    /**
     * 应用
     */
    @Value("${apiConfig.remoteApi.authorization-3.0}")
    private String authorization;

}
