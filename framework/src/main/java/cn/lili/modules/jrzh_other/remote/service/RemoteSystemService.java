package cn.lili.modules.jrzh_other.remote.service;

import cn.lili.common.vo.ResultMessage;

public interface RemoteSystemService {
    /**
     * 账号打通
     * @return
     */
    ResultMessage accountConnection(String userName);

    /**
     * 订单推送
     * @param sn
     * @return
     */
    ResultMessage orderPush(String sn);

    /**
     * 推送订单删除
     * @param sn
     * @return
     */
    Boolean orderDel(String sn);

    /**
     * 查询产品和额度
     * @return
     */
    ResultMessage getGoodsAndQuota();
}
