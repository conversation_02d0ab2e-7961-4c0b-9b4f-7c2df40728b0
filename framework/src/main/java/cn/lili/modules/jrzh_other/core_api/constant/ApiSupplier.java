package cn.lili.modules.jrzh_other.core_api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * 接口供应商枚举
 */
@Getter
@AllArgsConstructor
public enum ApiSupplier {
	/**
	 * 汉辰天眼查工商信息
	 */
	HC_SKY_EYES("hcSkyEyes", "汉辰天眼查工商信息"),
	/**
	 * 上上签电子签
	 */
	BEST_SIGN("bestSign", "上上签电子签"),
	/**
	 * 上上签实名认证
	 */
	BEST_SIGN_AUTH("bestSignApiAuth", "上上签实名认证"),
	/**
	 * 精锐纵横电子签
	 */
	JR_SIGN("jrSign", "精锐纵横电子签"),
	/**
	 * 精锐实名认证
	 */
	JR_SIGN_AUTH("jRApiAuth", "精锐纵横实名认证"),
	/**
	 * 天眼查工商信息
	 */
	SKY_EYES("skyEyes", "天眼查工商信息"),
	/**
	 * 华为云工商消息
	 */
	SKY_INDUSTRY("skyIndustry", "华为云工商消息"),
	/**
	 * 精锐风控系统
	 */

	JR_RISK_ORDER_API("jrRiskOrderApi", "精锐风控系统"),
	/**
	 * 精锐风控系统
	 */
	JR_SUPPLY_RISK("jrSupplyRisk", "精锐2.0风控"),
	/**
	 * 精锐风控系统2.0
	 */
	JR_RISK_TWO("jrRiskTwo", "精锐风控系统2.0"),
	/**
	 * 自主资方企业系统
	 */
	JR_CAPITAL_SALE_SYSTEM("capitalSaleSystem", "自主资方企业系统"),
	/**
	 * qq邮箱
	 */
	QQ_MAIL("qqMail", "qq邮箱"),
	/**
	 * 企业qq邮箱
	 */
	QQ_EX_MAIL("qqExMail", "企业qq邮箱"),
	/**
	 * 华为云
	 */
	HUAWEI_BANK_SERVICE("huaWeiBankService", "华为云银行信息服务"),
	/**
	 * 华为云银行卡实名服务
	 */
	HUAWEI_BANK_VERIFY_SERVICE("huaWeiBankVerifyService", "华为云银行卡实名服务"),
	/**
	 * 上上签银行卡实名服务
	 */
	BEST_SIGN_BANK_SERVICE("bestSignBankService", "上上签银行卡实名服务"),
	/**
	 * lianQiao连翘系统
	 */
	LIAN_QIAO_API("lianQiaoApi", "连翘交易系统"),
	/**
	 * lianQiao连翘仓储系统
	 */
	LIAN_QIAO_STORAGE_API("lianQiaoStorageApi", "连翘仓储系统"),
	/**
	 * 通联支付
	 */
	ALL_IN_PAY_API("allinPayApi", "通联支付"),
	/**
	 * 汉臣名认证
	 */
	SIGN_USER_REGISTER("signUserRegister", "汉臣活体识别"),
	/**
	 * 支付系统
	 */
	JR_PAY("jrPay", "支付系统"),
	;


	private final String code;
	private final String desc;

	public static String getDescByCode(String code) {
		for (ApiSupplier supplier : ApiSupplier.values()) {
			if (supplier.getCode().equals(code)) {
				return supplier.getDesc();
			}
		}
		return null;
	}
}
