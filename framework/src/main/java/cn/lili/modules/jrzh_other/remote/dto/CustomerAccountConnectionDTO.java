package cn.lili.modules.jrzh_other.remote.dto;

import cn.lili.modules.connect.entity.CustomerInfo;
import cn.lili.modules.connect.entity.CustomerPersonInfo;
import cn.lili.modules.jrzh_contract.contract_api.entity.Attach;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractSignSeal;
import lombok.Data;

import java.util.List;

/**
 * 用户账号打通传输对象实体类
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
public class CustomerAccountConnectionDTO {
    private String authorization;
    private String customerId;
    /**
     * 用户ID
     */
    private String memberId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 账号
     */
    private String accountUser;

    /**
     * 密码
     */
    private String password;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 个人客户信息
     */
    private CustomerPersonInfo customerPersonInfo;

    /**
     * 企业信息
     */
    private CustomerInfo customerInfo;

    /**
     * 签章
     */
    private List<ContractSignSeal> contractSignSealList;

    /**
     * 合同配置
     */
    private List<ContractConfig> contractConfigList;

    /**
     * 附件
     */
    private List<Attach> attachList;

    /**
     * 个人上上签账号
     */
    private String personSignSealAccount;

    /**
     * 企业上上签账号
     */
    private String entSignSealAccount;

}
