package cn.lili.modules.statistics.service;

import cn.lili.modules.store.entity.dos.Bill;
import cn.lili.modules.store.entity.enums.BillStatusEnum;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 结算单统计
 *
 * <AUTHOR>
 * @since 2020/11/17 4:28 下午
 */
public interface BillStatisticsService extends IService<Bill> {

    /**
     * 商家待结算数量
     *
     * @param billStatusEnum 结算单类型
     * @return 待结算商家数量
     */
    long billNum(BillStatusEnum billStatusEnum);
}