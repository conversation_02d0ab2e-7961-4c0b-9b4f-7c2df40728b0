package cn.lili.modules.system.mapper;

import cn.lili.modules.system.entity.dos.Bank;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BankMapper extends BaseMapper<Bank> {
//    List<BankVO> selectBankPage(IPage var1, BankVO var2);

//    List<Bank> searchBank(@Param("searchNames") List<String> var1, @Param("bankName") String var2);
}
