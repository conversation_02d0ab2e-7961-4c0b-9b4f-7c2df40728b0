package cn.lili.modules.system.serviceimpl;


import cn.lili.modules.system.entity.dos.Bank;
import cn.lili.modules.system.mapper.BankMapper;
import cn.lili.modules.system.service.IBankService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.HashMap;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jodd.util.StringUtil;
import org.springframework.stereotype.Service;

@Service
public class BankServiceImpl extends ServiceImpl<BankMapper, Bank> implements IBankService {
    private static final Map<String, String> BANK_MAP = new HashMap();

//    public IPage<BankVO> selectBankPage(IPage<BankVO> page, BankVO bank) {
//        return page.setRecords(((BankMapper)this.baseMapper).selectBankPage(page, bank));
//    }

    public String getBankCodeByName(String name) {
        String candidateCode = "";

        for(String key : BANK_MAP.keySet()) {
            String[] arr = ((String)BANK_MAP.get(key)).split(",");
            String firstName = arr[0];
            if (name.contains(firstName)) {
                return key;
            }

            for(int i = 1; i < arr.length; ++i) {
                String candidateName = arr[i];
                if (name.contains(candidateName)) {
                    candidateCode = key;
                }
            }
        }

        if (StringUtil.isNotBlank(candidateCode)) {
            return candidateCode;
        } else {
            return null;
        }
    }

    static {
        BANK_MAP.put("ICBC", "中国工商银行,工商");
        BANK_MAP.put("ABC", "中国农业银行,农业");
        BANK_MAP.put("CMBCHINA", "招商银行,招商");
        BANK_MAP.put("CCB", "中国建设银行,建设");
        BANK_MAP.put("BOCO", "交通银行,交通");
        BANK_MAP.put("BOC", "中国银行,中国银行");
        BANK_MAP.put("CMBC", "中国民生银行,民生");
        BANK_MAP.put("CGB", "广发银行,广发");
        BANK_MAP.put("HXB", "华夏银行,华夏");
        BANK_MAP.put("POST", "中国邮政储蓄银行,邮政");
        BANK_MAP.put("ECITIC", "中信银行,中信");
        BANK_MAP.put("CEB", "中国光大银行,光大");
        BANK_MAP.put("PINGAN", "平安银行,平安");
        BANK_MAP.put("CIB", "兴业银行,兴业");
        BANK_MAP.put("SPDB", "浦发银行,浦发,浦东发展");
        BANK_MAP.put("BCCB", "北京银行,北京银行");
        BANK_MAP.put("BON", "南京银行,南京银行");
        BANK_MAP.put("NBCB", "宁波银行,宁波银行");
        BANK_MAP.put("BEA", "东亚银行,东亚银行");
        BANK_MAP.put("SHB", "上海银行,上海银行");
        BANK_MAP.put("CZB", "浙商银行,浙商");
        BANK_MAP.put("TCCB", "天津银行,天津");
        BANK_MAP.put("HSBANK", "徽商银行,徽商");
        BANK_MAP.put("HFBANK", "恒丰银行,恒丰");
        BANK_MAP.put("CBHB", "渤海银行,渤海银行");
        BANK_MAP.put("JSB", "江苏银行,江苏银行");
        BANK_MAP.put("CITI", "花旗银行,花旗");
        BANK_MAP.put("THX", "贵阳银行,贵阳银行");
        BANK_MAP.put("HANGSENGBANK", "恒生银行,恒生");
        BANK_MAP.put("GDNYBANK", "南粤银行,南粤银行");
        BANK_MAP.put("LZBANK", "兰州银行,兰州银行");
        BANK_MAP.put("HZBANK", "杭州银行");
    }
}
