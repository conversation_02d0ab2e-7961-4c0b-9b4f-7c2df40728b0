package cn.lili.modules.system.entity.dos;


import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

@Data
@TableName("jrzh_resource_bank")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "Bank对象", description = "银行列表")
public class Bank extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("银行行号")
    private String bankNo;
    @ApiModelProperty("支行名称")
    private String name;
    @ApiModelProperty("清算行行号")
    private String clearBankNo;
    @ApiModelProperty("代理行行号")
    private String agentBankNo;

    public static BankBuilder builder() {
        return new BankBuilder();
    }

    public String getBankNo() {
        return this.bankNo;
    }

    public String getName() {
        return this.name;
    }

    public String getClearBankNo() {
        return this.clearBankNo;
    }

    public String getAgentBankNo() {
        return this.agentBankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setClearBankNo(String clearBankNo) {
        this.clearBankNo = clearBankNo;
    }

    public void setAgentBankNo(String agentBankNo) {
        this.agentBankNo = agentBankNo;
    }

    public String toString() {
        return "Bank(bankNo=" + this.getBankNo() + ", name=" + this.getName() + ", clearBankNo=" + this.getClearBankNo() + ", agentBankNo=" + this.getAgentBankNo() + ")";
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof Bank)) {
            return false;
        } else {
            Bank other = (Bank)o;
            if (!other.canEqual(this)) {
                return false;
            } else if (!super.equals(o)) {
                return false;
            } else {
                Object this$bankNo = this.getBankNo();
                Object other$bankNo = other.getBankNo();
                if (this$bankNo == null) {
                    if (other$bankNo != null) {
                        return false;
                    }
                } else if (!this$bankNo.equals(other$bankNo)) {
                    return false;
                }

                Object this$name = this.getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }

                Object this$clearBankNo = this.getClearBankNo();
                Object other$clearBankNo = other.getClearBankNo();
                if (this$clearBankNo == null) {
                    if (other$clearBankNo != null) {
                        return false;
                    }
                } else if (!this$clearBankNo.equals(other$clearBankNo)) {
                    return false;
                }

                Object this$agentBankNo = this.getAgentBankNo();
                Object other$agentBankNo = other.getAgentBankNo();
                if (this$agentBankNo == null) {
                    if (other$agentBankNo != null) {
                        return false;
                    }
                } else if (!this$agentBankNo.equals(other$agentBankNo)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof Bank;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Object $bankNo = this.getBankNo();
        result = result * 59 + ($bankNo == null ? 43 : $bankNo.hashCode());
        Object $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Object $clearBankNo = this.getClearBankNo();
        result = result * 59 + ($clearBankNo == null ? 43 : $clearBankNo.hashCode());
        Object $agentBankNo = this.getAgentBankNo();
        result = result * 59 + ($agentBankNo == null ? 43 : $agentBankNo.hashCode());
        return result;
    }

    public Bank(String bankNo, String name, String clearBankNo, String agentBankNo) {
        this.bankNo = bankNo;
        this.name = name;
        this.clearBankNo = clearBankNo;
        this.agentBankNo = agentBankNo;
    }

    public Bank() {
    }

    public static class BankBuilder {
        private String bankNo;
        private String name;
        private String clearBankNo;
        private String agentBankNo;

        BankBuilder() {
        }

        public BankBuilder bankNo(String bankNo) {
            this.bankNo = bankNo;
            return this;
        }

        public BankBuilder name(String name) {
            this.name = name;
            return this;
        }

        public BankBuilder clearBankNo(String clearBankNo) {
            this.clearBankNo = clearBankNo;
            return this;
        }

        public BankBuilder agentBankNo(String agentBankNo) {
            this.agentBankNo = agentBankNo;
            return this;
        }

        public Bank build() {
            return new Bank(this.bankNo, this.name, this.clearBankNo, this.agentBankNo);
        }

        public String toString() {
            return "Bank.BankBuilder(bankNo=" + this.bankNo + ", name=" + this.name + ", clearBankNo=" + this.clearBankNo + ", agentBankNo=" + this.agentBankNo + ")";
        }
    }
}

