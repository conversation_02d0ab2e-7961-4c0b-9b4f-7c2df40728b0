package cn.lili.modules.order.order.mapper;

import cn.lili.modules.order.order.entity.dos.OrderItemSettlement;
import cn.lili.modules.order.order.entity.vo.OrderItemSettlementVo;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月07日16:30
 */
public interface OrderItemSettlementMapper extends BaseMapper<OrderItemSettlement> {

    @Select("SELECT lois.*, lsd.store_name as storeName, lsd.company_name as companyName, lci.corp_name as corpName " +
            "FROM li_order_item_settlement lois " +
            "LEFT JOIN li_store_detail lsd ON lois.store_id = lsd.store_id " +
            "LEFT JOIN li_customer_info lci ON lois.member_id = lci.customer_id " +
            " ${ew.customSqlSegment}")
    IPage<OrderItemSettlementVo> queryByParams(Page<OrderItemSettlementVo> initPage,  @Param(Constants.WRAPPER) Wrapper<OrderItemSettlement> queryWrapper);

    @Select("SELECT lois.*, lsd.store_name as storeName, lsd.company_name as companyName, lci.corp_name as corpName " +
            "FROM li_order_item_settlement lois " +
            "LEFT JOIN li_store_detail lsd ON lois.store_id = lsd.store_id " +
            "LEFT JOIN li_customer_info lci ON lois.member_id = lci.customer_id " +
            " ${ew.customSqlSegment}")
    List<OrderItemSettlementVo> queryListByParams(@Param(Constants.WRAPPER) QueryWrapper<OrderItemSettlementVo> queryWrapper);
}
