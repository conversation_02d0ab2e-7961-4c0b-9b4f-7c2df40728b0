package cn.lili.modules.order.order.entity.dto;

import cn.lili.modules.order.order.entity.dos.Receipt;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.entity.enums.ReceiptStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Optional;


/**
 * 订单发票
 *
 * <AUTHOR>
 * @since 2020/11/28 11:38
 */
@Data
@ApiModel(value = "订单发票")
public class OrderReceiptDTO extends Receipt {

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "销售量")
    private Integer num;

    @ApiModelProperty(value = "已发货数量")
    private Integer deliverNumber;

    // 用于前端展示开票按钮
    public Boolean getCanInvoicing() {
        // 如果已开票则不展示开票按钮
        if (ReceiptStatusEnum.GENERATE.getCode().equals(this.getReceiptStatus())) {
            return Boolean.FALSE;
        }
        // 如果订单状态为已发货或已完成 展示
        if (OrderStatusEnum.COMPLETED.name().equals(this.orderStatus) || OrderStatusEnum.DELIVERED.name().equals(this.orderStatus)) {
            return Boolean.TRUE;
        }
        // 如果子订单数量与发货数量相等 展示
        if (this.num.equals(Optional.ofNullable(this.deliverNumber).orElse(0))) {
            return Boolean.TRUE;
        }
        // 其他情况不展示
        return Boolean.FALSE;
    }

}
