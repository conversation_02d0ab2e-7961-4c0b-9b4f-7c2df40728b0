package cn.lili.modules.order.order.entity.dos;

import cn.lili.modules.order.order.entity.dto.OrderPaymentDto;
import cn.lili.modules.order.order.entity.enums.PayRecordsPaymentMethodEnum;
import cn.lili.modules.order.order.entity.enums.PayRecordsPaymentStatusEnum;
import cn.lili.modules.order.order.entity.enums.PayRecordsTypeEnum;
import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 子订单付款记录
 * <AUTHOR>
 * @date 2025年06月04日17:34
 */
@Data
@TableName("li_order_item_pay_records")
@ApiModel(value = "子订单")
@NoArgsConstructor
public class OrderItemPayRecords extends BaseEntity {

    /**
     * 子订单编号
     */
    private String orderItemSn;

    /**
     * 付款凭证
     */
    private String paymentVoucherUrl;

    /**
     * 店铺id
     */
    private String storeId;

    /**
     * 买方id
     */
    private String memberId;

    /**
     * 支付确认状态
     * @see cn.lili.modules.order.order.entity.enums.PayRecordsPaymentStatusEnum
     */
    private String paymentStatus;

    /**
     * 应付金额
     */
    private BigDecimal payableAmount;

    /**
     * 支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 支付记录id
     */
    private String parentId;

    /**
     * 数据类型
     * @see PayRecordsTypeEnum
     */
    private String type;

    /**
     * 支付方式
     * @see PayRecordsPaymentMethodEnum
     */
    private String paymentMethod;

    /**
     * 付款通知回调的唯一编号
     */
    private String backPaidSn;

    /**
     * 操作确认人
     */
    private String operateMemberId;

    /**
     * 操作人名称
     */
    private String operateMemberName;

    /**
     * 操作时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    public OrderItemPayRecords(OrderItem orderItem, String fileUrl, String memberId, PayRecordsTypeEnum typeEnum, PayRecordsPaymentMethodEnum paymentMethodEnum) {
        this.orderItemSn = orderItem.getSn();
        this.paymentVoucherUrl = fileUrl;
        this.storeId = orderItem.getStoreId();
        this.memberId = memberId;
        this.paymentStatus = PayRecordsPaymentStatusEnum.PAYMENT_UN_CONFIRM.name();
        this.type = typeEnum.name();
        this.paymentMethod = paymentMethodEnum.name();
    }
}
