package cn.lili.modules.order.order.entity.dos;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 开票记录表，目前仅记录发票图片
 * 因一个大单可进行多次开票，即公司会有开票限额，故大额会多次开票
 * <AUTHOR>
 * @date 2025年06月03日15:36
 */
@Data
@TableName("li_receipt_records")
@NoArgsConstructor
public class ReceiptRecords extends BaseEntity {

    /**
     * 发票id
     */
    private String receiptId;

    /**
     * 发票文件url
     */
    private String fileUrl;

    public ReceiptRecords(Receipt receipt, String fileUrl) {
        this.receiptId = receipt.getId();
        this.fileUrl = fileUrl;
    }
}
