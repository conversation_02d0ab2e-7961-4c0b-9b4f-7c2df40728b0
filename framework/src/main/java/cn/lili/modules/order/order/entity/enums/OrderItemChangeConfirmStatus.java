package cn.lili.modules.order.order.entity.enums;

/**
 * 订单项改变确认状态
 * <AUTHOR>
 * @date 2025年04月25日16:06
 */
public enum OrderItemChangeConfirmStatus {

    UN_CONFIRM("未确认/待确认"),
    CONFIRM("已确认"),
    REFUSE("拒绝")
    ;
    private final String description;

    OrderItemChangeConfirmStatus(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}
