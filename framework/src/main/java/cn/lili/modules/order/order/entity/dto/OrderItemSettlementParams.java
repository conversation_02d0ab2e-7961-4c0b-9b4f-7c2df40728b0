package cn.lili.modules.order.order.entity.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.utils.StringUtils;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.order.order.entity.dos.OrderItemSettlement;
import cn.lili.modules.order.order.entity.enums.SettlementSourcEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025年05月07日19:23
 */
@Data
public class OrderItemSettlementParams extends PageVO {

    /**
     * 会员id
     */
    private String memberId;
    /**
     * 店铺id
     */
    private String storeId;
    /**
     * 订单号
     */
    private String orderSn;
    /**
     * 子订单号
     */
    private String orderItemSn;
    /**
     * 结算单来源
     */
    private SettlementSourcEnum settlementSourcEnum;

    /**
     * 商品名称
     */
    private String goodsName;

    public <T> QueryWrapper<T> queryWrapper() {
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        wrapper.eq(StringUtils.isNotEmpty(memberId), "lois.member_id", memberId);
        wrapper.eq(StringUtils.isNotEmpty(storeId), "lois.store_id", storeId);
        wrapper.eq(StringUtils.isNotEmpty(orderSn), "lois.order_sn", orderSn);
        wrapper.eq(StringUtils.isNotEmpty(orderItemSn), "lois.order_item_sn", orderItemSn);
        wrapper.like(StringUtils.isNotEmpty(goodsName), "lois.goods_name", goodsName);
        if (ObjectUtil.isNotEmpty(settlementSourcEnum)) {
            wrapper.eq("lois.settlement_sourc", settlementSourcEnum.name());
        }
        wrapper.eq("lois.delete_flag", Boolean.FALSE);
        wrapper.orderByDesc("lois.id");
        return wrapper;
    }

}
