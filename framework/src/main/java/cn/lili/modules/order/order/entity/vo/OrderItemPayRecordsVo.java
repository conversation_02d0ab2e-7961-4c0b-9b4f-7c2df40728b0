package cn.lili.modules.order.order.entity.vo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.utils.StringUtils;
import cn.lili.modules.order.order.entity.dos.OrderItemPayRecords;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.enums.PayRecordsPaymentMethodEnum;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月06日17:39
 */
@Data
public class OrderItemPayRecordsVo implements Serializable {

    private String id;
    /**
     * 子订单编号
     */
    private String orderItemSn;
    /**
     * 付款凭证
     */
    private String paymentVoucherUrl;
    /**
     * 店铺id
     */
    private String storeId;
    /**
     * 店铺公司名称
     */
    private String storeName;
    /**
     * 买方id
     */
    private String memberId;
    /**
     * 买方公司名称
     */
    private String corpName;
    /**
     * 支付确认状态
     * @see cn.lili.modules.order.order.entity.enums.PayRecordsPaymentStatusEnum
     */
    private String paymentStatus;
    /**
     * 主订单sn
     */
    private String orderSn;
    /**
     * 商品名
     */
    private String goodsName;

    private List<OrderItemPayRecords> paymentVoucherList;

    private List<String> paymentVoucherUrlList;

    /**
     * 支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 应付金额
     */
    private BigDecimal payableAmount;

    /**
     * 操作确认人
     */
    private String operateMemberId;

    /**
     * 操作人名称
     */
    private String operateMemberName;

    /**
     * 操作时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date operateTime;

    /**
     * 支付方式
     * @see PayRecordsPaymentMethodEnum
     */
    private String paymentMethod;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String simpleSpecs;

    @ApiModelProperty(value = "价格详情")
    private String priceDetail;

    /**
     * 调整后实际总金额
     */
    private BigDecimal changeFlowPrice;

    /**
     * 订单应付金额
     */
    private BigDecimal orderItemAmount;

    public PriceDetailDTO getPriceDetailDTO() {
        if (StringUtils.isEmpty(priceDetail)) {
            return new PriceDetailDTO();
        }
        return JSONUtil.toBean(priceDetail, PriceDetailDTO.class);
    }

    public BigDecimal getOrderItemAmount() {
        if (ObjectUtil.isEmpty(changeFlowPrice)) {
            return BigDecimal.ZERO;
        }
        Double freightPrice = getPriceDetailDTO().getFreightPrice();
        return changeFlowPrice.add(BigDecimal.valueOf(freightPrice));
    }
}
