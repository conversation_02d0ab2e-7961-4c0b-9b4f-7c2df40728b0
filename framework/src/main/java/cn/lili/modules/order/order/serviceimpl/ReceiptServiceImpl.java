package cn.lili.modules.order.order.serviceimpl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.Func;
import cn.lili.common.utils.SpringContextUtil;
import cn.lili.common.utils.StringUtils;
import cn.lili.modules.connect.entity.CustomerInfo;
import cn.lili.modules.connect.service.ICustomerInfoService;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dos.ReceiptRecords;
import cn.lili.modules.order.order.entity.dto.ApplyReceiptDto;
import cn.lili.modules.order.order.entity.enums.ApplyInvoicingStatusEnum;
import cn.lili.modules.order.order.entity.enums.ReceiptStatusEnum;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.order.order.service.ReceiptRecordsService;
import cn.lili.modules.order.trade.entity.dos.OrderLog;
import cn.lili.modules.order.trade.service.OrderLogService;
import cn.lili.mybatis.util.PageUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.order.order.entity.dos.Receipt;
import cn.lili.modules.order.order.entity.dto.OrderReceiptDTO;
import cn.lili.modules.order.order.entity.dto.ReceiptSearchParams;
import cn.lili.modules.order.order.mapper.ReceiptMapper;
import cn.lili.modules.order.order.service.ReceiptService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发票业务层实现
 *
 * <AUTHOR>
 * @since 2020/11/17 7:38 下午
 */
@Service
public class ReceiptServiceImpl extends ServiceImpl<ReceiptMapper, Receipt> implements ReceiptService {

    @Autowired
    private ReceiptRecordsService receiptRecordsService;
    @Autowired
    private OrderItemService orderItemService;
    @Autowired
    private OrderLogService orderLogService;
    @Autowired
    private ICustomerInfoService customerInfoService;

    @Override
    public IPage<OrderReceiptDTO> getReceiptData(ReceiptSearchParams searchParams, PageVO pageVO) {
        QueryWrapper<ReceiptSearchParams> wrapper = searchParams.wrapper();
        wrapper.orderByDesc("r.id");
        IPage<OrderReceiptDTO> receipt = this.baseMapper.getReceipt(PageUtil.initPage(pageVO), wrapper);
        List<OrderReceiptDTO> records = receipt.getRecords();
        receiptRecordsService.fullDtoList(records);
        return receipt;
    }

    @Override
    public List<Receipt> getByOrderSn(String orderSn) {
        LambdaQueryWrapper<Receipt> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(Receipt::getOrderSn, orderSn);
        List<Receipt> list = this.list(lambdaQueryWrapper);
        return receiptRecordsService.fullList(list);
    }

    /**
     * 根据子订单编号获取发票信息
     * @param orderItemSn
     * @return
     */
    @Override
    public Receipt getByOrderItemSn(String orderItemSn) {
        Receipt receipt = this.getOne(Wrappers.<Receipt>lambdaQuery()
                .eq(Receipt::getOrderItemSn, orderItemSn)
        );
        if (ObjectUtil.isEmpty(receipt)) {
            return receipt;
        }
        String fileUrl = receiptRecordsService.getFileUrlsByReceiptId(receipt.getId());
        receipt.setFileUrl(fileUrl);
        return receipt;
    }

    @Override
    public Receipt getDetail(String id) {
        Receipt receipt = this.getById(id);
        String fileUrl = receiptRecordsService.getFileUrlsByReceiptId(id);
        receipt.setFileUrl(fileUrl);
        return receipt;
    }

    @Override
    public Receipt saveReceipt(Receipt receipt) {
        LambdaQueryWrapper<Receipt> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Receipt::getReceiptTitle, receipt.getReceiptTitle());
        queryWrapper.eq(Receipt::getMemberId, receipt.getMemberId());
        if (receipt.getId() != null) {
            queryWrapper.ne(Receipt::getId, receipt.getId());
        }
        if (this.getOne(queryWrapper) == null) {
            this.save(receipt);
            return receipt;
        }
        return null;
    }

    @Override
    public Receipt invoicing(String receiptId) {
        //根据id查询发票信息
        Receipt receipt = this.getById(receiptId);
        if (ObjectUtil.isNotEmpty(receipt)) {
            receipt.setReceiptStatus(ReceiptStatusEnum.GENERATE.getCode());
            this.saveOrUpdate(receipt);
            return receipt;
        }
        throw new ServiceException(ResultCode.USER_RECEIPT_NOT_EXIST);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean uploadFileUrl(String id, String fileUrls) {
        if (StringUtils.isEmpty(fileUrls)) {
            throw new ServiceException("文件不存在");
        }
        List<String> stringList = Func.toStrList(fileUrls);
        //根据id查询发票信息，可存储多张发票文件
        Receipt receipt = this.getById(id);
        if (ObjectUtil.isNotEmpty(receipt) && CollectionUtil.isNotEmpty(stringList)) {
            String orderItemSn = receipt.getOrderItemSn();
            OrderItem item = orderItemService.getBySn(orderItemSn);
            item.setApplyInvoicingStatus(ApplyInvoicingStatusEnum.INVOICED.name());
//            receipt.setFileUrl(fileUrls);
            receipt.setReceiptStatus(ReceiptStatusEnum.GENERATE.getCode());
            List<ReceiptRecords> recordsList = new ArrayList<>();
            for (String fileUrl : stringList) {
                recordsList.add(new ReceiptRecords(receipt, fileUrl));
            }
            boolean update = this.saveOrUpdate(receipt);
            boolean recordsSaveBatch = receiptRecordsService.saveBatch(recordsList);
            // 变更子订单状态
            boolean updateItems = orderItemService.updateApplyInvoicingStatus(Arrays.asList(orderItemSn), ApplyInvoicingStatusEnum.APPLIED);
            return update && recordsSaveBatch && updateItems;
        }
        throw new ServiceException(ResultCode.USER_RECEIPT_NOT_EXIST);
    }

    /**
     * 申请开具发票
     * @param dto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean applyReceipt(ApplyReceiptDto dto) {
        // 获取当前登录用户
        AuthUser currentUser = UserContext.getCurrentUser();
        if (currentUser == null) {
            throw new ServiceException(ResultCode.USER_NOT_LOGIN);
        }
        // 保存发票信息
        List<String> orderItemSnList = dto.getOrderItemSnList();
        if (CollectionUtil.isEmpty(orderItemSnList)) {
            throw new ServiceException("请选择需要开具发票的订单项");
        }
        // 根据sn查询子订单
        List<OrderItem> itemList = orderItemService.list(Wrappers.<OrderItem>lambdaQuery()
                .in(OrderItem::getSn, orderItemSnList)
        );
        this.applyReceiptCheckItemList(itemList, dto, currentUser);
        List<Receipt> receipts = new ArrayList<>();
        List<OrderLog> orderLogs = new ArrayList<>();
        for (OrderItem orderItem : itemList) {
            // 创建收据
            Receipt receipt = new Receipt();
            receipt.setReceiptTitle(dto.getReceiptTitle());
            receipt.setTaxpayerId(dto.getTaxpayerId());
            receipt.setReceiptContent(dto.getReceiptContent());
            receipt.setMemberId(currentUser.getId());
            receipt.setMemberName(currentUser.getUsername());
            receipt.setStoreId(orderItem.getStoreId());
            receipt.setStoreName(orderItem.getStoreName());
            receipt.setOrderSn(orderItem.getOrderSn());
            receipt.setReceiptDetail(JSONUtil.toJsonStr(orderItem));
            receipt.setReceiptPrice(orderItem.getChangeFlowPrice().doubleValue());
            receipt.setReceiptStatus(ReceiptStatusEnum.UN_GENERATE.getCode());
            receipt.setOrderItemSn(orderItem.getSn());
            receipt.setReceiptType(dto.getReceiptType());
            receipts.add(receipt);
            orderLogs.add(new OrderLog(orderItem.getSn(), currentUser.getId(),
                    UserContext.getCurrentUser().getRole().getRole(),
                    UserContext.getCurrentUser().getUsername(),
                    "申请开具发票"));
        }

        boolean saveReceipts = this.saveBatch(receipts);
        // 记录发票申请日志
        boolean saveOrderLogs = orderLogService.saveBatch(orderLogs);
        // 变更子订单状态
        boolean updateItems = orderItemService.updateApplyInvoicingStatus(orderItemSnList, ApplyInvoicingStatusEnum.APPLIED);
        return saveReceipts && saveOrderLogs && updateItems;
    }

    private void applyReceiptCheckItemList(List<OrderItem> itemList, ApplyReceiptDto dto, AuthUser currentUser) {
        if (CollectionUtil.isEmpty(itemList)) {
            throw new ServiceException(ResultCode.ORDER_ITEM_NOT_EXIST);
        }
        List<String> orderSnList = CollStreamUtil.toList(itemList, OrderItem::getOrderSn);
        List<Order> orderList = SpringContextUtil.getBean(OrderService.class).list(Wrappers.<Order>lambdaQuery()
                .in(Order::getSn, orderSnList)
        );
        if (CollectionUtil.isEmpty(orderList)) {
            throw new ServiceException(ResultCode.ORDER_NOT_EXIST);
        }
        // 判断传递过来时否为同一个用户的
        List<String> memberIdList = CollStreamUtil.toList(orderList, Order::getMemberId).stream().distinct().collect(Collectors.toList());
        if (memberIdList.size() > 1) {
            throw new ServiceException("存在非同一个买家的数据");
        }
        String memberId = currentUser.getId();
        CustomerInfo customerInfo = customerInfoService.getBuyCustomerId(memberId);
        if (ObjectUtil.isEmpty(customerInfo)) {
            throw new ServiceException("买家企业信息有误");
        }
        if (!customerInfo.getCorpName().equals(dto.getReceiptTitle())) {
            throw new ServiceException("发票抬头有误");
        }
        if (!customerInfo.getBusinessLicenceNumber().equals(dto.getTaxpayerId())) {
            throw new ServiceException("纳税人识别号有误");
        }
    }
}