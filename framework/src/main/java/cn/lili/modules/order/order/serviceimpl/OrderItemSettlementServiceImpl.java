package cn.lili.modules.order.order.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.utils.StringUtils;
import cn.lili.modules.order.order.entity.dos.OrderItemSettlement;
import cn.lili.modules.order.order.entity.dto.OrderItemSettlementParams;
import cn.lili.modules.order.order.entity.enums.SettlementSourcEnum;
import cn.lili.modules.order.order.entity.vo.OrderItemSettlementVo;
import cn.lili.modules.order.order.mapper.OrderItemSettlementMapper;
import cn.lili.modules.order.order.service.OrderItemSettlementService;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月07日16:31
 */
@Service
public class OrderItemSettlementServiceImpl extends ServiceImpl<OrderItemSettlementMapper, OrderItemSettlement> implements OrderItemSettlementService {

    @Override
    public IPage<OrderItemSettlementVo> queryByParams(OrderItemSettlementParams params) {
        IPage<OrderItemSettlementVo> page = this.baseMapper.queryByParams(PageUtil.initPage(params), params.queryWrapper());
        return page;
    }

    @Override
    public List<OrderItemSettlementVo> queryListByParams(OrderItemSettlementParams params) {
        List<OrderItemSettlementVo> voList = this.baseMapper.queryListByParams(params.queryWrapper());
        return voList;
    }

    @Override
    public OrderItemSettlement getByOrderSnOrItemSn(String orderSn, String orderItemSn) {
        String settlementSourc = "";
        if (StringUtils.isNotEmpty(orderSn)) {
            settlementSourc = SettlementSourcEnum.ORDER.name();
        }
        if (StringUtils.isNotEmpty(orderItemSn)) {
            settlementSourc = SettlementSourcEnum.ORDER_ITEM.name();
        }
        return this.getOne(Wrappers.<OrderItemSettlement>lambdaQuery()
                .eq(StringUtils.isNotEmpty(settlementSourc), OrderItemSettlement::getSettlementSourc, settlementSourc)
                .eq(StringUtils.isNotEmpty(orderSn), OrderItemSettlement::getOrderSn, orderSn)
                .eq(StringUtils.isNotEmpty(orderItemSn), OrderItemSettlement::getOrderItemSn, orderItemSn)
                .eq(OrderItemSettlement::getDeleteFlag, Boolean.FALSE)
        );
    }

}
