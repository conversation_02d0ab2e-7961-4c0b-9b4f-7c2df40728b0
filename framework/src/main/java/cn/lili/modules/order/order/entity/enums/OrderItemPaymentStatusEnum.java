package cn.lili.modules.order.order.entity.enums;

/**
 * 子订单支付状态
 * <AUTHOR>
 * @date 2025年05月13日15:55
 */
public enum OrderItemPaymentStatusEnum {

    FIRST_PAYMENT_UN_CONFIRM("已上传一次凭证"),
    FIRST_PAYMENT_CONFIRM("已确认一次凭证"),
    FIRST_PAYMENT_REFUSE("已拒绝一次凭证"),
    BALANCE_PAYMENT_UN_CONFIRM("已上传尾款凭证"),
    BALANCE_PAYMENT_CONFIRM("已确认尾款凭证"),
    BALANCE_PAYMENT_REFUSE("已拒绝尾款凭证"),
    ;

    private final String desc;

    OrderItemPaymentStatusEnum(String desc) {
        this.desc = desc;
    }
}
