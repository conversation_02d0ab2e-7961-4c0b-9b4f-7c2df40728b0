package cn.lili.modules.order.order.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.lili.modules.jrzh_bases.BaseEntityWrapper;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.vo.OrderItemVO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年06月07日15:49
 */
public class OrderItemWrapper extends BaseEntityWrapper<OrderItem, OrderItemVO> {

    public static OrderItemWrapper build() {
        return new OrderItemWrapper();
    }

    @Override
    public OrderItemVO entityVO(OrderItem entity) {
        OrderItemVO vo = Objects.requireNonNull(BeanUtil.copyProperties(entity, OrderItemVO.class));
        return vo;
    }
}
