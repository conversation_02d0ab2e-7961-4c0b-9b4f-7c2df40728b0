package cn.lili.modules.order.order.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.UserEnums;
import cn.lili.common.utils.StringUtils;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.entity.enums.SettlementModelEnum;
import cn.lili.modules.order.order.entity.enums.ApplyInvoicingStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderItemPaymentStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.Data;

/**
 * 待开票子订单查询条件
 * <AUTHOR>
 * @date 2025年06月21日11:18
 */
@Data
public class PendingInvoicingPageParams extends PageVO {

    private String orderSn;

    private String orderItemSn;

    private String storeName;

    private String storeId;

    private String memberId;

    public <T> QueryWrapper<T> queryWrapper() {
        AuthUser currentUser = UserContext.getCurrentUser();
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        wrapper.eq("oi.apply_invoicing_status", ApplyInvoicingStatusEnum.NOT_APPLIED.name());
        wrapper.eq("oi.delete_flag", false);
        wrapper.and( w -> w.nested(i ->
                i.eq("oi.settlement_model", SettlementModelEnum.DEPOSIT_SEND_OUT_GOODS.name())
                .eq("oi.item_payment_status", OrderItemPaymentStatusEnum.BALANCE_PAYMENT_CONFIRM.name())
        ).or(i ->
                i.eq("oi.settlement_model", SettlementModelEnum.FULL_PAYMENT.name())
                .eq("oi.item_payment_status", OrderItemPaymentStatusEnum.FIRST_PAYMENT_CONFIRM.name())
        ));
        wrapper.eq(StringUtils.isNotEmpty(orderSn), "o.sn", orderSn);
        wrapper.eq(StringUtils.isNotEmpty(orderItemSn), "oi.sn", orderItemSn);
        wrapper.like(StringUtils.isNotEmpty(storeName), "oi.store_name", storeName);
        // 根据登录用户添加默认条件
        if (ObjectUtil.isNotEmpty(currentUser)) {
            //店铺查询
            wrapper.eq(CharSequenceUtil.equals(currentUser.getRole().name(), UserEnums.STORE.name())
                    && CharSequenceUtil.isEmpty(storeId), "oi.store_id", currentUser.getStoreId());

            //按买家查询
            wrapper.eq(CharSequenceUtil.equals(currentUser.getRole().name(), UserEnums.MEMBER.name())
                    && CharSequenceUtil.isEmpty(memberId), "o.member_id", currentUser.getId());
        }
        wrapper.orderByDesc("oi.id");
        return wrapper;
    }

}
