package cn.lili.modules.order.order.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.lili.modules.jrzh_bases.BaseEntityWrapper;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.vo.OrderConfirmedVO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年05月20日11:17
 */
public class OrderConfirmedVOWrapper extends BaseEntityWrapper<OrderItem, OrderConfirmedVO> {

    public static OrderConfirmedVOWrapper build() {
        return new OrderConfirmedVOWrapper();
    }

    @Override
    public OrderConfirmedVO entityVO(OrderItem entity) {
        OrderConfirmedVO vo = Objects.requireNonNull(BeanUtil.copyProperties(entity, OrderConfirmedVO.class));
        vo.setGoodsNum(entity.getNum());
        // 移除商品名称后面的规格名称
        String goodsName = entity.getGoodsName();
        String simpleSpecs = entity.getSimpleSpecs();
        if (goodsName != null && simpleSpecs != null && goodsName.endsWith(simpleSpecs)) {
            goodsName = goodsName.substring(0, goodsName.length() - simpleSpecs.length());
        }
        vo.setGoodsName(goodsName);
        
        return vo;
    }
}
