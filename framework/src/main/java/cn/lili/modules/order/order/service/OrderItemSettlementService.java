package cn.lili.modules.order.order.service;

import cn.lili.modules.order.order.entity.dos.OrderItemSettlement;
import cn.lili.modules.order.order.entity.dto.OrderItemSettlementParams;
import cn.lili.modules.order.order.entity.vo.OrderItemSettlementVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年05月07日16:30
 */
public interface OrderItemSettlementService extends IService<OrderItemSettlement> {

    IPage<OrderItemSettlementVo> queryByParams(OrderItemSettlementParams params);

    List<OrderItemSettlementVo> queryListByParams(OrderItemSettlementParams params);

    /**
     * 根据主订单sn或子订单sn查询
     * @param orderSn
     * @param orderItemSn
     * @return
     */
    OrderItemSettlement getByOrderSnOrItemSn(String orderSn, String orderItemSn);
}
