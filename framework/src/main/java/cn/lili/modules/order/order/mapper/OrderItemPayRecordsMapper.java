package cn.lili.modules.order.order.mapper;

import cn.lili.modules.order.order.entity.dos.OrderItemPayRecords;
import cn.lili.modules.order.order.entity.vo.OrderItemPayRecordsVo;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <AUTHOR>
 * @date 2025年06月05日16:49
 */
public interface OrderItemPayRecordsMapper extends BaseMapper<OrderItemPayRecords> {


    /**
     * 分页
     * @param initPage
     * @param queryWrapper
     * @return
     */
    @Select("select oipr.*, oi.order_sn as orderSn, oi.store_name as storeName, oi.goods_name as goodsName, " +
            "ci.corp_name as corpName, oipr.paid_amount as paidAmount, oi.simple_specs as simple_pecs, oi.price_detail as priceDetail, " +
            "oi.change_flow_price as changeFlowPrice " +
            "from li_order_item_pay_records oipr " +
            "left join li_order_item oi on oipr.order_item_sn = oi.sn left join li_customer_info ci on oipr.member_id = ci.customer_id " +
            "${ew.customSqlSegment} ")
    IPage<OrderItemPayRecordsVo> pageByParams(Page<OrderItemPayRecordsVo> initPage, @Param(Constants.WRAPPER) QueryWrapper<OrderItemPayRecordsVo> queryWrapper);

    @Select("select oipr.*, oi.order_sn as orderSn, oi.store_name as storeName, oi.goods_name as goodsName, " +
            "ci.corp_name as corpName, oipr.paid_amount as paidAmount, oi.simple_specs as simple_pecs, oi.price_detail as priceDetail, " +
            "oi.change_flow_price as changeFlowPrice " +
            "from li_order_item_pay_records oipr " +
            "left join li_order_item oi on oipr.order_item_sn = oi.sn left join li_customer_info ci on oipr.member_id = ci.customer_id " +
            "${ew.customSqlSegment} ")
    OrderItemPayRecordsVo oneByParams(@Param(Constants.WRAPPER) QueryWrapper<OrderItemPayRecordsVo> queryWrapper);


}
