package cn.lili.modules.order.order.entity.vo;


import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dos.OrderItemPayRecords;
import cn.lili.modules.order.order.entity.dos.Receipt;
import cn.lili.modules.order.order.entity.enums.DeliverStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderStatusEnum;
import cn.lili.modules.order.order.entity.enums.PayRecordsPaymentStatusEnum;
import cn.lili.modules.order.order.entity.enums.PayStatusEnum;
import cn.lili.modules.payment.entity.enums.PaymentMethodEnum;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.trade.entity.dos.OrderLog;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单详情VO
 *
 * <AUTHOR>
 * @since 2020/11/17 7:29 下午
 */
@Data
@NoArgsConstructor
public class OrderDetailVO implements Serializable {


    private static final long serialVersionUID = -6293102172184734928L;

    /**
     * 订单
     */
    private Order order;

    /**
     * 子订单信息
     */
    private List<OrderItem> orderItems;

    /**
     * 订单状态
     */
    private String orderStatusValue;

    /**
     * 付款状态
     */
    private String payStatusValue;

    /**
     * 物流状态
     */
    private String deliverStatusValue;

    /**
     * 物流类型
     */
    private String deliveryMethodValue;

    /**
     * 支付类型
     */
    private String paymentMethodValue;

    /**
     * 发票
     */
    private List<Receipt> receipts;

    /**
     * 获取订单日志
     */
    private List<OrderLog> orderLogs;
    @ApiModelProperty(value = "价格详情")
    private String priceDetail;

    /**
     * 实付金额
     */
    private BigDecimal paidAmount;

    public OrderDetailVO(Order order, List<OrderItem> orderItems, List<OrderLog> orderLogs, List<Receipt> receipts, List<OrderItemPayRecords> recordsList) {
        this.order = order;
        this.orderItems = orderItems;
        this.orderLogs = orderLogs;
        this.receipts =  receipts;
        BigDecimal paidAmount =  BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(orderItems)) {
            for (OrderItem orderItem : orderItems) {
                if (ObjectUtil.isNotEmpty(orderItem.getPaidAmount())) {
                    paidAmount = paidAmount.add(orderItem.getPaidAmount());
                }
            }
        }
        this.paidAmount =  paidAmount;
        this.fullOrderItems(orderItems, recordsList);
    }

    private void fullOrderItems(List<OrderItem> orderItems, List<OrderItemPayRecords> recordsList) {
        if (CollectionUtil.isNotEmpty(orderItems) && CollectionUtil.isNotEmpty(recordsList)) {
            Map<String, List<OrderItemPayRecords>> itemSnMap = CollStreamUtil.groupBy(recordsList, OrderItemPayRecords::getOrderItemSn, Collectors.toList());
            for (OrderItem orderItem : orderItems) {
                if (itemSnMap.containsKey(orderItem.getSn())) {
                    List<OrderItemPayRecords> list = itemSnMap.get(orderItem.getSn());
                    List<OrderItemPayRecords> paymentUnConfirmList = new ArrayList<>();
                    List<OrderItemPayRecords> paymentConfirmList = new ArrayList<>();
                    for (OrderItemPayRecords orderItemPayRecords : list) {
                        if (PayRecordsPaymentStatusEnum.PAYMENT_UN_CONFIRM.name().equals(orderItemPayRecords.getPaymentStatus())) {
                            paymentUnConfirmList.add(orderItemPayRecords);
                        }
                        if (PayRecordsPaymentStatusEnum.PAYMENT_CONFIRM.name().equals(orderItemPayRecords.getPaymentStatus())) {
                            paymentConfirmList.add(orderItemPayRecords);
                        }
                    }
                    orderItem.setPaymentUnConfirmList(paymentUnConfirmList);
                    orderItem.setPaymentConfirmList(paymentConfirmList);
                }
            }
        }
    }

    /**
     * 可操作类型
     */
    public AllowOperation getAllowOperationVO() {
        return new AllowOperation(this.order);
    }

    public String getOrderStatusValue() {
        try {
            return OrderStatusEnum.valueOf(order.getOrderStatus()).description();
        } catch (Exception e) {
            return "";
        }
    }

    public String getPayStatusValue() {
        try {
            return PayStatusEnum.valueOf(order.getPayStatus()).description();
        } catch (Exception e) {
            return "";
        }

    }

    public String getDeliverStatusValue() {
        try {
            return DeliverStatusEnum.valueOf(order.getDeliverStatus()).getDescription();
        } catch (Exception e) {
            return "";
        }
    }

    public String getDeliveryMethodValue() {
        try {
            return DeliveryMethodEnum.valueOf(order.getDeliveryMethod()).getDescription();
        } catch (Exception e) {
            return "";
        }
    }

    public String getPaymentMethodValue() {
        try {
            return PaymentMethodEnum.valueOf(order.getPaymentMethod()).paymentName();
        } catch (Exception e) {
            return "";
        }
    }
}