package cn.lili.modules.order.order.entity.dos;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.PromotionTypeEnum;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.SnowFlake;
import cn.lili.modules.goods.entity.enums.SettlementModelEnum;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.vo.CartSkuVO;
import cn.lili.modules.order.cart.entity.vo.CartVO;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.enums.*;
import cn.lili.modules.promotion.entity.vos.PromotionSkuVO;
import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 子订单
 *
 * <AUTHOR>
 * @since 2020/11/17 7:30 下午
 */
@Data
@TableName("li_order_item")
@ApiModel(value = "子订单")
@NoArgsConstructor
@AllArgsConstructor
public class OrderItem extends BaseEntity {

    private static final long serialVersionUID = 2108971190191410182L;

    @ApiModelProperty(value = "订单编号")
    private String orderSn;

    @ApiModelProperty(value = "子订单编号")
    private String sn;

    @ApiModelProperty(value = "单价")
    private Double unitPrice;

    @ApiModelProperty(value = "小记")
    private Double subTotal;

    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "货品ID")
    private String skuId;

    @ApiModelProperty(value = "销售量")
    private Integer num;

    @ApiModelProperty(value = "交易编号")
    private String tradeSn;

    @ApiModelProperty(value = "图片")
    private String image;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "分类ID")
    private String categoryId;

    @ApiModelProperty(value = "快照id")
    private String snapshotId;

    @ApiModelProperty(value = "规格json")
    private String specs;

    @ApiModelProperty(value = "促销类型")
    private String promotionType;

    @ApiModelProperty(value = "促销id")
    private String promotionId;

    @ApiModelProperty(value = "销售金额")
    private Double goodsPrice;

    @ApiModelProperty(value = "实际金额")
    private Double flowPrice;

    /**
     * @see CommentStatusEnum
     */
    @ApiModelProperty(value = "评论状态:未评论(UNFINISHED),待追评(WAIT_CHASE),评论完成(FINISHED)，")
    private String commentStatus;

    /**
     * @see OrderItemAfterSaleStatusEnum
     */
    @ApiModelProperty(value = "售后状态")
    private String afterSaleStatus;

    @ApiModelProperty(value = "价格详情")
    private String priceDetail;

    /**
     * @see OrderComplaintStatusEnum
     */
    @ApiModelProperty(value = "投诉状态")
    private String complainStatus;

    @ApiModelProperty(value = "交易投诉id")
    private String complainId;

    @ApiModelProperty(value = "退货商品数量")
    private Integer returnGoodsNumber;

    /**
     * @see cn.lili.modules.order.order.entity.enums.RefundStatusEnum
     */
    @ApiModelProperty(value = "退款状态")
    private String isRefund;

    @ApiModelProperty(value = "退款金额")
    private Double refundPrice;

    @ApiModelProperty(value = "已发货数量")
    private Integer deliverNumber;

    /**
     * 规格名称
     */
    @ApiModelProperty(value = "规格名称")
    private String simpleSpecs;

    @ApiModelProperty(value = "店铺ID")
    private String storeId;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    /**
     * 交货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryDate;

    /**
     * 配送方式
     * 1-买方自提 2-供方承运
     * @see DeliverStatusEnum
     */
    private String deliveryModel;
    /**
     * 结算方式
     * 1-定金发货 2-全额付款 3-分期付款
     * @see cn.lili.modules.goods.entity.enums.SettlementModelEnum
     */
    private String settlementModel;

    /**
     * 调整后的收货数
     */
    private Integer changeDeliverNumber;
    /**
     * 调整后实际总金额
     */
    private BigDecimal changeFlowPrice;
    /**
     * 调整描述
     */
    private String changeDesc;
    /**
     * 调整附件
     */
    private String changeUrl;

    /**
     * 调整收货数后状态
     * 待确认 上架已确认
     * @see OrderItemChangeConfirmStatus
     */
    private String changeConfirmStatus;

    /**
     * 定金比例 如20% 值为20
     */
    private BigDecimal earnestMoneyRatio;

    /**
     * 一次凭证url
     * 如果是定金即为定金凭证url, 如果是全款即为全款支付凭证
     */
    private String firstPaymentVoucherUrl;

    /**
     * 尾款凭证url
     */
    private String balancePaymentVoucherUrl;

    /**
     * 子订单支付状态
     * 已上传一次凭证 已确认一次凭证 已拒绝一次凭证 已上传尾款凭证 已确认尾款凭证 已拒绝尾款凭证
     * @see OrderItemPaymentStatusEnum
     */
    private String itemPaymentStatus;

    /**
     * 已确认支付金额
     */
    private BigDecimal paidAmount;

    /**
     * 支付方式
     */
    private String itemPaymentMethod;

    /**
     * 申请开票状态
     * @see ApplyInvoicingStatusEnum
     */
    private String applyInvoicingStatus;

    @TableField(exist = false)
    public List<OrderItemPayRecords> paymentUnConfirmList;
    @TableField(exist = false)
    public List<OrderItemPayRecords> paymentConfirmList;

    public Integer getDeliverNumber() {
        if(deliverNumber == null){
            return 0;
        }
        return deliverNumber;
    }

    public OrderItem(CartSkuVO cartSkuVO, CartVO cartVO, TradeDTO tradeDTO) {
        String oldId = this.getId();
        BeanUtil.copyProperties(cartSkuVO.getGoodsSku(), this);
        BeanUtil.copyProperties(cartSkuVO.getPriceDetailDTO(), this);
        BeanUtil.copyProperties(cartSkuVO, this);
        this.setId(oldId);
        if (cartSkuVO.getPriceDetailDTO().getJoinPromotion() != null && !cartSkuVO.getPriceDetailDTO().getJoinPromotion().isEmpty()) {
            this.setPromotionType(CollUtil.join(cartSkuVO.getPriceDetailDTO().getJoinPromotion().stream().map(PromotionSkuVO::getPromotionType).collect(Collectors.toList()), ","));
            this.setPromotionId(CollUtil.join(cartSkuVO.getPriceDetailDTO().getJoinPromotion().stream().map(PromotionSkuVO::getActivityId).collect(Collectors.toList()), ","));
        }
        this.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.NEW.name());
        this.setCommentStatus(CommentStatusEnum.NEW.name());
        this.setComplainStatus(OrderComplaintStatusEnum.NEW.name());
        this.setPriceDetailDTO(cartSkuVO.getPriceDetailDTO());
        this.setOrderSn(cartVO.getSn());
        this.setTradeSn(tradeDTO.getSn());
        this.setImage(cartSkuVO.getGoodsSku().getThumbnail());
        this.setGoodsName(cartSkuVO.getGoodsSku().getGoodsName());
        this.setSkuId(cartSkuVO.getGoodsSku().getId());
        this.setCategoryId(cartSkuVO.getGoodsSku().getCategoryPath().substring(
                cartSkuVO.getGoodsSku().getCategoryPath().lastIndexOf(",") + 1
        ));
        this.setGoodsPrice(cartSkuVO.getGoodsSku().getPrice());
        this.setUnitPrice(cartSkuVO.getPurchasePrice());
        this.setSubTotal(cartSkuVO.getSubTotal());
        this.setSn(SnowFlake.createStr("OI"));

        this.setChangeDeliverNumber(this.getNum());
        PriceDetailDTO priceDetailDTO = this.getPriceDetailDTO();
        Double freightPrice = ObjectUtil.isEmpty(priceDetailDTO) ? 0.0 : ObjectUtil.isEmpty(priceDetailDTO.getFreightPrice()) ? 0.0 : priceDetailDTO.getFreightPrice();
//        Double changeFlowPrice = CurrencyUtil.add(cartSkuVO.getSubTotal(), freightPrice);
//        this.setChangeFlowPrice(BigDecimal.valueOf(changeFlowPrice));
        this.setChangeFlowPrice(BigDecimal.valueOf(cartSkuVO.getSubTotal()));
        this.setSettlementModel(SettlementModelEnum.FULL_PAYMENT.name());
        this.setPaidAmount(BigDecimal.ZERO);
        this.setChangeConfirmStatus(" ");
        this.setItemPaymentMethod(" ");
        this.setApplyInvoicingStatus(ApplyInvoicingStatusEnum.NOT_APPLIED.name());
    }

    public String getIsRefund() {
        if (isRefund == null) {
            return RefundStatusEnum.NO_REFUND.name();
        }
        return isRefund;
    }

    public double getRefundPrice() {
        if (refundPrice == null) {
            return 0;
        }
        return refundPrice;
    }

    public PriceDetailDTO getPriceDetailDTO() {
        return JSONUtil.toBean(priceDetail, PriceDetailDTO.class);
    }

    public void setPriceDetailDTO(PriceDetailDTO priceDetail) {
        this.priceDetail = JSONUtil.toJsonStr(priceDetail);
    }

    public String getAfterSaleStatus() {
        if (!PromotionTypeEnum.isCanAfterSale(this.promotionType)) {
            return OrderItemAfterSaleStatusEnum.EXPIRED.name();
        }
        return afterSaleStatus;
    }
}