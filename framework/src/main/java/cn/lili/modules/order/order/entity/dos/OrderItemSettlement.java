package cn.lili.modules.order.order.entity.dos;

import cn.hutool.json.JSONUtil;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.enums.OrderItemChangeConfirmStatus;
import cn.lili.modules.order.order.entity.enums.SettlementSourcEnum;
import cn.lili.modules.order.order.entity.vo.OrderItemSettlementVo;
import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025年05月07日15:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("li_order_item_settlement")
@ApiModel(value = "订单结算单")
@NoArgsConstructor
public class OrderItemSettlement extends BaseEntity {

    /**
     * 会员id
     */
    private String memberId;
    /**
     * 店铺id
     */
    private String storeId;
    /**
     * 订单sn
     */
    private String orderSn;
    /**
     * 子订单sn
     */
    private String orderItemSn;
    /**
     * 主合同编号
     */
    private String mainContractNo;
    /**
     * 商品名
     */
    private String goodsName;
    /**
     * 规格名称
     */
    private String simpleSpecs;
    /**
     * 主订单数量
     */
    private Integer orderNum;
    /**
     * 商品单价
     */
    private BigDecimal goodsUnitPrice;
    /**
     * 发货数量
     */
    private BigDecimal deliverNumber;
    /**
     * 收货数量
     */
    private BigDecimal takeDeliveryNumber;
    /**
     * 结算数量同收货数量
     */
    private BigDecimal settlementNumber;
    /**
     * 结算单价
     */
    private BigDecimal settlementUnitPrice;
    /**
     * 总价 = 结算单价 * 收货数量
     */
    private BigDecimal totalAmount;
    /**
     * 扣款金额
     */
    private BigDecimal deductionAmount;
    /**
     * 应付金额 = 总价 - 扣款金额 + 其他费用
     */
    private BigDecimal payableAmount;
    /**
     * 结算说明
     */
    private String settlementDesc;
    /**
     * 结算单pdf
     */
    private String pdfUrl;
    /**
     * @see cn.lili.modules.order.order.entity.enums.SettlementSourcEnum
     * 结算单来源
     */
    private String settlementSourc;
    /**
     * 其他费用总计
     */
    private BigDecimal otherAmount;
    /**
     * 价格详情
     */
    private String priceDetail;

    public PriceDetailDTO getPriceDetailDTO() {
        return JSONUtil.toBean(priceDetail, PriceDetailDTO.class);
    }

    public void setPriceDetailDTO(PriceDetailDTO priceDetail) {
        this.priceDetail = JSONUtil.toJsonStr(priceDetail);
    }

    public OrderItemSettlement(Order order, OrderItem orderItem, String mainContractNo) {
        this.setMemberId(order.getMemberId());
        this.setStoreId(order.getStoreId());
        this.setOrderSn(order.getSn());
        this.setOrderItemSn(orderItem.getSn());
        this.setMainContractNo(mainContractNo);
        this.setGoodsName(orderItem.getGoodsName());
        this.setSimpleSpecs(orderItem.getSimpleSpecs());
        this.setOrderNum(orderItem.getNum());
        this.setGoodsUnitPrice(BigDecimal.valueOf(orderItem.getUnitPrice()));
        this.setDeliverNumber(BigDecimal.valueOf(orderItem.getDeliverNumber()));
        this.setTakeDeliveryNumber(BigDecimal.valueOf(orderItem.getChangeDeliverNumber()));
        this.setSettlementNumber(BigDecimal.valueOf(orderItem.getChangeDeliverNumber()));
        this.setPriceDetailDTO(orderItem.getPriceDetailDTO());
        this.setOtherAmount(BigDecimal.valueOf(orderItem.getPriceDetailDTO().getFreightPrice()));
        List<String> unChangeConfirmStatus = Arrays.asList(OrderItemChangeConfirmStatus.UN_CONFIRM.name(), OrderItemChangeConfirmStatus.REFUSE.name());
        BigDecimal payableAmount = orderItem.getChangeFlowPrice();
        if (unChangeConfirmStatus.contains(orderItem.getChangeConfirmStatus())) {
            payableAmount = BigDecimal.valueOf(orderItem.getSubTotal());
        }
        // 计算调整后的收货单价
        BigDecimal settlementUnitPrice = payableAmount.divide(BigDecimal.valueOf(orderItem.getChangeDeliverNumber()), 2, RoundingMode.HALF_UP);
        this.setSettlementUnitPrice(settlementUnitPrice);
        this.setTotalAmount(payableAmount);
        this.setDeductionAmount(BigDecimal.ZERO);
        this.setPayableAmount(this.getTotalAmount().subtract(this.getDeductionAmount()).add(this.getOtherAmount()));
        this.setSettlementDesc(Optional.ofNullable(orderItem.getChangeDesc()).orElse(""));
        this.setSettlementSourc(SettlementSourcEnum.ORDER_ITEM.name());
    }

    public OrderItemSettlement(Order order, List<OrderItem> orderItems, String mainContractNo) {
        this.setMemberId(order.getMemberId());
        this.setStoreId(order.getStoreId());
        this.setOrderSn(order.getSn());
        this.setMainContractNo(mainContractNo);
        this.setGoodsName(order.getGoodsName());
        this.setSimpleSpecs(order.getSimpleSpecs());
        this.setOrderNum(order.getGoodsNum());
        this.setPriceDetailDTO(order.getPriceDetailDTO());
        this.setOtherAmount(BigDecimal.valueOf(order.getPriceDetailDTO().getFreightPrice()));
        // 子订单原总价
        BigDecimal totalGoodsAmount = BigDecimal.ZERO;
        // 发货数量
        BigDecimal deliverNumber = BigDecimal.ZERO;
        // 收货数量
        BigDecimal takeDeliveryNumber = BigDecimal.ZERO;
        // 结算单价
        BigDecimal settlementUnitPrice = BigDecimal.ZERO;
        BigDecimal allChangeFlowPrice = BigDecimal.ZERO;
        String settlementDesc = "";
        List<String> unChangeConfirmStatus = Arrays.asList(OrderItemChangeConfirmStatus.UN_CONFIRM.name(), OrderItemChangeConfirmStatus.REFUSE.name());
        for (OrderItem orderItem : orderItems) {
            BigDecimal payableAmount = orderItem.getChangeFlowPrice();
            if (unChangeConfirmStatus.contains(orderItem.getChangeConfirmStatus())) {
                payableAmount = BigDecimal.valueOf(orderItem.getSubTotal());
            }
            totalGoodsAmount = totalGoodsAmount.add(BigDecimal.valueOf(orderItem.getUnitPrice()).multiply(BigDecimal.valueOf(orderItem.getDeliverNumber())));
            deliverNumber = deliverNumber.add(BigDecimal.valueOf(orderItem.getDeliverNumber()));
            takeDeliveryNumber = takeDeliveryNumber.add(BigDecimal.valueOf(orderItem.getChangeDeliverNumber()));
            allChangeFlowPrice = allChangeFlowPrice.add(payableAmount);
            settlementDesc = settlementDesc + Optional.ofNullable(orderItem.getChangeDesc()).orElse("");
        }
        // 商品单价 = 多个(子订单单价 * 子订单数量)相加 / 总子订单数量
        this.setGoodsUnitPrice(totalGoodsAmount.divide(deliverNumber, 2, RoundingMode.HALF_UP));
        this.setDeliverNumber(deliverNumber);
        this.setTakeDeliveryNumber(takeDeliveryNumber);
        this.setSettlementNumber(takeDeliveryNumber);
        // 计算调整后的收货单价
        settlementUnitPrice = allChangeFlowPrice.divide(takeDeliveryNumber, 2, RoundingMode.HALF_UP);
        this.setSettlementUnitPrice(settlementUnitPrice);
        this.setTotalAmount(allChangeFlowPrice);
        this.setDeductionAmount(BigDecimal.ZERO);
        this.setPayableAmount(this.getTotalAmount().subtract(this.getDeductionAmount()).add(this.getOtherAmount()));
        this.setSettlementDesc(settlementDesc);
        this.setSettlementSourc(SettlementSourcEnum.ORDER.name());
    }

}
