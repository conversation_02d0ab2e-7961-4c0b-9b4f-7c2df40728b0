package cn.lili.modules.order.order.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.lili.modules.jrzh_bases.BaseEntityWrapper;
import cn.lili.modules.order.order.entity.dos.OrderItemSettlement;
import cn.lili.modules.order.order.entity.vo.OrderItemSettlementVo;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年05月07日19:38
 */
public class OrderItemSettlementWrapper extends BaseEntityWrapper<OrderItemSettlement, OrderItemSettlementVo> {

    public static OrderItemSettlementWrapper build() {
        return new OrderItemSettlementWrapper();
    }

    @Override
    public OrderItemSettlementVo entityVO(OrderItemSettlement entity) {
        OrderItemSettlementVo vo = Objects.requireNonNull(BeanUtil.copyProperties(entity, OrderItemSettlementVo.class));
        return vo;
    }
}
