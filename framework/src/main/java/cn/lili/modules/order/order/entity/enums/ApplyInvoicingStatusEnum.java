package cn.lili.modules.order.order.entity.enums;

import lombok.Getter;

/**
 * 申请开票状态枚举
 *
 * <AUTHOR>
 * @since 2023/4/20 16:30
 */
@Getter
public enum ApplyInvoicingStatusEnum {

    /**
     * 未申请
     */
    NOT_APPLIED("未申请"),
    
    /**
     * 已申请
     */
    APPLIED("已申请"),
    
    /**
     * 已开票
     */
    INVOICED("已开票");

    private final String desc;

    ApplyInvoicingStatusEnum(String description) {
        this.desc = description;
    }

}