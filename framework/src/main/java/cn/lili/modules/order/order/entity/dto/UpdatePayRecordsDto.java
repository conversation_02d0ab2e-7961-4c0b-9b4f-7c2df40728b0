package cn.lili.modules.order.order.entity.dto;

import cn.lili.common.validation.EnumValue;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025年06月18日16:11
 */
@Data
public class UpdatePayRecordsDto implements Serializable {

    /**
     * 子订单sn
     */
    @NotEmpty(message = "子订单sn不能为空")
    private String orderItemSn;
    /**
     * 状态
     */
    @NotNull(message = "确认类型不能为空")
    @EnumValue(strValues = {"PAYMENT_REFUSE", "PAYMENT_CONFIRM"})
    private String paymentStatus;
    /**
     * 金额
     */
    @NotNull(message = "确认金额不能为空")
    private BigDecimal amount;
    /**
     * 支付记录id
     */
    @NotEmpty(message = "支付记录id不能为空")
    private String payRecordsId;

}
