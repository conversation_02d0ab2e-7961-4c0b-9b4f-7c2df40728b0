package cn.lili.modules.order.order.entity.enums;

/**
 * 订单状态枚举
 *
 * <AUTHOR>
 * @since 2020/11/17 7:28 下午
 */
public enum PayStatusEnum {

    /**
     * 支付状态
     */
    UNPAID("待付款"),
    PAYING("支付中即已上传支付凭证，卖家暂未确认"),
    IN_PROGRESS("交易中"),
    PAID("已付款"),
    CANCEL("已取消");

    private final String description;

    PayStatusEnum(String description) {
        this.description = description;
    }

    public String description() {
        return this.description;
    }


}
