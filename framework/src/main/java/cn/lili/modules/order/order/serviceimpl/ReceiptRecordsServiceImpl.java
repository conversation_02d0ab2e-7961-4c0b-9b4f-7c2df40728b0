package cn.lili.modules.order.order.serviceimpl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.lili.modules.order.order.entity.dos.Receipt;
import cn.lili.modules.order.order.entity.dos.ReceiptRecords;
import cn.lili.modules.order.order.entity.dto.OrderReceiptDTO;
import cn.lili.modules.order.order.mapper.ReceiptRecordsMapper;
import cn.lili.modules.order.order.service.ReceiptRecordsService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025年06月03日15:49
 */
@Service
@RequiredArgsConstructor
public class ReceiptRecordsServiceImpl extends ServiceImpl<ReceiptRecordsMapper, ReceiptRecords> implements ReceiptRecordsService {

    private final ReceiptRecordsMapper receiptRecordsMapper;

    /**
     * 根据发票id获取文件url
     * @param receiptId
     * @return
     */
    @Override
    public String getFileUrlsByReceiptId(String receiptId) {
        List<ReceiptRecords> list = this.getByReceiptId(receiptId);
        StringBuilder stringBuilder = new StringBuilder();
        for (ReceiptRecords receiptRecords : list) {
            stringBuilder = stringBuilder.append(receiptRecords.getFileUrl()).append(",");
        }
        return stringBuilder.toString();
    }

    /**
     * 根据发票id查询
     * @param receiptId
     * @return
     */
    @Override
    public List<ReceiptRecords> getByReceiptId(String receiptId) {
        List<ReceiptRecords> list = this.list(Wrappers.<ReceiptRecords>lambdaQuery()
                .eq(ReceiptRecords::getReceiptId, receiptId)
                .eq(ReceiptRecords::getDeleteFlag, Boolean.FALSE)
        );
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<ReceiptRecords> getByReceiptIdList(List<String> receiptIdList) {
        List<ReceiptRecords> list = this.list(Wrappers.<ReceiptRecords>lambdaQuery()
                .in(ReceiptRecords::getReceiptId, receiptIdList)
                .eq(ReceiptRecords::getDeleteFlag, Boolean.FALSE)
        );
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list;
    }

    @Override
    public List<Receipt> fullList(List<Receipt> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> idList = CollStreamUtil.toList(list, Receipt::getId);
        List<ReceiptRecords> allRecordsList = this.getByReceiptIdList(idList);
        if (CollectionUtil.isEmpty(allRecordsList)) {
            return list;
        }
        Map<String, List<ReceiptRecords>> receiptIdMap = CollStreamUtil.groupBy(allRecordsList, ReceiptRecords::getReceiptId, Collectors.toList());
        for (Receipt receipt : list) {
            if (receiptIdMap.containsKey(receipt.getId())) {
                List<ReceiptRecords> recordsList = receiptIdMap.get(receipt.getId());
                StringBuilder stringBuilder = new StringBuilder();
                for (ReceiptRecords receiptRecords : recordsList) {
                    stringBuilder = stringBuilder.append(receiptRecords.getFileUrl()).append(",");
                }
                receipt.setFileUrl(stringBuilder.toString());
            }
        }
        return list;
    }

    @Override
    public List<OrderReceiptDTO> fullDtoList(List<OrderReceiptDTO> list) {
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        List<String> idList = CollStreamUtil.toList(list, Receipt::getId);
        List<ReceiptRecords> allRecordsList = this.getByReceiptIdList(idList);
        if (CollectionUtil.isEmpty(allRecordsList)) {
            return list;
        }
        Map<String, List<ReceiptRecords>> receiptIdMap = CollStreamUtil.groupBy(allRecordsList, ReceiptRecords::getReceiptId, Collectors.toList());
        for (OrderReceiptDTO receipt : list) {
            if (receiptIdMap.containsKey(receipt.getId())) {
                List<ReceiptRecords> recordsList = receiptIdMap.get(receipt.getId());
                StringBuilder stringBuilder = new StringBuilder();
                for (ReceiptRecords receiptRecords : recordsList) {
                    stringBuilder = stringBuilder.append(receiptRecords.getFileUrl()).append(",");
                }
                receipt.setFileUrl(stringBuilder.toString());
            }
        }
        return list;
    }

}
