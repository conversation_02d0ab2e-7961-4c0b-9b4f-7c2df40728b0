package cn.lili.modules.order.order.entity.vo;

import cn.lili.modules.order.order.entity.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 子订单VO
 *
 * <AUTHOR>
 * @since 2020-08-17 20:28
 */
@Data
@NoArgsConstructor
public class OrderItemVO {

    @ApiModelProperty(value = "编号")
    private String sn;

    @ApiModelProperty(value = "商品ID")
    private String goodsId;

    @ApiModelProperty(value = "货品ID")
    private String skuId;

    @ApiModelProperty(value = "销售量")
    private String num;

    @ApiModelProperty(value = "图片")
    private String image;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "商品名称")
    private Double goodsPrice;

    /**
     * @see OrderItemAfterSaleStatusEnum
     */
    @ApiModelProperty(value = "售后状态", allowableValues = "NOT_APPLIED(未申请),ALREADY_APPLIED(已申请),EXPIRED(已失效不允许申请售后)")
    private String afterSaleStatus;

    /**
     * @see OrderComplaintStatusEnum
     */
    @ApiModelProperty(value = "投诉状态")
    private String complainStatus;

    /**
     * @see CommentStatusEnum
     */
    @ApiModelProperty(value = "评论状态:未评论(UNFINISHED),待追评(WAIT_CHASE),评论完成(FINISHED)，")
    private String commentStatus;

    /**
     * @see cn.lili.modules.order.order.entity.enums.RefundStatusEnum
     */
    @ApiModelProperty(value = "退款状态")
    private String isRefund;

    @ApiModelProperty(value = "退款金额")
    private String refundPrice;

    @ApiModelProperty(value = "已发货数量")
    private String deliverNumber;

    /**
     * 调整收货数后状态
     * 待确认 上架已确认
     * @see OrderItemChangeConfirmStatus
     */
    private String changeConfirmStatus;

    private String paidAmount;

    private String itemPaymentMethod;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 买方用户名
     */
    private String memberName;
    /**
     * 申请开票状态
     * @see ApplyInvoicingStatusEnum
     */
    private String applyInvoicingStatus;

    /**
     * 商品名
     */
    private String goodsName;
    /**
     * 买家企业名称
     */
    private String corpName;
    /**
     * 买家工商注册号
     */
    private String businessLicenceNumber;
    /**
     * 主订单sn
     */
    private String orderSn;
    /**
     * 买方企业名称
     */
    private String buyerCompanyName;
}
