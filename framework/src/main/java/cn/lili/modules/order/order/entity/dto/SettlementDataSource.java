package cn.lili.modules.order.order.entity.dto;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.utils.ThousandSeparatorUtil;
import cn.lili.modules.order.order.entity.vo.OrderItemSettlementVo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

/**
 * 结算单填充数据
 * <AUTHOR>
 * @date 2025年05月08日14:29
 */
@Data
public class SettlementDataSource implements Serializable {

    /**
     * 买方
     */
    private String memberName;
    /**
     * 卖方
     */
    private String storeName;
    /**
     * 主合同编号
     */
    private String mainContractNo;
    /**
     * 信息
     */
    private List<OrderItemSettlementVo> settlements;
    /**
     * 买方应付货款总价
     */
    private String buyerPayableAmount;
    private String buyerPayableAmountFormat;
    /**
     * 买家已付货款
     */
    private String paidAmount;
    private String paidAmountFormat;

    /**
     * 买方应补货款
     */
    private String buyerRestockingPayment;
    private String buyerRestockingPaymentFormat;

    /**
     * 其他金额
     */
    private String otherAmount;
    private String otherAmountFormat;

    public String getPaidAmountFormat() {
        if (ObjectUtil.isEmpty(paidAmount)) {
            return "￥0.00";
        }
        return "￥" + ThousandSeparatorUtil.format(new BigDecimal(paidAmount));
    }

    public String getOtherAmount() {
        if (CollectionUtil.isEmpty(settlements)) {
            return "0.00";
        }
        BigDecimal otherAmount = settlements.stream()
                .filter(item -> item != null && item.getOtherAmount() != null)
                .map(OrderItemSettlementVo::getOtherAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return otherAmount.setScale(2, RoundingMode.UP).toPlainString();
    }

    /**
     * 其他金额
     * @return
     */
    public String getOtherAmountFormat() {
        return "￥" + ThousandSeparatorUtil.format(new BigDecimal(getOtherAmount()));
    }

    public String getBuyerPayableAmount() {
        if (CollectionUtil.isEmpty(settlements)) {
            return "0.00";
        }
        BigDecimal totalPrice = settlements.stream()
                .filter(item -> item != null && item.getPayableAmount() != null)
                .map(OrderItemSettlementVo::getPayableAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return totalPrice.setScale(2, RoundingMode.UP).toPlainString();
    }

    public String getBuyerPayableAmountFormat() {
        return "￥" + ThousandSeparatorUtil.format(new BigDecimal(getBuyerPayableAmount()));
    }

    public String getBuyerRestockingPayment() {
        if (CollectionUtil.isEmpty(settlements)) {
            return "0.00";
        }
        BigDecimal totalPrice = settlements.stream()
                .filter(item -> item != null && item.getPayableAmount() != null)
                .map(OrderItemSettlementVo::getPayableAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        totalPrice = totalPrice.subtract(new BigDecimal(paidAmount));
        return totalPrice.setScale(2, RoundingMode.UP).toPlainString();
    }

    public String getBuyerRestockingPaymentFormat() {
        return "￥" + ThousandSeparatorUtil.format(new BigDecimal(getBuyerRestockingPayment()));
    }

    private String date;

    /**
     * 结算说明
     */
    private String settlementDesc;

}
