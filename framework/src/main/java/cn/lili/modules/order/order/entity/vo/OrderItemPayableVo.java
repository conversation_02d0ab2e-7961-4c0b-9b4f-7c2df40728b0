package cn.lili.modules.order.order.entity.vo;

import cn.lili.modules.order.order.entity.enums.OrderItemPaymentStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 子订单应付金额相关数据
 * <AUTHOR>
 * @date 2025年05月24日11:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderItemPayableVo implements Serializable {

    /**
     * 本次应付金额
     */
    private BigDecimal payableAmount;

    /**
     * 结算方式
     * 1-定金发货 2-全额付款 3-分期付款
     * @see cn.lili.modules.goods.entity.enums.SettlementModelEnum
     */
    private String settlementModel;

    /**
     * 子订单支付状态
     * 已上传一次凭证 已确认一次凭证 已拒绝一次凭证 已上传尾款凭证 已确认尾款凭证 已拒绝尾款凭证
     * @see OrderItemPaymentStatusEnum
     */
    private String itemPaymentStatus;

    /**
     * 已付金额
     */
    private BigDecimal paidAmount;

    /**
     * 订单金额
     */
    private BigDecimal orderItemAmount;

}
