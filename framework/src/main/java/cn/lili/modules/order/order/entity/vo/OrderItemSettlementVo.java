package cn.lili.modules.order.order.entity.vo;

import cn.lili.common.utils.ThousandSeparatorUtil;
import cn.lili.modules.order.order.entity.dos.OrderItemSettlement;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025年05月07日19:21
 */
@Data
public class OrderItemSettlementVo extends OrderItemSettlement {

    /**
     * 店铺名
     */
    private String storeName;
    /**
     * 卖家公司名
     */
    private String companyName;
    /**
     * 卖家公司名
     */
    private String corpName;
    /**
     * 商品单价 千分位
     */
    private String goodsUnitPriceFormat;
    public String getGoodsUnitPriceFormat() {
        return "￥" + ThousandSeparatorUtil.format(getGoodsUnitPrice());
    }

    /**
     * 结算单价 千分位
     */
    private String settlementUnitPriceFormat;
    public String getSettlementUnitPriceFormat() {
        return "￥" + ThousandSeparatorUtil.format(getSettlementUnitPrice());
    }

    /**
     * 总价 = 结算单价 * 收货数量  千分位
     */
    private String totalAmountFormat;
    public String getTotalAmountFormat() {
        return "￥" + ThousandSeparatorUtil.format(getTotalAmount());
    }

}
