package cn.lili.modules.order.order.entity.vo;

import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.ClientTypeEnum;
import cn.lili.common.security.sensitive.Sensitive;
import cn.lili.common.security.sensitive.enums.SensitiveStrategy;
import cn.lili.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum;
import cn.lili.modules.order.order.entity.enums.OrderPromotionTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单简略信息
 * 用于订单列表查看
 *
 * <AUTHOR>
 * @since 2020-08-17 20:28
 */
@Data
public class OrderSimpleVO {

    @ApiModelProperty("买家签署状态")
    private Integer buyerSignStatus;

    @ApiModelProperty("卖家签署状态")
    private Integer sellerSignStatus;

    @ApiModelProperty("买方企业名称")
    private String buyerCompanyName;

    @ApiModelProperty("sn")
    private String sn;

    @ApiModelProperty(value = "总价格")
    private Double flowPrice;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * @see cn.lili.modules.order.order.entity.enums.OrderStatusEnum
     */
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    /**
     * @see cn.lili.modules.order.order.entity.enums.PayStatusEnum
     */
    @ApiModelProperty(value = "付款状态")
    private String payStatus;

    @ApiModelProperty(value = "支付方式")
    private String paymentMethod;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    @ApiModelProperty(value = "用户名")
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String memberName;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺ID")
    private String storeId;

    /**
     * @see ClientTypeEnum
     */
    @ApiModelProperty(value = "订单来源")
    private String clientType;

    /**
     * 子订单信息
     */
    private List<OrderItemVO> orderItems;

    @ApiModelProperty(hidden = true, value = "item goods_id")
    @Setter
    private String groupGoodsId;

    @ApiModelProperty(hidden = true, value = "item sku id")
    @Setter
    private String groupSkuId;

    @ApiModelProperty(hidden = true, value = "item 数量")
    @Setter
    private String groupNum;

    @ApiModelProperty(hidden = true, value = "item 图片")
    @Setter
    private String groupImages;

    @ApiModelProperty(hidden = true, value = "item 名字")
    @Setter
    private String groupName;

    @ApiModelProperty(hidden = true, value = "item 编号")
    @Setter
    private String groupOrderItemsSn;

    @ApiModelProperty(hidden = true, value = "item 商品价格")
    @Setter
    private String groupGoodsPrice;
    /**
     * @see cn.lili.modules.order.order.entity.enums.OrderItemAfterSaleStatusEnum
     */
    @ApiModelProperty(hidden = true, value = "item 售后状态", allowableValues = "NOT_APPLIED(未申请),ALREADY_APPLIED(已申请),EXPIRED(已失效不允许申请售后)")
    @Setter
    private String groupAfterSaleStatus;

    /**
     * @see cn.lili.modules.order.order.entity.enums.OrderComplaintStatusEnum
     */
    @ApiModelProperty(hidden = true, value = "item 投诉状态")
    @Setter
    private String groupComplainStatus;

    /**
     * @see cn.lili.modules.order.order.entity.enums.CommentStatusEnum
     */
    @ApiModelProperty(hidden = true, value = "item 评价状态")
    @Setter
    private String groupCommentStatus;


    /**
     * @see cn.lili.modules.order.order.entity.enums.OrderTypeEnum
     */
    @ApiModelProperty(value = "订单类型")
    private String orderType;

    /**
     * @see cn.lili.modules.order.order.entity.enums.DeliverStatusEnum
     */
    @ApiModelProperty(value = "货运状态")
    private String deliverStatus;

    /**
     * @see cn.lili.modules.order.order.entity.enums.OrderPromotionTypeEnum
     */
    @ApiModelProperty(value = "订单促销类型")
    private String orderPromotionType;

    @ApiModelProperty(value = "是否退款")
    private String groupIsRefund;

    @ApiModelProperty(value = "退款金额")
    private String groupRefundPrice;

    @ApiModelProperty(value = "已发货数量")
    private String groupDeliverNumber;

    @ApiModelProperty(value = "调整收货数后状态")
    private String groupChangeConfirmStatus;

    @ApiModelProperty(value = "已确认支付金额")
    private String groupPaidAmount;

    @ApiModelProperty(value = "子订单支付方式")
    private String groupItemPaymentMethod;

    @ApiModelProperty(value = "买家企业名称")
    private String groupCorpName;

    @ApiModelProperty(value = "卖家订单备注")
    private String sellerRemark;

    /**
     * 支付凭证url
     */
    private String paymentVoucherUrl;
    /**
     * 结算方式
     * 1-定金发货 2-全额付款 3-分期付款
     * @see cn.lili.modules.goods.entity.enums.SettlementModelEnum
     */
    private String settlementModel;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractId;

    @ApiModelProperty(value = "卖方id")
    private Long sellerId;

    @ApiModelProperty(value ="买方id")
    private Long buyerId;

    public List<OrderItemVO> getOrderItems() {
        if (CharSequenceUtil.isEmpty(groupGoodsId)) {
            return new ArrayList<>();
        }
        List<OrderItemVO> orderItemVOS = new ArrayList<>();


        String[] goodsId = groupGoodsId.split(",");

        for (int i = 0; i < goodsId.length; i++) {
            orderItemVOS.add(this.getOrderItemVO(i));
        }
        return orderItemVOS;

    }

    private OrderItemVO getOrderItemVO(int i) {
        OrderItemVO orderItemVO = new OrderItemVO();
        orderItemVO.setGoodsId(groupGoodsId.split(",")[i]);
        if (CharSequenceUtil.isNotEmpty(groupOrderItemsSn)) {
            orderItemVO.setSn(groupOrderItemsSn.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupSkuId)) {
            orderItemVO.setSkuId(groupSkuId.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupName)) {
            orderItemVO.setName(groupName.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupNum) && groupNum.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setNum(groupNum.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupImages) && groupImages.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setImage(groupImages.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupAfterSaleStatus) && groupAfterSaleStatus.split(",").length == groupGoodsId.split(",").length) {
            if (!OrderPromotionTypeEnum.isCanAfterSale(this.orderPromotionType)) {
                orderItemVO.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.EXPIRED.name());
            } else {
                orderItemVO.setAfterSaleStatus(groupAfterSaleStatus.split(",")[i]);
            }
        }
        if (CharSequenceUtil.isNotEmpty(groupComplainStatus) && groupComplainStatus.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setComplainStatus(groupComplainStatus.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupCommentStatus) && groupCommentStatus.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setCommentStatus(groupCommentStatus.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupGoodsPrice) && groupGoodsPrice.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setGoodsPrice(Double.parseDouble(groupGoodsPrice.split(",")[i]));
        }
        if (CharSequenceUtil.isNotEmpty(groupIsRefund) && groupIsRefund.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setIsRefund(groupIsRefund.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupRefundPrice) && groupRefundPrice.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setRefundPrice(groupRefundPrice.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupDeliverNumber) && groupDeliverNumber.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setDeliverNumber(groupDeliverNumber.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupChangeConfirmStatus) && groupChangeConfirmStatus.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setChangeConfirmStatus(groupChangeConfirmStatus.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupPaidAmount) && groupPaidAmount.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setPaidAmount(groupPaidAmount.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupItemPaymentMethod) && groupItemPaymentMethod.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setItemPaymentMethod(groupItemPaymentMethod.split(",")[i]);
        }
        if (CharSequenceUtil.isNotEmpty(groupCorpName) && groupCorpName.split(",").length == groupGoodsId.split(",").length) {
            orderItemVO.setBuyerCompanyName(groupCorpName.split(",")[i]);
        }
        return orderItemVO;
    }

    /**
     * 初始化自身状态
     */
    public AllowOperation getAllowOperationVO() {
        //设置订单的可操作状态
        return new AllowOperation(this);
    }

    public String getGroupAfterSaleStatus() {
        // 不可售后的订单类型集合
        if (!OrderPromotionTypeEnum.isCanAfterSale(this.orderPromotionType)) {
            return OrderItemAfterSaleStatusEnum.EXPIRED.name();
        }
        return groupAfterSaleStatus;
    }
}
