package cn.lili.modules.order.order.wrapper;

import cn.hutool.core.bean.BeanUtil;
import cn.lili.modules.jrzh_bases.BaseEntityWrapper;
import cn.lili.modules.order.order.entity.dos.OrderItemPayRecords;
import cn.lili.modules.order.order.entity.vo.OrderItemPayRecordsVo;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年06月06日17:40
 */
public class OrderItemPayRecordsWrapper extends BaseEntityWrapper<OrderItemPayRecords, OrderItemPayRecordsVo> {

    public static OrderItemPayRecordsWrapper build() {
        return new OrderItemPayRecordsWrapper();
    }

    @Override
    public OrderItemPayRecordsVo entityVO(OrderItemPayRecords entity) {
        OrderItemPayRecordsVo vo = Objects.requireNonNull(BeanUtil.copyProperties(entity, OrderItemPayRecordsVo.class));
        return vo;
    }
}
