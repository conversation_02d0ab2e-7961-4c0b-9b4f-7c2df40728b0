package cn.lili.modules.order.order.entity.dto;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.security.enums.UserEnums;
import cn.lili.common.utils.StringUtils;
import cn.lili.common.vo.PageVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025年06月17日17:05
 */
@Data
public class OrderItemPayRecordsParams extends PageVO {

    private String id;
    /**
     * 子订单sn
     */
    private String orderItemSn;
    /**
     * 支付状态
     * @see cn.lili.modules.order.order.entity.enums.PayRecordsPaymentStatusEnum
     */
    private String paymentStatus;
    /**
     * 店铺id
     */
    private String storeId;
    /**
     * 买方id
     */
    private String memberId;
    /**
     * 主订单sn
     */
    private String orderSn;
    /**
     * 买方公司名称
     */
    private String corpName;

    public <T> QueryWrapper<T> queryWrapper() {
        AuthUser currentUser = UserContext.getCurrentUser();
        QueryWrapper<T> wrapper = new QueryWrapper<>();
        // 根据登录用户添加默认条件
        if (ObjectUtil.isNotEmpty(currentUser)) {
            //店铺查询
            wrapper.eq(CharSequenceUtil.equals(currentUser.getRole().name(), UserEnums.STORE.name())
                    && CharSequenceUtil.isEmpty(storeId), "oipr.store_id", currentUser.getStoreId());

            //按买家查询
            wrapper.eq(CharSequenceUtil.equals(currentUser.getRole().name(), UserEnums.MEMBER.name())
                    && CharSequenceUtil.isEmpty(memberId), "oipr.member_id", currentUser.getId());
        }
        wrapper.eq(StringUtils.isNotEmpty(orderItemSn), "oipr.order_item_sn", orderItemSn);
        wrapper.eq(StringUtils.isNotEmpty(paymentStatus), "oipr.payment_status", paymentStatus);
        wrapper.eq(StringUtils.isNotEmpty(id), "oipr.id", id);
        wrapper.eq(StringUtils.isNotEmpty(orderSn), "oi.order_sn", orderSn);
        wrapper.like(StringUtils.isNotEmpty(corpName), "ci.corp_name", corpName);
        wrapper.eq("oipr.delete_flag", false);
        return wrapper;
    }

}
