package cn.lili.modules.order.order.entity.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 变更子订单的dto
 * <AUTHOR>
 * @date 2025年04月25日11:53
 */
@Data
public class ChangeOrderItemDTO implements Serializable {

    /**
     * 子订单订单号
     */
    @NotBlank(message = "子订单号不能为空")
    private String orderItemSn;
    /**
     * 收货数量
     */
    @NotNull(message = "收货数量不能为空")
    private Integer num;
    /**
     * 调整的价格
     */
    @NotNull(message = "调整的价格不能为空")
    private BigDecimal flowPrice;
    /**
     * 调整描述
     */
    private String changeDesc;
    /**
     * 调整附件
     */
    private String changeUrl;

}
