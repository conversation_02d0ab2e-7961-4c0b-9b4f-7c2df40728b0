package cn.lili.modules.order.order.entity.dto;

import cn.lili.common.validation.EnumValue;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 申请开票dto
 * <AUTHOR>
 * @date 2025年06月21日16:46
 */
@Data
public class ApplyReceiptDto implements Serializable {

    /**
     * 子订单sn集合
     */
    private List<String> orderItemSnList;
    /**
     * 发票类型
     * @see cn.lili.modules.member.entity.enums.MemberReceiptEnum
     */
    @NotNull(message = "发票类型不能为空")
    @EnumValue(strValues = {"ELECTRONIC_INVOICE", "SPECIAL_VAT_INVOICE"})
    private String receiptType;
    /**
     * 发票内容
     */
    @NotNull(message = "发票内容不能为空")
    private String receiptContent;
    /**
     * 发票抬头
     */
    private String receiptTitle;
    /**
     * 纳税人识别号
     */
    private String taxpayerId;

}
