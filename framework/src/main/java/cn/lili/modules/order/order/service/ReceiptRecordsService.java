package cn.lili.modules.order.order.service;

import cn.lili.modules.order.order.entity.dos.Receipt;
import cn.lili.modules.order.order.entity.dos.ReceiptRecords;
import cn.lili.modules.order.order.entity.dto.OrderReceiptDTO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月03日15:48
 */
public interface ReceiptRecordsService extends IService<ReceiptRecords> {

    /**
     * 根据发票id获取发票文件url
     * @param receiptId
     * @return
     */
    String getFileUrlsByReceiptId(String receiptId);

    /**
     * 根据发票id查询
     * @param receiptId
     * @return
     */
    List<ReceiptRecords> getByReceiptId(String receiptId);

    /**
     * 根据发票idlist查询
     * @param receiptIdList
     * @return
     */
    List<ReceiptRecords> getByReceiptIdList(List<String> receiptIdList);

    /**
     * 填充文件url
     * @param list
     * @return
     */
    List<Receipt> fullList(List<Receipt> list);
    List<OrderReceiptDTO> fullDtoList(List<OrderReceiptDTO> list);
}
