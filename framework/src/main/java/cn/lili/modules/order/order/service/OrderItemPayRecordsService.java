package cn.lili.modules.order.order.service;

import cn.lili.common.dto.OrderFinancingPaidNotify;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dos.OrderItemPayRecords;
import cn.lili.modules.order.order.entity.dto.OrderItemPayRecordsParams;
import cn.lili.modules.order.order.entity.dto.OrderPaymentDto;
import cn.lili.modules.order.order.entity.dto.UpdatePayRecordsDto;
import cn.lili.modules.order.order.entity.enums.PayRecordsTypeEnum;
import cn.lili.modules.order.order.entity.vo.OrderItemPayRecordsVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025年06月05日16:50
 */
public interface OrderItemPayRecordsService extends IService<OrderItemPayRecords> {

    /**
     * 子订单支付记录
     * @param orderItem
     * @param fileUrls
     * @return
     */
    Boolean orderItemPayment(OrderItem orderItem, List<String> fileUrls);

    /**
     * 子订单融资支付记录
     * @param orderItem
     * @param orderFinancingPaidNotify
     * @param memberId
     * @return
     */
    Boolean orderItemFinancingPayment(OrderItem orderItem, OrderFinancingPaidNotify orderFinancingPaidNotify, String memberId);

    /**
     * 更新支付状态
     * @param dto
     * @return
     */
    Boolean updatePayRecords(UpdatePayRecordsDto dto);

    /**
     * 根据子订单编号获取所有的支付记录
     * @param orderItemSns
     * @param typeEnum
     * @return
     */
    List<OrderItemPayRecords> getListByItemSn(List<String> orderItemSns, PayRecordsTypeEnum typeEnum);

    /**
     * 分页
     * @param params
     * @return
     */
    IPage<OrderItemPayRecordsVo> pageByParams(OrderItemPayRecordsParams params);

    /**
     * 详情
     * @param id
     * @return
     */
    OrderItemPayRecordsVo detail(String id);
}
