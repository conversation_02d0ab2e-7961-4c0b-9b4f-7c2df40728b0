<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.businessinfo.mapper.CustomerBusinessInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="customerBusinessInfoResultMap" type="org.springblade.customer.businessinfo.entity.CustomerBusinessInfo">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="legal_person_name" property="legalPersonName"/>
        <result column="phone_number" property="phoneNumber"/>
        <result column="estiblish_time" property="estiblishTime"/>
        <result column="credit_code" property="creditCode"/>
        <result column="org_number" property="orgNumber"/>
        <result column="from_time" property="fromTime"/>
        <result column="to_time" property="toTime"/>
        <result column="reg_number" property="regNumber"/>
        <result column="approved_time" property="approvedTime"/>
        <result column="reg_capital" property="regCapital"/>
        <result column="reg_institute" property="regInstitute"/>
        <result column="company_org_type" property="companyOrgType"/>
        <result column="industry" property="industry"/>
        <result column="employee_num" property="employeeNum"/>
        <result column="medical_insurance" property="medicalInsurance"/>
        <result column="reg_location" property="regLocation"/>
        <result column="total_assets" property="totalAssets"/>
        <result column="business_scope" property="businessScope"/>
        <result column="reg_date" property="regDate"/>
        <result column="customer_id" property="customerId"/>
        <result column="change_date" property="changeDate"/>
        <result column="cal_reg_capital" property="calRegCapital"/>
        <result column="company_name" property="companyName"/>
        <result column="company_id" property="companyId"/>
        <result column="tenant_id" property="tenantId"/>

    </resultMap>


    <select id="selectCustomerBusinessInfoPage" resultMap="customerBusinessInfoResultMap">
        select * from jrzh_customer_business_info where is_deleted = 0
    </select>
    <select id="selectDelData" resultType="org.springblade.customer.businessinfo.entity.CustomerBusinessInfo">
      select  *  from   jrzh_customer_business_info  where  is_deleted=1
    </select>
    <select id="getCustomerBusinessInfo"
            resultType="org.springblade.customer.businessinfo.entity.CustomerBusinessInfo">

      select  *  from  jrzh_customer_business_info   where   is_deleted=0
    <if test="customerId!=null">
        and  customer_id=#{customerId}
    </if>
    <if test="companyName!=null">
        and company_name=#{companyName}
    </if>
    <if test="creditCode!=null">
        and credit_code=#{creditCode}
    </if>
    </select>

</mapper>
