/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_business_info.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.utils.BeanUtil;
import cn.lili.modules.connect.config.SkyEyeConnector;
import cn.lili.modules.connect.config.SkyEyeResult;
import cn.lili.modules.jrzh_business_info.dto.CustomerBusinessInfo;
import cn.lili.modules.jrzh_business_info.mapper.CustomerBusinessInfoMapper;
import cn.lili.modules.jrzh_business_info.service.ICustomerBusinessInfoService;
import cn.lili.modules.jrzh_other.core_api.constant.ApiSupplier;
import cn.lili.modules.otherapi.sky.constant.SkyEyeApiPath;
import cn.lili.modules.otherapi.sky.dto.CompanyBasicInfo;
import cn.lili.modules.otherapi.sky.dto.SkyEyeBaseInfo;
import cn.lili.modules.otherapi.sky.dto.SupplierBusinessInfo;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.TreeMap;

/**
 * 客户工商信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Service
@RequiredArgsConstructor
public class CustomerBusinessInfoServiceImpl extends ServiceImpl<CustomerBusinessInfoMapper, CustomerBusinessInfo> implements ICustomerBusinessInfoService {
    private final SkyEyeConnector connector;
    //    private final CompanyInfoApiHandlerFactory companyInfoApiHandlerFactory;
//
//    @Override
//    public IPage<CustomerBusinessInfoVO> selectCustomerBusinessInfoPage(IPage<CustomerBusinessInfoVO> page, CustomerBusinessInfoVO customerBusinessInfo) {
//        return page.setRecords(baseMapper.selectCustomerBusinessInfoPage(page, customerBusinessInfo));
//    }
//
//    @Override
//    public List<CustomerBusinessInfo> selectDelData() {
//        return baseMapper.selectDelData();
//    }
//
//    @Override
//    public Boolean updateBusinessInfo(Long customerId) {
//        return null;
//    }
//
//    @Override
//    public CustomerBusinessInfo getByCustomerId(Long customerId) {
//        return getOne(Wrappers.<CustomerBusinessInfo>lambdaQuery().eq(CustomerBusinessInfo::getCustomerId, customerId).last("limit 1"));
//    }
//
//    @Override
//    public CustomerBusinessInfo getCustomerBusinessInfo(CustomerBusinessInfoVO customerBusinessInfoVO) {
//        return baseMapper.getCustomerBusinessInfo(customerBusinessInfoVO);
//    }
//
//    @Override
//    public CustomerBusinessInfo getByName(String name) {
//        return getOne(Wrappers.<CustomerBusinessInfo>lambdaQuery().eq(CustomerBusinessInfo::getCompanyName, name).last("limit 1"));
//    }
//
//    @Override
//    public Boolean needReGen(String creditCode) {
//        CustomerBusinessInfo businessInfo = getByCreditCode(creditCode);
//        return !ObjectUtil.isNotEmpty(businessInfo);
//    }
//
//    @Override
//    public CustomerBusinessInfo getByCreditCode(String creditCode) {
//        return getOne(Wrappers.<CustomerBusinessInfo>lambdaQuery()
//                .eq(CustomerBusinessInfo::getCreditCode, creditCode)
//                .orderByDesc(CustomerBusinessInfo::getCreateTime).last("limit 1"));
//    }
//
//    @Override
//    public SupplierBusinessInfo getSupplierBusinessInfo(String keyword) {
//        return companyInfoApiHandlerFactory.template().getSupplierBusinessInfo(keyword);
//    }
//
//    @Override
//    public CustomerBusinessInfo getByKeyWord(String keyword) {
//        return getOne(Wrappers.<CustomerBusinessInfo>lambdaQuery().eq(CustomerBusinessInfo::getCompanyName, keyword).or().eq(CustomerBusinessInfo::getCreditCode, keyword)
//                .orderByDesc(CustomerBusinessInfo::getCreateTime).last("limit 1"));
//    }
//
    @Override
    public SupplierBusinessInfo saveBusinessInfo(String keyword) {
        SupplierBusinessInfo supplierBusinessInfo = saveBusinessInfo(keyword, Duration.ofDays(90));
        return supplierBusinessInfo;
    }

    @Override
    public SupplierBusinessInfo saveBusinessInfo(String keyword, Duration timeout) {
        CustomerBusinessInfo businessInfo = new CustomerBusinessInfo();
        SupplierBusinessInfo supplierBusinessInfo = this.skySaveBusinessInfo(keyword, timeout);
        CompanyBasicInfo companyBasicInfo = supplierBusinessInfo.getCompanyBasicInfo();
        //拷贝属性
        BeanUtil.copyProperties(companyBasicInfo, businessInfo);
        businessInfo.setCompanyName(companyBasicInfo.getName());
        saveOrUpdate(businessInfo);
        return supplierBusinessInfo;
    }

    private SupplierBusinessInfo skySaveBusinessInfo(String keywords, Duration timeout) {
        TreeMap<String, Object> queryParams = new TreeMap();
        queryParams.put("keyword", keywords);
        SkyEyeApiPath method = SkyEyeApiPath.COMPANY_BASE_INFO;
        SkyEyeResult skyEyeResult = this.connector.get(method.getPath(), queryParams);
        SkyEyeBaseInfo baseInfo = JSONUtil.toBean(skyEyeResult.getResult(), SkyEyeBaseInfo.class);
        return this.baseInfoTransfer(baseInfo);
    }
    private SupplierBusinessInfo baseInfoTransfer(SkyEyeBaseInfo skyEyeBaseInfo) {
        SupplierBusinessInfo businessInfo = new SupplierBusinessInfo();
        CompanyBasicInfo companyBasicInfo = new CompanyBasicInfo();
        BeanUtil.copyProperties(skyEyeBaseInfo, companyBasicInfo);
        companyBasicInfo.setEmployeeNum(skyEyeBaseInfo.getStaffNumRange());
        companyBasicInfo.setMedicalInsurance(skyEyeBaseInfo.getSocialStaffNum());
        companyBasicInfo.setApprovedTime(skyEyeBaseInfo.getApprovedTime());
        companyBasicInfo.setBase(skyEyeBaseInfo.getBase());
        businessInfo.setCompanyBasicInfo(companyBasicInfo);
        return businessInfo;
    }
//
//    @Override
//    public Boolean regen(String keyword) {
//        //重新拉取
//        SupplierBusinessInfo supplierBusinessInfo = saveBusinessInfo(keyword, Duration.ofDays(1));
//        //TODO 更新历史信息
//        return true;
//    }
//
//    @Override
//    public boolean companyThreeElementsIsTure(String creditCode, String companyName, String legalPersonName) {
//        return companyInfoApiHandlerFactory.template().companyThreeElementsIsTure(creditCode, companyName, legalPersonName);
//    }
//
}
