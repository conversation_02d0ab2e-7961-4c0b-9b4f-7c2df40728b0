/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_business_info.dto;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 客户工商信息表实体类
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
@Data
@TableName("jrzh_customer_business_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerBusinessInfo对象", description = "客户工商信息表")
public class CustomerBusinessInfo extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	* 法定代表人
	*/
//	@ForUpdate(fieldName = "法定代表人")
		@ApiModelProperty(value = "法定代表人")
		private String legalPersonName;
	/**
	* 手机号码
	*/
//	@ForUpdate(fieldName = "手机号码")
		@ApiModelProperty(value = "手机号码")
		private String phoneNumber;
	/**
	* 成立日期
	*/
//	@ForUpdate(fieldName = "成立日期")
		@ApiModelProperty(value = "成立日期")
		private String estiblishTime;
	/**
	* 统一社会信用代码
	*/
//	@ForUpdate(fieldName = "统一社会信用代码")
		@ApiModelProperty(value = "统一社会信用代码")
		private String creditCode;
	/**
	* 组织机构代码
	*/
//	@ForUpdate(fieldName = "组织机构代码")
		@ApiModelProperty(value = "组织机构代码")
		private String orgNumber;
	/**
	* 经营开始时间
	*/
//	@ForUpdate(fieldName = "经营开始时间")
		@ApiModelProperty(value = "经营开始时间")
		private String fromTime;
	/**
	* 经营结束时间
	*/
//	@ForUpdate(fieldName = "经营结束时间")
		@ApiModelProperty(value = "经营结束时间")
		private String toTime;
	/**
	* 工商注册号
	*/
//	@ForUpdate(fieldName = "工商注册号")
		@ApiModelProperty(value = "工商注册号")
		private String regNumber;
	/**
	* 核准日期
	*/
//	@ForUpdate(fieldName = "核准日期")
		@ApiModelProperty(value = "核准日期")
		private String approvedTime;
	/**
	* 注册资本
	*/
//	@ForUpdate(fieldName = "注册资本")
		@ApiModelProperty(value = "注册资本")
		private String regCapital;
	/**
	* 登记机关
	*/
//	@ForUpdate(fieldName = "登记机关")
		@ApiModelProperty(value = "登记机关")
		private String regInstitute;
	/**
	* 企业类型
	*/
//	@ForUpdate(fieldName = "企业类型")
		@ApiModelProperty(value = "企业类型")
		private String companyOrgType;
	/**
	* 行业
	*/
//	@ForUpdate(fieldName = "行业")
		@ApiModelProperty(value = "行业")
		private String industry;
	/**
	* 人员规模
	*/
//	@ForUpdate(fieldName = "人员规模")
		@ApiModelProperty(value = "人员规模")
		private String employeeNum;
	/**
	* 参保人员
	*/
//	@ForUpdate(fieldName = "参保人员")
		@ApiModelProperty(value = "参保人员")
		private String medicalInsurance;
	/**
	* 注册地址
	*/
//	@ForUpdate(fieldName = "注册地址")
		@ApiModelProperty(value = "注册地址")
		private String regLocation;
	/**
	* 总资产
	*/
//	@ForUpdate(fieldName = "总资产")
		@ApiModelProperty(value = "总资产")
		private String totalAssets;
	/**
	* 经营范围
	*/
//	@ForUpdate(fieldName = "经营范围")
		@ApiModelProperty(value = "经营范围")
		private String businessScope;
	/**
	* 注册时间
	*/
//	   @ForUpdate(fieldName = "注册时间")
		@ApiModelProperty(value = "注册时间")
		private String regDate;
	/**
	* 客户端用户表id
	*/
		@ApiModelProperty(value = "客户端用户表id")
		private String customerId;

	/**
	 * 更新时间
	 */
	private Date  changeDate;

	/**
	 * 总资产
	 */
    private  String calRegCapital;


	/***
	 * 企业名称
	 */
	private  String  companyName;


	/**
	 * 企业id
	 */

	private  String companyId;

	/**
	 * 省份拼音简写
	 */
	private String base;

}
