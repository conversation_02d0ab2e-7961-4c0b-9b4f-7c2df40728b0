/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_business_info.service;

import cn.lili.modules.jrzh_business_info.dto.CustomerBusinessInfo;
import cn.lili.modules.otherapi.sky.dto.SupplierBusinessInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.time.Duration;
import java.util.List;

/**
 * 客户工商信息表 服务类
 *
 * <AUTHOR>
 * @since 2022-01-19
 */
public interface ICustomerBusinessInfoService extends IService<CustomerBusinessInfo> {

//    /**
//     * 自定义分页
//     *
//     * @param page
//     * @param customerBusinessInfo
//     * @return
//     */
//    IPage<CustomerBusinessInfoVO> selectCustomerBusinessInfoPage(IPage<CustomerBusinessInfoVO> page, CustomerBusinessInfoVO customerBusinessInfo);
//
//
//    List<CustomerBusinessInfo> selectDelData();
//
//    /**
//     * 更新客户工商信息
//     *
//     * @return
//     */
//    Boolean updateBusinessInfo(Long customerId);
//
//
//    //SupplierBusinessInfo getBusinessInfoByCompanyName(String companyName);
//
//    /**
//     * 根据客户id获取对应工商信息
//     * @param customerId
//     * @return
//     */
//    //Boolean  getBusinessInfoByCustomerId(Long  customerId);
//
//    /**
//     * 根据客户id获取天眼查基础信息
//     *
//     * @param customerId
//     * @return
//     */
//    CustomerBusinessInfo getByCustomerId(Long customerId);
//
//
//    //Boolean updateBusinessInfoByCompanyName(String  companyName);
//
//
//    /***
//     * 根据客户id 企业名称  企业统一信用编码查寻工商信息
//     */
//
//    CustomerBusinessInfo getCustomerBusinessInfo(CustomerBusinessInfoVO customerBusinessInfoVO);
//
//    /**
//     * 根据企业名称
//     */
//    CustomerBusinessInfo getByName(String name);
//
////接口转移至customerInfoService getCustomerBusinessInfoByCompanyId
////    /**
////     * 根据企业id查询
////     *
////     * @param companyId 企业id
////     * @return CustomerBusinessInfo
////     */
////    CustomerBusinessInfo getByCompanyId(String companyId);
//
//    /**
//     * 是否需要重新获取天眼查信息 条件：超过10天
//     *
//     * @param creditCode 社会统一代码
//     * @return
//     */
//    Boolean needReGen(String creditCode);
//
//    /**
//     * 根据社会统一代码获取
//     */
//    CustomerBusinessInfo getByCreditCode(String creditCode);
//
//    /**
//     * 获取工商信息
//     *
//     * @param keyword 关键词
//     * @return
//     */
//    SupplierBusinessInfo getSupplierBusinessInfo(String keyword);
//
//    /**
//     * 通过关键字进行查找
//     *
//     * @param keyword
//     * @return
//     */
//    CustomerBusinessInfo getByKeyWord(String keyword);
//
    /**
     * 通过关键字保存工商基础信息
     *
     * @param keyword
     */
    SupplierBusinessInfo saveBusinessInfo(String keyword);

    /**
     * 通过关键字保存工商基础信息
     *
     * @param keyword 关键字
     * @param timeout 过期时间
     * @return
     */
    SupplierBusinessInfo saveBusinessInfo(String keyword, Duration timeout);
//
//    /**
//     * 重新拉取工商基础信息
//     *
//     * @param keyword
//     */
//    Boolean regen(String keyword);
//
//    /**
//     * 工商三要素验证
//     *
//     * @param creditCode      授信编码
//     * @param companyName     企业名称
//     * @param legalPersonName 法人名称
//     */
//    boolean companyThreeElementsIsTure(String creditCode, String companyName, String legalPersonName);
}
