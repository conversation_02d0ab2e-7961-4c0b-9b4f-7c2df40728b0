package cn.lili.modules.jrzh_business_info.handler;


import cn.lili.modules.otherapi.sky.dto.SupplierBusinessInfo;

import java.time.Duration;

/**
 * <AUTHOR>
 * 实名接口抽象类
 */
public interface CompanyInfoApiHandler {
//	/**
//	 * 工商三要素校验
//	 *
//	 * @param creditCode      社会统一代码
//	 * @param companyName     企业名称
//	 * @param legalPersonName 法人名称
//	 * @return
//	 */
//	boolean companyThreeElementsIsTure(String creditCode, String companyName, String legalPersonName);
//
	/**
	 * 请求获取工商信息
	 *
	 * @param keywords 关键词
	 * @param timeout  过期时间
	 */
	SupplierBusinessInfo saveBusinessInfo(String keywords, Duration timeout);
//	/**
//	 * 请求获取工商信息
//	 *
//	 * @param keywords 关键词
//	 */
//	SupplierBusinessInfo saveBusinessInfo(String keywords);
//	/**
//	 * 根据关键字获取工商信息
//	 *
//	 * @param keyword 关键字
//	 * @return
//	 */
//	SupplierBusinessInfo getSupplierBusinessInfo(String keyword);


}
