package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateGoodsConfig;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateGoodsConfigVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 合同模板商品配置服务接口
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
public interface IContractTemplateGoodsConfigService {

    /**
     * 根据商品ID获取模板配置（不再区分合同类型）
     *
     * @param goodsId 商品ID
     * @return 模板配置列表
     */
    List<ContractTemplateGoodsConfig> getGoodsConfigs(String goodsId);

    /**
     * 根据商品ID查询绑定的合同模板ID
     * 用于合同生成时直接获取模板
     *
     * @param goodsId 商品ID
     * @return 合同模板ID，如果未绑定则返回null
     */
    String getGoodsBoundTemplateId(String goodsId);

    /**
     * 保存或更新商品模板配置
     * 如果配置已存在则更新，否则新增
     *
     * @param config 商品配置
     * @return 保存结果
     */
    boolean saveOrUpdateGoodsConfig(ContractTemplateGoodsConfig config);

    /**
     * 保存商品模板配置（向后兼容方法）
     * 实际调用 saveOrUpdateGoodsConfig
     *
     * @param config 商品配置
     * @return 保存结果
     */
    default boolean saveGoodsConfig(ContractTemplateGoodsConfig config) {
        return saveOrUpdateGoodsConfig(config);
    }

    /**
     * 删除商品模板配置
     *
     * @param configId 配置ID
     * @return 删除结果
     */
    boolean deleteGoodsConfig(String configId);

    /**
     * 分页查询商品模板配置
     *
     * @param page 分页参数
     * @param goodsId 商品ID
     * @param templateId 模板ID
     * @return 分页结果
     */
    IPage<ContractTemplateGoodsConfigVO> getGoodsConfigPage(Page<ContractTemplateGoodsConfig> page, String goodsId, String templateId);
}
