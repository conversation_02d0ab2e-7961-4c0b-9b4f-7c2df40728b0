/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.customer_ct.service;


import cn.lili.modules.jrzh_contract.contract_api.dto.ContractSignParam;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractReturnData;
import cn.lili.modules.jrzh_contract.customer_ct.dto.CustomerContractDataParamsDTO;

/**
 * 客户端合同 服务类
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
public interface IContractCustomerService {
    /**
     * 通用生成合同方法
     * 必传参数：
     * templateId 合同模板id 这里是指上上签的模板id
     * goodsId 产品id 用于合同列表展示
     * signNode 签署节点
     * processType 流程类型 用于合同列表展示
     * contractBizNo 标识符 一般使用流程id作为标识符即可
     * 非必传参数
     * frontDataSource 前端数据源
     * originBackSource 后端数据源 对应后台合同模板方案设置的数据源 不传则使用内置数据源
     *
     * @param paramsDTO 生成合同的参数
     */
    ContractReturnData generateContractWithBusiness(CustomerContractDataParamsDTO paramsDTO);

    /**
     * 融资端使用当前用户进行签署
     *
     * @param signParam
     */
    void signByCurrentUser(ContractSignParam signParam);

    /**
     * 合同下载
     *
     * @param contractId
     * @return
     */
//    String downLoadContract(String contractId);
}
