/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.modules.jrzh_contract.contract_api.dto.DynamicContractDTO;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;

/**
 * 合同生成服务
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
public interface IContractGenService {
    /**
     * 合同生成 并保存
     * signNode 签署节点 必填
     * contractTemplateId 模板id 必填
     * processType 流程类型 必填
     * goodsId 产品id 必填
     * goodName 产品名称 必填
     * contractBizNo 合同业务号 必填
     * 其余参数说明请看 DynamicContractDTO
     *
     * @param dynamicContractDTO
     * @return
     */
    Contract genContract(DynamicContractDTO dynamicContractDTO);
}