/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 合同模板实体类
 *
 * <AUTHOR>
 * @since 2021-12-20
 */

@Data
@TableName("jrzh_contract_template")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractTemplate对象", description = "ContractTemplate对象")
public class ContractTemplate  extends BaseEntity {

	private static final long serialVersionUID = 1L;

	private Integer status;

	@ApiModelProperty(value = "模板id")
	private String templateId;

	@ApiModelProperty(value = "模板标题")
	private String templateName;

	@ApiModelProperty(value = "模板状态")
	private String templateStatus;


	@ApiModelProperty(value = "模板类型")
	private String templateCategory;

	@ApiModelProperty(value = "操作人")
	private String operateName;

	@ApiModelProperty(value = "操作时间")
	private Date operateTime;

	@ApiModelProperty(value = "附件id")
	private Long attachId;

	@ApiModelProperty(value = "过期时间")
	private Integer expireDay;

	@ApiModelProperty(value = "强制阅读秒数")
	private Integer forceReadingSecond;

	@ApiModelProperty(value = "签署校验类型")
	private String verifyType;

	@ApiModelProperty(value = "供应商")
	private String apiSupplier;

	@ApiModelProperty(value = "合同生成方式 1合同模板 2报表引擎")
	private Integer contractGenType;

	@ApiModelProperty(value = "签署人数量")
	private Integer signerNum;

	@ApiModelProperty(value = "合同模板url")
	private String templateUrl;

	@ApiModelProperty(value = "自定义模板word类型url")
	private String templateWordUrl;

	@ApiModelProperty(value = "是否需要手写签名 0不需要 1需要")
	private Integer needCustomizeWrite;

	@ApiModelProperty(value = "签署节点")
	private String signNode;

	@ApiModelProperty(value = "关联子合同编号")
	private String subContractNo;

	@ApiModelProperty(value = "需要平台方签署")
	private Integer needPlatSign;

	/**
	 * 默认模板标识
	 * null-非默认模板，1-默认模板
	 * 通过此字段判断是否为默认模板：1为默认模板，null为普通模板
	 */
	@ApiModelProperty(value = "默认模板标识：null-非默认模板，1-默认模板")
	private Integer defaultContractType;
}
