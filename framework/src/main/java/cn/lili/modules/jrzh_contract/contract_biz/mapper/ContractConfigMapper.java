/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.mapper;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 合同配置 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
public interface ContractConfigMapper extends BaseMapper<ContractConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param contractConfig
	 * @return
	 */
//	List<ContractConfigVO> selectContractConfigPage(IPage page, ContractConfigVO contractConfig);

}
