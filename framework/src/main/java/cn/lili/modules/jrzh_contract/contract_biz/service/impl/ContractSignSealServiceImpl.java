/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractSignSeal;
import cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractSignSealMapper;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractSignSealService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 合同签名/印章配置 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Service
@RequiredArgsConstructor
public class ContractSignSealServiceImpl extends ServiceImpl<ContractSignSealMapper, ContractSignSeal> implements IContractSignSealService {

}
