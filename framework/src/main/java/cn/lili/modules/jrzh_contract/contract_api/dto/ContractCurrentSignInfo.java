/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 当前签署人信息
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractCurrentSignInfo implements Serializable {
	private static final long serialVersionUID = 1L;
	//当前用户个人名称
	private String personalName;
	//用户类型 1个人 2企业
	private Integer customerType;
	//所属企业名称/个人名称
	private String companyName;
	//所属企业/个人用户id
	private Long userId;
	//当前用户的个人账户用户id
	private Long personalUserId;
}
