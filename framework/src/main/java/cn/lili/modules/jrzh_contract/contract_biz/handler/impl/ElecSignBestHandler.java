package cn.lili.modules.jrzh_contract.contract_biz.handler.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.context.UserContext;
import cn.lili.modules.connect.config.SsqCreateSignImg;
import cn.lili.modules.file.entity.File;
import cn.lili.modules.jrzh_bases.AuthUtil;
import cn.lili.modules.jrzh_bases.BladeFile;
import cn.lili.modules.jrzh_bases.CustomerTypeEnum;
import cn.lili.modules.jrzh_contract.contract_api.dto.SignParam;
import cn.lili.modules.jrzh_contract.contract_api.dto.SsqContractGenParam;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractSignConfig;
import cn.lili.modules.jrzh_contract.contract_biz.handler.ElecSignAbsHandler;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractConfigService;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractOperatorService;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractSignConfigService;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractReturnData;
import cn.lili.modules.jrzh_other.bestsign.connector.BestSignConnector;
import cn.lili.modules.jrzh_other.bestsign.constant.BestSignPathEnum;
import cn.lili.modules.jrzh_other.bestsign.constant.ContractConfigEnum;
import cn.lili.modules.jrzh_other.bestsign.constant.ContractEnum;
import cn.lili.modules.jrzh_other.bestsign.dto.ContractTemplateFileDTO;
import cn.lili.modules.jrzh_other.bestsign.dto.SsqContractInfo;
import cn.lili.modules.jrzh_other.bestsign.dto.SsqSignaturePositions;
import cn.lili.modules.jrzh_other.bestsign.service.IBestSignService;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import jodd.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * 上上签电子签实现类
 */
@Service("bestSign")
@Slf4j
@RequiredArgsConstructor
public class ElecSignBestHandler extends ElecSignAbsHandler {
    private final IBestSignService bestSignService;
    private static final String PARSE_TIME_FORMAT = "yyyy-MM-dd";
    private static final String SIGN_KEY = "bestSignLinkKey:";
    private static final Integer IZ_FRONT = 1;
    private final BestSignConnector connector;


    /**
     * 创建签名/印章图片
     *
     * @return 是否成功
     */
    @Override
    public boolean createSignImg(SsqCreateSignImg ssqCreateSignImg) {
        switch (ssqCreateSignImg.getType()) {
            // 上传
            case 0:
                bestSignService.uploadSignImg(ssqCreateSignImg.getAccountId(), ssqCreateSignImg.getImageData(), ssqCreateSignImg.getImageName());
                break;
            // 生成
            case 1:
                bestSignService.generateSignImg(ssqCreateSignImg.getAccountId(), ssqCreateSignImg.getCustomerType());
                break;
            default:
                throw new ServiceException("创建签名/印章方式不存在");
        }
        return true;
    }
//
//    /**
//     * 校验 上传返回结果
//     *
//     * @param ssqResult 上上签请求结果
//     */
//    private void checkUploadError(SSQResult ssqResult) {
//        String errmsg = ssqResult.getErrmsg();
//        // 上传错误的印章图片后返回的错误信息
//        final String noFtype = "no ftype";
//        if (noFtype.equals(errmsg)) {
//            throw new ServiceException("请上传正确的签名/印章图片");
//        }
//
//    }
//

    /**
     * 下载签名/印章图片
     *
     * @return 是否成功
     */
    @Override
    public BladeFile downloadSignImg(String accountId, String name) {
        String imageName = StringUtil.isNotBlank(name) ? name : "default";
        return bestSignService.downloadSignImg(imageName, accountId);
    }

    //
//    /**
//     * 在线验签
//     *
//     * @return 返回参数
//     */
//    @Override
//    public Boolean onlineVerifySignature(String contractId, String fhash) {
//        return bestSignService.verifySignature(contractId, fhash);
//    }
//
//    @Override
//    public ContractReturnData contractCreate(DynamicContractDTO dynamicContractDTO) {
//        //数据获取
//        ContractTemplateConfigVO contractTemplateConfigVO = dynamicContractDTO.getContractTemplateConfigVO();
//        String templateFieldsJson = contractTemplateConfigVO.getTemplateFieldsJson();
//        List<TemplateFieldsConfig> fieldsConfigs = contractTemplateConfigVO.getTemplateFieldsConfigs();
//        //根据字段类型进行分组 k:字段类型 v:需要绑定的字段列表 分组后筛选出需要填充的字段（ 图片 单行 多行）
//        List<SsqTemplateVars> needFullFields = new ArrayList<>();
//        List<SsqTemplateVars> ssqTemplateVars = JSONUtil.toList(templateFieldsJson, SsqTemplateVars.class);
//        Map<String, List<SsqTemplateVars>> fieldsMap = ssqTemplateVars.stream()
//                .collect(Collectors.groupingBy(SsqTemplateVars::getType));
//        String imgType = SsqStatusEnum.PARAM_TYPE.IMG.getType();
//        if (fieldsMap.containsKey(imgType)) {
//            needFullFields.addAll(fieldsMap.get(imgType));
//            fieldsMap.remove(imgType);
//        }
//        String multipleLineTextType = SsqStatusEnum.PARAM_TYPE.MULTIPLE_LINE_TEXT.getType();
//        if (fieldsMap.containsKey(multipleLineTextType)) {
//            needFullFields.addAll(fieldsMap.get(multipleLineTextType));
//            fieldsMap.remove(multipleLineTextType);
//        }
//        String singleLineTextType = SsqStatusEnum.PARAM_TYPE.SINGLE_LINE_TEXT.getType();
//        if (fieldsMap.containsKey(singleLineTextType)) {
//            needFullFields.addAll(fieldsMap.get(singleLineTextType));
//            fieldsMap.remove(singleLineTextType);
//        }
//        //初始化需要填充的字段
//        initFields(needFullFields);
//        //将字段配置分为：前端数据、后端数据
//        List<TemplateFieldsConfig> frontFieldConfig = new ArrayList<>();
//        List<TemplateFieldsConfig> backFieldConfig = new ArrayList<>();
//        fieldsConfigs.forEach(e -> {
//            if (IZ_FRONT.equals(e.getIzFrontField())) {
//                frontFieldConfig.add(e);
//            } else {
//                backFieldConfig.add(e);
//            }
//        });
//        //填充后端业务字段数据
//        fullFieldByDataSource(needFullFields, backFieldConfig, dynamicContractDTO.getOriginBackSource());
//        //填充前端业务字段数据
//        fullFrontFieldByDataSource(needFullFields, frontFieldConfig, dynamicContractDTO.getFrontDataSource());
//        //填充 模版变量组 和 模版变量
//        Map<String, Object> groupValues = new HashMap<>();
//        Map<String, Object> templateValues = new HashMap<>();
//        needFullFields.forEach(e -> {
//            String group = e.getGroup();
//            String ssqName = e.getName();
//            String value = e.getValue();
//            if (ObjectUtil.isEmpty(group) || "".equals(group)) {
//                templateValues.put(ssqName, value);
//            } else {
//                groupValues.put(ssqName, value);
//            }
//        });
//        //构造生成合同请求参数
//        ContractTemplate contractTemplate = dynamicContractDTO.getContractTemplate();
//        SsqContractGenParam contractGenParam = SsqContractGenParam.builder()
//                .templateValues(templateValues)
//                .groupValues(groupValues)
//                .templateId(contractTemplate.getTemplateId())
//                .templateName(contractTemplate.getTemplateName())
//                .expireDay(contractTemplate.getExpireDay())
//                .build();
//        //生成合同
//        return bestSignService.templateContractCreate(contractGenParam);
//    }
//
//    private void initFields(List<SsqTemplateVars> needFullFields) {
//        needFullFields.forEach(e -> {
//            e.setValue("******");
//        });
//    }
//
//    @Override
//    public ContractReturnData contractCreate(SsqContractGenParam contractGenParam) {
//        return bestSignService.templateContractCreate(contractGenParam);
//    }
//
    @Override
    public ContractReturnData createContractByReport(ContractTemplateFileDTO contractTemplateFileDTO) {
        String contractId = bestSignService.createContractByReport(contractTemplateFileDTO);
        return new ContractReturnData(contractTemplateFileDTO.getDownLoadUrl(), contractId);
    }

    //
//    /**
//     * 填充前端传输字段
//     *
//     * @param needFullFields   上上签合同字段
//     * @param frontFieldConfig 前端字段配置
//     * @param frontData        前端传输数据
//     * @return 上上签业务字段
//     */
//    private void fullFrontFieldByDataSource(List<SsqTemplateVars> needFullFields,
//                                            List<TemplateFieldsConfig> frontFieldConfig, Object frontData) {
//        if (frontData == null || CollectionUtil.isEmpty(frontFieldConfig)) {
//            return;
//        }
//        //获取需要填充的合同
//        JSONObject frontDataJson = JSONUtil.parseObj(frontData);
//        // 筛选出前端传输字段的映射配置
//        Map<String, TemplateFieldsConfig> frontFieldConfigMap = frontFieldConfig.stream()
//                .collect(Collectors.toMap(TemplateFieldsConfig::getSsqFieldName, e -> e));
//        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(PARSE_TIME_FORMAT);
//        // 填充图片类型字段
//        needFullFields.forEach(e -> {
//            if (frontFieldConfigMap.containsKey(e.getName())) {
//                TemplateFieldsConfig fieldsConfig = frontFieldConfigMap.get(e.getName());
//                if (ObjectUtil.isNotEmpty(frontFieldConfig)) {
//                    String fieldName = fieldsConfig.getFieldName();
//                    Object var = dealTimeField(frontDataJson.get(fieldName), simpleDateFormat);
//                    e.setValue(ObjectUtil.isNotEmpty(var) ? var.toString() : "");
//                }
//            }
//        });
//    }
//
//    private String fullFieldObjVar(String fieldName, JSONObject frontDataJson, SimpleDateFormat simpleDateFormat) {
//        JSONObject currentVar = frontDataJson;
//        String[] split = fieldName.split("#");
//        int lastIndex = split.length - 1;
//        try {
//            for (int i = 0; i < lastIndex; i++) {
//                currentVar = currentVar.getJSONObject(split[i]);
//            }
//            //填充叶子结点数据
//            return dealTimeField(currentVar.get(split[lastIndex]), simpleDateFormat).toString();
//        } catch (NullPointerException e) {
//            return "";
//        }
//    }
//
//    /**
//     * 根据数据源填充上上签业务字段
//     *
//     * @param needFullFields       上上签合同字段
//     * @param backFieldConfig      后端字段配置
//     * @param originBackDataSource 后端数据源 k:类名 v：值
//     * @return
//     */
//    private void fullFieldByDataSource(List<SsqTemplateVars> needFullFields,
//                                       List<TemplateFieldsConfig> backFieldConfig, Map<String, Object> originBackDataSource) {
//        if (ObjectUtil.isEmpty(originBackDataSource) || CollectionUtil.isEmpty(needFullFields)) {
//            return;
//        }
//        //k:ssq业务字段名称 v:字段配置类
//        Map<String, TemplateFieldsConfig> fieldsConfigMap = backFieldConfig.stream()
//                .collect(Collectors.toMap(TemplateFieldsConfig::getSsqFieldName, e -> e));
//        //填充上上签合同业务数据
//        needFullFields.forEach(e -> {
//            TemplateFieldsConfig fieldsConfig = fieldsConfigMap.get(e.getName());
//            if (ObjectUtil.isNotEmpty(fieldsConfig)) {
//                Object data = originBackDataSource.get(fieldsConfig.getClazzName());
//                //进行值的填充
//                if (ObjectUtil.isNotEmpty(fieldsConfig) && ObjectUtil.isNotEmpty(data)) {
//                    com.alibaba.fastjson.JSONObject dataSource = com.alibaba.fastjson.JSONObject
//                            .parseObject(JSON.toJSONString(data));
//                    if (ObjectUtil.isNotEmpty(dataSource)) {
//                        Object valByJsonObj = BusinessFieldUtil.getValByJsonObj(dataSource, JSONUtil.toBean(fieldsConfig.getConfigJson(), BusinessFieldConfig.class));
//                        e.setValue(ObjectUtil.isNotEmpty(valByJsonObj) ? valByJsonObj.toString() : "");
//                    }
//                }
//            }
//        });
//    }
//
//    /**
//     * 获取数据源中匹配的值
//     *
//     * @param fieldsConfig 字段配置
//     * @param dataSource   数据源
//     * @return
//     */
//    private String getValueByFormDataSource(TemplateFieldsConfig fieldsConfig, JSONObject dataSource, SimpleDateFormat simpleDateFormat) {
//        JSONObject data = dataSource;
//        String parentFieldName = fieldsConfig.getParentFieldName();
//        String value = null;
//        try {
//            if (StringUtil.isNotBlank(parentFieldName)) {
//                for (String key : Func.toStrList(parentFieldName)) {
//                    data = data.get(key, JSONObject.class);
//                }
//            }
//            value = dealTimeField(data.get(fieldsConfig.getFieldName()), simpleDateFormat).toString();
//        } catch (NullPointerException e) {
//            return value;
//        }
//        return value;
//    }
//
//    /**
//     * 处理时间
//     *
//     * @param o
//     * @return
//     */
//    private Object dealTimeField(Object o, SimpleDateFormat simpleDateFormat) {
//        if (ObjectUtil.isNotEmpty(o)) {
//            //处理时间
//            if (o instanceof LocalDateTime) {
//                return LocalDateTimeUtil.format((LocalDateTime) o, PARSE_TIME_FORMAT);
//            }
//            if (o instanceof LocalDate) {
//                return LocalDateTimeUtil.format((LocalDate) o, PARSE_TIME_FORMAT);
//            }
//            if (o instanceof Date) {
//                return simpleDateFormat.format((Date) o);
//            }
//        }
//        return o;
//    }
//
//    @Override
//    public JSONArray getTemplateFields(String templateId) {
//        return bestSignService.getTemplateFields(templateId);
//    }
//
//    @Override
//    public String skipTemplatePage() {
//        return bestSignService.skipTemplatePage();
//    }
//
//    @Override
//    public String preview(String templateId) {
//        return bestSignService.preview(templateId);
//    }
//
//    @Override
//    public List<ContractTemplate> listContractTemplate(Query query) {
//        List<SsqTemplate> ssqTemplates = bestSignService.listContractTemplate(query);
//        //将结果转为精锐模板
//        return ssqTransJRFormat(ssqTemplates);
//    }
//
    @Override
    public SsqContractInfo getContractInfo(String contractId) {
        return bestSignService.getContractInfo(contractId);
    }

    //
//    @Override
//    public void contractFileSign(Contract contract, ContractConfig contractConfig, Long userId, String fileBase64, Long signAttachId) {
//
//    }
//
//    @Override
//    public SsqUserBaseInfo userBaseInfo(String account) {
//        return bestSignService.userBaseInfo(account);
//    }
//
//    @Override
//    public SsqCertInfo checkCerInfo(Long userId) {
//        //查询证书编号
//        return bestSignService.checkCerInfo(userId.toString());
//    }
//
//    @Override
//    public ContractReturnData contractResend(ContractVO contract) {
//        ContractTemplate contractTemplate = contract.getContractTemplate();
//        SsqContractGenParam contractGenParam = SsqContractGenParam.builder()
//                .expireDay(7)
//                .templateId(contractTemplate.getTemplateId())
//                .templateName(contractTemplate.getTemplateName())
//                .build();
//        return bestSignService.contractResend(contractGenParam, contract.getContractVar());
//    }
//
    @Override
    public void delayExpireTime(List<String> contractId, String time) {
        bestSignService.delayExpireTime(contractId, time);
    }

    //
//    @Override
//    public void templateAutoSign(SignParam signParam) {
//        Integer userType = signParam.getCustomerType();
//        Long signerId = signParam.getSignerId();
//        String signOrStampName = signParam.getSignOrStampName();
//        //获取当前签署人签署配置
//        List<ContractSignConfig> contractSignConfigs = signParam.getContractSignConfigs();
//        Map<Integer, ContractSignConfig> signConfigMap = contractSignConfigs.stream().collect(Collectors.toMap(ContractSignConfig::getSignType, e -> e, (oldVal, newVal) -> oldVal));
//        JSONObject signVars = new JSONObject();
//        ContractSignConfig stampConfig = signConfigMap.get(ContractEnum.signType.STAMP.getType());
//        ContractSignConfig timeConfig = signConfigMap.get(ContractEnum.signType.TIME.getType());
//        ContractSignConfig signConfig = signConfigMap.get(ContractEnum.signType.SIGN.getType());
//        //企业签署变量
//        if (CustomerTypeEnum.ENTERPRISE.getCode().equals(userType)) {
//            //若个人签署字段不为空 并且个人账号存在使用个人账户进行个人签署
//            if (ObjectUtil.isNotEmpty(stampConfig)) {
//                signVars.putOnce(stampConfig.getKeywords(), varsChange(signOrStampName, "", signerId.toString()));
//            }
//            Long personId = signParam.getPersonalUserId();
//            // 说明包含个人签名
//            if (ObjectUtil.isNotEmpty(signConfig) && ObjectUtil.isNotEmpty(personId)) {
//                JSONObject companyUsePersonSignVar = new JSONObject();
//                companyUsePersonSignVar.putOnce(signConfig.getKeywords(), varsChange(signOrStampName, signParam.getCustomizeWriteBase64(), personId.toString()));
//                if (ObjectUtil.isNotEmpty(stampConfig)) {
//                    companyUsePersonSignVar.putOnce(stampConfig.getKeywords(), varsChange(signOrStampName, "", signerId.toString()));
//                }
//                if (ObjectUtil.isNotEmpty(timeConfig)) {
//                    companyUsePersonSignVar.putOnce(timeConfig.getKeywords(), varsChange("", "", signerId.toString()));
//                }
//                signParam.setVarsJSON(companyUsePersonSignVar);
//                signParam.setSignerId(personId);
//                contractAutoSign(signParam);
//                return;
//            }
//        } else {
//            signVars.putOnce(signConfig.getKeywords(), varsChange(signOrStampName, signParam.getCustomizeWriteBase64(), signerId.toString()));
//        }
//        if (ObjectUtil.isNotEmpty(timeConfig)) {
//            signVars.putOnce(timeConfig.getKeywords(), varsChange("", "", signerId.toString()));
//        }
//        signParam.setSignerId(signerId);
//        signParam.setVarsJSON(signVars);
//        contractAutoSign(signParam);
//    }
//
    @Override
    public SsqSignaturePositions getSignaturePositions(String fileBase64, String signAlign, String keywords) {
        return bestSignService.getSignaturePositions(fileBase64, signAlign, keywords);
    }

    //
//    @Override
//    public void reApplyCert(Long signerId) {
//        bestSignService.reApplyCert(signerId.toString());
//    }
//
//    @Override
//    public void regPerson(RegEleSignDTO regEleSignDTO) {
//        PersonEAuthInfoDTO personalCredential = bestSignService.getPersonalCredential(regEleSignDTO.getAccount());
//        if (ObjectUtil.isNotEmpty(personalCredential)) {
//            return;
//        }
//        SSqCredential credential = SSqCredential.builder()
//                .identity(regEleSignDTO.getIdentity())
//                .identityType("0")
//                .contactMobile(regEleSignDTO.getMobile())
//                .contactMail("")
//                .province("")
//                .city("")
//                .address("").build();
//        SSqPersonAuth personAuth = SSqPersonAuth.builder()
//                .account(regEleSignDTO.getAccount().toString())
//                .name(regEleSignDTO.getName())
//                .userType("1")
//                .mail("")
//                .mobile(regEleSignDTO.getAccount())
//                .applyCert("1")
//                .credential(credential).build();
//        String taskId = bestSignService.regPersonUser(personAuth);
//        //申请签名
//        bestSignService.reApplyCert(regEleSignDTO.getAccount());
//    }
//
//    @Override
//    public void regEnt(RegEntEleSignDTO regEntEleSignDTO) {
//        EntEAuthInfoDTO eAuthInfoDTO = bestSignService.getEntCredential(regEntEleSignDTO.getAccount());
//        if (ObjectUtil.isNotEmpty(eAuthInfoDTO)) {
//            return;
//        }
//        SSqEntCredential entCredential = new SSqEntCredential();
//        entCredential.setEnterpriseIdentityType("8");
//        entCredential.setRegCode(regEntEleSignDTO.getRegCode());
//        entCredential.setOrgCode("");
//        entCredential.setTaxCode("");
//        entCredential.setLegalPerson(regEntEleSignDTO.getName());
//        entCredential.setLegalPersonIdentity(regEntEleSignDTO.getLegalPersonIdentity());
//        entCredential.setLegalPersonMobile(regEntEleSignDTO.getLegalPersonMobile());
//
//        entCredential.setLegalPersonIdentityType("0");
//        entCredential.setContactMobile(regEntEleSignDTO.getContactMobile());
//        entCredential.setContactMail("");
//        entCredential.setProvince("");
//        entCredential.setCity("");
//        entCredential.setAddress("");
//        SSqEntReg build = SSqEntReg.builder()
//                .account(regEntEleSignDTO.getAccount())
//                .name(regEntEleSignDTO.getName())
//                .userType("2")
//                .mail("")
//                .mobile("")
//                .applyCert("1")
//                .credential(entCredential).build();
//        bestSignService.regEntUser(build);
//        //签名
//        bestSignService.reApplyCert(regEntEleSignDTO.getAccount());
//    }
//
    @Override
    public JSONObject getContractStatus(String contractId) {
        return bestSignService.getContractStatus(contractId);
    }
//
//    @Override
//    public void contractAutoSign(SignParam signParam) {
//        bestSignService.contractAutoSign(signParam.getContractId(), signParam.getTemplateId(), signParam.getVarsJSON());
//    }
//
//    private JSONObject varsChange(String signOrStampName, String signatureImageData, String account) {
//        JSONObject signOrStampVar = new JSONObject();
//        signOrStampVar.putOnce("account", account);
//        if (StringUtil.isNotBlank(signatureImageData)) {
//            signOrStampVar.putOnce("signatureImageData", signatureImageData);
//        } else if (StringUtil.isNotBlank(signOrStampName)) {
//            signOrStampVar.putOnce("signatureImageName", signOrStampName);
//        }
//        return signOrStampVar;
//    }
//

    /**
     * 自定义模板引擎自动签
     *
     * @param signParam
     */
    @Override
    public void customizeAutoSign(SignParam signParam) {
        Contract contract = signParam.getContract();
        String signKeywordJsonStr = contract.getSignKeywordJson();
        Long signerId = signParam.getSignerId();
        IContractOperatorService operatorService = SpringUtil.getBean(IContractOperatorService.class);
        //获取当前签署人顺序
        String contractId = contract.getContractId();
        //这里是谁先签，谁就按照关键字顺序先执行，现把关键字顺序作为类型，1：买家签的位置，2：卖家签的位置
//        Integer signerSort = operatorService.currentCountByContractId(contractId, signerId);
        Integer signerSort = signParam.getCustomerTradingType();
        //根据签署人顺序 获取关键字配置选项
        IContractSignConfigService signConfigService = SpringUtil.getBean(IContractSignConfigService.class);
        List<ContractSignConfig> signConfigs = signConfigService.listByTemplateIdAndSort(contract.getContractTemplateId(), signerSort);
        if (CollectionUtil.isEmpty(signConfigs)) {
            throw new ServiceException("该合同不可继续签署或签署人员配置错误");
        }
        //获取签署关键字信息
        JSONObject signKeywordJson = JSONUtil.parseObj(signKeywordJsonStr);
        //根据用户类型进行签署
        Integer userType = signParam.getCustomerType();
        if (CustomerTypeEnum.PERSONAL.getCode().equals(userType)) {
            personCustomizeAutoSign(signParam, signConfigs, signKeywordJson);
        } else {
            companyCustomizeAutoSign(signParam, signConfigs, signKeywordJson);
        }
    }

    //
    private void companyCustomizeAutoSign(SignParam signParam, List<ContractSignConfig> signConfigs, JSONObject signKeywordJson) {
        JSONObject req = new JSONObject();
        req.putOnce("contractId", signParam.getContractId());
        req.putOnce("signerAccount", signParam.getSignerId());
//        IContractConfigService contractConfigService = getContractConfigService();
        IContractConfigService contractConfigService = SpringUtil.getBean(IContractConfigService.class);
        String name = contractConfigService.getImgNameByCompanyId(signParam.getSignerId(), ContractConfigEnum.CATEGORY.SEAL.getCode());
        req.putOnce("signatureImageData", name);
        //构造签署位置参数
        JSONArray array = new JSONArray();
        for (ContractSignConfig signConfig : signConfigs) {
            Integer signType = signConfig.getSignType();
            String keywords = signConfig.getKeywords();
            if (signKeywordJson.containsKey(keywords)) {
                JSONObject signPosition = signKeywordJson.getJSONObject(keywords);
                //若企业需要签名 则使用其个人账户进行签名
                if (ContractEnum.signType.SIGN.getType().equals(signType)) {
                    customizeCompanyUsePersonAccountToSign(signParam, signPosition);
                    continue;
                }
                //签署日期需添加标识符
                if (ContractEnum.signType.TIME.getType().equals(signType)) {
                    signPosition.putOnce("type", "date");
                    signPosition.putOnce("dateTimeFormat", LocalDateTimeUtil.format(LocalDate.now(), "yyyy-MM-dd"));
                }
                array.add(signPosition);
            }
        }
        req.putOnce("signaturePositions", array);
        connector.post(BestSignPathEnum.CONTRACT_SIGN_CERT.getPath(), JSONUtil.toJsonStr(req));
    }

    /**
     * 自定义模板企业使用其个人账户进行签名
     *
     * @param signParam
     * @param signPosition
     */
    private void customizeCompanyUsePersonAccountToSign(SignParam signParam, JSONObject signPosition) {
        String customizeWriteBase64 = signParam.getCustomizeWriteBase64();
        String contractId = signParam.getContractId();
        Long personalUserId = signParam.getPersonalUserId();
        JSONObject req = new JSONObject();
        req.putOnce("contractId", contractId);
//        req.putOnce("signerAccount", personalUserId.toString());
        req.putOnce("signerAccount", personalUserId + "person");
        //使用手写板或是设置的签名图片进行签名
        if (StringUtil.isNotBlank(customizeWriteBase64)) {
            req.putOnce("signatureImageData", customizeWriteBase64);
        } else {
            IContractConfigService contractConfigService = SpringUtil.getBean(IContractConfigService.class);
            String name = contractConfigService.getImgNameByCompanyId(personalUserId, ContractConfigEnum.CATEGORY.SIGN.getCode());
            req.putOnce("signatureImageName", name);
        }
        JSONArray array = new JSONArray();
        array.add(signPosition);
        req.putOnce("signaturePositions", array);
        connector.post(BestSignPathEnum.CONTRACT_SIGN_CERT.getPath(), JSONUtil.toJsonStr(req));
    }
//

    /**
     * 自定义模板进行个人自定义模板签名
     *
     * @param signParam
     * @param signConfigs
     * @param signKeywordJson
     */
    private void personCustomizeAutoSign(SignParam signParam, List<ContractSignConfig> signConfigs, JSONObject signKeywordJson) {
        JSONObject req = new JSONObject();
        req.putOnce("contractId", signParam.getContractId());
        req.putOnce("signerAccount", signParam.getSignerId());
        //若用户传入手写板签名 使用手写板签名
        String customizeWriteBase64 = signParam.getCustomizeWriteBase64();
        if (StringUtil.isNotBlank(customizeWriteBase64)) {
            req.putOnce("signatureImageData", customizeWriteBase64);
        } else {
//            IContractConfigService contractConfigService = getContractConfigService();
            IContractConfigService contractConfigService = SpringUtil.getBean(IContractConfigService.class);
            String name = contractConfigService.getImgNameByCompanyId(signParam.getSignerId(), ContractConfigEnum.CATEGORY.SIGN.getCode());
            req.putOnce("signatureImageName", name);
        }
        //构造签署位置参数
        JSONArray array = new JSONArray();
        for (ContractSignConfig signConfig : signConfigs) {
            Integer signType = signConfig.getSignType();
            String keywords = signConfig.getKeywords();
            if (signKeywordJson.containsKey(keywords) && !ContractEnum.signType.STAMP.getType().equals(signType)) {
                JSONObject signPosition = signKeywordJson.getJSONObject(keywords);
                //签署日期需添加标识符
                if (ContractEnum.signType.TIME.getType().equals(signType)) {
                    signPosition.putOnce("type", "date");
                    signPosition.putOnce("dateTimeFormat", LocalDateTimeUtil.format(LocalDate.now(), "MM-dd-yyyy"));
                }
                array.add(signPosition);
            }
        }
        req.putOnce("signaturePositions", array);
        connector.post(BestSignPathEnum.CONTRACT_SIGN_CERT.getPath(), JSONUtil.toJsonStr(req));
    }

    //
//    /**
//     * 获取关键字
//     *
//     * @param fileBase64
//     * @param signConfig
//     * @return
//     */
//    private SsqSignaturePositions getSignaturePositions(String fileBase64, ContractSignConfig signConfig) {
//        return bestSignService.getSignaturePositions(fileBase64, signConfig.getSignAlign(), signConfig.getKeywords());
//    }
//
//    /**
//     * @param keyWordsPositions 关键字位置信息
//     * @param signConfig        关键字配置
//     */
//    private SsqSignaturePositions buildPosition(SSqKeyWordsPosition keyWordsPositions, ContractSignConfig signConfig) {
//        SsqSignaturePositions positions = new SsqSignaturePositions();
//        String signAlign = signConfig.getSignAlign();
//        //1 左上角 2 右上角 3 左下角 4 右下角
//        String x;
//        String y;
//        switch (signAlign) {
//            case "1":
//                x = keyWordsPositions.getX1();
//                y = keyWordsPositions.getY1();
//                break;
//            case "2":
//                x = keyWordsPositions.getX2();
//                y = keyWordsPositions.getY1();
//                break;
//            case "3":
//                x = keyWordsPositions.getX1();
//                y = keyWordsPositions.getY2();
//                break;
//            case "4":
//                x = keyWordsPositions.getX2();
//                y = keyWordsPositions.getY2();
//                break;
//            default:
//                throw new ServiceException("关键字坐标配置错误，请联系管理员");
//        }
//        positions.setX(x);
//        positions.setY(y);
//        positions.setPageNum(keyWordsPositions.getPageNum());
//        return positions;
//    }
//
    @Override
    public BladeFile downLoadContract(Contract contract) {
        return bestSignService.downLoadContract(contract.getContractId(), contract.getContractTitle() + IdWorker.getIdStr() + ".pdf");
    }
//
//    private BladeFile downLoadContractById(String contractId, String contractTitle) {
//        return bestSignService.downLoadContract(contractId, contractTitle + IdWorker.getIdStr() + ".pdf");
//    }

    @Override
    public void contractRevoke(String contractId) {
        bestSignService.contractRevoke(contractId);
    }
//
//    @Override
//    public void contractRevoke(List<String> contractIds) {
//        if (CollectionUtil.isNotEmpty(contractIds)) {
//            for (String contractId : contractIds) {
//                try {
//                    contractRevoke(contractId);
//                    //TODO 需要做一个失败重试
//                } catch (Exception e) {
//                    log.info(e.getMessage());
//                }
//            }
//        }
//    }
//
//    @Override
//    public String previewContract(String contractId, String userId) {
//        return bestSignService.previewContract(contractId, userId);
//    }
//
//    @Override
//    public String skipToSign(SsqSkipSignParam skipSignParam) {
//        return bestSignService.skipToSign(skipSignParam);
//    }
//

    /**
     * 锁定合同
     *
     * @param contractIds
     */
    @Override
    public void contractLock(String contractIds) {
        bestSignService.contractLock(contractIds);
    }
//
//    @Override
//    public String platTurnToContractPreView(String contractId) {
//        ApiParamDTO apiParams = getApiParams();
//        return previewContract(contractId, apiParams.getParamMap().get("account"));
//    }
//
//    /**
//     * 上上签模板转精锐模板
//     *
//     * @param ssqTemplates
//     * @return
//     */
//    private List<ContractTemplate> ssqTransJRFormat(List<SsqTemplate> ssqTemplates) {
//        List<ContractTemplate> contractTemplates = new ArrayList<>(ssqTemplates.size());
//        ssqTemplates.forEach(e -> {
//            ContractTemplate template = new ContractTemplate();
//            template.setTemplateId(e.getTid());
//            template.setTemplateName(e.getTitle());
//            template.setTemplateCategory(e.getCategoryName());
//            template.setApiSupplier(ApiSupplier.BEST_SIGN.getCode());
//            template.setContractGenType(1);
//            contractTemplates.add(template);
//        });
//        return contractTemplates;
//    }
//
//    /**
//     * 获取Api链接参数
//     *
//     * @return
//     */
//    @Override
//    public ApiParamDTO getApiParams() {
//        return super.getApiParams();
//    }
}
