package cn.lili.modules.jrzh_contract.contract_api.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 异步通知枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum AsyncNotifyEnum {
	SIGN_CONTRACT("signContract", "签署合同"),
	SIGN_CATALOG_CONTRACT("signCatalogContract", "签署多文档"),
	CERT_APPLY("certApply", "异步申请证书"),
	CREATE_TEMPLATE("createTemplate", "创建模板"),
	MODIFY_TEMPLATE("modifyTemplate", "编辑模板"),
	onlinePay("onlinePay", "支付接口"),
	checkContract("checkContract", "合同有效性检查结果"),
	/**
	 * 支付宝刷脸结果推送
	 */
	ALIPAYMP_FACE_AUTH("alipaympFaceAuth", "支付宝刷脸结果推送"),
	/**
	 * h5刷脸结果推送
	 */
	H5_FACE_AUTH("h5FaceAuth","h5刷脸结果推送")
	;
	private String method;
	private String desc;

	/**
	 * 获取描述
	 *
	 * @param method
	 * @return
	 */
	public static String getDescByMethod(String method) {
		for (AsyncNotifyEnum methodEnum : AsyncNotifyEnum.values()) {
			if (methodEnum.getMethod().equals(method)) {
				return methodEnum.getDesc();
			}
		}
		return null;
	}

}
