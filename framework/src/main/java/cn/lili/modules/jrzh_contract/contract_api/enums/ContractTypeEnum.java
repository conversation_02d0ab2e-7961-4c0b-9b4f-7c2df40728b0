package cn.lili.modules.jrzh_contract.contract_api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 合同类型枚举
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Getter
@AllArgsConstructor
public enum ContractTypeEnum {

    /**
     * 现货合同
     */
    SPOT(1, "现货"),

    /**
     * 期货合同
     */
    FUTURES(2, "期货");

    /**
     * 类型代码
     */
    private final Integer code;

    /**
     * 类型描述
     */
    private final String description;

    /**
     * 根据代码获取枚举
     *
     * @param code 类型代码
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static ContractTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ContractTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据交易模式获取合同类型
     *
     * @param tradeModel 交易模式：1-现货交易 2-期货交易 3-混合交易
     * @return 合同类型，默认返回现货
     */
    public static ContractTypeEnum getByTradeModel(Integer tradeModel) {
        if (tradeModel == null) {
            return SPOT;
        }
        switch (tradeModel) {
            case 2:
            case 3:
                return FUTURES;
            default:
                return SPOT;
        }
    }
}
