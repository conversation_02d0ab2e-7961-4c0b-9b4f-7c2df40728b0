/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.vo;

import cn.lili.modules.jrzh_contract.contract_api.dto.SmsCodeDTO;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * 合同签署请求参数
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Data
@ApiModel(value = "ContractConfigVO对象", description = "合同签署请求参数")
public class ContractSignVO implements Serializable {
	private static final long serialVersionUID = 1L;
	private String contractId;
	private String returnUrl;
	private String customizeWriteBase64;
	private Integer verifyType;
	private SmsCodeDTO code;
	private Integer autoLock;
	/**
	 * 后台签署时传入 用于校验
	 */
	private Long deptId;
}
