/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.modules.jrzh_bases.BladeFile;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractConfig;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * 合同配置 服务类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
public interface IContractConfigService extends IService<ContractConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param contractConfig
	 * @return
	 */
//	IPage<ContractConfigVO> selectContractConfigPage(IPage<ContractConfigVO> page, ContractConfigVO contractConfig);

	/**
	 * 创建签章图片
	 *
	 * @param type    第三方模板ID
	 * @param request 请求数据
	 * @return 签章图片
	 */
	BladeFile createSignImg(Integer type, Map<String, Object> request, String companyId, Integer customerType);

	/**
	 * 详情
	 *
	 * @return 详情信息
	 */
//	ContractConfigVO detail(Long companyId);


	/**
	 * 提交申请
	 *
	 * @param contractConfig 合同配置
	 * @return 是否成功
	 */
//	boolean submit(ContractConfigDTO contractConfig, String companyId, Integer customerType);

	/**
	 * 根据企业id获取合同配置
	 *
	 * @param companyId 企业id
	 * @return 企业的合同配置
	 */
	ContractConfig getByCompanyId(Long companyId);

	/**
	 * 添加 账户签名/印章设置
	 *
//	 * @param customerId   账户id
//	 * @param customerType 账户类型
	 */
	void addSignSealConfig(String customerId, Integer customerType);

	String getImgNameByCompanyId(Long companyId, Integer category);
	
}
