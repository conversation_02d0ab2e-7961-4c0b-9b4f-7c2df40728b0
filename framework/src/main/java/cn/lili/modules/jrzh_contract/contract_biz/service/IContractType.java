/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service;


import cn.lili.modules.jrzh_contract.contract_api.dto.DynamicContractDTO;
import cn.lili.modules.jrzh_contract.contract_api.dto.SignParam;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractReturnData;

import java.util.List;

/**
 * 合同模板种类
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
public interface IContractType {
    /**
     * 预览模板
     *
     * @param contractTemplate
     * @return
     */
    String preViewTemplate(ContractTemplate contractTemplate);

//    /**
//     * 装载合同数据源
//     */
//    void loadingDataSource(DynamicContractDTO dynamicContractDTO);
//
    /**
     * 合同生成
     *
     * @return
     */
    ContractReturnData gen(DynamicContractDTO dynamicContractDTO);

    /**
     * 合同签署
     */
    Contract sign(SignParam signParam);

    /**
     * 合同类型
     *
     * @return
     */
    Integer support();

    /**
     * 加载数据源配置信息
     *
     * @param templateConfigVO
     * @return
     */
//    List<IContractDataSource> getConfigDataSource(ContractTemplateConfigVO templateConfigVO, List<IContractDataSource> contractDataSourceList);
}
