package cn.lili.modules.jrzh_contract.contract_biz.handler;

import cn.hutool.extra.spring.SpringUtil;
import cn.lili.modules.jrzh_other.core_api.constant.OtherApiTypeEnum;
import cn.lili.modules.jrzh_other.core_api.dto.ApiParamDTO;
import lombok.RequiredArgsConstructor;


/**
 * 电子签抽象类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public abstract class ElecSignAbsHandler implements ElecSignHandler {
	final static String CODE = OtherApiTypeEnum.ELEC_SIGN.getCode();

	/**
	 * 获取Api链接参数
	 *
	 * @return
	 */
//	public ApiParamDTO getApiParams() {
//		return SpringUtil.getBean(IOthersApiService.class).getSingleParamByTypeCode(CODE);
//	}
}
