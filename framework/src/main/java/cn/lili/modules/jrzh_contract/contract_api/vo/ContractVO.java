package cn.lili.modules.jrzh_contract.contract_api.vo;

import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ContractVO extends Contract {
    private static final long serialVersionUID = 1L;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 合同状态
     */
    private String contractStatus;

    /***
     * 流程类型
     */
    private String contractProcessType;

    /**
     * 产品id
     */
    private String goodIds;
    /**
     * 合同持有人
     */
    private Long userId;
    private ContractTemplate contractTemplate;
    /**
     * 流程类型名称
     */
    private String typeName;
    /**
     * 产品类型
     */
    private List<Integer> goodsTypeList;
}
