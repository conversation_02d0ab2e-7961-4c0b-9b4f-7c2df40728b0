/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.wrapper;


import cn.hutool.core.bean.BeanUtil;
import cn.lili.modules.jrzh_bases.BaseEntityWrapper;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractVO;

import java.util.Objects;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
public class ContractWrapper extends BaseEntityWrapper<Contract, ContractVO> {

	public static ContractWrapper build() {
		return new ContractWrapper();
	}

	@Override
	public ContractVO entityVO(Contract contract) {
		ContractVO contractVO = Objects.requireNonNull(BeanUtil.copyProperties(contract, ContractVO.class));
//		if (ObjectUtil.isNotEmpty(contractVO.getGoodsId())) {
//			GoodsVO goods = SpringUtil.getBean(IGoodsService.class).selectDetailById(contractVO.getGoodsId());
//			if (ObjectUtil.isNotEmpty(goods)) {
//				contractVO.setGoodType(goods.getType());
//				contractVO.setGoodName(goods.getGoodsName());
//			}
//		}
		return contractVO;
	}

}
