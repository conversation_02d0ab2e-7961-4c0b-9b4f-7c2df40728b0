/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.vo;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.TemplateFieldsConfig;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 合同模板方案表视图实体类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractTemplateConfigVO对象", description = "合同模板方案表")
public class ContractTemplateConfigVO extends ContractTemplateConfig {
	private static final long serialVersionUID = 1L;
	/**
	 * 字段配置类
	 */
	List<TemplateFieldsConfig> templateFieldsConfigs;
}
