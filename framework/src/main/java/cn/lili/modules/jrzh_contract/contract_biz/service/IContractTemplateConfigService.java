package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateCategoryConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateGoodsConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateCategoryConfigVO;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateGoodsConfigVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 合同模板配置服务接口
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
public interface IContractTemplateConfigService {

    /**
     * 根据商品ID获取最适合的合同模板
     * 优先级：商品专用模板 > 分类模板 > 默认模板
     *
     * @param goodsId 商品ID
     * @return 合同模板
     */
    ContractTemplate getBestMatchTemplate(String goodsId);

    /**
     * 根据商品ID获取最适合的合同模板（兼容方法）
     * 优先级：商品专用模板 > 分类模板 > 默认模板
     * 注意：contractType参数已废弃，不再区分合同类型
     *
     * @param goodsId 商品ID
     * @param contractType 合同类型（已废弃，传入任何值都会被忽略）
     * @return 合同模板
     */
    ContractTemplate getBestMatchTemplate(String goodsId, Integer contractType);

    /**
     * 获取默认模板
     * 查找defaultContractType=1的模板作为默认模板
     * 注意：contractType参数已废弃，传入任何值都会被忽略
     *
     * @param contractType 合同类型（已废弃，传入任何值都会被忽略）
     * @return 默认合同模板（defaultContractType=1的模板）
     */
    ContractTemplate getDefaultTemplate(Integer contractType);

    /**
     * 根据商品分类获取模板配置
     * 每个分类可以绑定多个合同模板
     *
     * @param categoryId 分类ID
     * @return 模板配置列表
     */
    List<ContractTemplateCategoryConfig> getCategoryConfigs(String categoryId);

    /**
     * 批量根据商品分类获取模板配置
     * 每个分类可以绑定多个合同模板
     *
     * @param categoryIds 分类ID列表
     * @return 模板配置列表
     */
    List<ContractTemplateCategoryConfig> getBatchCategoryConfigs(List<String> categoryIds);

    /**
     * 根据商品ID获取模板配置
     * 一个商品只能绑定一个合同模板
     *
     * @param goodsId 商品ID
     * @return 模板配置列表
     */
    List<ContractTemplateGoodsConfig> getGoodsConfigs(String goodsId);

    /**
     * 根据商品ID获取所有分类层级绑定的合同模板列表
     * 用于店铺端商品发布时选择合同模板
     * 返回该商品所属分类的所有层级绑定的模板，供店铺选择
     *
     * @param goodsId 商品ID
     * @return 合同模板列表
     */
    List<ContractTemplate> getAvailableTemplatesForGoods(String goodsId);

    /**
     * 根据商品分类路径获取所有层级绑定的合同模板列表
     * 用于店铺端商品发布时选择合同模板
     * 返回分类路径中所有层级分类绑定的模板，供店铺选择
     *
     * @param categoryPath 分类路径，格式如：1,2,3
     * @return 合同模板列表
     */
    List<ContractTemplate> getAvailableTemplatesByCategoryPath(String categoryPath);

    /**
     * 根据商品ID查询绑定的合同模板ID
     * 用于合同生成时直接获取模板
     * 一个商品只能绑定一个合同模板
     *
     * @param goodsId 商品ID
     * @return 合同模板ID，如果未绑定则返回null
     */
    String getGoodsBoundTemplateId(String goodsId);

    /**
     * 保存或更新分类模板配置
     * 如果配置已存在则更新，否则新增
     *
     * @param config 分类配置
     * @return 保存结果
     */
    boolean saveOrUpdateCategoryConfig(ContractTemplateCategoryConfig config);

    /**
     * 保存分类模板配置（向后兼容方法）
     * 实际调用 saveOrUpdateCategoryConfig
     *
     * @param config 分类配置
     * @return 保存结果
     */
    default boolean saveCategoryConfig(ContractTemplateCategoryConfig config) {
        return saveOrUpdateCategoryConfig(config);
    }

    /**
     * 保存或更新商品模板配置
     * 如果配置已存在则更新，否则新增
     *
     * @param config 商品配置
     * @return 保存结果
     */
    boolean saveOrUpdateGoodsConfig(ContractTemplateGoodsConfig config);

    /**
     * 保存商品模板配置（向后兼容方法）
     * 实际调用 saveOrUpdateGoodsConfig
     *
     * @param config 商品配置
     * @return 保存结果
     */
    default boolean saveGoodsConfig(ContractTemplateGoodsConfig config) {
        return saveOrUpdateGoodsConfig(config);
    }

    /**
     * 删除分类模板配置
     *
     * @param configId 配置ID
     * @return 删除结果
     */
    boolean deleteCategoryConfig(String configId);

    /**
     * 删除商品模板配置
     *
     * @param configId 配置ID
     * @return 删除结果
     */
    boolean deleteGoodsConfig(String configId);

    /**
     * 批量配置分类模板
     *
     * @param categoryIds 分类ID列表
     * @param templateId 模板ID
     * @return 配置结果
     */
    boolean batchConfigCategoryTemplate(List<String> categoryIds, String templateId);

    /**
     * 分页查询分类模板配置
     *
     * @param page 分页参数
     * @param categoryId 分类ID
     * @param templateId 模板ID
     * @return 分页结果
     */
    IPage<ContractTemplateCategoryConfigVO> getCategoryConfigPage(Page<ContractTemplateCategoryConfig> page, String categoryId, String templateId);

    /**
     * 分页查询商品模板配置
     *
     * @param page 分页参数
     * @param goodsId 商品ID
     * @param templateId 模板ID
     * @return 分页结果
     */
    IPage<ContractTemplateGoodsConfigVO> getGoodsConfigPage(Page<ContractTemplateGoodsConfig> page, String goodsId, String templateId);
}
