/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.customer_ct.service;


import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.vo.TemplateCustomerVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 产品合同模板 服务类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
public interface ITemplateCustomerService extends IService<ContractTemplate> {
    /**
     * 根据产品id查询产品合同模板
     *
     * @param goodsId
     * @return
     */
//    List<GoodsContractTemplateVO> getGoodsContractTemplateListByGoodsId(Long goodsId);

    /**
     * 合同模板查询
     *
     * @param signUser       签署用户
     * @param update         是否拉取状态
     * @param contractIds    合同ids
     * @param bizNos         业务编号列表
     * @param bigBizNos      大业务编号列表
     * @return
     */
    List<TemplateCustomerVO> selectContractTemplateAndContractStatusList(Integer signUser, boolean update, String contractIds, String bizNos, String bigBizNos);

    /**
     * 检查重复合同模板
     *
     * @param goodsContractTemplates
     */
//    void checkDuplicateContracts(List<GoodsContractTemplateVO> goodsContractTemplates);
}
