package cn.lili.modules.jrzh_contract.contract_api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 动态生成合同
 * <AUTHOR>
 */
public interface DynamicTemplateEnum {

	/**
	 * 排序字段
	 */
	String  TABLE_ORDER_FEILD="create_time";


	String  CAPITAL_TABLE_NAME="blade_dept";



	String ENTERPRISE_QUOTA="jrzh_enterprise_quota";



	String GENERATE_CONTRACT_URL="/sendDynamicContract/generateContractWithBusiness";


	/**
	  证件类型需要转化
	 */
	String  DOCUMENT_TYPE="证件类型";


	/**
	 * 产品名称
	 */
	String  GOODS_NAME="产品名称";


	/**
	 * 核心企业名称
	 */
	String  DEPT_NAME="核心企业名称";


	/**
	 *单表通过id且通过id就能查询出来的字段,并且此表需要查询的字段就1~3个  多了就要用另一种方法
	 */
	String  SINGLE_QUERY_BUSINESS_FEILD="产品名称,核心企业名称";



	String SPECIAL_DEAL_FEILD="资方名称";



	String  SINGLE_QUERY_TABLE_NAME="blade_dept,jrzh_goods";

	/**
     最高额担保函结束时间需要加上半年
     */
	String  END_TIME_MAX_GUARANTEE_LETTER="最高额担保函结束时间";


	/**
	 * 最高额担保函开始时间格式化
	 */
	String  SATRT_TIME_MAX_GUARANTEE_LETTER="最高额保证担保函开始时间";


	/**
	 * 时间
	 */
	String  TIME_INCLUDE="时间";





	String  IS_CONTRACT="合同,协议,担保函,授权书";



	String  NO_CONTRACT_FEILD="编号";

	@Getter
	@AllArgsConstructor
	enum templateEnum implements DynamicTemplateEnum {




		/***
		 * 个人信息查询授权书
		 */
		personInfoQueryEnum("2968917604523049989", "2968918113459897352", "个人信息查询授权书"),


		/**
		 * 企业相关授权书
		 */
		entRelevantCetificate("2969601997335954434", "", "企业相关授权书");

		/**
		 * 上上签编号
		 */
		private String ssqNo;
		/**
		 * 模板角色id
		 */
		private String roleId;
		/**
		 * 描述
		 */
		private String desc;


	}

	@AllArgsConstructor
	@Getter
	enum IdCardType implements DynamicTemplateEnum {

		/**
		 * 证件类型
		 */

		ID("0", "证件类型", "身份证"),
		PASSPORT("1", "证件类型", "护照");
		private String code;
		private String ColumName;
		private String desc;

		public static String getDescByCode(String code) {
			IdCardType[] idCardTypes = values();
			for (IdCardType idCardType : idCardTypes) {
				if (idCardType.getCode().equals(code)) {
					return idCardType.getDesc();
				}
			}
			return null;
		}
	}

	@AllArgsConstructor
	@Getter
	enum CapitalEnum implements DynamicTemplateEnum {
		/**
		 * 资金方
		 */
		CAPITAL("资方名称", "资方名称");
		private String ssqFeild;
		private String desc;
	}


	@AllArgsConstructor
	@Getter
	enum BusinessIdEnum {
		/**
		 * 资金方
		 */
		CUSTOMER_ID("customer_id", "融资企业客户id"),

		GOODS_ID("goods_id", "产品id"),

		ENTERPRISE_Id("enterprise_id", "核心企业用户id"),

		ENTERPRISE_TYPE("enterprise_type","企业类型"),

		CAPITAL_ID("capital_id", "资金方id");
		private String feildName;
		private String desc;
	}


}
