/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractOperator;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractOperatorMapper;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractOperatorService;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateService;
import cn.lili.modules.jrzh_other.bestsign.constant.ContractEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同签署表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Service
@RequiredArgsConstructor
public class ContractOperatorServiceImpl extends ServiceImpl<ContractOperatorMapper, ContractOperator> implements IContractOperatorService {
	private final IContractTemplateService contractTemplateService;

//	@Override
//	public IPage<ContractOperatorVO> selectContractOperatorPage(IPage<ContractOperatorVO> page, ContractOperatorVO contractOperator) {
//		return page.setRecords(baseMapper.selectContractOperatorPage(page, contractOperator));
//	}
//
	@Override
	public ContractOperator getByContractIdAndCustomerId(String contractId, Long customerId) {
		return getOne(Wrappers.<ContractOperator>lambdaQuery().eq(ContractOperator::getContractId, contractId)
			.eq(ContractOperator::getSignerId, customerId));
	}

	@Override
	public Boolean updateContractStatus(Long id, Integer status) {
		return update(Wrappers.<ContractOperator>lambdaUpdate().set(ContractOperator::getStatus, status).eq(ContractOperator::getId, id));
	}
//
//	@Override
//	public List<ContractOperator> listByContractIdsAndUserId(List<String> contractId, Long userId) {
//		return list(Wrappers.<ContractOperator>lambdaQuery().in(ContractOperator::getContractId, contractId).eq(ContractOperator::getSignerId, userId));
//	}
//
//	@Override
//	public List<ContractOperator> listByContractByCustomerId(Long customerId) {
//		return list(Wrappers.<ContractOperator>lambdaQuery().eq(ContractOperator::getSignerId, customerId));
//	}
//
	@Override
	public void updateStatusByContractId(String contractId, Integer status) {
		update(Wrappers.<ContractOperator>lambdaUpdate().eq(ContractOperator::getContractId, contractId).set(ContractOperator::getStatus, status));
	}

	@Override
	public boolean operatorAllComplate(String contractTemplateId, String contractId) {
		IContractTemplateService templateService = SpringUtil.getBean(IContractTemplateService.class);
		ContractTemplate template = templateService.getByTemplateId(contractTemplateId);
		return count(Wrappers.<ContractOperator>lambdaQuery().eq(ContractOperator::getContractId, contractId)
			.eq(ContractOperator::getStatus, ContractEnum.CONTRACT_OPERATOR_STATUS.SIGN_COMPLETE.getStatus())) == template.getSignerNum();
	}
//
	@Override
	public List<ContractOperator> listByContractId(String contractId) {
		return list(Wrappers.<ContractOperator>lambdaQuery().eq(ContractOperator::getContractId, contractId));
	}

	@Override
	public Integer currentCountByContractId(String contractId, Long userId) {
		return Math.toIntExact(count(Wrappers.<ContractOperator>lambdaQuery().eq(ContractOperator::getContractId, contractId)
				.ne(ContractOperator::getSignerId, userId)) + 1);
	}

	public Integer currentSignedCountByContractId(String contractId) {
		return Math.toIntExact(count(Wrappers.<ContractOperator>lambdaQuery().eq(ContractOperator::getContractId, contractId)
				.eq(ContractOperator::getStatus, ContractEnum.CONTRACT_OPERATOR_STATUS.SIGN_COMPLETE.getStatus())));
	}

	@Override
	public ContractOperator saveOrUpdateContractOperator(ContractOperator contractOperator, ContractTemplate template) {
		if (ObjectUtil.isNotEmpty(contractOperator.getId())) {
			updateById(contractOperator);
			return contractOperator;
		}
		contractOperator.setTemplateName(template.getTemplateName());
		Integer index = currentCountByContractId(contractOperator.getContractId(), contractOperator.getSignerId());
		if (index.compareTo(template.getSignerNum()) > 0) {
			throw new ServiceException("签署失败，签署人已达到设置上限");
		}
		contractOperator.setSorted(index);
		save(contractOperator);
		return contractOperator;
	}
//
//	@Override
//	public Map<String, String> listSignedByContractName(String templateName, String userId) {
//		if (StringUtil.isBlank(templateName)) {
//			return new HashMap<>();
//		}
//		List<ContractOperator> list = list(Wrappers.<ContractOperator>lambdaQuery()
//			.eq(ContractOperator::getSignerId, userId).in(ContractOperator::getTemplateName, Func.toStrList(templateName))
//			.orderByDesc(ContractOperator::getCreateTime)
//			.eq(ContractOperator::getStatus, ContractEnum.CONTRACT_OPERATOR_STATUS.SIGN_COMPLETE.getStatus()));
//		if (CollectionUtil.isEmpty(list)) {
//			return new HashMap<>();
//		}
//		return list.stream().collect(Collectors.toMap(ContractOperator::getTemplateName, e -> e.getContractNo(), (oldVal, newVal) -> oldVal));
//	}
//
//	@Override
//	public Boolean checkCurrentSignerIsSigned(List<String> contractIds, Long userId) {
//		return count(Wrappers.<ContractOperator>lambdaQuery()
//			.eq(ContractOperator::getStatus, ContractEnum.CONTRACT_OPERATOR_STATUS.SIGN_COMPLETE.getStatus())
//			.in(ContractOperator::getContractId, contractIds)
//			.eq(ContractOperator::getSignerId, userId)) == contractIds.size();
//	}
}
