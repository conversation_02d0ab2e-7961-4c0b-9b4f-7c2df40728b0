/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.vo;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractOperator;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 合同详情展示
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@ApiModel(value = "ContractBusinessVO对象", description = "合同业务表")
public class ContractDetailsVO implements Serializable {
	private static final long serialVersionUID = 1L;
	/**
	 * 平台信息
	 */
	private ContractVO contract;
	/**
	 * 签署人信息
	 */
	private List<ContractOperator> contractOperators;
}




