package cn.lili.modules.jrzh_contract.contract_api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface SkipUrlEnum {
	/**
	 *目标地址
	 */
	String getTargetPage();

	/**
	 * 参数名称
	 */
    String getParamName();
	/**
	 *描述
	 */
    String getDesc();
    @AllArgsConstructor
	@Getter
    enum   skipUrl implements  SkipUrlEnum{

		/**
		 * 跳转地址
		 */
		CONTRACT_PREVIEW("targetPage","permissionView","合同预览页")
    	;
    	private  final  String paramName;
    	private  final String targetPage;
    	private  final String desc;

	}
}
