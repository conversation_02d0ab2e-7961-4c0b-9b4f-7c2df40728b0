package cn.lili.modules.jrzh_contract.contract_biz.service.impl;


import cn.lili.modules.jrzh_contract.contract_api.entity.Attach;
import cn.lili.modules.jrzh_bases.IAttachService;
import cn.lili.modules.jrzh_contract.contract_biz.mapper.AttachMapper;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class AttachServiceImpl extends ServiceImpl<AttachMapper, Attach> implements IAttachService {
    public AttachServiceImpl() {
    }

//    public IPage<AttachVO> selectAttachPage(IPage<AttachVO> page, AttachVO attach) {
//        return page.setRecords(((AttachMapper)this.baseMapper).selectAttachPage(page, attach));
//    }
//
//    public List<Attach> listByLinks(List<String> links) {
//        return this.list((Wrapper)Wrappers.lambdaQuery().in(Attach::getLink, links));
//    }
//
//    public Attach getByLink(String link) {
//        return (Attach)this.getOne((Wrapper)((LambdaQueryWrapper)((LambdaQueryWrapper)Wrappers.lambdaQuery().eq(Attach::getLink, link)).orderByDesc(BaseEntity::getCreateTime)).last("limit 1"));
//    }
//
//    public List<Attach> getAttachListByIds(List<Long> idList) {
//        return CollUtil.isNotEmpty(idList) ? this.list((Wrapper)Wrappers.lambdaQuery().in(BaseEntity::getId, idList)) : Collections.EMPTY_LIST;
//    }
}
