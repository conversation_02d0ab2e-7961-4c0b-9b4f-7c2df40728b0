<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractConfigResultMap" type="cn.lili.modules.jrzh_contract.contract_api.entity.ContractConfig">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="contract_id" property="contractId"/>
        <result column="sign_way" property="signWay"/>
        <result column="seal_type" property="sealType"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


    <select id="selectContractConfigPage" resultMap="contractConfigResultMap">
        select * from jrzh_contract_config where is_deleted = 0
    </select>

</mapper>
