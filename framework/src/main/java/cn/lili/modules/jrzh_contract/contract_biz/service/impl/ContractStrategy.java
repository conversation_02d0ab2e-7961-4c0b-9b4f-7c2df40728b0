/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.lili.common.exception.ServiceException;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 策略类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Service
@RequiredArgsConstructor
public class ContractStrategy {
    private final List<IContractType> genContractList;

    public IContractType genContractInstance(Integer type) {
        for (IContractType genContract : genContractList) {
            if (genContract.support().equals(type)) {
                return genContract;
            }
        }
        throw new ServiceException("找不到对应的合同策略方式");
    }
}
