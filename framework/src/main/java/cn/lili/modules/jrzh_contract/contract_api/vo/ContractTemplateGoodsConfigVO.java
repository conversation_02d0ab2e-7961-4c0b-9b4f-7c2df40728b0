package cn.lili.modules.jrzh_contract.contract_api.vo;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateGoodsConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同模板商品配置VO
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "合同模板商品配置VO", description = "包含商品名称等扩展信息")
public class ContractTemplateGoodsConfigVO extends ContractTemplateGoodsConfig {

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "商品分类路径")
    private String categoryPath;

    @ApiModelProperty(value = "商品分类名称")
    private String categoryName;

    @ApiModelProperty(value = "商品价格")
    private Double goodsPrice;

    @ApiModelProperty(value = "商品状态")
    private String goodsStatus;

    @ApiModelProperty(value = "合同类型名称")
    private String contractTypeName;
}
