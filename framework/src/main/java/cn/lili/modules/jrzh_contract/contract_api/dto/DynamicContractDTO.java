package cn.lili.modules.jrzh_contract.contract_api.dto;/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */


import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractOperator;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractSignConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateConfigVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;


/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DynamicContractDTO extends Contract {
    private static final long serialVersionUID = 1L;
    /**
     * 是否需要平台端签署 0非 1是 默认为否
     */
    private Integer needPlatSign;
    /**
     * 合同模板信息 无需填写
     */
    private ContractTemplate contractTemplate;
    /**
     * 模板模式前端传输字段 选填
     */
    private Object frontDataSource;
    /**
     * 模板模式后端数据 选填 k:类clazzName v:实际请求值
     */
    private Map<String, Object> originBackSource;
    /**
     * 需要添加的签署人列表 选填 添加后在合同生成后直接添加该签署人
     */
    private List<ContractOperator> contractOperatorList;

    /**
     * 平台签署人
     *
     * @return
     */
    private Long platSigner;

    /**
     * 合同模板配置
     */
    private ContractTemplateConfigVO contractTemplateConfigVO;

    /**
     * 自定义模板模式 数据源 k:key值 v:值
     */
    private Map<String, Object> customizeSource;
    /**
     * 签署人配置
     */
    private List<ContractSignConfig> contractSignConfigList;
    /**
     * 合同数据源服务 必须 传入 或者 使用方案表
     */
//    private List<IContractDataSource> contractDataSource;
    /**
     * 额外参数 融资编号
     */
    private String financeNo;

    /**
     * 订单编号
     */
//    private String orderSn;
}
