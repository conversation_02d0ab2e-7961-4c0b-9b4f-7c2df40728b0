package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.jrzh_bases.Query;
import cn.lili.modules.jrzh_contract.contract_api.dto.ContractParamDTO;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractDetailsVO;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 * 合同服务类
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
public interface IContractService extends IService<Contract> {

    /**
     * 下载合同
     *
     * @param contractId 合同id
     * @return
     */
//    String downLoadPDF(String contractId);

    /**
     * 合同分页查询
     *
     * @param page
     * @param contract
     * @return
     */
    IPage<ContractVO> selectContractPage(IPage page, ContractVO contract);

    /**
     * 合同分页查询
     *
     * @param contract
     * @return
     */
    IPage<ContractVO> getContractList(Contract contract, PageVO vo);

    /**
     * 获取合同详情
     *
     * @param contractId
     * @return
     */
    ContractDetailsVO getDetails(String contractId);

    /**
     * 指定用户预览合同
     */
//    String preViewWithAccountId(String contractId, String accountId);

    /***
     * 开发者预览合同
     **/
//    String preViewWithDevAccount(String contractId);

    /**
     * 检查当前签署人是否签署
     *
     * @param contractIds
     * @param userId
     * @return
     */
//    boolean checkContractIsSign(List<String> contractIds, Long userId);

    /**
     * 分页查询
     *
     * @param page
     * @param contract
     * @return
     */
//    IPage<ContractVO> selectContractCustomerPage(IPage page, ContractVO contract, Long userId);

    /**
     * 获取合同当前用户签署信息并更新签署状态
     *
     * @param contract   合同信息
     * @param customerId 当前用户id
     * @return
     */
    Integer getSignStatus(Contract contract, Long customerId, Boolean needUpdate);

    /**
     * 根据产品id删除
     *
     * @param goodsId 产品id
     */
//    void removeByGoodsIdAndUserId(Long goodsId);

    /**
     * 根据业务流水号获取合同
     *
     * @param bizNo
     * @return
     */
//    List<Contract> listByBizNo(String bizNo);

    /**
     * 根据合同id获取合同
     *
     * @param contractId
     */
    Contract getByContractId(String contractId);

    /**
     * 根据业务流水号查找合同是否存在
     *
     * @param bizNo      业务流水号
     * @param templateId 模板编号
     * @return
     */
//    Contract getByBizNoAndTemplateId(String bizNo, String templateId);

    /**
     * 更新合同状态
     *
     * @param id
     * @param contractStatus
     */
    Boolean updateContractStatus(Long id, Integer contractStatus);

    /**
     * 获取合同
     *
     * @param params 查询条件
     * @return
     */
    Contract getContractByEntity(ContractParamDTO params);

    /**
     * 合同撤销
     *
     * @param contractId
     */
    void contractRevoke(String contractId);

    /**
     * 合同查找
     */
    List<Contract> listByContract(ContractParamDTO contract);

    /**
     * 合同删除
     *
     * @param goodsId
     * @param signNode
     * @param processType
     */
//    void removeContractFromOps(Long goodsId, String signNode, Integer processType);

    /**
     * 下载合同 并更新
     *
     * @param contractId
     * @return
     */
    String downLoadAndUpdateContract(String contractId);

    /**
     * 签署后更新签署文件 需要等待1秒 防止签署后拉取不到最新的合同
     *
     * @param contractIds
     */
    void signedUpdateContractUrl(List<String> contractIds);

    /**
     * 合同删除并作废
     *
     * @param contractIds 合同ids
     * @return
     */
//    void deleteAndCancel(List<String> contractIds);

    /**
     * 合同作废
     *
     * @param contractIds 合同ids
     * @return
     */
//    void cancelContracts(List<String> contractIds);

    /**
     * 融资端用户查看合同列表
     *
     * @param page
     * @param contract
     * @return
     */
//    IPage<ContractVO> getListAllPage(IPage<Object> page, ContractVO contract, Long userId);

//    /**
//     * 签署注册时的合同
//     *
//     * @param contractId
//     */
//    void signRegistContract(String contractId);

    /**
     * 签署取消
     *
     * @param contractIds
     */
//    void cancelContractsAndChangeStatus(List<String> contractIds);

    /**
     * 合同锁定
     *
     * @param contractIds 合同ids
     */
    void contractLock(String contractIds);

    /**
     * 合同通过锁定合同
     *
     * @param delegateExecution
     */
//    void passContract(DelegateExecution delegateExecution);

    /**
     * 合同终止撤销
     *
     * @param delegateExecution
     */
//    void terminalContract(DelegateExecution delegateExecution);

    /**
     * 合同终止撤销
     *
     * @param contractIds
     */
//    void terminalContract(List<String> contractIds);

    /**
     * 作废自己发起的合同
     *
     * @param contractParamDTO
     */
//    Boolean cancelContractsBySelf(ContractParamDTO contractParamDTO, Long userId);

    /**
     * 重新发送合同
     *
     * @param contractId
     */
//    void resend(String contractId);

    /**
     * 延期
     *
     * @param contractId
     */
    void delay(String contractId);

    /***
     * 查询当前资金方 查询合同
     * @param companyId
     * @param query
     * @return
     */
//    IPage<ContractVO> getContract(Long companyId, Query query);
    //移到 IGoodsContractTemplateService checkDuplicateContracts（）
//    void checkDuplicateContracts(List<GoodsContractTemplateVO> goodsContractTemplates)

}
