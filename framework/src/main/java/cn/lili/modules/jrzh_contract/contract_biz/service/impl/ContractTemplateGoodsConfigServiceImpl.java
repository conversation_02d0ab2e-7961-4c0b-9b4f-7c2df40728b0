package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.ValidationUtils;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateGoodsConfig;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateGoodsConfigVO;
import cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractTemplateGoodsConfigMapper;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateGoodsConfigService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同模板商品配置服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContractTemplateGoodsConfigServiceImpl implements IContractTemplateGoodsConfigService {

    private final ContractTemplateGoodsConfigMapper goodsConfigMapper;
//    private final GoodsService goodsService;

    @Override
    public List<ContractTemplateGoodsConfig> getGoodsConfigs(String goodsId) {
        LambdaQueryWrapper<ContractTemplateGoodsConfig> wrapper = Wrappers.<ContractTemplateGoodsConfig>lambdaQuery()
            .eq(ContractTemplateGoodsConfig::getGoodsId, goodsId)
            .eq(ContractTemplateGoodsConfig::getStatus, 1)
            .orderByDesc(ContractTemplateGoodsConfig::getPriority);

        return goodsConfigMapper.selectList(wrapper);
    }

    @Override
    public String getGoodsBoundTemplateId(String goodsId) {
        log.info("开始查询商品绑定的合同模板ID，商品ID：{}", goodsId);

        // 查询商品绑定的合同模板配置
        LambdaQueryWrapper<ContractTemplateGoodsConfig> wrapper = Wrappers.<ContractTemplateGoodsConfig>lambdaQuery()
            .eq(ContractTemplateGoodsConfig::getGoodsId, goodsId)
            .eq(ContractTemplateGoodsConfig::getStatus, 1)
            .orderByDesc(ContractTemplateGoodsConfig::getPriority)
            .last("LIMIT 1");

        ContractTemplateGoodsConfig config = goodsConfigMapper.selectOne(wrapper);

        if (ObjectUtil.isNotEmpty(config)) {
            log.info("找到商品绑定的合同模板，商品ID：{}，模板ID：{}", goodsId, config.getTemplateId());
            return config.getTemplateId();
        }

        log.info("商品未绑定合同模板，商品ID：{}", goodsId);
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateGoodsConfig(ContractTemplateGoodsConfig config) {
        log.info("开始保存或更新商品模板配置，商品ID：{}，模板ID：{}", config.getGoodsId(), config.getTemplateId());

        // 参数验证
        ValidationUtils.notNull(config, "配置信息不能为空");
        ValidationUtils.notEmpty(config.getGoodsId(), "商品ID不能为空");
        ValidationUtils.notEmpty(config.getTemplateId(), "模板ID不能为空");



        // 查找是否已存在相同配置
        ContractTemplateGoodsConfig existingConfig = goodsConfigMapper.selectOne(
            Wrappers.<ContractTemplateGoodsConfig>lambdaQuery()
                .eq(ContractTemplateGoodsConfig::getGoodsId, config.getGoodsId())
                .eq(ContractTemplateGoodsConfig::getTemplateId, config.getTemplateId())
        );

        if (ObjectUtil.isNotEmpty(existingConfig)) {
            // 更新现有配置
            log.info("找到现有配置，执行更新操作，配置ID：{}", existingConfig.getId());

            // 保留原有ID和创建信息
            config.setId(existingConfig.getId());
            config.setCreateTime(existingConfig.getCreateTime());
            config.setCreateBy(existingConfig.getCreateBy());

            // 设置默认值（如果前端没有传递）
            if (ObjectUtil.isEmpty(config.getPriority())) {
                config.setPriority(existingConfig.getPriority() != null ? existingConfig.getPriority() : 100);
            }
            if (ObjectUtil.isEmpty(config.getStatus())) {
                config.setStatus(existingConfig.getStatus() != null ? existingConfig.getStatus() : 1);
            }

            // 执行更新
            return ValidationUtils.executeAndValidate(
                () -> goodsConfigMapper.updateById(config),
                "商品模板配置更新成功，配置ID：{}",
                "更新商品模板配置失败",
                config.getId()
            );
        } else {
            // 新增配置
            log.info("未找到现有配置，执行新增操作");

            // 设置默认值
            if (ObjectUtil.isEmpty(config.getPriority())) {
                // 商品级别默认比分类级别高
                config.setPriority(100);
            }
            if (ObjectUtil.isEmpty(config.getStatus())) {
                config.setStatus(1);
            }

            // 执行插入
            return ValidationUtils.executeAndValidate(
                () -> goodsConfigMapper.insert(config),
                "商品模板配置保存成功，配置ID：{}",
                "保存商品模板配置失败",
                config.getId()
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteGoodsConfig(String configId) {
        log.info("开始删除商品模板配置，配置ID：{}", configId);

        // 参数验证
        ValidationUtils.notEmpty(configId, "配置ID不能为空");

        // 检查配置是否存在
        ContractTemplateGoodsConfig config = goodsConfigMapper.selectById(configId);
        ValidationUtils.checkExists(config, "配置不存在", configId);

        // 执行删除并校验结果
        return ValidationUtils.executeAndValidate(
            () -> goodsConfigMapper.deleteById(configId),
            "商品模板配置删除成功，配置ID：{}",
            "删除商品模板配置失败",
            configId
        );
    }


    @Override
    public IPage<ContractTemplateGoodsConfigVO> getGoodsConfigPage(Page<ContractTemplateGoodsConfig> page, String goodsId, String templateId) {
        log.info("开始分页查询商品模板配置，页码：{}，页大小：{}，商品ID：{}，模板ID：{}",
            page.getCurrent(), page.getSize(), goodsId, templateId);

        try {
            LambdaQueryWrapper<ContractTemplateGoodsConfig> wrapper = Wrappers.<ContractTemplateGoodsConfig>lambdaQuery()
                    .eq(StrUtil.isNotEmpty(goodsId), ContractTemplateGoodsConfig::getGoodsId, goodsId)
                    .eq(StrUtil.isNotEmpty(templateId), ContractTemplateGoodsConfig::getTemplateId, templateId)
                    .orderByDesc(ContractTemplateGoodsConfig::getCreateTime);

            IPage<ContractTemplateGoodsConfig> result = goodsConfigMapper.selectPage(page, wrapper);

            // 转换为VO
            IPage<ContractTemplateGoodsConfigVO> voPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
            List<ContractTemplateGoodsConfigVO> voList = result.getRecords().stream()
                    .map(config -> {
                        try {
                            ContractTemplateGoodsConfigVO vo = new ContractTemplateGoodsConfigVO();
                            // 复制基础属性
                            vo.setId(config.getId());
                            vo.setGoodsId(config.getGoodsId());
                            vo.setGoodsName(config.getGoodsName());
                            vo.setTemplateId(config.getTemplateId());
                            vo.setPriority(config.getPriority());
                            vo.setStatus(config.getStatus());
                            vo.setRemark(config.getRemark());
                            vo.setContractType(config.getContractType());
                            vo.setCreateTime(config.getCreateTime());
                            vo.setUpdateTime(config.getUpdateTime());

                            // 合同类型字段保留但不再使用业务逻辑
                            // contractType字段已废弃，不再区分现货期货类型

                            // TODO: 这里可以查询并填充商品详细信息和模板名称
                            // Goods goods = goodsService.getById(config.getGoodsId());
                            // if (ObjectUtil.isNotEmpty(goods)) {
                            //     vo.setCategoryPath(goods.getCategoryPath());
                            //     vo.setGoodsPrice(goods.getPrice());
                            //     vo.setGoodsStatus(goods.getMarketEnable());
                            // }
                            // vo.setTemplateName(templateService.getById(config.getTemplateId()).getTemplateName());

                            return vo;
                        } catch (Exception e) {
                            log.error("转换商品配置VO异常，配置ID：{}，错误：{}", config.getId(), e.getMessage());
                            return null;
                        }
                    })
                    .filter(ObjectUtil::isNotEmpty)
                    .collect(Collectors.toList());
            voPage.setRecords(voList);

            log.info("分页查询商品模板配置完成，总数：{}，当前页数据：{}", result.getTotal(), voList.size());
            return voPage;
        } catch (Exception e) {
            log.error("分页查询商品模板配置异常，错误：{}", e.getMessage(), e);
            throw new ServiceException("查询商品模板配置失败：" + e.getMessage());
        }
    }
}
