//package cn.lili.modules.jrzh_contract.customer_ct.controller;
//
//import cn.hutool.core.util.ObjectUtil;
//import cn.lili.common.enums.ResultUtil;
//import cn.lili.common.vo.ResultMessage;
//import cn.lili.modules.jrzh_bases.CommonConstant;
//import cn.lili.modules.jrzh_contract.contract_api.vo.TemplateCustomerVO;
//import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateService;
//import cn.lili.modules.jrzh_contract.customer_ct.service.ITemplateCustomerService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
//@RestController
//@AllArgsConstructor
//@RequestMapping("/web-front/templateCustomer")
//@Api(value = "产品合同模板", tags = "产品合同模板接口")
//public class TemplateCustomerFrontController {
//    private final ITemplateCustomerService contractTemplateService;
//
//    @GetMapping("/getContractTemplate")
//    @ApiOperation("获取合同模板")
//    public ResultMessage<List<TemplateCustomerVO>> getContractTemplate(@RequestParam Integer signUser, Integer needUpdate, String contractIds,
//                                                                       String bizNos, String bigBizNos) {
//        boolean update = true;
//        if (ObjectUtil.isNotEmpty(needUpdate) && needUpdate == 0) {
//            update = false;
//        }
//        return ResultUtil.data(contractTemplateService
//                .selectContractTemplateAndContractStatusList(signUser, update, contractIds, bizNos, bigBizNos));
//    }
//}
