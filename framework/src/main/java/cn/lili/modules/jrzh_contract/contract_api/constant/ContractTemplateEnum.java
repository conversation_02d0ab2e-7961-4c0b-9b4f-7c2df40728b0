package cn.lili.modules.jrzh_contract.contract_api.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

public interface ContractTemplateEnum {
    @AllArgsConstructor
    @Getter
    enum CONTRACT_TEMPLATE_STATUS implements ContractTemplateEnum {


        /**
         * 合同保存
         */
        TEMPLATE_SAVE("TEMPLATE_SAVE", "模板编辑或删除");
        private final String type;
        private final String desc;
    }

    @AllArgsConstructor
    @Getter
    enum CONTRACT_TEMPLATE_STATE implements ContractTemplateEnum {
        /**
         * 合同模板
         */
        CONTRACT_TEMPLATE_ADD("ADD", "新增"),
        CONTRACT_TEMPLATE_EDIT("EDIT", "修改");
        private final String code;
        private final String desc;
    }


    @AllArgsConstructor
    @Getter
    enum CONTRACT_USE_TYPE implements ContractTemplateEnum {


        /**
         * 合同类型
         */
        PERSON("1", "个人"),
        ENTERPRISE("2", "企业");
        private final String code;
        private final String desc;
    }


    @AllArgsConstructor
    @Getter
    enum SENDER_INFO implements ContractTemplateEnum {


        /***
         *合同发送账号
         */
        ACCOUNT("account", "系统发送合同账号"),
        ENTERPRISE_NAME("enterpriseName", "系统发送合同企业名称");
        private final String code;
        private final String desc;
    }
    
}
