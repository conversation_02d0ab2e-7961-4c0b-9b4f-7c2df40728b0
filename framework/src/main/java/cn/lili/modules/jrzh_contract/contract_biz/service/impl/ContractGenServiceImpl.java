package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.jrzh_contract.contract_api.dto.DynamicContractDTO;
import cn.lili.modules.jrzh_contract.contract_api.entity.*;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateConfigVO;
import cn.lili.modules.jrzh_contract.contract_biz.service.*;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractReturnData;
import cn.lili.modules.jrzh_contract.customer_ct.service.ContractDataSourceService;
import cn.lili.modules.jrzh_other.bestsign.constant.ContractEnum;
import cn.lili.modules.order.order.entity.vo.OrderDetailVO;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.service.StoreService;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 合同生成服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContractGenServiceImpl implements IContractGenService {
    private final IContractTemplateService contractTemplateService;
    private final IContractOperatorService contractOperatorService;
    private final ContractStrategy contractStrategy;
    //    private final IContractTemplateConfigService templateConfigService;
    private final IContractService contractService;
//    private final IContractSignService contractSignService;
    private final IContractSignConfigService contractSignConfigService;
    private final ContractDataSourceService contractDataSourceService;
    private final OrderService orderService;
    private final StoreService storeService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Contract genContract(DynamicContractDTO dynamicContractDTO) {
        //参数检查 数据组装
        validAndComposeDynamicContract(dynamicContractDTO);

        //按传入的合同方式进行生成
        log.info("开始生成合同 方法{} 参数：params：{}", "genContract()", dynamicContractDTO);
        Integer contractGenType = dynamicContractDTO.getContractTemplate().getContractGenType();
        IContractType genContractService = contractStrategy.genContractInstance(contractGenType);
        ContractReturnData genContract = genContractService.gen(dynamicContractDTO);
        log.info("生成合同结束 方法{} 返回参数：params：{}", "genContract()", genContract);
        //合同保存
        saveNewContract(dynamicContractDTO, genContract);
        //添加合同签署人
        addSigner(dynamicContractDTO.getContractOperatorList(), dynamicContractDTO.getContractTemplate(), genContract.getContractId());
        //使用平台进行签署
//        Long platSigner = dynamicContractDTO.getPlatSigner();
//        if (ObjectUtil.isNotEmpty(platSigner)) {
//            platSign(platSigner, dynamicContractDTO);
//        }
        return dynamicContractDTO;
    }

    /*private void platSign(Long platSignerId, DynamicContractDTO dynamicContractDTO) {
        //获取平台签署人
        List<ContractOperator> contractOperatorList = dynamicContractDTO.getContractOperatorList();
        ContractOperator platSigne = null;
        for (ContractOperator contractOperator : contractOperatorList) {
            if (platSignerId.equals(contractOperator.getSignerId())) {
                platSigne = contractOperator;
                break;
            }
        }
        if (platSigne == null) {
            throw new ServiceException("平台方签署人未设置");
        }
        final ContractOperator finalPlatSigne = platSigne;
        //异步签署
        String tenantId = AuthUtil.getTenantId();
        ThreadUtils.runAsync(() -> {
            TenantBroker.runAs(tenantId, e -> {
                //签署
                SignParam signParam = SignParam.builder()
                        .signerId(finalPlatSigne.getSignerId())
                        .customerType(CustomerTypeEnum.ENTERPRISE.getCode())
                        .signerName(finalPlatSigne.getSignerCompanyName())
                        .personalName(finalPlatSigne.getSignerName())
                        .personalUserId(null)
                        .code(null)
                        .contractId(dynamicContractDTO.getContractId())
                        .customizeWriteBase64(null)
                        .build();
                contractSignService.sign(signParam);
                //更新签署后的合同
                contractService.downLoadAndUpdateContract(signParam.getContractId());
            });
        });
    }*/

    /**
     * 合同保存
     *
     * @param dynamicContractDTO
     * @param gen
     */
    private void saveNewContract(DynamicContractDTO dynamicContractDTO, ContractReturnData gen) {
        dynamicContractDTO.setSignKeywordJson(gen.getSignKeyWordJson());
        dynamicContractDTO.setContractId(gen.getContractId());
        dynamicContractDTO.setFileUrl(gen.getUrl());
        dynamicContractDTO.setContractVar(gen.getContractVar());
        dynamicContractDTO.setStatus(ContractEnum.CONTRACT_OPERATOR_STATUS.NOT_START.getStatus());
        contractService.save(dynamicContractDTO);
    }

    private void addSigner(List<ContractOperator> contractOperatorList, ContractTemplate contractTemplate, String contractId) {
        for (ContractOperator contractOperator : contractOperatorList) {
            contractOperator.setStatus(ContractEnum.CONTRACT_OPERATOR_STATUS.NOT_START.getStatus());
            contractOperator.setContractId(contractId);
            contractOperatorService.saveOrUpdateContractOperator(contractOperator, contractTemplate);
        }
    }

    /**
     * 参数检查
     *
     * @param dynamicContractDTO
     */
    private void validAndComposeDynamicContract(DynamicContractDTO dynamicContractDTO) {
        boolean allNotEmpty = ObjectUtil.isAllNotEmpty(dynamicContractDTO.getContractTemplateId(), dynamicContractDTO.getContractBizNo());
        if (!allNotEmpty) {
            throw new ServiceException("合同参数缺失");
        }
        //合同模板信息
        String templateId = dynamicContractDTO.getContractTemplateId().toString();
        ContractTemplate template = contractTemplateService.getByTemplateId(templateId);
        if (ObjectUtil.isEmpty(template)) {
            throw new ServiceException("合同模板不存在,请检查产品是否配置该合同模板");
        }
        //校验签署人数
        List<ContractOperator> signerList = dynamicContractDTO.getContractOperatorList();
        if (signerList.size() > template.getSignerNum()) {
            throw new ServiceException("签署人数设置过多");
        }
        OrderDetailVO orderDetailVO = orderService.queryDetail(dynamicContractDTO.getBigBizNo());
        Store store = storeService.getById(orderDetailVO.getOrder().getStoreId());
        dynamicContractDTO.setBuyerId(Long.valueOf(orderDetailVO.getOrder().getMemberId()));
        dynamicContractDTO.setSellerId(Long.valueOf(store.getMemberId()));
        if (ObjectUtil.isEmpty(dynamicContractDTO.getContractNo())) {
            dynamicContractDTO.setContractNo("J" + IdWorker.getIdStr());
        }
        JSONObject dataSource = new JSONObject();
        //组装数据到合同对应字段
        dataSource.putAll(JSONUtil.parseObj(contractDataSourceService.buildContractDataSourcePurchase(dynamicContractDTO)));
        dynamicContractDTO.setCustomizeSource(dataSource);
        //获取签署人配置
        List<ContractSignConfig> contractSignConfigs = contractSignConfigService.listByTemplateId(templateId);
        //数据组装
        Date now= new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, template.getExpireDay());
        Date signDeadLine = calendar.getTime();
        dynamicContractDTO.setContractTitle(template.getTemplateName());
        dynamicContractDTO.setSendTime(now);
        dynamicContractDTO.setSignDeadLine(signDeadLine);
        dynamicContractDTO.setContractTemplate(template);
        dynamicContractDTO.setContractSignConfigList(contractSignConfigs);
//        dynamicContractDTO.setContractTemplateConfigVO(templateConfigVO);
    }

    /**
     * 获取方案配置的数据源
     *
     * @param templateConfigVO 方案配置
     * @return
     */
//    private List<IContractDataSource> getConfigDataSource(ContractTemplateConfigVO templateConfigVO) {
//        List<TemplateFieldsConfig> templateFieldsConfigs = templateConfigVO.getTemplateFieldsConfigs();
//        if (CollUtil.isEmpty(templateFieldsConfigs)) {
//            return Collections.emptyList();
//        }
//        List<String> beanClazzPathList = templateFieldsConfigs.stream()
//                .map(TemplateFieldsConfig::getClazzName).filter(StringUtil::isNotBlank).distinct().collect(Collectors.toList());
//        List<IContractDataSource> contractDataSources = new LinkedList<>();
//        contractDataSourceList.forEach(dataSourceService -> {
//            if (beanClazzPathList.contains(dataSourceService.support())) {
//                contractDataSources.add(dataSourceService);
//            }
//        });
//        return contractDataSources;
//    }
}
