package cn.lili.modules.jrzh_contract.customer_ct.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.ValidationUtils;
import cn.lili.modules.jrzh_contract.contract_api.dto.ContractParamDTO;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractOperator;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.vo.TemplateCustomerVO;
import cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractTemplateMapper;
import cn.lili.modules.jrzh_contract.contract_biz.service.*;
import cn.lili.modules.jrzh_contract.customer_ct.service.ITemplateCustomerService;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.order.order.service.OrderItemService;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class TemplateCustomerServiceImpl extends ServiceImpl<ContractTemplateMapper, ContractTemplate> implements ITemplateCustomerService {

    private final IContractService contractService;
    private final IContractConfigService contractConfigService;
    private final IContractTemplateService contractTemplateService;
    private final OrderService orderService;
    private final OrderItemService orderItemService;
    private final IContractTemplateConfigService contractTemplateConfigService;
    private final IContractOperatorService contractOperatorService;

    /**
     * 手动签署
     */
    private final static Integer HAND_SIGN_WAY = 0;

    @Override
    public List<TemplateCustomerVO> selectContractTemplateAndContractStatusList(Integer signUser, boolean update, String contractIds, String bizNos, String bigBizNos) {
        AuthUser user = UserContext.getCurrentUser();
        String userId = user.getId();

        // 校验订单是否存在
        Order order = orderService.getBySn(bigBizNos);
        ValidationUtils.notNull(order,"订单不存在");

        // 校验子订单并获取子订单列表
        List<String> bizNosList = Arrays.asList(bizNos.split(","));
        List<OrderItem> orderItems = orderItemService.list(Wrappers.<OrderItem>lambdaQuery().in(OrderItem::getSn, bizNosList));
        ValidationUtils.notEmpty(orderItems,"子订单不存在",bizNosList);

        // 设置查询参数，检查是否已经生成合同
        ContractParamDTO contractParamDTO = new ContractParamDTO();
        contractParamDTO.setBigBizNos(bigBizNos);
        contractParamDTO.setBizNos(bizNos);

        // 查看是否已经生成合同
        List<Contract> contracts = contractService.listByContract(contractParamDTO);

        // 根据子订单编号分组已生成的合同，处理contractBizNo可能包含多个子订单编号的情况
        Map<String, Contract> contractMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(contracts)) {
            for (Contract contract : contracts) {
                String contractBizNo = contract.getContractBizNo();
                ValidationUtils.notEmpty(contractBizNo,"合同业务号不存在");
                // contractBizNo可能是单个子订单编号，也可能是多个子订单编号的逗号分隔字符串
                String[] bizNoArray = contractBizNo.split(",");
                for (String bizNo : bizNoArray) {
                    if (ObjectUtil.isNotEmpty(bizNo.trim())) {
                        contractMap.put(bizNo.trim(), contract);
                    }
                }
            }
        }

        // 获取当前签署人信息
        Long customerId = Long.parseLong(userId);

        // 按合同模板分组子订单
        Map<String, List<OrderItem>> templateGroupMap = new HashMap<>();
        Map<String, ContractTemplate> templateMap = new HashMap<>();
        Map<String, Contract> templateContractMap = new HashMap<>();

        for (OrderItem orderItem : orderItems) {
            ContractTemplate contractTemplate = null;
            Contract existingContract = contractMap.get(orderItem.getSn());

            if (ObjectUtil.isNotEmpty(existingContract)) {
                // 已生成合同，使用合同对应的模板
                contractTemplate = contractTemplateService.getByTemplateId(existingContract.getContractTemplateId().toString());
                if (ObjectUtil.isNotEmpty(contractTemplate)) {
                    String templateId = contractTemplate.getTemplateId();
                    templateGroupMap.computeIfAbsent(templateId, k -> new ArrayList<>()).add(orderItem);
                    templateMap.put(templateId, contractTemplate);
                    templateContractMap.put(templateId, existingContract);
                }
            } else {
                // 未生成合同，根据商品查询对应的合同模板
                contractTemplate = getBestContractTemplateByGoods(orderItem.getGoodsId());
                String templateId = contractTemplate.getTemplateId();
                templateGroupMap.computeIfAbsent(templateId, k -> new ArrayList<>()).add(orderItem);
                templateMap.put(templateId, contractTemplate);

            }
        }

        // 为每个模板创建对应的TemplateCustomerVO
        List<TemplateCustomerVO> result = new ArrayList<>();
        for (Map.Entry<String, List<OrderItem>> entry : templateGroupMap.entrySet()) {
            String templateId = entry.getKey();
            List<OrderItem> items = entry.getValue();
            ContractTemplate contractTemplate = templateMap.get(templateId);
            Contract existingContract = templateContractMap.get(templateId);

            TemplateCustomerVO templateCustomerVO = new TemplateCustomerVO();

            // 将使用相同模板的子订单编号合并
            String combinedBizNos = items.stream()
                    .map(OrderItem::getSn)
                    .collect(Collectors.joining(","));
            templateCustomerVO.setBizNo(combinedBizNos);

            // 复制模板信息
            BeanUtil.copyProperties(contractTemplate, templateCustomerVO);

            // 如果已生成合同，设置合同相关信息
            if (ObjectUtil.isNotEmpty(existingContract)) {
                ContractOperator operator = contractOperatorService.getByContractIdAndCustomerId(existingContract.getContractId(), customerId);
                templateCustomerVO.setPdfUrl(existingContract.getFileUrl());
                templateCustomerVO.setSignStatus(ObjectUtil.isEmpty(operator)?null:operator.getStatus());
                templateCustomerVO.setContractId(existingContract.getContractId());
            }

            result.add(templateCustomerVO);
        }

        return result;
    }


    /**
     * 根据商品ID获取最佳匹配的合同模板
     * 优先级：商品绑定模板 > 分类绑定模板（按优先级） > 默认模板
     * 如果有多个相同优先级的模板，选择最新的
     */
    private ContractTemplate getBestContractTemplateByGoods(String goodsId) {
        try {
            // 使用智能模板配置服务获取最佳匹配模板
            ContractTemplate bestTemplate = contractTemplateConfigService.getBestMatchTemplate(goodsId);
            if (ObjectUtil.isNotEmpty(bestTemplate)) {
                log.info("找到最佳匹配的合同模板，商品ID：{}，模板ID：{}", goodsId, bestTemplate.getTemplateId());
                return bestTemplate;
            }
        } catch (Exception e) {
            log.warn("获取最佳匹配模板失败，使用默认模板。商品ID：{}，错误：{}", goodsId, e.getMessage());
        }

        // 如果智能匹配失败，降级到默认模板
        log.info("使用默认模板，商品ID：{}", goodsId);
        return getDefaultTemplate();
    }

    /**
     * 获取默认合同模板
     */
    private ContractTemplate getDefaultTemplate() {
        // 查询最新的启用的默认合同模板
        ContractTemplate defaultTemplate = baseMapper.selectOne(Wrappers.<ContractTemplate>lambdaQuery()
                .eq(ContractTemplate::getStatus, 1)
                .eq(ContractTemplate::getDefaultContractType, 1)
                .orderByDesc(ContractTemplate::getCreateTime)
                .last("LIMIT 1"));

        if (ObjectUtil.isEmpty(defaultTemplate)) {
            throw new ServiceException("系统中没有配置默认合同模板");
        }

        return defaultTemplate;
    }



}
