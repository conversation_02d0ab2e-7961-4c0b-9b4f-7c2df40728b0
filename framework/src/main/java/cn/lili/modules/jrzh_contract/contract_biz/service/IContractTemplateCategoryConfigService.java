package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateCategoryConfig;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateCategoryConfigVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 合同模板分类配置服务接口
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
public interface IContractTemplateCategoryConfigService {

    /**
     * 根据商品分类获取模板配置（不再区分合同类型）
     *
     * @param categoryId 分类ID
     * @return 模板配置列表
     */
    List<ContractTemplateCategoryConfig> getCategoryConfigs(String categoryId);

    /**
     * 批量根据商品分类获取模板配置（不再区分合同类型）
     *
     * @param categoryIds 分类ID列表
     * @return 模板配置列表
     */
    List<ContractTemplateCategoryConfig> getBatchCategoryConfigs(List<String> categoryIds);

    /**
     * 保存或更新分类模板配置
     * 如果配置已存在则更新，否则新增
     *
     * @param config 分类配置
     * @return 保存结果
     */
    boolean saveOrUpdateCategoryConfig(ContractTemplateCategoryConfig config);

    /**
     * 保存分类模板配置（向后兼容方法）
     * 实际调用 saveOrUpdateCategoryConfig
     *
     * @param config 分类配置
     * @return 保存结果
     */
    default boolean saveCategoryConfig(ContractTemplateCategoryConfig config) {
        return saveOrUpdateCategoryConfig(config);
    }

    /**
     * 删除分类模板配置
     *
     * @param configId 配置ID
     * @return 删除结果
     */
    boolean deleteCategoryConfig(String configId);

    /**
     * 批量配置分类模板
     *
     * @param categoryIds 分类ID列表
     * @param templateId 模板ID
     * @return 配置结果
     */
    boolean batchConfigCategoryTemplate(List<String> categoryIds, String templateId);

    /**
     * 分页查询分类模板配置
     *
     * @param page 分页参数
     * @param categoryId 分类ID
     * @param templateId 模板ID
     * @return 分页结果
     */
    IPage<ContractTemplateCategoryConfigVO> getCategoryConfigPage(Page<ContractTemplateCategoryConfig> page, String categoryId, String templateId);
}
