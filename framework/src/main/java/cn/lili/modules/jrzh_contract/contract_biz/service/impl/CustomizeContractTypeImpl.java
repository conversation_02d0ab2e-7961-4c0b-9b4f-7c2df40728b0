/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.context.UserContext;
import cn.lili.modules.file.plugin.FilePluginFactory;
import cn.lili.modules.jrzh_bases.PoiTiUtils;
import cn.lili.modules.jrzh_contract.contract_api.dto.DynamicContractDTO;
import cn.lili.modules.jrzh_contract.contract_api.dto.ReportDataParam;
import cn.lili.modules.jrzh_contract.contract_api.dto.SignParam;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractSignConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_biz.handler.ElecSignHandlerFactory;
import cn.lili.modules.jrzh_contract.contract_biz.handler.impl.ElecSignBestHandler;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractType;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractReturnData;
import cn.lili.modules.jrzh_other.bestsign.constant.ContractEnum;
import cn.lili.modules.jrzh_other.bestsign.dto.ContractTemplateFileDTO;
import cn.lili.modules.jrzh_other.bestsign.dto.SsqSignaturePositions;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.stereotype.Service;

import java.io.*;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 自定义模板合同 服务实现类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Service
@RequiredArgsConstructor
public class CustomizeContractTypeImpl implements IContractType {
    private final ElecSignHandlerFactory elecSignHandlerFactory;
    //    private final ElecSignHandler elecSignHandler;
    private final ElecSignBestHandler elecSignBestHandler;
    private final FilePluginFactory filePluginFactory;

//    private final OssBuilder ossBuilder;

    @Override
    public String preViewTemplate(ContractTemplate contractTemplate) {
        return contractTemplate.getTemplateWordUrl();
    }

    //    @Override
//    public void loadingDataSource(DynamicContractDTO dynamicContractDTO) {
//        JSONObject dataSource = new JSONObject();
//        dynamicContractDTO.getContractDataSource().forEach(dataSourceService -> {
//            final DynamicContractDTO contractDTO = dynamicContractDTO;
//            dataSource.putAll(JSONUtil.parseObj(dataSourceService.getDataSource(contractDTO)));
//        });
//        dynamicContractDTO.setCustomizeSource(dataSource);
//    }
//
    @Override
    public ContractReturnData gen(DynamicContractDTO dynamicContractDTO) {
//        ContractTemplateConfigVO config = dynamicContractDTO.getContractTemplateConfigVO();
//        if (ObjectUtil.isEmpty(config)) {
//            throw new ServiceException("合同数据源未配置,请联系工作人员");
//        }
        //通过word模板生成pdf合同文件流
        ReportDataParam reportDataParam = new ReportDataParam();
        reportDataParam.setObj(dynamicContractDTO.getCustomizeSource());
        reportDataParam.setBizNo(dynamicContractDTO.getContractBizNo());
        reportDataParam.setContractTemplate(dynamicContractDTO.getContractTemplate());
        ContractTemplateFileDTO contractTemplateFileDTO = contractTemplateGenFile(reportDataParam);
        //根据系统设置 确定合同的签署关键字位置
        Map<String, SsqSignaturePositions> keyWord = calPosition(contractTemplateFileDTO.getFdata(), dynamicContractDTO.getContractSignConfigList());
        //通过pdf创建合同
        //TODO这里直接拿到具体策略实现类来使用
//        ContractReturnData contractByReport = elecSignHandlerFactory.template().createContractByReport(contractTemplateFileDTO);
        ContractReturnData contractByReport = elecSignBestHandler.createContractByReport(contractTemplateFileDTO);
        contractByReport.setSignKeyWordJson(JSONUtil.toJsonStr(keyWord));
        return contractByReport;
//        return null;
    }

    //
//
    @SneakyThrows
    private ContractTemplateFileDTO contractTemplateGenFile(ReportDataParam param) {
        ContractTemplateFileDTO contractTemplateFile = null;
        FileInputStream contractStream = null;
        InputStream templateInputStream = null;
        try {
            ContractTemplate template = param.getContractTemplate();
            //获取合同模板文件
            String templateWordUrl = template.getTemplateWordUrl();
            if (ObjectUtil.isEmpty(templateWordUrl)) {
                throw new ServiceException("合同模板文件不存在");
            }
            Object backObj = param.getObj();
            templateInputStream = PoiTiUtils.getFileInputStream(templateWordUrl);
            File file = PoiTiUtils.genPdfFile(backObj, templateInputStream);
            //合同pdf生成并将签署参数预填充到合同中
            contractTemplateFile = contractFileGen(file);
            //设置合同其他参数
            contractTemplateFile.setTitle(template.getTemplateName());
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime endTime = now.plusDays(template.getExpireDay());
            Long validTime = endTime.toEpochSecond(ZoneOffset.of("+8"));
            //合同有效期=当前时间戳秒数+有效秒数
            contractTemplateFile.setExpireTime(validTime.toString());
            contractTemplateFile.setDescription(template.getTemplateName());
            //上传合同文件
            contractStream = new FileInputStream(file);
//            BladeFile bladeFile = ossBuilder.template().putFile(file.getName(), contractStream);
//            contractTemplateFile.setDownLoadUrl(bladeFile.getLink());
            String fileKey = UserContext.getCurrentUser().getRole().name() + "/" + file.getName();

            String result = filePluginFactory.filePlugin().inputStreamUpload(contractStream, file.getName());
//            contractTemplateFile.setDownLoadUrl(result);
            contractTemplateFile.setDownLoadUrl(result);

        } finally {
            if (ObjectUtil.isNotEmpty(templateInputStream)) {
                templateInputStream.close();
            }
            if (ObjectUtil.isNotEmpty(contractStream)) {
                contractStream.close();
            }
        }
        return contractTemplateFile;
    }

    /**
     * 生成合同文件参数
     *
     * @param file 合同pdf源文件
     */
    private ContractTemplateFileDTO contractFileGen(File file) {
        ContractTemplateFileDTO contractTemplateFile = new ContractTemplateFileDTO();
        contractTemplateFile.setPdfFile(file);
        FileInputStream fileInputStream = null;
        try {
            try {
                fileInputStream = new FileInputStream(file);
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            }
            byte[] bdata = new byte[0];
            try {
                bdata = IOUtils.toByteArray(fileInputStream);
            } catch (IOException e) {
                e.printStackTrace();
            }
            //读取文件获取文件MD5Hex编码
            String md5Hex = DigestUtils.md5Hex(bdata);
            //读取文件获取文件base64编码
            String fileEncode = Base64.encode(file);
            contractTemplateFile.setFdata(fileEncode);
            contractTemplateFile.setFmd5(md5Hex);
            //设置合同其他参数
            contractTemplateFile.setFname(file.getName());
            contractTemplateFile.setFtype("pdf");
            contractTemplateFile.setFpages("1");
//		} catch (FileNotFoundException e) {
        } catch (Exception e) {
            throw new ServiceException("生成合同文件错误");
        } finally {
            if (ObjectUtil.isNotEmpty(fileInputStream)) {
                try {
                    fileInputStream.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
        return contractTemplateFile;
    }

    private Map<String, SsqSignaturePositions> calPosition(String fileEncode, List<ContractSignConfig> contractSignConfigList) {
        //填充签署参数
        Map<String, SsqSignaturePositions> signaturePositions = calculateSignPosition(contractSignConfigList, fileEncode);
        return signaturePositions;
    }

    /**
     * 计算pdf签署坐标
     *
     * @param signConfigs 签署关键字配置
     * @param fileBase64  签署文件Base64
     * @return
     */
    private Map<String, SsqSignaturePositions> calculateSignPosition(List<ContractSignConfig> signConfigs, String fileBase64) {
        //获取pdf坐标集合
        Map<String, SsqSignaturePositions> signaturePositions = new HashMap<>();
        for (ContractSignConfig signConfig : signConfigs) {
            //TODO这里直接拿到具体策略实现类来使用
//            SsqSignaturePositions positions = elecSignHandlerFactory.template().getSignaturePositions(fileBase64, signConfig.getSignAlign(), signConfig.getKeywords());
            SsqSignaturePositions positions = elecSignBestHandler.getSignaturePositions(fileBase64, signConfig.getSignAlign(), signConfig.getKeywords());
            this.adjustPositions(positions, signConfig.getSignType());
            signaturePositions.put(signConfig.getKeywords(), positions);
        }
        return signaturePositions;
    }

    private void adjustPositions(SsqSignaturePositions positions, Integer signType) {
        //1、签名 2、印章 3、日期
        switch (signType) {
            case 1:
                positions.setY(String.valueOf(Double.parseDouble(positions.getY()) - 0.02));
                break;
            case 2:
                positions.setY(String.valueOf(Double.parseDouble(positions.getY()) - 0.05));
                break;
            case 3:
//                positions.setY(String.valueOf(Double.parseDouble(positions.getY()) - 0.03));
                positions.setY(String.valueOf(Double.parseDouble(positions.getY())-0.015));
                break;
        }

    }

    @Override
    public Contract sign(SignParam signParam) {
//        elecSignHandlerFactory.template().templateAutoSign(signParam);
        elecSignBestHandler.customizeAutoSign(signParam);
        return signParam.getContract();
    }

    //
    @Override
    public Integer support() {
        return ContractEnum.ContractGenType.CUSTOMIZE_TEMPLATE.getType();
    }
//
//    @Override
//    public List<IContractDataSource> getConfigDataSource(ContractTemplateConfigVO templateConfigVO,List<IContractDataSource> contractDataSourceList) {
//        if(StrUtil.isBlank(templateConfigVO.getBeanClazzPath())){
//            throw new ServiceException("方案数据源未配置");
//        }
//        List<String> beanClazzPathList = Func.toStrList(templateConfigVO.getBeanClazzPath());
//        List<IContractDataSource> contractDataSources = new LinkedList<>();
//        contractDataSourceList.forEach(dataSourceService -> {
//            if (beanClazzPathList.contains(dataSourceService.support())) {
//                contractDataSources.add(dataSourceService);
//            }
//        });
//        return contractDataSources;
//    }
}
