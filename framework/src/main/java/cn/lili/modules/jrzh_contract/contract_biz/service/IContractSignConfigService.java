/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractSignConfig;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractSignConfigVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 合同模板签署配置 服务类
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
public interface IContractSignConfigService extends IService<ContractSignConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param contractSignConfig
	 * @return
	 */
	IPage<ContractSignConfigVO> selectContractSignConfigPage(IPage<ContractSignConfigVO> page, ContractSignConfigVO contractSignConfig);

	/**
	 * 根据模板id获取签署关键字
	 *
	 * @param templateId
	 * @return
	 */
	List<ContractSignConfig> listByTemplateId(String templateId);

	/**
	 * 删除模板id
	 *
	 * @param templateId
	 */
	void removeByTemplateId(String templateId);

	/**
	 * 根据模板id与签署顺序获取关键字配置
	 * @param contractTemplateId
	 * @param signerSort
	 * @return
	 */
    List<ContractSignConfig> listByTemplateIdAndSort(Long contractTemplateId, Integer signerSort);

	/**
	 * 根据模板ids获取签署配置
	 * @param templateIds
	 * @return
	 */
	Map<String,List<ContractSignConfig>> mapByTemplateIds(ArrayList<String> templateIds);
}
