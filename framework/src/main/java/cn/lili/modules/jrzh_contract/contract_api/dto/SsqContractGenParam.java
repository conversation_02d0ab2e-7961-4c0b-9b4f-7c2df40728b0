package cn.lili.modules.jrzh_contract.contract_api.dto;


import java.util.Map;

public class SsqContractGenParam {
    private String templateId;
    private String templateName;
    private Integer expireDay;
    private Map<String, Object> groupValues;
    private Map<String, Object> templateValues;
    private String templateFieldsJson;

    public static SsqContractGenParamBuilder builder() {
        return new SsqContractGenParamBuilder();
    }

    public String getTemplateId() {
        return this.templateId;
    }

    public String getTemplateName() {
        return this.templateName;
    }

    public Integer getExpireDay() {
        return this.expireDay;
    }

    public Map<String, Object> getGroupValues() {
        return this.groupValues;
    }

    public Map<String, Object> getTemplateValues() {
        return this.templateValues;
    }

    public String getTemplateFieldsJson() {
        return this.templateFieldsJson;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public void setExpireDay(Integer expireDay) {
        this.expireDay = expireDay;
    }

    public void setGroupValues(Map<String, Object> groupValues) {
        this.groupValues = groupValues;
    }

    public void setTemplateValues(Map<String, Object> templateValues) {
        this.templateValues = templateValues;
    }

    public void setTemplateFieldsJson(String templateFieldsJson) {
        this.templateFieldsJson = templateFieldsJson;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SsqContractGenParam)) {
            return false;
        } else {
            SsqContractGenParam other = (SsqContractGenParam)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$expireDay = this.getExpireDay();
                Object other$expireDay = other.getExpireDay();
                if (this$expireDay == null) {
                    if (other$expireDay != null) {
                        return false;
                    }
                } else if (!this$expireDay.equals(other$expireDay)) {
                    return false;
                }

                Object this$templateId = this.getTemplateId();
                Object other$templateId = other.getTemplateId();
                if (this$templateId == null) {
                    if (other$templateId != null) {
                        return false;
                    }
                } else if (!this$templateId.equals(other$templateId)) {
                    return false;
                }

                Object this$templateName = this.getTemplateName();
                Object other$templateName = other.getTemplateName();
                if (this$templateName == null) {
                    if (other$templateName != null) {
                        return false;
                    }
                } else if (!this$templateName.equals(other$templateName)) {
                    return false;
                }

                Object this$groupValues = this.getGroupValues();
                Object other$groupValues = other.getGroupValues();
                if (this$groupValues == null) {
                    if (other$groupValues != null) {
                        return false;
                    }
                } else if (!this$groupValues.equals(other$groupValues)) {
                    return false;
                }

                Object this$templateValues = this.getTemplateValues();
                Object other$templateValues = other.getTemplateValues();
                if (this$templateValues == null) {
                    if (other$templateValues != null) {
                        return false;
                    }
                } else if (!this$templateValues.equals(other$templateValues)) {
                    return false;
                }

                Object this$templateFieldsJson = this.getTemplateFieldsJson();
                Object other$templateFieldsJson = other.getTemplateFieldsJson();
                if (this$templateFieldsJson == null) {
                    if (other$templateFieldsJson != null) {
                        return false;
                    }
                } else if (!this$templateFieldsJson.equals(other$templateFieldsJson)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SsqContractGenParam;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $expireDay = this.getExpireDay();
        result = result * 59 + ($expireDay == null ? 43 : $expireDay.hashCode());
        Object $templateId = this.getTemplateId();
        result = result * 59 + ($templateId == null ? 43 : $templateId.hashCode());
        Object $templateName = this.getTemplateName();
        result = result * 59 + ($templateName == null ? 43 : $templateName.hashCode());
        Object $groupValues = this.getGroupValues();
        result = result * 59 + ($groupValues == null ? 43 : $groupValues.hashCode());
        Object $templateValues = this.getTemplateValues();
        result = result * 59 + ($templateValues == null ? 43 : $templateValues.hashCode());
        Object $templateFieldsJson = this.getTemplateFieldsJson();
        result = result * 59 + ($templateFieldsJson == null ? 43 : $templateFieldsJson.hashCode());
        return result;
    }

    public String toString() {
        return "SsqContractGenParam(templateId=" + this.getTemplateId() + ", templateName=" + this.getTemplateName() + ", expireDay=" + this.getExpireDay() + ", groupValues=" + this.getGroupValues() + ", templateValues=" + this.getTemplateValues() + ", templateFieldsJson=" + this.getTemplateFieldsJson() + ")";
    }

    public SsqContractGenParam(String templateId, String templateName, Integer expireDay, Map<String, Object> groupValues, Map<String, Object> templateValues, String templateFieldsJson) {
        this.templateId = templateId;
        this.templateName = templateName;
        this.expireDay = expireDay;
        this.groupValues = groupValues;
        this.templateValues = templateValues;
        this.templateFieldsJson = templateFieldsJson;
    }

    public SsqContractGenParam() {
    }

    public static class SsqContractGenParamBuilder {
        private String templateId;
        private String templateName;
        private Integer expireDay;
        private Map<String, Object> groupValues;
        private Map<String, Object> templateValues;
        private String templateFieldsJson;

        SsqContractGenParamBuilder() {
        }

        public SsqContractGenParamBuilder templateId(String templateId) {
            this.templateId = templateId;
            return this;
        }

        public SsqContractGenParamBuilder templateName(String templateName) {
            this.templateName = templateName;
            return this;
        }

        public SsqContractGenParamBuilder expireDay(Integer expireDay) {
            this.expireDay = expireDay;
            return this;
        }

        public SsqContractGenParamBuilder groupValues(Map<String, Object> groupValues) {
            this.groupValues = groupValues;
            return this;
        }

        public SsqContractGenParamBuilder templateValues(Map<String, Object> templateValues) {
            this.templateValues = templateValues;
            return this;
        }

        public SsqContractGenParamBuilder templateFieldsJson(String templateFieldsJson) {
            this.templateFieldsJson = templateFieldsJson;
            return this;
        }

        public SsqContractGenParam build() {
            return new SsqContractGenParam(this.templateId, this.templateName, this.expireDay, this.groupValues, this.templateValues, this.templateFieldsJson);
        }

        public String toString() {
            return "SsqContractGenParam.SsqContractGenParamBuilder(templateId=" + this.templateId + ", templateName=" + this.templateName + ", expireDay=" + this.expireDay + ", groupValues=" + this.groupValues + ", templateValues=" + this.templateValues + ", templateFieldsJson=" + this.templateFieldsJson + ")";
        }
    }
}
