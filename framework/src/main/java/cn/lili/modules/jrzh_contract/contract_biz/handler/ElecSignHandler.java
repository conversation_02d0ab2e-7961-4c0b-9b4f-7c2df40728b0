package cn.lili.modules.jrzh_contract.contract_biz.handler;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.lili.modules.connect.config.SsqCreateSignImg;
import cn.lili.modules.file.entity.File;
import cn.lili.modules.jrzh_bases.BladeFile;
import cn.lili.modules.jrzh_contract.contract_api.dto.DynamicContractDTO;
import cn.lili.modules.jrzh_contract.contract_api.dto.SignParam;
import cn.lili.modules.jrzh_contract.contract_api.dto.SsqContractGenParam;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractVO;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractReturnData;
import cn.lili.modules.jrzh_other.bestsign.dto.ContractTemplateFileDTO;
import cn.lili.modules.jrzh_other.bestsign.dto.SsqContractInfo;
import cn.lili.modules.jrzh_other.bestsign.dto.SsqSignaturePositions;

import java.util.List;

/**
 * <AUTHOR>
 * 电子签接口
 */
public interface ElecSignHandler {

    /**
     * 创建个人签名/印章图片
     *
     * @return 是否成功
     */
    boolean createSignImg(SsqCreateSignImg ssqCreateSignImg);

    /**
     * 下载签名/印章图片
     *
     * @return 是否成功
     */
    BladeFile downloadSignImg(String accountId, String name);
//
//    /**
//     * 验签
//     *
//     * @return 返回参数
//     */
//    Boolean onlineVerifySignature(String contractId, String fhash);
//
//
//    /**
//     * 合同生成
//     *
//     * @return 合同id
//     */
//    ContractReturnData contractCreate(DynamicContractDTO dynamicContractDTO);
//
//    /**
//     * 合同生成
//     *
//     * @param contractGenParam
//     * @return
//     */
//
//    ContractReturnData contractCreate(SsqContractGenParam contractGenParam);
//
    /**
     * 通过生成的合同文件创建合同
     *
     * @param contractTemplateFileDTO
     * @return
     */
    ContractReturnData createContractByReport(ContractTemplateFileDTO contractTemplateFileDTO);
//
//    /**
//     * 自动签署
//     *
//     * @param signParam
//     * @return
//     */
//    void contractAutoSign(SignParam signParam);
//
    /**
     * 自定义模板签署
     *
     * @param signParam
     */
    void customizeAutoSign(SignParam signParam);
//
//    /**
//     * 获取合同模板所有字段
//     *
//     * @param templateId
//     * @return
//     */
//    JSONArray getTemplateFields(String templateId);
//
//
//    /**
//     * 跳转到创建模板页面
//     *
//     * @return 跳转地址
//     */
//    String skipTemplatePage();
//
//    /**
//     * 根据模板id预览
//     *
//     * @param templateId
//     * @return
//     */
//    String preview(String templateId);
//
//    /**
//     * 获取开发者模板列表
//     *
//     * @param query
//     * @return
//     */
//    List<ContractTemplate> listContractTemplate(Query query);
//
//
    /**
     * 下载合同
     *
     * @param contract 合同
     * @return
     */
    BladeFile downLoadContract(Contract contract);

    /**
     * 撤销合同
     *
     * @param contractId 合同id
     */
    void contractRevoke(String contractId);
//
//    /**
//     * 撤销合同
//     *
//     * @param contractIds 合同id
//     */
//    void contractRevoke(List<String> contractIds);
//
//    /**
//     * 预览合同
//     *
//     * @param contractId
//     * @param userId
//     * @return
//     */
//    String previewContract(String contractId, String userId);
//
//    /**
//     * 跳转去签署
//     *
//     * @param skipSignParam
//     * @return
//     */
//    String skipToSign(SsqSkipSignParam skipSignParam);
//
    /**
     * 合同锁定
     *
     * @param contractIds 合同ids
     */
    void contractLock(String contractIds);
//
//    /**
//     * 平台端跳转预览合同
//     *
//     * @param contractId
//     * @return
//     */
//    String platTurnToContractPreView(String contractId);
//
    /**
     * 合同最新信息
     *
     * @param contractId
     * @return
     */
    SsqContractInfo getContractInfo(String contractId);
//
//    /**
//     * 合同文件签署
//     *
//     * @param contract       合同文件
//     * @param contractConfig 签署配置
//     * @param userId         用户id
//     * @param fdata          文件base64
//     * @param signAttachId   手绘签名附加id
//     */
//    void contractFileSign(Contract contract, ContractConfig contractConfig, Long userId, String fdata, Long signAttachId);
//
//
//    /**
//     * 查询用户基本资料
//     *
//     * @param account
//     * @return
//     */
//    SsqUserBaseInfo userBaseInfo(String account);
//
//    /**
//     * 检查安全证书
//     *
//     * @param userId
//     */
//    SsqCertInfo checkCerInfo(Long userId);
//
//    /**
//     * 通过现存变量重新发送合同
//     *
//     * @param contract
//     * @return
//     */
//    ContractReturnData contractResend(ContractVO contract);
//
    /**
     * 延期
     *
     * @param contractId
     * @param time
     */
    void delayExpireTime(List<String> contractId, String time);
//
//    /**
//     * 模板自动签署
//     *
//     * @param signParam
//     * @return
//     */
//    void templateAutoSign(SignParam signParam);
//
    /**
     * 查找关键字坐标
     *
     * @param fileBase64 文件base64
     * @param signAlign
     * @param keywords   关键字
     * @return
     */
    SsqSignaturePositions getSignaturePositions(String fileBase64, String signAlign, String keywords);
//
//    /**
//     * 证书申请
//     *
//     * @param signerId
//     */
//    void reApplyCert(Long signerId);
//
//    /**
//     * 注册个人
//     *
//     * @param regEleSignDTO
//     */
//    void regPerson(RegEleSignDTO regEleSignDTO);
//
//    /**
//     * 注册企业
//     *
//     * @param regEntEleSignDTO
//     */
//    void regEnt(RegEntEleSignDTO regEntEleSignDTO);
//
    /**
     * 获取当前合同状态
     * @param contractId
     * @return
     */
    JSONObject getContractStatus(String contractId);
}
