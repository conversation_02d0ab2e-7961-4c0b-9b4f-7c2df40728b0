/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 合同签名/印章配置实体类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Data
@Accessors(chain = true)
@TableName("jrzh_contract_sign_seal")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractConfig对象", description = "合同配置")
public class ContractSignSeal extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 签章类型（0：签名，1：印章）
     */
    @ApiModelProperty(value = "类别（0：签名，1：印章）")
    private Integer category;
    /**
     * 印章图片类型（0：生成，1：上传）
     */
    @ApiModelProperty(value = "签名/印章附件id")
    private Long attachId;

    /**
     * 账户id
     */
    @ApiModelProperty(value = "生成的签名/印章URL")
    private String generateLink;

}
