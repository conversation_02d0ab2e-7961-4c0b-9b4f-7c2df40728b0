package cn.lili.modules.jrzh_contract.contract_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同模板分类配置实体类
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Data
@TableName("jrzh_contract_template_category_config")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractTemplateCategoryConfig对象", description = "合同模板分类配置")
public class ContractTemplateCategoryConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品分类ID")
    private String categoryId;

    @ApiModelProperty(value = "分类路径")
    private String categoryPath;

    @ApiModelProperty(value = "合同模板ID")
    private String templateId;

    @ApiModelProperty(value = "优先级，数值越大优先级越高")
    private Integer priority;

    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "合同类型：1-现货，2-期货")
    private Integer contractType;
}
