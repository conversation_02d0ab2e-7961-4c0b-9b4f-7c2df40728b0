package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.utils.BatchOperationResult;
import cn.lili.common.utils.ValidationUtils;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateCategoryConfig;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateCategoryConfigVO;
import cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractTemplateCategoryConfigMapper;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateCategoryConfigService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同模板分类配置服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContractTemplateCategoryConfigServiceImpl implements IContractTemplateCategoryConfigService {

    private final ContractTemplateCategoryConfigMapper categoryConfigMapper;

    @Override
    public List<ContractTemplateCategoryConfig> getCategoryConfigs(String categoryId) {
        LambdaQueryWrapper<ContractTemplateCategoryConfig> wrapper = Wrappers.<ContractTemplateCategoryConfig>lambdaQuery()
            .eq(ContractTemplateCategoryConfig::getCategoryId, categoryId)
            .eq(ContractTemplateCategoryConfig::getStatus, 1)
            .orderByDesc(ContractTemplateCategoryConfig::getPriority);

        return categoryConfigMapper.selectList(wrapper);
    }

    @Override
    public List<ContractTemplateCategoryConfig> getBatchCategoryConfigs(List<String> categoryIds) {
        if (CollUtil.isEmpty(categoryIds)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<ContractTemplateCategoryConfig> wrapper = Wrappers.<ContractTemplateCategoryConfig>lambdaQuery()
            .in(ContractTemplateCategoryConfig::getCategoryId, categoryIds)
            .eq(ContractTemplateCategoryConfig::getStatus, 1)
            .orderByDesc(ContractTemplateCategoryConfig::getPriority);

        return categoryConfigMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveOrUpdateCategoryConfig(ContractTemplateCategoryConfig config) {
        log.info("开始保存或更新分类模板配置，分类ID：{}，模板ID：{}", config.getCategoryId(), config.getTemplateId());

        // 参数验证
        ValidationUtils.notNull(config, "配置信息不能为空");
        ValidationUtils.notEmpty(config.getCategoryId(), "分类ID不能为空");
        ValidationUtils.notEmpty(config.getTemplateId(), "模板ID不能为空");

        // 查找是否已存在相同配置
        ContractTemplateCategoryConfig existingConfig = categoryConfigMapper.selectOne(
            Wrappers.<ContractTemplateCategoryConfig>lambdaQuery()
                .eq(ContractTemplateCategoryConfig::getCategoryId, config.getCategoryId())
                .eq(ContractTemplateCategoryConfig::getTemplateId, config.getTemplateId())
        );

        if (ObjectUtil.isNotEmpty(existingConfig)) {
            // 更新现有配置
            log.info("找到现有配置，执行更新操作，配置ID：{}", existingConfig.getId());

            // 保留原有ID和创建信息
            config.setId(existingConfig.getId());
            config.setCreateTime(existingConfig.getCreateTime());
            config.setCreateBy(existingConfig.getCreateBy());

            // 设置默认值（如果前端没有传递）
            if (ObjectUtil.isEmpty(config.getPriority())) {
                config.setPriority(existingConfig.getPriority() != null ? existingConfig.getPriority() : 0);
            }
            if (ObjectUtil.isEmpty(config.getStatus())) {
                config.setStatus(existingConfig.getStatus() != null ? existingConfig.getStatus() : 1);
            }

            // 执行更新
            return ValidationUtils.executeAndValidate(
                () -> categoryConfigMapper.updateById(config),
                "分类模板配置更新成功，配置ID：{}",
                "更新分类模板配置失败",
                config.getId()
            );
        } else {
            // 新增配置
            log.info("未找到现有配置，执行新增操作");

            // 设置默认值
            if (ObjectUtil.isEmpty(config.getPriority())) {
                config.setPriority(0);
            }
            if (ObjectUtil.isEmpty(config.getStatus())) {
                config.setStatus(1);
            }

            // 执行插入
            return ValidationUtils.executeAndValidate(
                () -> categoryConfigMapper.insert(config),
                "分类模板配置保存成功，配置ID：{}",
                "保存分类模板配置失败",
                config.getId()
            );
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteCategoryConfig(String configId) {
        log.info("开始删除分类模板配置，配置ID：{}", configId);

        // 参数验证
        ValidationUtils.notEmpty(configId, "配置ID不能为空");

        // 检查配置是否存在
        ContractTemplateCategoryConfig config = categoryConfigMapper.selectById(configId);
        ValidationUtils.checkExists(config, "配置不存在", configId);

        // 执行删除并校验结果
        return ValidationUtils.executeAndValidate(
            () -> categoryConfigMapper.deleteById(configId),
            "分类模板配置删除成功，配置ID：{}",
            "删除分类模板配置失败",
            configId
        );
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchConfigCategoryTemplate(List<String> categoryIds, String templateId) {
        // 参数验证
        ValidationUtils.notEmpty(categoryIds, "分类ID列表不能为空");
        ValidationUtils.notEmpty(templateId, "模板ID不能为空");

        BatchOperationResult result = new BatchOperationResult("批量配置分类模板");
        result.logStart(categoryIds.size(), templateId);

        for (String categoryId : categoryIds) {
            try {
                // 跳过空值
                if (StrUtil.isEmpty(categoryId)) {
                    result.logSkip("分类ID为空");
                    continue;
                }

                // 检查重复性
                long count = categoryConfigMapper.selectCount(
                    Wrappers.<ContractTemplateCategoryConfig>lambdaQuery()
                        .eq(ContractTemplateCategoryConfig::getCategoryId, categoryId)
                        .eq(ContractTemplateCategoryConfig::getTemplateId, templateId)
                );

                if (count > 0) {
                    result.logSkip("配置已存在", categoryId, templateId);
                    continue;
                }

                // 创建配置
                ContractTemplateCategoryConfig config = new ContractTemplateCategoryConfig();
                config.setCategoryId(categoryId);
                config.setTemplateId(templateId);
                config.setPriority(0);
                config.setStatus(1);

                // 执行插入
                int insertResult = categoryConfigMapper.insert(config);
                if (insertResult > 0) {
                    result.addSuccess();
                    log.debug("分类模板配置成功，分类ID：{}，配置ID：{}", categoryId, config.getId());
                } else {
                    result.logError("插入失败", categoryId);
                }
            } catch (Exception e) {
                result.logError("配置异常", e, categoryId);
            }
        }

        result.logComplete();
        result.checkAndThrowIfError();

        return result.hasSuccess();
    }

    @Override
    public IPage<ContractTemplateCategoryConfigVO> getCategoryConfigPage(Page<ContractTemplateCategoryConfig> page, String categoryId, String templateId) {
        log.info("开始分页查询分类模板配置，页码：{}，页大小：{}，分类ID：{}，模板ID：{}",
            page.getCurrent(), page.getSize(), categoryId, templateId);

        try {
            LambdaQueryWrapper<ContractTemplateCategoryConfig> wrapper = Wrappers.<ContractTemplateCategoryConfig>lambdaQuery()
                    .eq(StrUtil.isNotEmpty(categoryId), ContractTemplateCategoryConfig::getCategoryId, categoryId)
                    .eq(StrUtil.isNotEmpty(templateId), ContractTemplateCategoryConfig::getTemplateId, templateId)
                    .orderByDesc(ContractTemplateCategoryConfig::getCreateTime);

            IPage<ContractTemplateCategoryConfig> result = categoryConfigMapper.selectPage(page, wrapper);
            IPage<ContractTemplateCategoryConfigVO> voPage = convertToCategoryConfigVOPage(result);

            log.info("分页查询分类模板配置完成，总数：{}，当前页数据：{}", result.getTotal(), voPage.getRecords().size());
            return voPage;
        } catch (Exception e) {
            log.error("分页查询分类模板配置异常，错误：{}", e.getMessage(), e);
            throw new ServiceException("查询分类模板配置失败：" + e.getMessage());
        }
    }

    /**
     * 转换为分类配置VO分页对象
     */
    private IPage<ContractTemplateCategoryConfigVO> convertToCategoryConfigVOPage(IPage<ContractTemplateCategoryConfig> result) {
        IPage<ContractTemplateCategoryConfigVO> voPage = new Page<>(result.getCurrent(), result.getSize(), result.getTotal());
        List<ContractTemplateCategoryConfigVO> voList = result.getRecords().stream()
                .map(this::convertToCategoryConfigVO)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());
        voPage.setRecords(voList);
        return voPage;
    }

    /**
     * 转换为分类配置VO
     */
    private ContractTemplateCategoryConfigVO convertToCategoryConfigVO(ContractTemplateCategoryConfig config) {
        try {
            ContractTemplateCategoryConfigVO vo = new ContractTemplateCategoryConfigVO();
            vo.setId(config.getId());
            vo.setCategoryId(config.getCategoryId());
            vo.setCategoryPath(config.getCategoryPath());
            vo.setTemplateId(config.getTemplateId());
            vo.setPriority(config.getPriority());
            vo.setStatus(config.getStatus());
            vo.setRemark(config.getRemark());
            vo.setContractType(config.getContractType());
            vo.setCreateTime(config.getCreateTime());
            vo.setUpdateTime(config.getUpdateTime());

            // 合同类型字段保留但不再使用业务逻辑
            // contractType字段已废弃，不再区分现货期货类型

            // TODO: 填充分类名称和模板名称
            return vo;
        } catch (Exception e) {
            log.error("转换分类配置VO异常，配置ID：{}，错误：{}", config.getId(), e.getMessage());
            return null;
        }
    }
}
