<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractTemplateResultMap" type="cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate">
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="create_dept" property="createDept"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="template_id" property="templateId"/>
        <result column="template_name" property="templateName"/>
        <result column="template_status" property="templateStatus"/>
        <result column="template_category" property="templateCategory"/>
        <result column="operate_time" property="operateTime"/>
        <result column="operate_name" property="operateName"/>
        <result column="attach_id" property="attachId"/>

    </resultMap>

    <select id="selectTemplatePage" resultType="cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate">
        select * from jrzh_contract_template  where   is_deleted=0
        <if test="contractTemplate.templateId!=null">
            and  template_id= #{contractTemplate.templateId}
        </if>
        <if test="contractTemplate.operateName!=null">
            and  operate_name= #{contractTemplate.operateName}
        </if>
        <if test="contractTemplate.templateName!=null">
            and  template_name= #{contractTemplate.templateName}
        </if>
        order by  create_time  desc

    </select>
    <select id="selectContractTemplatePage" resultMap="contractTemplateResultMap">
        select * from jrzh_contract_template where is_deleted = 0
        <if test="contractTemplate.templateId!=null">
            and  template_id= #{contractTemplate.templateId}
        </if>
        <if test="contractTemplate.operateName!=null">
            and  operate_name= #{contractTemplate.operateName}
        </if>
        <if test="contractTemplate.templateName!=null">
            and  template_name= #{contractTemplate.templateName}
        </if>
        order by  create_time desc

    </select>
    <select id="selectContractTemplateQuery"
            resultType="cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate">
        select  *  from  jrzh_contract_template  where  is_deleted=0 and status=1  limit #{currentPage} , #{pageSize}

    </select>
    <select id="selectDel" resultType="cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate">
        select *  from  jrzh_contract_template where   is_deleted=1
    </select>
    <select id="selectAppointBusinessInfo" resultType="java.lang.String" statementType="STATEMENT">
        select   ${fieldName} from  ${tableName} where   is_deleted=0
        <if test="customerId!=null">
            and customer_id=${customerId}
        </if>
        <if test="goodId!=null and tableName='jrzh_goods'">
            and id=${goodId}
        </if>
        <if test="goodId!=null and tableName!='jrzh_goods'">
            and goods_id=${goodId}
        </if>

    </select>

    <select id="selectTableInfo" resultType="java.lang.Object" statementType="STATEMENT">
        select  *  from  ${tableName}  where  is_deleted=0
        <if test="customerId!=null">
            and customer_id=${customerId}
        </if>
        <if test="goodId!=null">
            and id=${goodId}
        </if>
    </select>

</mapper>
