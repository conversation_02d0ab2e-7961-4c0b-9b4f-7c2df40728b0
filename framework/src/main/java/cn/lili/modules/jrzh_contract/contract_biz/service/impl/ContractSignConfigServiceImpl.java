/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractSignConfig;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractSignConfigVO;
import cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractSignConfigMapper;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractSignConfigService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 合同模板签署配置 服务实现类
 *
 * <AUTHOR>
 * @since 2022-09-05
 */
@Service
@RequiredArgsConstructor
public class ContractSignConfigServiceImpl extends ServiceImpl<ContractSignConfigMapper, ContractSignConfig> implements IContractSignConfigService {

	@Override
	public IPage<ContractSignConfigVO> selectContractSignConfigPage(IPage<ContractSignConfigVO> page, ContractSignConfigVO contractSignConfig) {
		return page.setRecords(baseMapper.selectContractSignConfigPage(page, contractSignConfig));
	}

	@Override
	public List<ContractSignConfig> listByTemplateId(String templateId) {
		return list(Wrappers.<ContractSignConfig>lambdaQuery().eq(ContractSignConfig::getTemplateId, templateId));
	}

	@Override
	public void removeByTemplateId(String templateId) {
		remove(Wrappers.<ContractSignConfig>lambdaQuery().eq(ContractSignConfig::getTemplateId, templateId));
	}

	@Override
	public List<ContractSignConfig> listByTemplateIdAndSort(Long contractTemplateId, Integer signerSort) {
		return list(Wrappers.<ContractSignConfig>lambdaQuery().eq(ContractSignConfig::getTemplateId, contractTemplateId).eq(ContractSignConfig::getSignNo, signerSort));
	}

	@Override
	public Map<String, List<ContractSignConfig>> mapByTemplateIds(ArrayList<String> templateIds) {
		List<ContractSignConfig> list = list(Wrappers.<ContractSignConfig>lambdaQuery().in(ContractSignConfig::getTemplateId, templateIds));
		if (CollUtil.isEmpty(list)) {
			return new HashMap<>();
		}
		return list.stream().collect(Collectors.groupingBy(ContractSignConfig::getTemplateId));
	}

}
