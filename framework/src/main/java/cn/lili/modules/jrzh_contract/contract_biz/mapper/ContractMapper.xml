<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractResultMap" type="cn.lili.modules.jrzh_contract.contract_api.entity.Contract">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="create_dept" property="createDept"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="contract_title" property="contractTitle"/>
        <result column="contract_id" property="contractId"/>
        <result column="send_time" property="sendTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="sign_dead_line" property="signDeadLine"/>
        <result column="file_url" property="fileUrl"/>
        <result column="attach_id" property="attachId"/>
        <result column="sub_contract_id" property="subContractId"/>
        <result column="has_sub_contract" property="hasSubContract"/>
        <result column="customer_id" property="customerId"/>
        <result column="sign_node" property="signNode"/>
        <result column="contract_template_id" property="contractTemplateId"/>
        <result column="contract_biz_no" property="contractBizNo"/>
        <result column="process_type" property="processType"/>
        <result column="goods_id" property="goodsId"/>
        <result column="goods_type" property="goodsType"/>
        <result column="good_name" property="goodName"/>
        <result column="finance_apply_id" property="financeApplyId"/>
        <result column="seller_id" property="sellerId"/>
        <result column="buyer_id" property="buyerId"/>
    </resultMap>


    <select id="selectContractPage" resultMap="contractResultMap">
        select * from jrzh_contract where is_deleted = 0
        <if test="contract.goodName!=null">
            and goods_id=#{contract.goodName}
        </if>
        <if test="contract.contractId!=null">
            and contract_id=#{contract.contractId}
        </if>
        <if test="contract.contractTitle!=null">
            and contract_title like concat('%',#{contract.contractTitle} ,'%')
        </if>
        <if test="contract.status">
            and status=#{contract.status}
        </if>
        <if test="contract.processType!=null">
            and process_type=#{contract.processType}
        </if>

        <if test="contract.signNode!=null">
            and sign_node=#{contract.signNode}
        </if>
        <if test="contract.sendTime!=null">
            and send_time=#{contract.sendTime}
        </if>
        <if test="contract.financeApplyId!=null">
            and finance_apply_id=#{contract.financeApplyId}
        </if>
        <if test="contract.goodsTypeList!=null">
            and good_type in
            <foreach collection="contract.goodsTypeList" item="goodsType" index="index" open="(" close=")" separator=",">
                #{goodsType}
            </foreach>
        </if>

        order by create_time desc
    </select>

    <select id="selectCustomerContractPage" resultMap="contractResultMap">
        select * from jrzh_contract_operator op  left join jrzh_contract con  on op.contract_id=con.contract_id where
        con.is_deleted = 0
        and con.status not in (2,4)
        <if test="contract.userId!=null">
            and op.signer_id=#{contract.userId}
        </if>
        <if test="contract.contractId!=null">
            and op.contract_id=#{contract.contractId}
        </if>
        <if test="contract.contractTitle!=null">
            and con.contract_title like concat('%',#{contract.contractTitle} ,'%')
        </if>
        <if test="contract.contractStatus">
            and find_in_set(op.status,#{contract.contractStatus})
        </if>
        <if test="contract.goodName!=null and contract.goodIds!=null">
            and find_in_set(con.goods_id,#{contract.goodIds})
        </if>
        <if test="contract.startTime!=null">
            and con.send_time>= #{contract.startTime}
        </if>
        <if test="contract.contractProcessType!=null">
            and find_in_set (con.process_type,#{contract.contractProcessType})
        </if>

        <if test="contract.endTime!=null">
            and #{contract.endTime}>= con.send_time
        </if>
        <if test="contract.financeApplyId!=null">
            and con.finance_apply_id=#{contract.financeApplyId}
        </if>
        <if test="contract.goodsTypeList!=null">
            and con.good_type in
            <foreach collection="contract.goodsTypeList" item="goodsType" open="(" close=")" separator=",">
                #{goodsType}
            </foreach>
        </if>
        order by send_time desc
    </select>

    <select id="selectUserSignContractPage" resultMap="contractResultMap">
        select * from jrzh_contract_operator op  left join jrzh_contract con  on op.contract_id=con.contract_id
        LEFT JOIN
        (select id as other_goods_id,goods_name from jrzh_goods
        union select  id as other_goods_id,goods_name from jrzh_agent_goods
        union select  id as other_goods_id,goods_name from jrzh_cloud_product) goods on con.goods_id=goods.other_goods_id
        where
        con.is_deleted = 0
        <if test="contract.userId!=null">
            and op.signer_id=#{contract.userId}
        </if>
        <if test="contract.contractId!=null">
            and op.contract_id=#{contract.contractId}
        </if>
        <if test="contract.contractTitle!=null">
            and con.contract_title like concat('%',#{contract.contractTitle} ,'%')
        </if>
        <if test="contract.contractStatus">
            and find_in_set(op.status,#{contract.contractStatus})
        </if>
        <if test="contract.goodName!=null">
            and goods.goods_name like concat('%',#{contract.goodName} ,'%')
        </if>
        <if test="contract.startTime!=null">
            and <![CDATA[ DATE_FORMAT(con.send_time, '%Y-%m-%d') >= #{contract.startTime} ]]>
        </if>
        <if test="contract.contractProcessType!=null">
            and find_in_set (con.process_type,#{contract.contractProcessType})
        </if>

        <if test="contract.endTime!=null">
            and <![CDATA[ DATE_FORMAT(con.send_time, '%Y-%m-%d') <= #{contract.endTime} ]]>
        </if>
        <if test="contract.financeApplyId!=null">
            and con.finance_apply_id=#{contract.financeApplyId}
        </if>
        <if test="contract.goodsTypeList!=null">
            and con.good_type in
            <foreach collection="contract.goodsTypeList" item="goodsType" open="(" close=")" separator=",">
                #{goodsType}
            </foreach>
        </if>
        order by send_time desc
    </select>

    <select id="selectByGenerateDynamicContractDTO"
            resultType="cn.lili.modules.jrzh_contract.contract_api.entity.Contract">
        select *
        from jrzh_contract where is_deleted = 0
        and status %2!=0
        <if test="goodId!=null">
            and goods_id = #{goodId}
        </if>
        <if test="processType!=null">
            and process_type = #{processType}
        </if>
        <if test="signNode!=null">
            and sign_node = #{signNode}
        </if>
        <if test="templateId!=null">
            and contract_template_id = #{templateId}
        </if>
        <if test="financeApplyId!=null">
            and finance_apply_id=#{financeApplyId}
        </if>
        order by create_time desc
    </select>

</mapper>
