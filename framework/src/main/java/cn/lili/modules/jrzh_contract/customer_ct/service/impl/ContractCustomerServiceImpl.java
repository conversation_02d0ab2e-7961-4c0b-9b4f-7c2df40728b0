/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.customer_ct.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.lili.common.enums.CustomerTradingTypeEnum;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.modules.connect.entity.CustomerInfo;
import cn.lili.modules.connect.service.ICustomerInfoService;
import cn.lili.modules.jrzh_bases.CustomerTypeEnum;
import cn.lili.modules.jrzh_bases.Func;
import cn.lili.modules.jrzh_bases.ThreadUtils;
import cn.lili.modules.jrzh_contract.contract_api.dto.ContractParamDTO;
import cn.lili.modules.jrzh_contract.contract_api.dto.ContractSignParam;
import cn.lili.modules.jrzh_contract.contract_api.dto.DynamicContractDTO;
import cn.lili.modules.jrzh_contract.contract_api.dto.SignParam;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractOperator;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.enums.ContractTypeEnum;
import cn.lili.modules.jrzh_contract.contract_biz.service.*;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractReturnData;
import cn.lili.modules.jrzh_contract.customer_ct.dto.CustomerContractDataParamsDTO;
import cn.lili.modules.jrzh_contract.customer_ct.service.IContractCustomerService;
import cn.lili.modules.jrzh_other.bestsign.constant.ContractEnum;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 客户端合同 服务类实现
 *
 * <AUTHOR>
 * @since 2022-01-07
 */
@Slf4j
@Service
@AllArgsConstructor
public class ContractCustomerServiceImpl implements IContractCustomerService {
    private IContractService contractService;
    private IContractGenService contractGenService;
    private IContractSignService contractSignService;
    private IContractConfigService contractConfigService;
    private ICustomerInfoService customerInfoService;
    private IContractTemplateConfigService contractTemplateConfigService;
    private IContractTypeDetectionService contractTypeDetectionService;

    /**
     * 自动签署配置
     */
    private final static Integer AUTH_SIGN_WAY = 1;
    /**
     * 手动签配置
     */
    private final static Integer HAND_SIGN_WAY = 0;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ContractReturnData generateContractWithBusiness(CustomerContractDataParamsDTO params) {
        //参数校验
        checkParam(params);
        //查询是否生成过 生成过则直接返回
        Contract contractOld = contractService.getContractByEntity(BeanUtil.copyProperties(params, ContractParamDTO.class));
        if (ObjectUtil.isNotEmpty(contractOld)) {
            //若合同过期 则自动续期
            if (contractOld.getSignDeadLine().compareTo(new Date()) < 0) {
                //TODO暂时没续期
                contractService.delay(contractOld.getContractId());
            }
            return new ContractReturnData(contractOld.getFileUrl(), contractOld.getContractId(),contractOld.getContractTitle());
        }
        //合同生成的参数填充
        DynamicContractDTO dynamicContractDTO = settingContractParam(params);
        //合同生成 并保存
        Contract contract = contractGenService.genContract(dynamicContractDTO);
        return new ContractReturnData(contract.getFileUrl(), contract.getContractId(),contract.getContractTitle());
    }

    //
    @Override
    public void signByCurrentUser(ContractSignParam signParam) {
        AuthUser authUser = UserContext.getCurrentUser();
        if (null == authUser) {
            throw new ServiceException("请先登录");
        }
        CustomerInfo info = customerInfoService.getBuyCustomerId(authUser.getId());
        if (ObjectUtil.isEmpty(info)){
            throw new ServiceException("当前用户未实名");
        }
        Long userId = Long.valueOf(UserContext.getCurrentUser().getId());

        Integer customerTradingType;
        if (null == authUser.getStoreId()) {
            customerTradingType = CustomerTradingTypeEnum.BUYER.getCode();
        } else {
            customerTradingType = CustomerTradingTypeEnum.SELLER.getCode();
        }
        //合同检查
        validSign(signParam);
        //当前签署人用户信息
        String personalName = info.getCorporationName();
        String companyName = info.getCorpName();
        Long personalUserId = userId;
        //获取签署人配置 根据用户设置的签署方式进行合同异步合同签署
        ContractConfig contractConfig = contractConfigService.getByCompanyId(userId);
        Integer signWay = HAND_SIGN_WAY;
        if (ObjectUtil.isNotEmpty(contractConfig)) {
            signWay = contractConfig.getSignWay();
        }
        //设置异步线程
        List<String> contractIds = Func.toStrList(signParam.getContractIds());
        List<CompletableFuture<?>> futures = new ArrayList<>();
        for (int i = 0; i < contractIds.size(); i++) {
            //非自动签 第一份合同进行校验
            if (i == 0 && ContractEnum.ContractVerifyType.V_CODE.getType().equals(signParam.getVerifyType())) {
                //TODO暂时不校验
                validContractByType(signParam, signWay, "000000");
            }
            String contractId = contractIds.get(i);
            final SignParam param = SignParam.builder()
                    //TODO用户还没生成签章，先用3.0的签署用户
                    .signerId(userId)
                    .customerType(CustomerTypeEnum.ENTERPRISE.getCode())
                    .signerName(companyName)
                    .customerTradingType(customerTradingType)
                    .personalName(personalName)
                    .personalUserId(personalUserId)
                    .code(signParam.getCode().getValue())
                    .contractId(contractId)
                    .customizeWriteBase64(signParam.getCustomizeWriteBase64())
                    .build();
            int finalI = i;
            //TODO服务启动注释
            futures.add(ThreadUtils.supplyAsync(() -> {
                contractSignService.sign(param);
                return finalI;
            }));
        }
        //等待异步签署完成//TODO服务启动注释
        ThreadUtils.allOf(futures.toArray(new CompletableFuture[0])).join();
//        签署完成后异步更新合同文件
        ThreadUtils.runAsync(() -> {
            contractService.signedUpdateContractUrl(contractIds);
        });
    }
//
//    @Override
//    public String downLoadContract(String contractId) {
//        return contractService.downLoadAndUpdateContract(contractId);
//    }
//

    /**
     * 根据签署方式校验
     *
     * @param signParam
     * @param signWay
     * @param tenantId
     */
    private void validContractByType(ContractSignParam signParam, Integer signWay, String tenantId) {
        if (!AUTH_SIGN_WAY.equals(signWay)) {
//            签署校验验证码//TODO服务开起来注释
//            boolean verification = smsBuilder.myTemplate(tenantId, "verification").validateMessage(signParam.getCode());
//            if (!verification) {
//                throw new ServiceException("验证码错误或已失效，请重新发送验证码");
//            }
        }
    }

    //
//
//
    private void validSign(ContractSignParam signParam) {
        //合同检查
        String contractIds = signParam.getContractIds();
        int count = (int) contractService.count(Wrappers.<Contract>lambdaQuery()
                .in(Contract::getContractId, Func.toStrList(contractIds)));
        if (count < Func.toStrList(contractIds).size()) {
            throw new ServiceException("合同签署异常，存在非法合同，请确认合同是否存在");
        }
    }
//
//    private void platAutoSign(ContractOperator platSigner, Contract contract) {
////        String tenantId = MyAuthUtil.getTentId();
////        ThreadUtils.runAsync(() -> {
////            TenantBroker.runAs(tenantId, e -> {
////                ContractCurrentSignInfo currentSignInfo = ContractCurrentSignInfo.builder()
////                        .userId(platUserId)
////                        .customerType(CustomerTypeEnum.ENTERPRISE.getCode())
////                        .companyName(platCompanyInfo.getCorpName())
////                        .personalName(platCustomer.getName())
////                        .personalUserId(platCustomer.getUserId())
////                        .build();
////                ContractSignParam signParam = ContractSignParam.builder()
////                        .code(new SmsCode())
////                        .contractIds(contractId)
////                        .customizeWriteBase64(null)
////                        .currentSignInfo(currentSignInfo)
////                        .build();
////                platSign(signParam, tenantId);
////            });
////        });
//    }
//

    /// /    /**
    /// /     * 添加平台签署账号
    /// /     *
    /// /     * @param dynamicContractDTO
    /// /     * @return
    /// /     */
    /// /    private ContractOperator addPlatSigner(DynamicContractDTO dynamicContractDTO) {
    /// /        //若需要平台方签署 添加平台方签署
    /// /        ContractTemplate template = contractTemplateService.getByTemplateId(dynamicContractDTO.getContractTemplateId().toString());
    /// /        if (CommonConstant.YES.equals(template.getNeedPlatSign())) {
    /// /            //获取平台方参数
    /// /            ApiParamDTO param = OtherApiUtils.getMapSingleParams(OtherApiTypeEnum.ELEC_SIGN.getCode());
    /// /            Map<String, String> paramMap = param.getParamMap();
    /// /            String platAccount = paramMap.get("platAccount");
    /// /            if (ObjectUtil.isEmpty(platAccount)) {
    /// /                throw new ServiceException("平台方合同参数未设置");
    /// /            }
    /// /            //查询平台方账号信息
    /// /            Long platUserId = Func.toLong(platAccount);
    /// /            CustomerFrontUserType frontUserType = customerFrontUserTypeService.getByRoleId(platAccount);
    /// /            CustomerInfo platCompanyInfo = customerInfoService.getByCompanyId(platUserId);
    /// /            Customer platCustomer = customerService.getById(frontUserType.getCustomerFrontUserId());
    /// /            //添加平台为当前签署人
    /// /            ContractOperator contractOperator = ContractOperator.builder()
    /// /                    .signerCompanyName(platCompanyInfo.getCorpName())
    /// /                    .userType(CustomerTypeEnum.ENTERPRISE.getCode())
    /// /                    .signerName(platCustomer.getName())
    /// /                    .signerId(platUserId)
    /// /                    .build();
    /// /            dynamicContractDTO.getContractOperatorList().add(contractOperator);
    /// /            dynamicContractDTO.setPlatSigner(platUserId);
    /// /            return contractOperator;
    /// /        }
    /// /        return null;
    /// /    }
//
    private DynamicContractDTO settingContractParam(CustomerContractDataParamsDTO params) {
        //合同基础参数添加
        DynamicContractDTO dynamicContractDTO = new DynamicContractDTO();

        dynamicContractDTO.setContractTemplateId(Long.valueOf(params.getTemplateId()));
        dynamicContractDTO.setContractBizNo(params.getContractBizNo());
        dynamicContractDTO.setBigBizNo(params.getBigBizNo());
        dynamicContractDTO.setFrontDataSource(params.getFrontDataSource());
        //合同生成参数添加
        AuthUser user = UserContext.getCurrentUser();
        CustomerInfo info = customerInfoService.getOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCustomerId, user.getId()));
        //添加当前签署人为签署者
        List<ContractOperator> contractOperatorList = new ArrayList<>();
        //TODO
        ContractOperator contractOperator = ContractOperator.builder()
                .signerId(Long.valueOf(user.getId()))
                .signerName(user.getNickName())
                .signerCompanyName(info.getCorpName())
                .contractNo(params.getContractNo())
                .build();
        contractOperatorList.add(contractOperator);
        dynamicContractDTO.setContractOperatorList(contractOperatorList);
        return dynamicContractDTO;
    }

    /**
     * 获取合同模板ID
     * 优先级：手动指定 > 商品绑定模板
     */
    private Long getTemplateId(CustomerContractDataParamsDTO params) {
        // 1. 如果手动指定了模板ID，直接使用
        if (StrUtil.isNotEmpty(params.getTemplateId())) {
            return Long.valueOf(params.getTemplateId());
        }

        // 2. 如果有商品ID，直接查询商品绑定的合同模板
        if (ObjectUtil.isNotEmpty(params.getGoodId())) {
            String templateId = contractTemplateConfigService.getGoodsBoundTemplateId(params.getGoodId().toString());
            if (StrUtil.isNotEmpty(templateId)) {
                return Long.valueOf(templateId);
            }
        }

        throw new ServiceException("该商品未配置合同模板，请联系商家配置后重试");
    }

    /**
     * 从参数中检测合同类型
     */
    private Integer detectContractTypeFromParams(CustomerContractDataParamsDTO params) {
        // 如果有订单编号，根据订单信息检测合同类型
        if (StrUtil.isNotEmpty(params.getContractBizNo())) {
            ContractTypeEnum contractType = contractTypeDetectionService.detectContractType(params.getContractBizNo());
            return contractType.getCode();
        }

        // 如果没有订单编号，抛出异常
        throw new IllegalArgumentException("无法检测合同类型：缺少订单编号");
    }

    private void checkParam(CustomerContractDataParamsDTO params) {
        // 参数校验逻辑：templateId和contractBizNo都是必须的
        if (StrUtil.isEmpty(params.getTemplateId())  || ObjectUtil.isEmpty(params.getContractBizNo())) {
            throw new ServiceException("参数校验失败：合同模板ID和合同业务号不能为空");
        }
    }
}
