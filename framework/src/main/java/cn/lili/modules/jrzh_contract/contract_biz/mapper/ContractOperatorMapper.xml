<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.contract.mapper.ContractOperatorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="contractOperatorResultMap" type="org.springblade.modules.contract.entity.ContractOperator">
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="contract_id" property="contractId"/>
        <result column="signer_id" property="signerId"/>
        <result column="is_last" property="isLast"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>


    <select id="selectContractOperatorPage" resultMap="contractOperatorResultMap">
        select * from jrzh_contract_operator where is_deleted = 0
    </select>

</mapper>
