package cn.lili.modules.jrzh_contract.contract_api.dto;/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */


import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ContractParamDTO extends Contract {
    private static final long serialVersionUID = 1L;
    private String signNodes;
    private String processTypes;
    private String contractTemplates;
    private String contractIds;
    private String bizNos;
    private String bigBizNos;
    private String templateId;
    private Long goodId;

    @ApiModelProperty(value = "页号")
    private Integer pageNumber = 1;

    @ApiModelProperty(value = "页面大小")
    private Integer pageSize = 10;

    @ApiModelProperty(value = "排序字段")
    private String sort;

    @ApiModelProperty(value = "排序方式 asc/desc")
    private String order;
}
