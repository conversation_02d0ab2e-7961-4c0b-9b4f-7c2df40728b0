package cn.lili.modules.jrzh_contract.customer_ct.dto;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 客户合同-生成合同传输参数
 *
 * <AUTHOR>
 * @date 2022-6-27
 */
@Data
public class CustomerContractDataParamsDTO {
    /**
     * 合同模板id
     */
    private String templateId;
    /**
     * 合同模板ids
     */
    private List<String> templateIds;
    /**
     * 商品id
     */
    @NotNull
    private String goodId;
    /**
     * 产品类型
     */
    private Integer goodsType;
    /**
     * 流程id
     */
    private Long businessId;
    /**
     * 签署节点
     */
    @NotNull
    private String signNode;
    /**
     * 流程类型
     */
    @NotNull
    private Integer processType;
    /**
     * 用户类型id
     */
    private Long typeId;
    /**
     * 放款申请id
     */
    private Long financeApplyId;
    /**
     * 前端传输字段
     */
    private Object frontDataSource;
    /**
     * 合同业务流水
     */
    private String contractBizNo;
    /**
     * 是否提前缓存合同文件 0:否 1:是 默认不缓存
     */
    private Integer cacheOpen;

    /**
     * 大流程流水号
     */
    private String bigBizNo;

    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 融资编号
     */
    private String financeNo;
}
