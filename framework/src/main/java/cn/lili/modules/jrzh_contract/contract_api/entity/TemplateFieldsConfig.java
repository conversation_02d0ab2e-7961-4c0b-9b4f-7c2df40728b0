/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 合同模板字段表实体类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Data
@TableName("jrzh_contract_template_fields_config")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TemplateFieldsConfig对象", description = "合同模板字段表")
public class TemplateFieldsConfig extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 模板方案id
	 */
	@ApiModelProperty(value = "模板方案id")
	private Long templateConfigId;
	/**
	 * 配置id
	 */
	private Long configId;
	/**
	 * 配置json
	 */
	private String configJson;
	/**
	 * 父级id
	 */
	@ApiModelProperty(value = "父级id")
	@TableField("parentId")
	private Long parentId;
	/**
	 * 上上签业务字段名
	 */
	@ApiModelProperty(value = "上上签业务字段名")
	private String ssqFieldName;
	/**
	 * 属性名
	 */
	@ApiModelProperty(value = "属性名")
	private String fieldName;
	/**
	 * 父级属性名
	 */
	@ApiModelProperty(value = "父级属性名")
	private String parentFieldName;
	/**
	 * 合同绑定标识;0 未绑定 1绑定
	 */
	@ApiModelProperty(value = "合同绑定标识;0 未绑定 1绑定")
	private Integer izBind;

	@ApiModelProperty(value = "前端传输字段标识; 0:否 1:是")
	private Integer izFrontField;
	/**
	 * 类名
	 */
	private String clazzName;
}
