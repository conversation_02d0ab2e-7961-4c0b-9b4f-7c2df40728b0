package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.file.entity.File;
import cn.lili.modules.jrzh_bases.BladeFile;
import cn.lili.modules.jrzh_bases.Func;
import cn.lili.modules.jrzh_bases.Query;
import cn.lili.modules.jrzh_contract.contract_api.dto.ContractParamDTO;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractOperator;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractDetailsVO;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractVO;
import cn.lili.modules.jrzh_contract.contract_api.wrapper.ContractWrapper;
import cn.lili.modules.jrzh_contract.contract_biz.handler.ElecSignHandlerFactory;
import cn.lili.modules.jrzh_contract.contract_biz.handler.impl.ElecSignBestHandler;
import cn.lili.modules.jrzh_contract.contract_biz.mapper.ContractMapper;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractOperatorService;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractService;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateService;
import cn.lili.modules.jrzh_other.bestsign.constant.ContractEnum;
import cn.lili.modules.jrzh_other.bestsign.dto.SsqContractInfo;
import cn.lili.mybatis.util.PageUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.convert.ConversionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 合同服务
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContractServiceImpl extends ServiceImpl<ContractMapper, Contract> implements IContractService {
    private final IContractTemplateService contractTemplateService;
    private final IContractOperatorService contractOperatorService;
    private final ElecSignHandlerFactory elecSignHandlerFactory;
    private final ElecSignBestHandler elecSignBestHandler;
//    private final IDictBizService dictBizService;

    /**
     * 证书检查与注册
     *
     */
/*    private void signCertCheckAndRegister(Long signerId) {
        //查询证书
        SsqCertInfo ssqCertInfo = elecSignHandlerFactory.template().checkCerInfo(signerId);
        //若过期 则重新申请
        LocalDateTime now = LocalDateTime.now();
        if (now.isAfter(ssqCertInfo.getStopTime())) {
            elecSignHandlerFactory.template().reApplyCert(signerId);
        }
    }*/


    @Override
    public IPage<ContractVO> selectContractPage(IPage page, ContractVO contract) {
        return ContractWrapper.build().pageVO(baseMapper.selectContractPage(page, contract));
    }

    @SneakyThrows
    @Override
    public IPage<ContractVO> getContractList(Contract contract, PageVO vo) {
        ContractVO contractVO = BeanUtil.copyProperties(contract, ContractVO.class);
        IPage<ContractVO> contractIPage = this.selectContractPage(PageUtil.initPage(vo), contractVO);
        List<ContractVO> records = contractIPage.getRecords();
//        List<ContractVO> collect = records.stream().map(e -> BeanUtil.copyProperties(contract, ContractVO.class)).collect(Collectors.toList());
        //设置合同流程类型
//        setContractProcessType(records);
        if (CollectionUtil.isNotEmpty(records)){
            Date now= new Date();
            for (ContractVO e : records) {
                Date signDeadLine = e.getSignDeadLine();
                if (!ContractEnum.SSQ_CONTRACT_STATUS.isFinished(e.getStatus()) && ObjectUtil.isNotEmpty(signDeadLine) && now.compareTo(signDeadLine)>=0) {
                    e.setStatus(ContractEnum.SSQ_CONTRACT_STATUS.INVALID.getStatus());
                }
            }
        }
//        IPage<ContractVO> contractIPageBack = BeanUtil.copyProperties(contractIPage, IPage.class);
//        contractIPageBack.setRecords(collect);
        return contractIPage;
    }
//
//    /**
//     * 设置合同流程类型
//     *
//     * @param list
//     */
//    private void setContractProcessType(List<ContractVO> list) {
//        if (CollectionUtil.isEmpty(list)) {
//            return;
//        }
//        //字典构建 k:产品类型 v:产品对应的流程类型k：v
////        Map<Integer, Map<String, String>> contractProcessTypeMap = composeContractDicMap();
//        LocalDateTime now = LocalDateTime.now();
//        //合同状态变更
//        for (ContractVO contractVO : list) {
////            Integer goodType = contractVO.getGoodType();
////            Map<String, String> goodsTypeMap = contractProcessTypeMap.get(goodType);
////            if (CollectionUtil.isNotEmpty(goodsTypeMap)) {
////                contractVO.setTypeName(goodsTypeMap.getOrDefault(contractVO.getProcessType().toString(), ""));
////            } else {
////                contractVO.setTypeName("非流程合同");
////                contractVO.setGoodName("非产品");
////            }
//            contractVO.setGoodName("非产品");
//
//            //若合同未完成 已过期 显示过期
//            LocalDateTime signDeadLine = contractVO.getSignDeadLine();
//            if (!ContractEnum.SSQ_CONTRACT_STATUS.isFinished(contractVO.getStatus()) && ObjectUtil.isNotEmpty(signDeadLine) && now.compareTo(signDeadLine) >= 0) {
//                contractVO.setStatus(ContractEnum.SSQ_CONTRACT_STATUS.INVALID.getStatus());
//            }
//        }
//    }
//
///*    private Map<Integer, Map<String, String>> composeContractDicMap() {
//        Map<Integer, Map<String, String>> contractProcessTypeMap = new HashMap<>();
//        //需要查询的字典
//        final String RECEIVABLE_PROCESS_TYPE = "receivable_process_type";
//        final String SUBSTITUTE_PURCHASE_PROCESS_TYPE = "substitute_purchase_process_type";
//        final String CLOUD_PROCESS_CHILD_TYPE = "cloud_process_type";
//        final String ORDER_FINANCING_PROCESS_TYPE = "order_financing_process_type";
//        //查询字段进行组装
//        contractProcessTypeMap.put(
//                GoodsEnum.PLEDGE_OF_ACCOUNTS_RECEIVABLE.getCode()
//                , dictBizService.getList(RECEIVABLE_PROCESS_TYPE).stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (oldVal, newVal) -> oldVal))
//        );
//        contractProcessTypeMap.put(
//                GoodsEnum.AGENT_PURCHASE_FINANCING.getCode()
//                , dictBizService.getList(SUBSTITUTE_PURCHASE_PROCESS_TYPE).stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (oldVal, newVal) -> oldVal)));
//        contractProcessTypeMap.put(
//                GoodsEnum.CLOUD_CREDIT.getCode()
//                , dictBizService.getList(CLOUD_PROCESS_CHILD_TYPE).stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (oldVal, newVal) -> oldVal)
//                ));
//        contractProcessTypeMap.put(
//                GoodsEnum.ORDER_FINANCING.getCode()
//                , dictBizService.getList(ORDER_FINANCING_PROCESS_TYPE).stream().collect(Collectors.toMap(DictBiz::getDictKey, DictBiz::getDictValue, (oldVal, newVal) -> oldVal)
//                ));
//        return contractProcessTypeMap;
//    }*/
//
    @Override
    public ContractDetailsVO getDetails(String contractId) {
        ContractDetailsVO contractDetailsVO = new ContractDetailsVO();
        Contract contract = getByContractId(contractId);
        ContractVO contractVO = ContractWrapper.build().entityVO(contract);
        List<ContractOperator> contractOperatorList = contractOperatorService.listByContractId(contractId);
        ContractTemplate template = contractTemplateService.getByTemplateId(contractVO.getContractTemplateId().toString());
        contractVO.setContractTemplate(template);
        contractDetailsVO.setContract(contractVO);
        contractDetailsVO.setContractOperators(contractOperatorList);
        return contractDetailsVO;
    }
//
//    @Override
//    public String preViewWithAccountId(String contractId, String userId) {
//        return elecSignHandlerFactory.template().previewContract(contractId, userId);
//    }
//
//    @Override
//    public String preViewWithDevAccount(String contractId) {
//        return elecSignHandlerFactory.template().platTurnToContractPreView(contractId);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public String downLoadPDF(String contractId) {
//        Contract contract = getByContractId(contractId);
//        if (ObjectUtil.isEmpty(contract)) {
//            throw new ServiceException("合同查找失败");
//        }
//        if (ObjectUtil.isNotEmpty(contract.getSignedUrl())) {
//            return contract.getSignedUrl();
//        } else {
//            BladeFile bladeFile = elecSignHandlerFactory.template().downLoadContract(contract);
//            return bladeFile.getLink();
//        }
//    }
//
//
//    @Override
//    public boolean checkContractIsSign(List<String> contractIds, Long userId) {
//        if (CollectionUtils.isEmpty(contractIds)) {
//            return false;
//        }
//        Boolean allSigned = contractOperatorService.checkCurrentSignerIsSigned(contractIds, userId);
//        Assert.isTrue(allSigned, "合同未签署");
//        return true;
//    }
//
//    @Override
//    public IPage<ContractVO> selectContractCustomerPage(IPage page, ContractVO contract, Long userId) {
//        //TODO 待做
////        if (ObjectUtil.isNotEmpty(contract.getGoodName())) {
////            List<Long> goodsId = SpringUtil.getBean(IGoodsService.class).lambdaQuery()
////                    .like(Goods::getGoodsName, contract.getGoodName()).list()
////                    .stream().map(Goods::getId).collect(Collectors.toList());
////            contract.setGoodIds(Func.join(goodsId));
////        }
//
//        contract.setUserId(userId);
//        IPage<Contract> pages = baseMapper.selectCustomerContractPage(page, contract);
//        IPage<ContractVO> pageVO = ContractWrapper.build().pageVO(pages);
//        //设置模板信息
//        List<ContractVO> records = pageVO.getRecords();
//        if (CollectionUtil.isEmpty(records)) {
//            return pageVO;
//        }
//        List<String> templateIds = StreamUtil.map(records, e -> e.getContractTemplateId().toString());
//        Map<String, ContractTemplate> templateMap = contractTemplateService.listByTemplates(templateIds).stream().collect(Collectors.toMap(ContractTemplate::getTemplateId, e -> e));
//        for (ContractVO record : records) {
//            contract.setContractTemplate(templateMap.getOrDefault(record.getContractTemplateId().toString(), new ContractTemplate()));
//        }
//        return pageVO;
//    }
//
//    @Override
//    public boolean saveBatch(Collection<Contract> entityList) {
//        return super.saveBatch(entityList);
//    }
//
    /**
     * 请求上上签获取当前查询当前用户合同签署情况
     *
     * @param contract   合同信息
     * @param customerId 客户id
     * @return
     */
    @Override
    public Integer getSignStatus(Contract contract, Long customerId, Boolean needUpdate) {
        String contractId = contract.getContractId();
        Integer contractStatus = contract.getStatus();
        ContractOperator signerStatus = contractOperatorService.getByContractIdAndCustomerId(contractId, customerId);
        //获取合同结束节点状态  若未结束 则请求上上签
        Integer finishStatus = getFinishStatus(signerStatus, contractStatus);
        if (ObjectUtil.isEmpty(signerStatus)) {
            return ObjectUtil.isEmpty(finishStatus) ? ContractEnum.CONTRACT_OPERATOR_STATUS.NOT_START.getStatus() : finishStatus;
        }
        if (ObjectUtil.isEmpty(finishStatus)) {
            //TODO 待做
            if (needUpdate) {
                SsqContractInfo contractInfo = elecSignBestHandler.getContractInfo(contract.getContractId());
                cn.hutool.json.JSONObject ssqContractSigner = elecSignBestHandler.getContractStatus(contract.getContractId());
                //更新签署人状态及合同状态
                updateContractAndOperatorStatus(signerStatus, ssqContractSigner, contract, contractInfo);
            }
            //签署状态
            return signerStatus.getStatus();
        }
        return finishStatus;
    }
//
    /**
     * 对比最新状态 若与系统存在不一致 则更新
     *
     * @param contractOperator  系统签署人信息
     * @param ssqContractSigner 上上签签署人信息
     * @param contract          系统合同信息
     * @param contractDetails   上上签合同信息
     */
    private void updateContractAndOperatorStatus(ContractOperator contractOperator, cn.hutool.json.JSONObject ssqContractSigner, Contract contract, SsqContractInfo contractDetails) {
        IContractOperatorService operatorService = SpringUtil.getBean(IContractOperatorService.class);
        IContractService contractService = SpringUtil.getBean(IContractService.class);
        //上上签状态->精锐状态 更新签署人信息
        Integer operatorStatus = ContractEnum.CONTRACT_OPERATOR_STATUS.matchEnum(ssqContractSigner.getStr(contractOperator.getSignerId().toString()));
        if (!operatorStatus.equals(contractOperator.getStatus())) {
            operatorService.updateContractStatus(Long.valueOf(contractOperator.getId()), operatorStatus);
        }
        //若所有签署人都签署完毕 则将合同锁定 并完成合同
        List<Integer> singerIds = new ArrayList(ssqContractSigner.keySet());
        String contractId = contract.getContractId();
        contract = contractService.getByContractId(contractId);
        if (singerIds.size() > 0 && !ContractEnum.SSQ_CONTRACT_STATUS.COMPLETE.getStatus().equals(contract.getStatus())
                && contractOperatorService.operatorAllComplate(contract.getContractTemplateId().toString(), contractId)
        ) {
            //锁定
            contractLock(contractId);
            contractService.updateContractStatus(Long.valueOf(contract.getId()), ContractEnum.SSQ_CONTRACT_STATUS.COMPLETE.getStatus());
            //将前端状态改为已签署
        } else {
            //上上签状态->精锐状态 更新合同信息
            Integer contractStatus = ContractEnum.SSQ_CONTRACT_STATUS.matchEnum(contractDetails.getStatus());
            contractService.updateContractStatus(Long.valueOf(contract.getId()), contractStatus);
        }
        //回显当前用户签署状态
        contractOperator.setStatus(operatorStatus);
    }

    /**
     * 获取当前结束节点的状态
     *
     * @param signerStatus   当前签署人状态
     * @param contractStatus 合同状态
     * @return
     */
    private Integer getFinishStatus(ContractOperator signerStatus, Integer contractStatus) {
        //合同状态处于结束节点则跳过
        if (ContractEnum.SSQ_CONTRACT_STATUS.isFinished(contractStatus)) {
            return contractStatus;
        }
        //签署人状态是否处于结束节点
        if (ObjectUtil.isNotEmpty(signerStatus) && ContractEnum.CONTRACT_OPERATOR_STATUS.isFinished(signerStatus.getStatus())) {
            return signerStatus.getStatus();
        }
        return null;
    }
//
//    @Override
//    public void removeByGoodsIdAndUserId(Long goodsId) {
//        remove(Wrappers.<Contract>lambdaQuery()
//                .eq(Contract::getGoodsId, goodsId));
//    }
//
//    @Override
//    public List<Contract> listByBizNo(String bizNo) {
//        return list(Wrappers.<Contract>lambdaQuery().eq(Contract::getContractBizNo, bizNo).orderByDesc(Contract::getCreateTime));
//    }
//
//
    @Override
    public Contract getByContractId(String contractId) {
        return getOne(Wrappers.<Contract>lambdaQuery().eq(Contract::getContractId, contractId).orderByDesc(Contract::getCreateTime).last("limit 1"));
    }
//
//    @Override
//    public Contract getByBizNoAndTemplateId(String bizNo, String templateId) {
//        return getOne(Wrappers.<Contract>lambdaQuery().eq(Contract::getContractBizNo, bizNo)
//                .eq(Contract::getContractTemplateId, templateId).orderByDesc(Contract::getCreateTime).last("limit 1"));
//    }
//
    @Override
    public Boolean updateContractStatus(Long id, Integer contractStatus) {
        return update(Wrappers.<Contract>lambdaUpdate().set(Contract::getStatus, contractStatus).eq(Contract::getId, id));
    }
//
    @Override
    public Contract getContractByEntity(ContractParamDTO params) {
        //忽略已驳回 已过期
//        String ignoreStatus = "2,4";
        List<Integer> ignoreStatus = Arrays.asList(2, 4);
        String contractBizNo = params.getContractBizNo();
        return getOne(Wrappers.<Contract>lambdaQuery()
                .eq(ObjectUtil.isNotNull(params.getBigBizNo()), Contract::getBigBizNo, params.getBigBizNo())
                .eq(ObjectUtil.isNotEmpty(contractBizNo), Contract::getContractBizNo, contractBizNo)
                .notIn(Contract::getStatus, ignoreStatus)
                .orderByDesc(Contract::getCreateTime).last("limit 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void contractRevoke(String contractId) {
        Contract contract = getByContractId(contractId);
        if (ObjectUtil.isEmpty(contract)) {
            return;
        }
        //更新合同状态为拒绝
        updateContractStatus(Long.valueOf(contract.getId()), ContractEnum.SSQ_CONTRACT_STATUS.REJECT.getStatus());
        //更新签署人状态为拒绝
        contractOperatorService.updateStatusByContractId(contractId, ContractEnum.SSQ_CONTRACT_STATUS.REJECT.getStatus());
        //拒绝
        elecSignBestHandler.contractRevoke(contractId);
    }

    @Override
    public List<Contract> listByContract(ContractParamDTO contract) {
        //忽略已驳回 已过期
        String ignoreStatus = "2,4";
        //合同模板
        String contractTemplates = contract.getContractTemplates();
        //合同ids
        String contractIds = contract.getContractIds();
        //大流程流水号
        String bigBizNos = contract.getBigBizNos();
        //小流程流水号
        String bizNos = contract.getBizNos();
        LambdaQueryWrapper<Contract> wrapper = Wrappers.<Contract>lambdaQuery().
                in(ObjectUtil.isNotEmpty(contractTemplates), Contract::getContractTemplateId, Func.toStrList(contractTemplates)).
                in(ObjectUtil.isNotEmpty(contractIds), Contract::getContractId, Func.toStrList(contractIds)).
                in(ObjectUtil.isNotEmpty(bigBizNos), Contract::getBigBizNo, Func.toStrList(bigBizNos)).
                ge(Contract::getSignDeadLine, new Date()).
                eq(ObjectUtil.isNotEmpty(contract.getGoodsId()), Contract::getGoodsId, contract.getGoodsId()).
                eq(Objects.nonNull(contract.getFinanceApplyId()), Contract::getFinanceApplyId, contract.getFinanceApplyId()).
                notIn(Contract::getStatus, Func.toIntList(ignoreStatus)).
                orderByDesc(Contract::getCreateTime);
        if (ObjectUtil.isNotEmpty(bizNos)){
            wrapper.and(w->{
                List<String> bizNosList = Func.toStrList(bizNos);
                for (int i = 0; i < bizNosList.size(); i++) {
                    if (0==i){
                        w.like(Contract::getContractBizNo, bizNosList.get(i));
                    }else{
                        w.or().like(Contract::getContractBizNo, bizNosList.get(i));
                    }
                }
            });
        }
        List<Contract> list = list(wrapper);
        return list;
    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void removeContractFromOps(Long goodsId, String signNode, Integer processType) {
//        List<Contract> list = list(Wrappers.<Contract>lambdaQuery().eq(Contract::getGoodsId, goodsId)
//                .eq(Contract::getSignNode, signNode).eq(Contract::getProcessType, processType));
//        if (CollectionUtil.isNotEmpty(list)) {
//            List<String> contractIds = StreamUtil.map(list, Contract::getContractId);
//            cancelContractsAndChangeStatus(contractIds);
//        }
//    }
//
    @Override
    public String downLoadAndUpdateContract(String contractId) {
        Contract contract = getByContractId(contractId);
        if (ObjectUtil.isEmpty(contract)) {
            throw new ServiceException("合同不存在");
        }
        if (ObjectUtil.isNotEmpty(contract.getSignedUrl())) {
            return contract.getSignedUrl();
        }
//        SsqContractInfo contractInfo = elecSignHandlerFactory.template().getContractInfo(contractId);
        SsqContractInfo contractInfo = elecSignBestHandler.getContractInfo(contractId);
        //签署完成的url回显
        BladeFile bladeFile = elecSignBestHandler.downLoadContract(contract);

        String link = bladeFile.getLink();
        if (ContractEnum.SSQ_CONTRACT_STATUS.matchEnum(contractInfo.getStatus()).equals(ContractEnum.SSQ_CONTRACT_STATUS.COMPLETE.getStatus())) {
            contract.setSignedUrl(link);
        }
        //更新合同
        update(Wrappers.<Contract>lambdaUpdate().eq(Contract::getContractId, contractId)
                .set(Contract::getFileUrl, link)
                .set(ObjectUtil.isNotEmpty(contract.getSignedUrl()), Contract::getSignedUrl, link));
        return link;
    }

    @Override
    public void signedUpdateContractUrl(List<String> contractIds) {
        ThreadUtil.safeSleep(1000);
        contractIds.forEach(this::downLoadAndUpdateContract);
    }
//
//    @Override
//    public void deleteAndCancel(List<String> contractIds) {
//        removeByContractIds(contractIds);
//        ThreadUtils.runAsync(() -> {
//            elecSignHandlerFactory.template().contractRevoke(contractIds);
//        });
//    }
//
//    @Override
//    public void cancelContracts(List<String> contractIds) {
//        elecSignHandlerFactory.template().contractRevoke(contractIds);
//    }
//
//    @Override
//    public IPage<ContractVO> getListAllPage(IPage<Object> page, ContractVO contract, Long userId) {
//        contract.setUserId(userId);
//        IPage<Contract> pages = baseMapper.selectUserSignContractPage(page, contract);
//        IPage<ContractVO> pageVO = ContractWrapper.build().pageVO(pages);
//        List<ContractVO> records = pageVO.getRecords();
//        if (CollectionUtil.isEmpty(records)) {
//            return pageVO;
//        }
//        //设置合同流程类型
//        setContractProcessType(records);
//        return pageVO;
//    }
//
//
//    @Override
//    public void cancelContractsAndChangeStatus(List<String> contractIds) {
//        List<Contract> contractList = list(Wrappers.<Contract>lambdaQuery().ne(Contract::getStatus, ContractEnum.SSQ_CONTRACT_STATUS.REJECT.getStatus())
//                .in(Contract::getContractId, contractIds));
//        if (CollUtil.isNotEmpty(contractList)) {
//            List<String> needRevokeIds = contractList.stream().map(Contract::getContractId).collect(Collectors.toList());
//            elecSignHandlerFactory.template().contractRevoke(needRevokeIds);
//            //修改签署者状态为取消
//            contractOperatorService.update(Wrappers.<ContractOperator>lambdaUpdate()
//                    .set(ContractOperator::getStatus, ContractEnum.CONTRACT_OPERATOR_STATUS.REJECT.getStatus())
//                    .in(ContractOperator::getContractId, needRevokeIds));
//            //修改合同状态为取消
//            update(Wrappers.<Contract>lambdaUpdate()
//                    .set(Contract::getStatus, ContractEnum.SSQ_CONTRACT_STATUS.REJECT.getStatus())
//                    .in(Contract::getContractId, needRevokeIds));
//        }
//    }
//
    @Override
    public void contractLock(String contractIds) {
        elecSignBestHandler.contractLock(contractIds);
    }

///*    @Override
//    public void passContract(DelegateExecution delegateExecution) {
//        String contractIds = delegateExecution.getVariable(ProcessConstant.CONTRACT_ID, String.class);
//        if (ObjectUtil.isEmpty(contractIds)) {
//            Object variable1 = delegateExecution.getVariable(ProcessConstant.CONTRACT_ID_LIST);
//            JSONArray variable = JSONObject.parseArray(JSONUtil.toJsonStr(variable1));
//            if (CollUtil.isNotEmpty(variable)) {
//                contractIds = StringUtil.join(variable, ",");
//            }
//        }
//        if (ObjectUtil.isNotEmpty(contractIds)) {
//            List<Contract> contracts = list(Wrappers.<Contract>lambdaQuery().eq(Contract::getStatus, ContractEnum.SSQ_CONTRACT_STATUS.SINGED.getStatus())
//                    .in(Contract::getContractId, contractIds));
//            if (CollUtil.isNotEmpty(contracts)) {
//                List<String> needUpdateContractIds = StreamUtil.map(contracts, Contract::getContractId);
//                elecSignHandlerFactory.template().contractLock(StringUtil.join(needUpdateContractIds, ","));
//                update(Wrappers.<Contract>lambdaUpdate().in(Contract::getContractId, needUpdateContractIds)
//                        .set(Contract::getFinishTime, LocalDateTime.now())
//                        .set(Contract::getStatus, ContractEnum.SSQ_CONTRACT_STATUS.COMPLETE.getStatus()));
//            }
//        }
//    }
//
//    @Override
//    public void terminalContract(DelegateExecution delegateExecution) {
//        String contractIds = delegateExecution.getVariable(ProcessConstant.CONTRACT_ID, String.class);
//        if (StringUtil.isBlank(contractIds)) {
//            Object contractObj = delegateExecution.getVariable(ProcessConstant.CONTRACT_ID_LIST);
//            if(ObjectUtil.isNotEmpty(contractObj)){
//                JSONArray variable = JSONObject.parseArray(JSONUtil.toJsonStr(contractObj));
//                if (CollUtil.isNotEmpty(variable)) {
//                    contractIds = StringUtil.join(variable, ",");
//                }
//            }
//        }
//        if (StringUtil.isNotBlank(contractIds)) {
//            cancelContractsAndChangeStatus(Func.toStrList(contractIds));
//        }
//    }
//
//    @Override
//    public void terminalContract(List<String> contractIds) {
//        if (CollUtil.isNotEmpty(contractIds)) {
//            cancelContractsAndChangeStatus(contractIds);
//        }
//    }*/
//
//    @Override
//    public Boolean cancelContractsBySelf(ContractParamDTO contractParamDTO, Long userId) {
//        List<Contract> contractList = listByContract(contractParamDTO);
//        if (CollUtil.isEmpty(contractList)) {
//            return false;
//        }
//        List<String> contractIds = contractList.stream().map(Contract::getContractId).collect(Collectors.toList());
//        List<ContractOperator> contractOperators = contractOperatorService.listByContractIdsAndUserId(contractIds, userId);
//        if (contractIds.size() != contractOperators.size()) {
//            throw new ServiceException("撤销合同失败");
//        }
//        cancelContractsAndChangeStatus(contractIds);
//        return true;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void resend(String contractId) {
//        ContractVO contract = ContractWrapper.build().entityVO(getByContractId(contractId));
//        if (CommonConstant.YES.equals(contract.getResended())) {
//            throw new ServiceException("该合同已重新发送,请勿重新发送");
//        }
//        String contractVar = contract.getContractVar();
//        //作废旧合同
//        cancelContractsAndChangeStatus(Func.toStrList(contractId));
//        //记录重新发送
//        update(Wrappers.<Contract>lambdaUpdate().eq(Contract::getId, contract.getId()).set(Contract::getResended, CommonConstant.YES));
//        ContractTemplate template = contractTemplateService.getByTemplateId(contract.getContractTemplateId().toString());
//        contract.setContractTemplate(template);
//        //生成新的合同
//        ContractReturnData contractReturnData = elecSignHandlerFactory.template().contractResend(contract);
//        Contract newContract = new Contract();
//        BeanUtil.copyProperties(contract, newContract, "status", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//        newContract.setContractVar(contractVar);
//        newContract.setContractNo(CodeUtil.generateCode(CodeEnum.CONTRACT_CODE));
//        newContract.setContractId(contractReturnData.getContractId());
//        newContract.setFileUrl(contractReturnData.getUrl());
//        LocalDateTime now = LocalDateTime.now();
//        newContract.setSendTime(now);
//        newContract.setSignDeadLine(now.plusDays(template.getExpireDay()));
//        //生成新合同及签署人
//        save(newContract);
//        //添加已有签署人
//        List<ContractOperator> contractOperators = contractOperatorService.listByContractId(contract.getContractId());
//        List<Long> contractOperatorIds = new ArrayList<>();
//        for (ContractOperator contractOperator : contractOperators) {
//            contractOperatorIds.add(Long.valueOf(contractOperator.getId()));
//            ContractOperator newContractOperator = new ContractOperator();
//            BeanUtil.copyProperties(contractOperator, newContractOperator, "status", "id", "createTime", "createUser", "createDept", "updateTime", "updateUser");
//            newContractOperator.setContractNo(newContract.getContractNo());
//            newContractOperator.setContractId(newContract.getContractId());
//            contractOperatorService.save(newContractOperator);
//        }
//    }
//
//    private Boolean removeByContractIds(List<String> contractIds) {
//        return remove(Wrappers.<Contract>lambdaQuery().in(CollectionUtil.isNotEmpty(contractIds), Contract::getContractId, contractIds));
//    }
//
    @Override
    public void delay(String contractId) {
        Long currentTime = System.currentTimeMillis() / 1000;
        if (null==contractId){
            throw new ServiceException("合同id不能为空");
        }
        List<String> strId= Collections.singletonList(contractId);
        //延迟7天
        elecSignBestHandler.delayExpireTime(strId, String.valueOf(currentTime + (86400 * 7)));
        update(Wrappers.<Contract>lambdaUpdate().set(Contract::getSignDeadLine, LocalDateTime.now().plusDays(7))
                .set(Contract::getStatus, ContractEnum.SSQ_CONTRACT_STATUS.SINGED.getStatus()));
    }
//
//    @Override
//    public IPage<ContractVO> getContract(Long companyId, Query query) {
//        IPage<Contract> page = SpringUtil.getBean(IContractService.class).lambdaQuery()
//                .eq(Contract::getCapitalId, companyId)
//                .page(Condition.getPage(query));
//        IPage<ContractVO> contractVOIPage = ContractWrapper.build().pageVO(page);
//        return contractVOIPage;
//    }
}
