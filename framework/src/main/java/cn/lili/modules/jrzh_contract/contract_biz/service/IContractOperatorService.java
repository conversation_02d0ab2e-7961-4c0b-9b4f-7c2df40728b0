/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractOperator;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * 合同签署表 服务类
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
public interface IContractOperatorService extends IService<ContractOperator> {
//
//    /**
//     * 自定义分页
//     *
//     * @param page
//     * @param contractOperator
//     * @return
//     */
//    IPage<ContractOperatorVO> selectContractOperatorPage(IPage<ContractOperatorVO> page, ContractOperatorVO contractOperator);
//
    /**
     * 获取客户签署的合同
     *
     * @param contractId 合同id
     * @param customerId 客户id
     * @return
     */
    ContractOperator getByContractIdAndCustomerId(String contractId, Long customerId);

    /**
     * 根据id更新合同签署状态
     *
     * @param id     签署id
     * @param status 更新状态
     */
    Boolean updateContractStatus(Long id, Integer status);

    /**
     * 根据用户id 合同id获取合同签署列表
     *
     * @param contractId
     * @param userId
     * @return
     */
//    List<ContractOperator> listByContractIdsAndUserId(List<String> contractId, Long userId);
//
//    /**
//     * 获取签署签署列表
//     *
//     * @param customerId 客户id
//     * @return
//     */
//    List<ContractOperator> listByContractByCustomerId(Long customerId);
//
    /**
     * 根据合同id 更新
     *
     * @param contractId
     * @param status
     */
    void updateStatusByContractId(String contractId, Integer status);

    /**
     * 签署人是否全部签署完毕
     *
     * @param contractTempalteId 签署id
     * @return
     */
    boolean operatorAllComplate(String contractTempalteId, String contractId);

    /**
     * 根据合同查看签署列表
     *
     * @param contractId
     */
    List<ContractOperator> listByContractId(String contractId);
//
    /**
     * 获取当前合同签署人顺序
     *
     * @param contractId
     */
    Integer currentCountByContractId(String contractId, Long userId);

    /**
     * 获取已签署人数
     *
     * @param contractId
     * @return
     */
    Integer currentSignedCountByContractId(String contractId);
//
    /**
     * 添加签署人
     *
     * @param contractOperator 操作人信息
     * @param template         模板信息
     * @return
     */
    ContractOperator saveOrUpdateContractOperator(ContractOperator contractOperator, ContractTemplate template);
//
//    /**
//     * 根据名称查询签署过的独一份合同 k:合同名称 v:合同编号
//     *
//     * @param contractName
//     * @param userId
//     * @return
//     */
//    Map<String, String> listSignedByContractName(String contractName, String userId);
//
//    /**
//     * 检查当前签署人合同是否签署完毕
//     *
//     * @param contractIds 合同编号
//     * @param userId      用户id
//     */
//    Boolean checkCurrentSignerIsSigned(List<String> contractIds, Long userId);
}
