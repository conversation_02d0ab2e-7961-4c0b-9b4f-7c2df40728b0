/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.mapper;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
public interface ContractTemplateMapper extends BaseMapper<ContractTemplate> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param contractTemplate
	 * @return
	 */
	List<ContractTemplateVO> selectContractTemplatePage(IPage page, ContractTemplateVO contractTemplate);


	IPage<ContractTemplate> selectTemplatePage(IPage page,@Param("contractTemplate") ContractTemplate contractTemplate);

	/**
	 * 自定义分页
	 * @param currentPage
	 * @param pageSize
	 * @return
	 */
	List<ContractTemplate> selectContractTemplateQuery(Integer currentPage,Integer pageSize);



	List<ContractTemplate> selectDel();


	/**
	 * 查询指定业务信息
	 * @param businessQueryDTO
	 * @return
	 */
//	String  selectAppointBusinessInfo(BusinessQueryDTO businessQueryDTO);


	/**
	 * 查询业务表所有字段信息
	 */

//	Object   selectTableInfo(BusinessQueryDTO businessQueryDTO);


}
