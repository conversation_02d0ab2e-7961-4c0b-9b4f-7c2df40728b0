package cn.lili.modules.jrzh_contract.contract_biz.handler;

import cn.lili.modules.jrzh_contract.contract_biz.handler.impl.ElecSignBestHandler;
import cn.lili.modules.jrzh_other.core.service.AbsSingleOpenApi;
import cn.lili.modules.jrzh_other.core.utils.OtherApiUtils;
import cn.lili.modules.jrzh_other.core_api.constant.OtherApiTypeEnum;
import com.aliyun.oss.ServiceException;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 电子签构建类
 *
 * <AUTHOR>
 */
@Component
@RequiredArgsConstructor
public class ElecSignHandlerFactory extends AbsSingleOpenApi {
	private final Map<String, ElecSignHandler> ElecSignHandlerMap;


	@Override
	public ElecSignHandler template() {
		String handlerCode = OtherApiUtils.getCode(OtherApiTypeEnum.ELEC_SIGN.getCode());
		if (ElecSignHandlerMap.containsKey(handlerCode)) {
			return ElecSignHandlerMap.get(handlerCode);
		}
		throw new ServiceException("请求的电子签资源不存在，请联系管理员");
//		return null;
	}
}
