package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.modules.jrzh_contract.contract_api.enums.ContractTypeEnum;

/**
 * 合同类型检测服务接口
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
public interface IContractTypeDetectionService {

    /**
     * 根据订单编号检测合同类型
     * 
     * @param orderSn 订单编号
     * @return 合同类型
     */
    ContractTypeEnum detectContractType(String orderSn);

    /**
     * 根据订单类型和询价信息检测合同类型
     * 
     * @param orderType 订单类型（normal/baojia）
     * @param orderSn 订单编号（用于查询询价信息）
     * @return 合同类型
     */
    ContractTypeEnum detectContractType(String orderType, String orderSn);
}
