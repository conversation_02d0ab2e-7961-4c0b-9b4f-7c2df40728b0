/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 合同签署表实体类
 *
 * <AUTHOR>
 * @since 2022-07-08
 */
@Data
@TableName("jrzh_contract_operator")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractOperator对象", description = "合同签署表")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ContractOperator extends BaseEntity {

	private static final long serialVersionUID = 1L;

	private Integer status;
	/**
	 * 合同id
	 */
	@ApiModelProperty(value = "合同id")
	private String contractId;
	/**
	 * 合同签署人id
	 */
	@ApiModelProperty(value = "合同签署人id")
	private Long signerId;
	/**
	 * 用户类型
	 */
	@ApiModelProperty(value = "用户类型")
	private Integer userType;
	/**
	 * 签署人名称
	 */
	@ApiModelProperty(value = "签署人名称")
	private String signerName;

	@ApiModelProperty(value = "签署企业名称")
	private String signerCompanyName;
	@ApiModelProperty(value = "合同名称")
	private String templateName;
	@ApiModelProperty(value = "合同生成编号")
	private String contractNo;
	@ApiModelProperty(value = "签署顺序")
	private Integer sorted;
}
