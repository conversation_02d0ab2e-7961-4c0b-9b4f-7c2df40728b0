/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.dto;

import cn.lili.modules.jrzh_bases.SmsCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 签署参数
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractSignParam implements Serializable {
	private static final long serialVersionUID = 1L;
	@ApiModelProperty("当前签署的合同ids")
	private String contractIds;
	@ApiModelProperty("签署的base64编码")
	private String customizeWriteBase64;
	@ApiModelProperty("校验类型 1验证码 2人脸（待定）")
	private Integer verifyType;
	@ApiModelProperty("校验码 校验类型为1时必传")
	private SmsCode code;
	@ApiModelProperty("当前签署人信息 当非使用当前用户进行签署时必传")
	private ContractCurrentSignInfo currentSignInfo;
	@ApiModelProperty("签署达到上线后是否自动锁定完成合同 0不需要 1需要")
	private Integer autoLock;
	/**
	 * 后台签署时传入 用于校验
	 */
	private Long deptId;

}
