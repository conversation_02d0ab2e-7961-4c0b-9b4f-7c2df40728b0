package cn.lili.modules.jrzh_contract.contract_api.vo;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateCategoryConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 合同模板分类配置VO
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "合同模板分类配置VO", description = "包含分类名称等扩展信息")
public class ContractTemplateCategoryConfigVO extends ContractTemplateCategoryConfig {

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    @ApiModelProperty(value = "模板名称")
    private String templateName;

    @ApiModelProperty(value = "父分类ID")
    private String parentCategoryId;

    @ApiModelProperty(value = "分类层级")
    private Integer categoryLevel;

    @ApiModelProperty(value = "合同类型名称")
    private String contractTypeName;
}
