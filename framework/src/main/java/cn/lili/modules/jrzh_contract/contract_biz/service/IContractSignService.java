/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service;


import cn.lili.modules.jrzh_contract.contract_api.dto.SignParam;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;

/**
 * 合同签署服务
 *
 * <AUTHOR>
 * @since 2022-02-17
 */
public interface IContractSignService {

    /**
     * 合同变量签署
     *
     * @param signParam 签署参数 查看实体
     * @return
     */
    Contract sign(SignParam signParam);

    /**
     * 跳转去页面签署
     *
     * @param contractId 合同id
     * @param returnUrl  返回链接
     * @param accountId  accountId 账号id
     * @param varNames   varNames 签署参数
     */
//    String skipToSign(String contractId, String returnUrl, String accountId, String varNames);

}
