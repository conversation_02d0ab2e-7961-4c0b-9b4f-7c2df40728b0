///*
// *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
// *
// *  Redistribution and use in source and binary forms, with or without
// *  modification, are permitted provided that the following conditions are met:
// *
// *  Redistributions of source code must retain the above copyright notice,
// *  this list of conditions and the following disclaimer.
// *  Redistributions in binary form must reproduce the above copyright
// *  notice, this list of conditions and the following disclaimer in the
// *  documentation and/or other materials provided with the distribution.
// *  Neither the name of the dreamlu.net developer nor the names of its
// *  contributors may be used to endorse or promote products derived from
// *  this software without specific prior written permission.
// *  Author: Chill 庄骞 (<EMAIL>)
// */
//package cn.lili.modules.jrzh_contract.contract_biz.controller.front;
//
//import cn.hutool.core.util.ObjectUtil;
//import cn.lili.common.enums.ResultUtil;
//import cn.lili.common.vo.ResultMessage;
//import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateVO;
//import cn.lili.modules.jrzh_contract.contract_api.vo.TemplateCustomerVO;
//import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateService;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import java.util.List;
//
///**
// * 控制器
// *
// * <AUTHOR>
// * @since 2021-12-20
// */
//@RestController
//@AllArgsConstructor
//@RequestMapping("contract/web-front/contractTemplate")
//@Api(value = "合同模板", tags = "接口")
//public class ContractTemplateFrontController {
//
//
////    /**
////     * 详情
////     */
////    @GetMapping("/detail")
////    @ApiOperationSupport(order = 1)
////    @ApiOperation(value = "详情", notes = "传入contractTemplate")
////    public ResultMessage<ContractTemplateVO> detail(String templateId) {
////        return ResultUtil.data(contractTemplateService.details(templateId));
////    }
////
////
////    @GetMapping("/list")
////    @ApiOperationSupport(order = 2)
////    @ApiOperation(value = "分页", notes = "传入contractTemplate")
////    public ResultMessage<IPage<ContractTemplate>> list(@RequestParam Map<String, Object> param, Query query) {
////        IPage<ContractTemplate> pages = contractTemplateService.page(Condition.getPage(query), Condition.getQueryWrapper(param, ContractTemplate.class)
////                .lambda().orderByDesc(ContractTemplate::getCreateTime));
////        return ResultUtil.data(pages);
////    }
////
//////    @PostMapping("/syncContractTemplate")
//////    public ResultMessage syncContractTemplate(Query query) {
//////        return ResultUtil.data(contractTemplateService.syncBestSign(query));
//////    }
////
////    /**
////     * 自定义分页
////     */
////    @GetMapping("/page")
////    @ApiOperationSupport(order = 3)
////    @ApiOperation(value = "分页", notes = "传入contractTemplate")
////    public ResultMessage<IPage<ContractTemplateVO>> page(ContractTemplateVO contractTemplate, Query query) {
////        IPage<ContractTemplateVO> pages = contractTemplateService.selectContractTemplatePage(Condition.getPage(query), contractTemplate);
////        return ResultUtil.data(pages);
////    }
////
////    /**
////     * 新增或修改
////     */
////    @PostMapping("/submit")
////    @ApiOperationSupport(order = 6)
////    @ApiOperation(value = "新增或修改", notes = "传入contractTemplate")
////    public ResultMessage submit(@Valid @RequestBody ContractTemplateDTO contractTemplateDTO) {
////        return ResultUtil.data(contractTemplateService.saveOrUpdateContractTemplateDTO(contractTemplateDTO));
////    }
////
////
////    /**
////     * 删除
////     */
////    @PostMapping("/remove")
////    @ApiOperationSupport(order = 7)
////    @ApiOperation(value = "逻辑删除", notes = "传入ids")
////    @Transactional(rollbackFor = Exception.class)
////    public ResultMessage remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
////        return ResultUtil.data(contractTemplateService.remove(ids));
////
////    }
////
////    /**
////     * 跳转到模板
////     *
////     * @return
////     */
//////    @PostMapping("skipToTemplate")
//////    public ResultMessage getSkipUrl() {
//////        return ResultUtil.data(contractTemplateService.skipToTemplate());
//////    }
////
////
////    @PostMapping("updateStatus")
////    public ResultMessage<?> updateStatus(@RequestBody UpdateStatusDTO updateStatusDTO) {
////        return ResultUtil.data(this.contractTemplateService.enableDisable(updateStatusDTO));
////    }
////
////    @GetMapping("findById")
////    @ApiOperationSupport(order = 11)
////    @ApiOperation(value = "根据id查找模板", notes = "传入合同模板id")
////    public ResultMessage<ContractTemplate> findById(@RequestParam Long id) {
////        return ResultUtil.data(contractTemplateService.lambdaQuery().eq(ContractTemplate::getId, id)
////                .eq(ContractTemplate::getStatus, ContractEnum.contract.CONTRACT_TEMPLATE_STATE_ABLE.getStatus()).one());
////    }
////
////    /**
////     * 预览模板
////     *
////     * @param contractTemplate
////     * @return
////     */
////    @PostMapping("getTemplatePicUrl")
////    public ResultMessage<?> getTemplatePicUrl(@RequestBody ContractTemplate contractTemplate) {
////        String url = contractTemplateService.preView(contractTemplate.getTemplateId());
////        JSONObject returnObj = new JSONObject();
////        returnObj.put("contractGenType", contractTemplateService.getByTemplateId(contractTemplate.getTemplateId()).getContractGenType());
////        returnObj.put("url", url);
////        return ResultUtil.data(returnObj);
////    }
////
////    /**
////     * 查看所有开启的模板
////     *
////     * @return
////     */
////    @GetMapping("/all")
////    public ResultMessage<List<ContractTemplate>> all() {
////        return ResultUtil.data(contractTemplateService.listEnableAll());
////    }
////
////    @GetMapping("/listAll")
////    @ApiOperation("查询所有合同模板无论开启关闭")
////    public ResultMessage<List<ContractTemplate>> getAll() {
////        return ResultUtil.data(contractTemplateService.list());
////    }
////
////
////    /**
////     * 根据类型查询所有合同模板
////     *
////     * @param type 类型
////     * @return 产品类型关联的合同模板
////     */
////    @GetMapping("/allByGoodType")
////    @ApiOperation("根据产品类型查询所有合同模板")
////    public ResultMessage<List<ContractTemplate>> allByGoodType(@RequestParam Integer type) {
////        return ResultUtil.data(contractTemplateService.list(Wrappers.<ContractTemplate>lambdaQuery().eq(ContractTemplate::getStatus, 1)));
////    }
////
////    /**
////     * 查询所有可作为子合同的合同模板
////     */
////    @ApiOperation("查询所有可作为子合同的合同模板")
////    @GetMapping("/list_all_not_contain_self")
////    public ResultMessage listAllNotContainSelf(String excludeTemplateIds) {
////        return ResultUtil.data(contractTemplateService.list(Wrappers.<ContractTemplate>lambdaQuery()
////                .notIn(StringUtil.isNotBlank(excludeTemplateIds), ContractTemplate::getTemplateId, excludeTemplateIds)));
////    }
//
//    /**
//     * 根据合同id查询签署节点
//     *
//     * @param id
//     * @return
//     */
////    @ApiOperation("根据合同id查询签署节点")
////    @GetMapping("/getNodeById")
////    public ResultMessage<String> getNodeById(@RequestParam String id) {
////        if (StringUtil.isBlank(id)) {
////            return null;
////        }
////        ContractTemplate contract = contractTemplateService.getById(id);
////        if (contract.getStatus() == 0) {
////            throw new ServiceException("合同未启用");
////        }
////        String signNode = contract.getSignNode();
////        return ResultUtil.data(signNode);
////    }
//}
