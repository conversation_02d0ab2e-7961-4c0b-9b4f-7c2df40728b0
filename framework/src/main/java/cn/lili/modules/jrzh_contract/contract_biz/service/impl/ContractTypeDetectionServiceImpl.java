package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.lili.common.utils.ValidationUtils;
import cn.lili.modules.goods.entity.dos.CommodityPriceInquiry;
import cn.lili.modules.goods.service.ICommodityPriceInquiryService;
import cn.lili.modules.jrzh_contract.contract_api.enums.ContractTypeEnum;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTypeDetectionService;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.enums.OrderTypeEnum;
import cn.lili.modules.order.order.service.OrderService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 合同类型检测服务实现
 *
 * <AUTHOR>
 * @since 2024-06-20
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ContractTypeDetectionServiceImpl implements IContractTypeDetectionService {

    private final OrderService orderService;
    private final ICommodityPriceInquiryService commodityPriceInquiryService;

    @Override
    public ContractTypeEnum detectContractType(String orderSn) {
        log.info("开始检测合同类型，订单编号：{}", orderSn);

        Order order = orderService.getBySn(orderSn);
        ValidationUtils.checkExists(order, "订单不存在，订单编号：%s", orderSn);

        return detectContractType(order.getOrderType(), orderSn);
    }

    @Override
    public ContractTypeEnum detectContractType(String orderType, String orderSn) {
        log.info("开始检测合同类型，订单类型：{}，订单编号：{}", orderType, orderSn);

        ValidationUtils.notEmpty(orderType, "订单类型不能为空");
        ValidationUtils.notEmpty(orderSn, "订单编号不能为空");

        // 1. 如果是普通订单，直接返回现货
        if (OrderTypeEnum.NORMAL.name().equalsIgnoreCase(orderType)) {
            log.info("普通订单，返回现货类型");
            return ContractTypeEnum.SPOT;
        }

        // 2. 如果是报价订单，需要查询询价信息
        if (OrderTypeEnum.BAOJIA.name().equalsIgnoreCase(orderType)) {
            log.info("报价订单，查询询价信息判断合同类型");
            return detectContractTypeByInquiry(orderSn);
        }

        // 3. 其他类型抛出异常
        log.error("不支持的订单类型：{}，支持的类型：NORMAL（普通订单）、BAOJIA（报价订单）", orderType);
        throw new IllegalArgumentException("不支持的订单类型：" + orderType);
    }

    /**
     * 根据询价信息检测合同类型
     */
    private ContractTypeEnum detectContractTypeByInquiry(String orderSn) {
        CommodityPriceInquiry inquiry = commodityPriceInquiryService.getOne(
            Wrappers.<CommodityPriceInquiry>lambdaQuery()
                .eq(CommodityPriceInquiry::getOrderSn, orderSn)
                .last("LIMIT 1")
        );

        ValidationUtils.checkExists(inquiry, "未找到对应的询价信息，订单编号：%s", orderSn);

        Integer tradeModel = inquiry.getTradeModel();
        ContractTypeEnum contractType = ContractTypeEnum.getByTradeModel(tradeModel);

        log.info("根据询价信息检测合同类型完成，订单编号：{}，交易模式：{}，合同类型：{}",
            orderSn, tradeModel, contractType.getDescription());

        return contractType;
    }
}
