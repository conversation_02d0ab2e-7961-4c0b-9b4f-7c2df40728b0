package cn.lili.modules.jrzh_contract.contract_api.vo;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 客户查询合同模版VO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TemplateCustomerVO对象", description = "TemplateCustomerVO对象")
public class TemplateCustomerVO extends ContractTemplate {

    /**
     * 合同id
     */
    private String contractId;
    /**
     * 合同当前url
     */
    private String pdfUrl;

    private Integer signStatus;

    private String templateUrl;

    /**
     * 子订单编号
     */
    @ApiModelProperty(value = "子订单编号")
    private String bizNo;

}
