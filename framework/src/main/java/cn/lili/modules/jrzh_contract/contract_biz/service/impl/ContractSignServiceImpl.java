package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.jrzh_bases.CommonConstant;
import cn.lili.modules.jrzh_bases.CustomerTypeEnum;
import cn.lili.modules.jrzh_bases.Func;
import cn.lili.modules.jrzh_contract.contract_api.dto.SignParam;
import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractOperator;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractSignConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_biz.handler.ElecSignHandlerFactory;
import cn.lili.modules.jrzh_contract.contract_biz.handler.impl.ElecSignBestHandler;
import cn.lili.modules.jrzh_contract.contract_biz.service.*;
import cn.lili.modules.jrzh_other.bestsign.constant.ContractConfigEnum;
import cn.lili.modules.jrzh_other.bestsign.constant.ContractEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 合同签署服务
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContractSignServiceImpl implements IContractSignService {
    private final IContractOperatorService contractOperatorService;
    private final ContractStrategy contractStrategy;
    private final IContractService contractService;
    private final IContractSignConfigService contractSignConfigService;
    private final ElecSignHandlerFactory elecSignHandlerFactory;
    private final IContractConfigService contractConfigService;
    private final IContractTemplateService contractTemplateService;
    private final ElecSignBestHandler elecSignBestHandler;
//    private final RemoteUserService remoteUserService;
//    private final RemoteDeptSearchService remoteDeptSearchService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Contract sign(SignParam signParam) {
        //参数校验
        checkSignParamAndCompose(signParam);
        //如果当前签署人已签署 则跳过
        ContractOperator contractOperator = signParam.getContractOperator();
        if (ObjectUtil.isNotEmpty(contractOperator) && ContractEnum.CONTRACT_OPERATOR_STATUS.isFinished(contractOperator.getStatus())) {
            return null;
        }
        //添加签署人并签署
        addSignerAndSigned(signParam);
        //更新合同状态
        contractService.updateById(signParam.getContract());
        return signParam.getContract();
    }
//

    /**
     * 添加签署人并签署合同
     *
     * @param signParam 签署参数
     */
    @Transactional(rollbackFor = Exception.class)
    public void addSignerAndSigned(SignParam signParam) {
        Contract contract = signParam.getContract();
        ContractOperator contractOperator = signParam.getContractOperator();
        if (ObjectUtil.isEmpty(contractOperator)) {
            contractOperator = ContractOperator.builder()
                    .contractId(signParam.getContractId())
                    .signerCompanyName(signParam.getSignerName())
                    .userType(signParam.getCustomerType())
                    .signerName(signParam.getPersonalName())
                    .signerId(signParam.getSignerId())
                    .build();
            signParam.setContractOperator(contractOperator);
        }
        ContractTemplate template = signParam.getContactTemplate();
        String contractId = signParam.getContractId();
        //合同签署
        contractStrategy.genContractInstance(template.getContractGenType()).sign(signParam);
        //签署后更新状态
        contractOperator.setStatus(ContractEnum.CONTRACT_OPERATOR_STATUS.SIGN_COMPLETE.getStatus());
        contractOperatorService.saveOrUpdateContractOperator(contractOperator, template);
        //若签署人达到上限并都已签署 并自动开启自动锁定 则更新为已完成
        Integer currentIndex = contractOperatorService.currentSignedCountByContractId(contractId);
        Integer updateContractStatus = currentIndex.equals(template.getSignerNum()) && CommonConstant.YES.equals(signParam.getAutoLock())
                ? ContractEnum.SSQ_CONTRACT_STATUS.COMPLETE.getStatus()
                : ContractEnum.SSQ_CONTRACT_STATUS.SINGED.getStatus();
        //完成则锁合同
        if (ContractEnum.SSQ_CONTRACT_STATUS.COMPLETE.getStatus().equals(updateContractStatus)) {

//            elecSignHandlerFactory.template().contractLock(contractId);
            elecSignBestHandler.contractLock(contractId);
            contract.setFinishTime(new Date());
        }
        contract.setStatus(updateContractStatus);
    }

    private void checkSignParamAndCompose(SignParam signParam) {
        //获取合同信息,合同模板信息,签署人配置信息,当前签署人信息,
        Contract contract = contractService.getByContractId(signParam.getContractId());
        if (ObjectUtil.isEmpty(contract)) {
            throw new ServiceException("合同签署异常，合同不存在");
        }
        ContractTemplate template = contractTemplateService.getByTemplateId(contract.getContractTemplateId().toString());
        Integer customerType = signParam.getCustomerType();
        Integer sealType = CustomerTypeEnum.PERSONAL.getCode().equals(customerType) ? ContractConfigEnum.CATEGORY.SIGN.getCode() : ContractConfigEnum.CATEGORY.SEAL.getCode();
        Long signerId = signParam.getSignerId();
        String signOrStampName = contractConfigService.getImgNameByCompanyId(signerId, sealType);
        //这里是谁先签，谁就按照关键字顺序先执行，现把关键字顺序作为类型，1：买家签的位置，2：卖家签的位置
//        Integer sort = contractOperatorService.currentCountByContractId(signParam.getContractId(), signerId);
        Integer sort = signParam.getCustomerTradingType();
        List<ContractSignConfig> contractSignConfigs = contractSignConfigService.listByTemplateIdAndSort(Func.toLong(String.valueOf(contract.getContractTemplateId())), sort);
        ContractOperator contractOperator = contractOperatorService.getByContractIdAndCustomerId(signParam.getContractId(), signParam.getSignerId());
        if (CollUtil.isEmpty(contractSignConfigs)) {
            throw new ServiceException("签署配置未配置");
        }
        //设置合同信息,合同模板信息,签署人配置信息,当前签署人信息
        signParam.setContractOperator(contractOperator);
        signParam.setSignOrStampName(signOrStampName);
        signParam.setContractSignConfigs(contractSignConfigs);
        signParam.setContract(contract);
        signParam.setTemplateId(contract.getContractTemplateId().toString());
        signParam.setContactTemplate(template);
    }
//
//    @Override
//    @Deprecated
//    public String skipToSign(String contractId, String returnUrl, String accountId, String varNames) {
//        Contract contract = contractService.getByContractId(contractId);
//        SsqSkipSignParam skipSignParam = SsqSkipSignParam.builder()
//                .contractId(contractId)
//                .tid(contract.getContractTemplateId().toString())
//                .account(accountId)
//                .returnUrl(returnUrl)
//                .isDrawSignatureImage("1")
//                .signatureImageName("default")
//                .signer(accountId)
//                .varNames(varNames)
//                .build();
//        return elecSignHandlerFactory.template().skipToSign(skipSignParam);
//    }
}
