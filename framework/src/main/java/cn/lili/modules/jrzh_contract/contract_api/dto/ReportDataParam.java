package cn.lili.modules.jrzh_contract.contract_api.dto;

import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自定义数据源参数
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReportDataParam {
    @ApiModelProperty("模板信息")
    private ContractTemplate contractTemplate;
    @ApiModelProperty("唯一标识，标记唯一一条，或是字段组合成的唯一标识")
    private String bizNo;
    @ApiModelProperty("文本域数据源")
    private Object obj;
}
