package cn.lili.modules.jrzh_contract.customer_ct.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.enums.ResultCode;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.connect.entity.CustomerInfo;
import cn.lili.modules.connect.service.ICustomerInfoService;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.service.GoodsService;
import cn.lili.modules.jrzh_contract.contract_api.dto.DynamicContractDTO;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractDataSourcePurchase;
import cn.lili.modules.jrzh_contract.customer_ct.dto.OrderContractDTO;
import cn.lili.modules.jrzh_contract.customer_ct.service.ContractDataSourceService;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.vo.OrderDetailVO;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.service.StoreService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@AllArgsConstructor
public class ContractDataSourceServiceImpl implements ContractDataSourceService {
    private final OrderService orderService;
    private final ICustomerInfoService customerInfoService;
    private final GoodsService goodsService;
    private final StoreService storeService;


    @Override
    public ContractDataSourcePurchase buildContractDataSourcePurchase(DynamicContractDTO params) {

        ContractDataSourcePurchase purchase = new ContractDataSourcePurchase();
        //查询订单，获取下单时间、店铺名称、供应商名称
        OrderDetailVO orderDetailVO = orderService.queryDetail(params.getBigBizNo());
        Order order = orderDetailVO.getOrder();
        Store store = storeService.getById(order.getStoreId());
        CustomerInfo customerInfoBuyer = customerInfoService.getOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCustomerId, order.getMemberId()));
        CustomerInfo customerInfoSeller = customerInfoService.getOne(Wrappers.<CustomerInfo>lambdaQuery().eq(CustomerInfo::getCustomerId, store.getMemberId()));
        purchase.setCompanyName(customerInfoBuyer.getCorpName());
        purchase.setSupplierName(customerInfoSeller.getCorpName());
        purchase.setOrderTime(order.getCreateTime());

        // 根据contractBizNo筛选对应的子订单
        List<OrderItem> filteredOrderItems = getFilteredOrderItems(orderDetailVO.getOrderItems(), params.getContractBizNo());

        // 计算筛选后子订单的总金额
        double totalFlowPrice = filteredOrderItems.stream()
                .mapToDouble(OrderItem::getFlowPrice)
                .sum();
        purchase.setFlowPrice(totalFlowPrice);

        //设置运费
        double freightPrice = 0.0;

        List<OrderContractDTO> orderList = new ArrayList<>();
        for (int i = 0; i < filteredOrderItems.size(); i++) {
            OrderItem orderItem = filteredOrderItems.get(i);
            PriceDetailDTO priceDetailDTO = orderItem.getPriceDetailDTO();
            Goods goods = goodsService.getById(orderItem.getGoodsId());
            OrderContractDTO contractDTO = new OrderContractDTO();
            contractDTO.setName(orderItem.getGoodsName());
            contractDTO.setQuantity(orderItem.getNum());
            contractDTO.setUnitName(goods.getGoodsUnit());
            contractDTO.setPrice(orderItem.getUnitPrice());
            contractDTO.setTotalAmount(BigDecimal.valueOf(priceDetailDTO.getGoodsPrice()));
            contractDTO.setIndex(i + 1);
            orderList.add(contractDTO);
            freightPrice = freightPrice + priceDetailDTO.getFreightPrice();
        }
        purchase.setOrderList(orderList);
        purchase.setContractNo(params.getContractNo());
        purchase.setChinesePrice(Convert.digitToChinese(totalFlowPrice));
        purchase.setFreightPrice(freightPrice);

        return purchase;
    }

    /**
     * 根据contractBizNo筛选对应的子订单
     * contractBizNo可能是单个子订单编号，也可能是多个子订单编号的逗号分隔字符串
     *
     * @param allOrderItems 订单的所有子订单
     * @param contractBizNo 合同业务号，可能包含多个子订单编号
     * @return 筛选后的子订单列表
     */
    private List<OrderItem> getFilteredOrderItems(List<OrderItem> allOrderItems, String contractBizNo) {
        if (ObjectUtil.isEmpty(contractBizNo) || allOrderItems == null || allOrderItems.isEmpty()) {
            throw new ServiceException(ResultCode.PARAMS_ERROR.message());
        }

        // 将contractBizNo按逗号分割，获取需要处理的子订单编号列表
        List<String> targetBizNos = Arrays.stream(contractBizNo.split(","))
                .map(String::trim)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());

        // 从所有子订单中筛选出contractBizNo指定的子订单
        return allOrderItems.stream()
                .filter(orderItem -> targetBizNos.contains(orderItem.getSn()))
                .collect(Collectors.toList());
    }
}
