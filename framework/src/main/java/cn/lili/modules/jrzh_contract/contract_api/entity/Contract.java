/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
@Data
@TableName("jrzh_contract")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "Contract对象", description = "Contract对象")
public class Contract extends BaseEntity {

    private static final long serialVersionUID = 1L;
    private Integer status;
    /**
     * 合同标题
     */
    @ApiModelProperty(value = "合同标题")
    private String contractTitle;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractId;


    @ApiModelProperty(value = "发送时间")
    private Date sendTime;

    @ApiModelProperty(value = "签收时间")
    private Date finishTime;

    @ApiModelProperty(value = "截止时间")
    private Date signDeadLine;

    /**
     * 合同存储地址
     */
    @ApiModelProperty(value = "合同存储地址")
    private String fileUrl;

    /**
     * 签署节点
     */
    @ApiModelProperty(value = "签署节点")
    private String signNode;

    /**
     * 合同模板id
     */
    private Long contractTemplateId;
    /**
     * 合同业务号
     */
    private String contractBizNo;

    @ApiModelProperty(value = "流程类型")
    private Integer processType;

    @ApiModelProperty(value = "产品id")
    private Long goodsId;

    @ApiModelProperty(value = "产品名称")
    private String goodName;

    @ApiModelProperty(value = "融资申请id")
    private Long financeApplyId;

    @ApiModelProperty(value = "大流程业务流水号")
    private String bigBizNo;

    @ApiModelProperty(value = "资方id")
    private Long capitalId;

    @ApiModelProperty(value = "签署关键字json")
    private String signKeywordJson;

    @ApiModelProperty(value = "合同生成编号")
    private String contractNo;

    @ApiModelProperty(value = "产品类型")
    private Integer goodType;

    @ApiModelProperty(value = "完成后的合同url")
    private String signedUrl;

    @ApiModelProperty(value = "合同生成变量")
    private String contractVar;

    @ApiModelProperty(value = "合同已重新发送 0非 1是")
    private Integer resended;

    @ApiModelProperty(value = "卖方id")
    private Long  sellerId;

    @ApiModelProperty(value ="买方id")
    private Long  buyerId;

}
