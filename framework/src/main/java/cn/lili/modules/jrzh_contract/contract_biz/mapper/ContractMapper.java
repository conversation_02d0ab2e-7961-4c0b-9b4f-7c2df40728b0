/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.mapper;

import cn.lili.modules.jrzh_contract.contract_api.entity.Contract;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2021-12-31
 */
public interface ContractMapper extends BaseMapper<Contract> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param contract
	 * @return
	 */
	//List<ContractVO> selectContractPage(IPage page, ContractVO contract);

	/**
	 * @param iPage
	 * @param contract
	 * @return
	 */
//	IPage<Contract> selectCustomerContractPage(IPage iPage,@Param("contract") ContractVO contract);

	/**
	 * 查询前端用户所有合同
	 *
//	 * @param iPage
//	 * @param contract
	 * @return
	 */
//	IPage<Contract> selectUserSignContractPage(IPage iPage,@Param("contract") ContractVO contract);

	IPage<Contract> selectContractPage(IPage iPage,@Param("contract") ContractVO contract);


//	List<Contract> selectByGenerateDynamicContractDTO(@Param("generateDynamicContractDTO")GenerateDynamicContractDTO generateDynamicContractDTO);
}
