package cn.lili.modules.jrzh_contract.contract_biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.goods.entity.dos.Goods;
import cn.lili.modules.goods.service.GoodsService;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateCategoryConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateGoodsConfig;
import cn.lili.modules.jrzh_contract.contract_api.enums.ContractTypeEnum;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateCategoryConfigVO;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateGoodsConfigVO;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateConfigService;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateService;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateCategoryConfigService;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateGoodsConfigService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合同模板配置服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ContractTemplateConfigServiceImpl implements IContractTemplateConfigService {

    private final IContractTemplateCategoryConfigService categoryConfigService;
    private final IContractTemplateGoodsConfigService goodsConfigService;
    private final IContractTemplateService contractTemplateService;
    private final GoodsService goodsService;

    @Override
    public ContractTemplate getBestMatchTemplate(String goodsId) {
        // 不再区分合同类型，直接获取最佳匹配模板
        return getBestMatchTemplate(goodsId, null);
    }

    @Override
    public ContractTemplate getBestMatchTemplate(String goodsId, Integer contractType) {
        log.info("开始获取最佳匹配模板，商品ID：{}", goodsId);

        // 1. 获取商品信息
        Goods goods = goodsService.getById(goodsId);
        if (ObjectUtil.isEmpty(goods)) {
            log.warn("商品不存在，商品ID：{}", goodsId);
            throw new ServiceException("商品不存在，无法获取合同模板");
        }

        // 2. 收集所有可能的模板配置
        List<TemplateCandidate> candidates = new ArrayList<>();

        // 2.1 查找商品专用模板
        List<ContractTemplateGoodsConfig> goodsConfigs = goodsConfigService.getGoodsConfigs(goodsId);
        for (ContractTemplateGoodsConfig config : goodsConfigs) {
            ContractTemplate template = contractTemplateService.getByTemplateId(config.getTemplateId());
            if (isTemplateEnabled(template)) {
                candidates.add(new TemplateCandidate(template, config.getPriority(), "商品专用模板", goodsId));
                log.debug("找到商品专用模板：{}，优先级：{}", config.getTemplateId(), config.getPriority());
            } else if (ObjectUtil.isNotEmpty(template)) {
                log.debug("跳过禁用的商品专用模板：{}，状态：{}", config.getTemplateId(), template.getStatus());
            }
        }

        // 2.2 查找分类模板（根据categoryPath中的所有层级分类）- 批量查询优化
        if (StrUtil.isNotEmpty(goods.getCategoryPath())) {
            String[] categoryIds = goods.getCategoryPath().split(",");
            List<String> validCategoryIds = Arrays.stream(categoryIds)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(validCategoryIds)) {
                // 批量查询所有分类的启用配置
                List<ContractTemplateCategoryConfig> allCategoryConfigs = categoryConfigService.getBatchCategoryConfigs(validCategoryIds);

                if (CollUtil.isNotEmpty(allCategoryConfigs)) {
                    // 收集所有模板ID
                    List<String> templateIds = allCategoryConfigs.stream()
                        .map(ContractTemplateCategoryConfig::getTemplateId)
                        .distinct()
                        .collect(Collectors.toList());

                    // 批量查询启用的合同模板
                    List<ContractTemplate> enabledTemplates = contractTemplateService.listEnabledByTemplateIds(templateIds);
                    Map<String, ContractTemplate> templateMap = enabledTemplates.stream()
                        .collect(Collectors.toMap(ContractTemplate::getTemplateId, Function.identity()));

                    // 处理配置和模板的匹配
                    for (ContractTemplateCategoryConfig config : allCategoryConfigs) {
                        ContractTemplate template = templateMap.get(config.getTemplateId());
                        if (template != null) {
                            candidates.add(new TemplateCandidate(template, config.getPriority(), "分类模板", config.getCategoryId()));
                            log.debug("找到分类模板：{}，分类ID：{}，优先级：{}", config.getTemplateId(), config.getCategoryId(), config.getPriority());
                        }
                    }
                }
            }
        }

        // 3. 根据优先级排序并选择最佳模板
        if (CollUtil.isNotEmpty(candidates)) {
            // 按优先级降序排序（数值越大优先级越高）
            candidates.sort((a, b) -> Integer.compare(b.getPriority(), a.getPriority()));

            TemplateCandidate bestCandidate = candidates.get(0);
            log.info("选择最佳模板：{}，来源：{}，优先级：{}",
                bestCandidate.getTemplate().getTemplateId(),
                bestCandidate.getSource(),
                bestCandidate.getPriority());

            return bestCandidate.getTemplate();
        }

        // 4. 如果没有找到任何配置的模板，使用默认模板
        ContractTemplate defaultTemplate = getDefaultTemplate(null);
        if (ObjectUtil.isNotEmpty(defaultTemplate)) {
            log.info("使用默认模板：{}", defaultTemplate.getTemplateId());
            return defaultTemplate;
        }

        // 5. 如果没有找到默认模板，直接报错
        throw new ServiceException("未找到可用的合同模板，请确保已配置默认合同模板或为该商品分类配置合同模板");
    }

    @Override
    public ContractTemplate getDefaultTemplate(Integer contractType) {
        log.info("开始查找默认合同模板");

        // 查找默认模板（defaultContractType = 1 表示默认模板）
        List<ContractTemplate> defaultTemplates = contractTemplateService.list(
            Wrappers.<ContractTemplate>lambdaQuery()
                .eq(ContractTemplate::getDefaultContractType, 1)
                .eq(ContractTemplate::getStatus, 1)
                .orderByDesc(ContractTemplate::getCreateTime)
        );

        if (CollUtil.isNotEmpty(defaultTemplates)) {
            ContractTemplate template = defaultTemplates.get(0);
            log.info("找到默认模板：{}", template.getTemplateName());
            return template;
        }

        log.warn("未找到任何默认合同模板");
        return null;
    }

    @Override
    public List<ContractTemplateCategoryConfig> getCategoryConfigs(String categoryId) {
        return categoryConfigService.getCategoryConfigs(categoryId);
    }

    @Override
    public List<ContractTemplateCategoryConfig> getBatchCategoryConfigs(List<String> categoryIds) {
        return categoryConfigService.getBatchCategoryConfigs(categoryIds);
    }

    @Override
    public List<ContractTemplateGoodsConfig> getGoodsConfigs(String goodsId) {
        return goodsConfigService.getGoodsConfigs(goodsId);
    }

    @Override
    public List<ContractTemplate> getAvailableTemplatesForGoods(String goodsId) {
        log.info("开始获取商品可用的合同模板列表，商品ID：{}", goodsId);

        // 1. 获取商品信息
        Goods goods = goodsService.getById(goodsId);
        if (ObjectUtil.isEmpty(goods)) {
            log.warn("商品不存在，商品ID：{}", goodsId);
            throw new ServiceException("商品不存在，无法获取合同模板");
        }

        // 2. 根据分类路径获取模板
        return getAvailableTemplatesByCategoryPath(goods.getCategoryPath());
    }

    @Override
    public List<ContractTemplate> getAvailableTemplatesByCategoryPath(String categoryPath) {
        log.info("开始根据分类路径获取可用的合同模板列表，分类路径：{}", categoryPath);

        Set<String> templateIds = new HashSet<>();

        // 1. 根据分类路径获取所有层级的模板
        if (StrUtil.isNotEmpty(categoryPath)) {
            String[] categoryIds = categoryPath.split(",");
            List<String> validCategoryIds = Arrays.stream(categoryIds)
                .filter(StrUtil::isNotEmpty)
                .collect(Collectors.toList());

            if (CollUtil.isNotEmpty(validCategoryIds)) {
                // 批量查询所有分类的启用配置
                List<ContractTemplateCategoryConfig> allCategoryConfigs = categoryConfigService.getBatchCategoryConfigs(validCategoryIds);

                // 收集所有模板ID
                templateIds.addAll(allCategoryConfigs.stream()
                    .map(ContractTemplateCategoryConfig::getTemplateId)
                    .collect(Collectors.toSet()));
            }
        }

        // 2. 查询启用的合同模板
        if (CollUtil.isNotEmpty(templateIds)) {
            List<ContractTemplate> templates = contractTemplateService.listEnabledByTemplateIds(new ArrayList<>(templateIds));
            log.info("找到{}个可用的合同模板", templates.size());
            return templates;
        }

        log.info("未找到可用的合同模板");
        return new ArrayList<>();
    }

    @Override
    public String getGoodsBoundTemplateId(String goodsId) {
        return goodsConfigService.getGoodsBoundTemplateId(goodsId);
    }

    @Override
    public boolean saveOrUpdateCategoryConfig(ContractTemplateCategoryConfig config) {
        return categoryConfigService.saveOrUpdateCategoryConfig(config);
    }

    @Override
    public boolean saveOrUpdateGoodsConfig(ContractTemplateGoodsConfig config) {
        return goodsConfigService.saveOrUpdateGoodsConfig(config);
    }

    @Override
    public boolean deleteCategoryConfig(String configId) {
        return categoryConfigService.deleteCategoryConfig(configId);
    }

    @Override
    public boolean deleteGoodsConfig(String configId) {
        return goodsConfigService.deleteGoodsConfig(configId);
    }

    @Override
    public boolean batchConfigCategoryTemplate(List<String> categoryIds, String templateId) {
        return categoryConfigService.batchConfigCategoryTemplate(categoryIds, templateId);
    }

    @Override
    public IPage<ContractTemplateCategoryConfigVO> getCategoryConfigPage(Page<ContractTemplateCategoryConfig> page, String categoryId, String templateId) {
        return categoryConfigService.getCategoryConfigPage(page, categoryId, templateId);
    }

    @Override
    public IPage<ContractTemplateGoodsConfigVO> getGoodsConfigPage(Page<ContractTemplateGoodsConfig> page, String goodsId, String templateId) {
        return goodsConfigService.getGoodsConfigPage(page, goodsId, templateId);
    }

    /**
     * 检查合同模板是否启用
     *
     * @param template 合同模板
     * @return true-启用，false-禁用或模板为空
     */
    private boolean isTemplateEnabled(ContractTemplate template) {
        return ObjectUtil.isNotEmpty(template) && Integer.valueOf(1).equals(template.getStatus());
    }

    /**
     * 模板候选者类，用于比较不同来源的模板优先级
     */
    @Data
    @AllArgsConstructor
    private static class TemplateCandidate {
        /**
         * 合同模板
         */
        private ContractTemplate template;

        /**
         * 优先级（数值越大优先级越高）
         */
        private Integer priority;

        /**
         * 模板来源描述
         */
        private String source;

        /**
         * 来源ID（商品ID或分类ID）
         */
        private String sourceId;
    }
}
