/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 合同配置实体类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Data
@TableName("jrzh_contract_config")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractConfig对象", description = "合同配置")
public class ContractConfig extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 签署方式（0：自动签，1：手动签）
     */
    @ApiModelProperty(value = "签署方式（0：手动签，1：自动签）")
    @NotNull
    private Integer signWay;
    /**
     * 印章图片类型（0：生成，1：上传）
     */
    @ApiModelProperty(value = "签名/印章图片类型（0：上传，1：生成）")
    @NotNull
    private Integer imgType;
    /**
     * 账户id
     */
    @ApiModelProperty(value = "账户id")
    private Long userId;
    /**
     * 签名配置id
     */
    @ApiModelProperty(value = "签名配置id")
    private Long signId;
    /**
     * 印章配置id
     */
    @ApiModelProperty(value = "印章配置id")
    private Long sealId;

}
