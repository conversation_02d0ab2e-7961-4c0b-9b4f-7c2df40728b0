/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_api.entity;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 合同模板方案表实体类
 *
 * <AUTHOR>
 * @since 2022-06-25
 */
@Data
@TableName("jrzh_contract_template_config")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ContractTemplateConfig对象", description = "合同模板方案表")
public class ContractTemplateConfig extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 模板id
	 */
	@ApiModelProperty(value = "模板id")
	private String templateId;
	/**
	 * bean的类路径
	 */
	@ApiModelProperty(value = "bean的全限定类名")
	private String beanClazzPath;
	/**
	 * json合同字段
	 */
	@ApiModelProperty(value = "json合同字段")
	private String templateFieldsJson;
	/**
	 * json全限名类字段json
	 */
	private String templateFieldsConfigJson;
	/**
	 * 产品类型 多选
	 */
	private String goodsType;
	/**
	 * 是否为内置数据源 1 是 0 非
	 */
	private Integer izBuilt;
	/**
	 * 签署节点
	 */
	private String signNode;
}
