package cn.lili.modules.jrzh_contract.contract_api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 合同模板配置DTO
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@Data
@ApiModel(value = "合同模板配置DTO", description = "用于批量配置合同模板")
public class ContractTemplateConfigDTO {

    @ApiModelProperty(value = "配置类型：CATEGORY-分类配置，GOODS-商品配置", required = true)
    @NotEmpty(message = "配置类型不能为空")
    private String configType;

    @ApiModelProperty(value = "分类ID列表（配置类型为CATEGORY时必填）")
    private List<String> categoryIds;

    @ApiModelProperty(value = "商品ID列表（配置类型为GOODS时必填）")
    private List<String> goodsIds;

    @ApiModelProperty(value = "合同模板ID", required = true)
    @NotEmpty(message = "合同模板ID不能为空")
    private String templateId;

    @ApiModelProperty(value = "优先级")
    private Integer priority;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 配置类型枚举
     */
    public enum ConfigType {
        CATEGORY("CATEGORY", "分类配置"),
        GOODS("GOODS", "商品配置");

        private final String code;
        private final String description;

        ConfigType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }
}
