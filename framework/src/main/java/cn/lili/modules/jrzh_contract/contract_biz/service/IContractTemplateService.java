/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.jrzh_contract.contract_biz.service;

import cn.lili.modules.jrzh_contract.contract_api.dto.ContractTemplateDTO;
import cn.lili.modules.jrzh_contract.contract_api.dto.UpdateStatusDTO;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateVO;
import cn.lili.modules.jrzh_contract.contract_api.vo.TemplateCustomerVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
public interface IContractTemplateService extends IService<ContractTemplate> {

    /**
     * 自定义分页
     *
     * @param page
     * @param contractTemplate
     * @return
     */
//    IPage<ContractTemplateVO> selectContractTemplatePage(IPage<ContractTemplateVO> page, ContractTemplateVO contractTemplate);

    /**
     * 启用禁用
     *
     * @param updateStatusDTO
     * @return
     */
    Boolean enableDisable(UpdateStatusDTO updateStatusDTO);

    /**
     * 自定义新增修改接口
     */
    Boolean saveOrUpdateContractTemplateDTO(ContractTemplateDTO contractTemplateDTO);

    /**
     * 同步三方模板
     *
     * @return
     */
//    Boolean syncBestSign(Query query);


    /***
     * 删除相应合同模板  下架模板相关产品
     */
    Boolean remove(String ids);

    /**
     * 获取合同模板名称
     *
     * @param name
     * @return
     */
//    ContractTemplate getByTemplateName(String name);

    /**
     * 获取合同模板
     *
     * @param templateId
     * @return
     */
    ContractTemplate getByTemplateId(String templateId);

    /**
     * 获取合同模板集合
     *
     * @param contractTemplateIds
     * @return
     */
    List<ContractTemplate> listByTemplates(List<String> contractTemplateIds);

    /**
     * 批量获取启用状态的合同模板
     *
     * @param contractTemplateIds 模板ID列表
     * @return 启用状态的合同模板列表
     */
    List<ContractTemplate> listEnabledByTemplateIds(List<String> contractTemplateIds);


//    List<ContractTemplate> listEnableAll();

    /**
     * 详情
     *
     * @param templateId
     * @return
     */
    ContractTemplateVO details(String templateId);

//    String skipToTemplate();

    /**
     * 预览
     *
     * @param templateId
     */
    String preView(String templateId);



}
