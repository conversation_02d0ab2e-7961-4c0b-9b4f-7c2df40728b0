package cn.lili.modules.permission.entity.dos;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 角色部门绑定关系
 *
 * <AUTHOR>
 * @since 2020/11/19 12:18
 */
@Data
@TableName("li_department_role")
@ApiModel(value = "角色部门")
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentRole extends BaseEntity {


    private static final long serialVersionUID = 2342812932116647050L;

    @ApiModelProperty(value = "角色id")
    private String roleId;

    @ApiModelProperty(value = "部门id")
    private String departmentId;

}