<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">-->
<!--<mapper namespace="cn.lili.modules.goods.mapper.CommodityPriceQuoteMapper">-->

<!--    <select id="getCommodityPriceQuoteDtos" resultType="cn.lili.modules.goods.entity.vos.CommodityPriceQuoteVo"-->
<!--            parameterType="cn.lili.modules.goods.entity.dto.CommodityPriceQuoteVoQueryDto">-->
<!--        select jcpq.* , jcl.name as commodityListName, jcp.commodity_spec as commoditySpecName-->
<!--        from jrzh_commodity_price_quote jcpq-->
<!--        left join jrzh_commodity_list jcl on jcl.id = jcpq.commodity_list_id-->
<!--        left join jrzh_commodity_spec jcp on jcp.id = jcpq.commodity_spec_id-->
<!--        <where>-->
<!--            jcpq.is_deleted = 0 and jcl.is_deleted = 0 and jcp.is_deleted = 0-->
<!--            <if test="dto.supplierId != null">-->
<!--                and jcpq.supplier_id = #{dto.supplierId}-->
<!--            </if>-->
<!--            <if test="dto.inquiryIds != null">-->
<!--                and jcpq.commodity_price_inquiry_id in-->
<!--                <foreach item="id" collection="dto.inquiryIds" open="(" separator="," close=")">-->
<!--                    #{id}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="dto.ids != null">-->
<!--                and jcpq.id in-->
<!--                <foreach item="id" collection="dto.ids" open="(" separator="," close=")">-->
<!--                    #{id}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="dto.status != null">-->
<!--                and jcpq.status = #{dto.status}-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->

<!--</mapper>-->
