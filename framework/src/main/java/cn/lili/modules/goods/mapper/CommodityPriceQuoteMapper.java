package cn.lili.modules.goods.mapper;

import cn.lili.modules.goods.entity.dos.CommodityPriceQuote;
import cn.lili.modules.goods.entity.dto.CommodityPriceQuoteVoQueryDto;
import cn.lili.modules.goods.entity.vos.CommodityPriceQuoteVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CommodityPriceQuoteMapper extends BaseMapper<CommodityPriceQuote> {

    @Select("<script>" +
            "SELECT jcpq.*, lg.goods_name AS commodityListName, lgs.simple_specs AS commoditySpecName, " +
            "jcpq.create_time as createTime, ls.store_name AS storeName " +
            "FROM jrzh_commodity_price_quote jcpq " +
            "LEFT JOIN li_goods lg ON lg.id = jcpq.commodity_list_id " +
            "LEFT JOIN li_goods_sku lgs ON lgs.id = jcpq.commodity_spec_id " +
            "LEFT JOIN li_store ls ON ls.id = jcpq.supplier_id " +
            "WHERE jcpq.delete_flag = 0 AND lg.delete_flag = 0 AND lgs.delete_flag = 0 " +
            "<if test='dto.supplierId != null'>AND jcpq.supplier_id = #{dto.supplierId}</if> " +
            "<if test='dto.inquiryIds != null'>AND jcpq.commodity_price_inquiry_id IN " +
            "<foreach item='id' collection='dto.inquiryIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</if> " +
            "<if test='dto.ids != null'>AND jcpq.id IN " +
            "<foreach item='id' collection='dto.ids' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</if> " +
            "<if test='dto.status != null'>AND jcpq.status = #{dto.status}</if> " +
            "ORDER BY jcpq.id DESC " +
            "</script>")
    List<CommodityPriceQuoteVo> getCommodityPriceQuoteDtos(@Param("dto") CommodityPriceQuoteVoQueryDto dto);
}
