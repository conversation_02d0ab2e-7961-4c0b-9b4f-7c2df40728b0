package cn.lili.modules.goods.entity.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 报价dto
 * <AUTHOR>
 * @date 2025/3/6
 */
@Data
public class CommodityPriceQuoteDto implements Serializable {

    /**
     * 报价记录id
     */
    private Long commodityPriceInquiryId;
    /**
     * 供应商报价金额
     */
    private BigDecimal supplierQuotationAmount;
    /**
     * 商品交货数量
     */
    private Integer deliveryQuantity;
    /**
     * 如果是期货的时候才有
     * 交货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deliveryDate;
    /**
     * 配送方式
     * 1-买方自提 2-供方承运
     */
    private Integer deliveryModel;
    /**
     * 结算方式
     * 1-定金发货 2-全额付款 3-分期付款
     */
    private Integer settlementModel;
    /**
     * 供应商商品id
     */
    private Long commodityListId;
    /**
     * 商品规格id
     */
    private Long commoditySpecId;
    /**
     * 定金比例
     * 如20% 传入20
     */
    private BigDecimal earnestMoneyRatio;
}
