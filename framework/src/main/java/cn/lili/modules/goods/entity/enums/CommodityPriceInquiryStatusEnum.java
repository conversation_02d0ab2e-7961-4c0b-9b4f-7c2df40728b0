package cn.lili.modules.goods.entity.enums;

import lombok.Getter;

/**
 * 商品询价状态枚举
 */
@Getter
public enum CommodityPriceInquiryStatusEnum {

    PRICE_INQUIRY(0, "询价中"),
    PRICE_QUOTE(1, "已报价"),
    PRICE_CONFIRM(2, "已确认报价"),
    PRICE_EXPIRE(3, "已过期"),
    ;

    private Integer code;
    private String desc;

    CommodityPriceInquiryStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
