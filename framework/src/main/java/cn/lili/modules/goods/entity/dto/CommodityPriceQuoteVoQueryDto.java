package cn.lili.modules.goods.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
@Data
public class CommodityPriceQuoteVoQueryDto implements Serializable {

    /**
     * 询价记录ids
     */
    private List<String> inquiryIds;
    /**
     * 供应商id
     * 对应店铺li_store的id
     */
    private String supplierId;
    /**
     * 报价记录ids
     */
    private List<Long> ids;
    /**
     * 状态
     */
    private Integer status;

}
