package cn.lili.modules.goods.service;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.entity.dos.CommodityPriceQuote;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquiryQueryDto;
import cn.lili.modules.goods.entity.dto.CommodityPriceQuoteDto;
import cn.lili.modules.goods.entity.dto.CommodityPriceQuoteVoQueryDto;
import cn.lili.modules.goods.entity.enums.CommodityPriceQuoteStatusEnum;
import cn.lili.modules.goods.entity.vos.CommodityPriceInquiryVo;
import cn.lili.modules.goods.entity.vos.CommodityPriceQuoteDetailVo;
import cn.lili.modules.goods.entity.vos.CommodityPriceQuoteVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 商品报价
 */
public interface ICommodityPriceQuoteService extends IService<CommodityPriceQuote> {

    /**
     * 针对询价记录进行报价
     * @param dtos
     * @return
     */
    Boolean quota(List<CommodityPriceQuoteDto> dtos);

    /**
     * 融资端用户我的询价记录
     * @param query
     * @param dto
     * @return
     */
    IPage<CommodityPriceInquiryVo> pageByQueryDto(PageVO query, CommodityPriceInquiryQueryDto dto);

    /**
     * 已报价分页
     * @param query
     * @param storeId
     * @param dto
     * @return
     */
    IPage<CommodityPriceInquiryVo> quotePage(PageVO query, String storeId, CommodityPriceInquiryQueryDto dto);

    /**
     * 融资端用户确认报价
     * @param commodityPriceQuoteIds
     * @param userId
     * @return
     */
    Boolean confirmQuote(List<Long> commodityPriceQuoteIds, String userId);

    /**
     * 变更报价单状态
     * @param id
     * @param quoteStatusEnum
     * @return
     */
    Boolean updateStatus(Long id, CommodityPriceQuoteStatusEnum quoteStatusEnum);
    Boolean updateStatus(List<Long> ids, CommodityPriceQuoteStatusEnum quoteStatusEnum);

    /**
     * 报价详情
     * @param commodityPriceInquiryId
     * @return
     */
    List<CommodityPriceQuoteDetailVo> priceQuoteDetail(String commodityPriceInquiryId);

    List<CommodityPriceQuoteVo> getCommodityPriceQuoteDtos(CommodityPriceQuoteVoQueryDto dto);

    /**
     * 根据订单号查询
     * @param orderSn
     * @return
     */
    CommodityPriceInquiryVo getByOrderSn(String orderSn);
}
