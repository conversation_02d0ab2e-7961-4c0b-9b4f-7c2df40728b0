<!--<?xml version="1.0" encoding="UTF-8"?>-->
<!--<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">-->
<!--<mapper namespace="cn.lili.modules.goods.mapper.CommodityPriceInquiryMapper">-->

<!--    <select id="pageByQueryDto" resultType="cn.lili.modules.goods.entity.vos.CommodityPriceInquiryVo"-->
<!--            parameterType="cn.lili.modules.goods.entity.dto.CommodityPriceInquiryQueryDto">-->
<!--        select jcpi.* , jci.username as customerName-->
<!--        from jrzh_commodity_price_inquiry jcpi-->
<!--        left join li_member jci on jci.id = jcpi.customer_id-->
<!--        <where>-->
<!--            jcpi.delete_flag = 0 and jci.delete_flag = 0-->
<!--            <if test="dto.customerId != null">-->
<!--                and jcpi.customer_id = #{dto.customerId}-->
<!--            </if>-->
<!--            <if test="dto.status != null">-->
<!--                and jcpi.status = #{dto.status}-->
<!--            </if>-->
<!--            <if test="dto.tradeModel != null">-->
<!--                and jcpi.trade_model = #{tradeModel}-->
<!--            </if>-->
<!--            <if test="dto.commodityListName != null">-->
<!--                and jcpi.commodity_list_name like concat('%',#{dto.commodityListName},'%')-->
<!--            </if>-->
<!--            <if test="dto.inquiryIds != null">-->
<!--                and jcpi.id in-->
<!--                <foreach item="id" collection="dto.inquiryIds" open="(" separator="," close=")">-->
<!--                    #{id}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--    </select>-->

<!--</mapper>-->
