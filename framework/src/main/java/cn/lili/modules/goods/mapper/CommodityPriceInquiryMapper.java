package cn.lili.modules.goods.mapper;

import cn.lili.modules.goods.entity.dos.CommodityPriceInquiry;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquiryQueryDto;
import cn.lili.modules.goods.entity.vos.CommodityPriceInquiryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

public interface CommodityPriceInquiryMapper extends BaseMapper<CommodityPriceInquiry> {

    @Select("<script>" +
            "SELECT jcpi.*, jci.username AS customerName, lci.corp_name AS customerCorpName " +
            "FROM jrzh_commodity_price_inquiry jcpi " +
            "LEFT JOIN li_member jci ON jci.id = jcpi.customer_id " +
            "LEFT JOIN li_customer_info lci ON lci.customer_id = jcpi.customer_id " +
            "WHERE jcpi.delete_flag = 0 AND jci.delete_flag = 0 " +
            "<if test='dto.customerId != null'>AND jcpi.customer_id = #{dto.customerId}</if> " +
            "<if test='dto.status != null'>AND jcpi.status = #{dto.status}</if> " +
            "<if test='dto.tradeModel != null'>AND jcpi.trade_model = #{dto.tradeModel}</if> " +
            "<if test='dto.commodityListName != null'>AND jcpi.commodity_list_name LIKE CONCAT('%', #{dto.commodityListName}, '%')</if> " +
            "<if test='dto.corpName != null and dto.corpName != \"\"'>AND lci.corp_name LIKE CONCAT('%', #{dto.corpName}, '%')</if> " +
            "<if test='dto.inquiryIds != null'>AND jcpi.id IN " +
            "<foreach item='id' collection='dto.inquiryIds' open='(' separator=',' close=')'>" +
            "#{id}" +
            "</foreach>" +
            "</if>" +
            "ORDER BY jcpi.id DESC " +
            "</script>")
    IPage<CommodityPriceInquiryVo> pageByQueryDto(IPage<Object> page, @Param("dto") CommodityPriceInquiryQueryDto dto);

    @Select("<script>" +
            "SELECT jcpi.*, jci.username AS customerName, lci.corp_name AS customerCorpName " +
            "FROM jrzh_commodity_price_inquiry jcpi " +
            "LEFT JOIN li_member jci ON jci.id = jcpi.customer_id " +
            "LEFT JOIN li_customer_info lci ON lci.customer_id = jcpi.customer_id " +
            "WHERE jcpi.delete_flag = 0 AND jci.delete_flag = 0 " +
            "AND jcpi.order_sn = #{orderSn} " +
            "ORDER BY jcpi.id DESC " +
            "</script>")
    CommodityPriceInquiryVo getByOrderSn(@Param("orderSn") String orderSn);
}
