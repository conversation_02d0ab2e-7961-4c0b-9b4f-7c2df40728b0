package cn.lili.modules.goods.entity.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 商品询价查询dto
 * <AUTHOR>
 * @date 2025/3/5
 */
@Data
public class CommodityPriceInquiryQueryDto implements Serializable {

    /**
     * 商品名称
     */
    private String commodityListName;
    /**
     * 询价用户id
     */
    private String customerId;
    /**
     * 询价记录状态
     */
    private Integer status;
    /**
     * 交货方式
     * 1-现货交易 2-期货交易 3-混合交易
     */
    private Integer tradeModel;
    /**
     * 询价ids
     */
    private List<String> inquiryIds;
    /**
     * 询价企业
     */
    private String corpName;
}
