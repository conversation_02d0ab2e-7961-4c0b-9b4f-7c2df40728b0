package cn.lili.modules.goods.entity.vos;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2025/4/8
 */
@Data
public class CommodityPriceQuoteDetailVo implements Serializable {

    /**
     * 报价ids
     */
    private String commodityPriceQuoteIds;
    /**
     * 供应商机构名称
     */
    private String supplierName;
    /**
     * 报价总金额
     */
    private BigDecimal totalSupplierQuotationAmount;
    /**
     * 具体的报价
     */
    private List<CommodityPriceQuoteVo> commodityPriceQuoteList;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 店铺名
     */
    private String storeName;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
