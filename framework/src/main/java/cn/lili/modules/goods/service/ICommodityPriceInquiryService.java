package cn.lili.modules.goods.service;

import cn.lili.common.vo.PageVO;
import cn.lili.modules.goods.entity.dos.CommodityPriceInquiry;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquiryQueryDto;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquirySubmitDto;
import cn.lili.modules.goods.entity.enums.CommodityPriceInquiryStatusEnum;
import cn.lili.modules.goods.entity.vos.CommodityPriceInquiryVo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 商品询价
 * <AUTHOR>
 * @date 2025/3/5
 */
public interface ICommodityPriceInquiryService extends IService<CommodityPriceInquiry> {

    /**
     * 融资端用户发起询价
     * @param dto
     * @return
     */
    Boolean submit(CommodityPriceInquirySubmitDto dto);

    /**
     * 根据供应商所属机构查询询价记录
     * @param query
     * @param deptIds
     * @param dto
     * @return
     */
    IPage<CommodityPriceInquiryVo> pageBySupplier(PageVO query, String deptIds, CommodityPriceInquiryQueryDto dto);

    /**
     * 根据id变更询价记录状态
     * @param id
     * @param inquiryStatusEnum
     * @return
     */
    Boolean updateStatus(String id, CommodityPriceInquiryStatusEnum inquiryStatusEnum);

    /**
     * 根据id变更状态并设置报价机构id
     * @param id
     * @param inquiryStatusEnum
     * @param storeId
     * @return
     */
    Boolean updateStatusAndSetQuotaStoreId(Long id, CommodityPriceInquiryStatusEnum inquiryStatusEnum, String storeId);

    IPage<CommodityPriceInquiryVo> pageByQueryDto(PageVO query, CommodityPriceInquiryQueryDto dto);

    /**
     * 根据订单号查询
     * @param orderSn
     * @return
     */
    CommodityPriceInquiryVo getByOrderSn(String orderSn);
}
