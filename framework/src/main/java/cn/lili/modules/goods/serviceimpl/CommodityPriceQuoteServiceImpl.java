package cn.lili.modules.goods.serviceimpl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.common.enums.ClientTypeEnum;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.BeanUtil;
import cn.lili.common.utils.CurrencyUtil;
import cn.lili.common.utils.SnowFlake;
import cn.lili.common.vo.PageVO;
import cn.lili.modules.connect.entity.CustomerInfo;
import cn.lili.modules.connect.service.ICustomerInfoService;
import cn.lili.modules.goods.entity.dos.CommodityPriceInquiry;
import cn.lili.modules.goods.entity.dos.CommodityPriceQuote;
import cn.lili.modules.goods.entity.dos.GoodsSku;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquiryQueryDto;
import cn.lili.modules.goods.entity.dto.CommodityPriceQuoteDto;
import cn.lili.modules.goods.entity.dto.CommodityPriceQuoteVoQueryDto;
import cn.lili.modules.goods.entity.enums.CommodityPriceInquiryStatusEnum;
import cn.lili.modules.goods.entity.enums.CommodityPriceQuoteStatusEnum;
import cn.lili.modules.goods.entity.enums.DeliveryModelEnum;
import cn.lili.modules.goods.entity.enums.SettlementModelEnum;
import cn.lili.modules.goods.entity.vos.CommodityPriceInquiryVo;
import cn.lili.modules.goods.entity.vos.CommodityPriceQuoteDetailVo;
import cn.lili.modules.goods.entity.vos.CommodityPriceQuoteVo;
import cn.lili.modules.goods.mapper.CommodityPriceQuoteMapper;
import cn.lili.modules.goods.service.GoodsSkuService;
import cn.lili.modules.goods.service.ICommodityPriceInquiryService;
import cn.lili.modules.goods.service.ICommodityPriceQuoteService;
import cn.lili.modules.order.cart.entity.dto.TradeDTO;
import cn.lili.modules.order.cart.entity.enums.CartTypeEnum;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import cn.lili.modules.order.order.entity.dos.Order;
import cn.lili.modules.order.order.entity.dos.OrderItem;
import cn.lili.modules.order.order.entity.dos.Receipt;
import cn.lili.modules.order.order.entity.dto.OrderMessage;
import cn.lili.modules.order.order.entity.dto.PriceDetailDTO;
import cn.lili.modules.order.order.entity.enums.*;
import cn.lili.modules.order.order.service.OrderItemService;
import cn.lili.modules.order.order.service.OrderService;
import cn.lili.modules.order.order.service.ReceiptService;
import cn.lili.modules.order.trade.entity.dos.OrderLog;
import cn.lili.modules.order.trade.service.OrderLogService;
import cn.lili.modules.payment.entity.enums.PaymentMethodEnum;
import cn.lili.modules.store.entity.dos.FreightTemplateChild;
import cn.lili.modules.store.entity.dos.Store;
import cn.lili.modules.store.entity.dos.StoreDetail;
import cn.lili.modules.store.entity.dto.FreightTemplateChildDTO;
import cn.lili.modules.store.entity.enums.FreightTemplateEnum;
import cn.lili.modules.store.entity.vos.FreightTemplateVO;
import cn.lili.modules.store.entity.vos.StoreVO;
import cn.lili.modules.store.service.FreightTemplateService;
import cn.lili.modules.store.service.StoreDetailService;
import cn.lili.modules.store.service.StoreService;
import cn.lili.modules.system.entity.dos.Region;
import cn.lili.modules.system.service.RegionService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报价处理器
 * <AUTHOR>
 * @date 2025/3/5
 */
@Service
@RequiredArgsConstructor
public class CommodityPriceQuoteServiceImpl extends ServiceImpl<CommodityPriceQuoteMapper, CommodityPriceQuote> implements ICommodityPriceQuoteService {

    private final ICommodityPriceInquiryService commodityPriceInquiryService;
    private final OrderService orderService;
    private final OrderItemService orderItemService;
    private final OrderLogService orderLogService;
    private final GoodsSkuService goodsSkuService;
    private final FreightTemplateService freightTemplateService;
    private final RegionService regionService;

    private final CommodityPriceQuoteMapper commodityPriceQuoteMapper;

    private final StoreService storeService;
    private final ICustomerInfoService customerInfoService;
    private final ReceiptService receiptService;
    private final StoreDetailService storeDetailService;

    /**
     * 针对询价记录进行报价
     * @param dtos
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean quota(List<CommodityPriceQuoteDto> dtos) {
        CommodityPriceQuoteDto commodityPriceQuoteDto = dtos.get(0);
        // 询价记录id
        Long commodityPriceInquiryId = commodityPriceQuoteDto.getCommodityPriceInquiryId();
        CommodityPriceInquiry inquiry = commodityPriceInquiryService.getById(commodityPriceInquiryId);
        if (ObjectUtil.isEmpty(inquiry)) {
            throw new ServiceException("询价记录不存在");
        }
        if (CommodityPriceInquiryStatusEnum.PRICE_CONFIRM.getCode().equals(inquiry.getStatus())) {
            throw new ServiceException("已确认询价，无法再次报价");
        }
        // 店铺id
        String storeId = UserContext.getCurrentUser().getStoreId();
        StoreDetail storeDetail = storeDetailService.getStoreDetail(storeId);
        // 如果是非已确认报价状态则变更状态为已报价
        if (!CommodityPriceInquiryStatusEnum.PRICE_CONFIRM.getCode().equals(inquiry.getStatus())) {
            // 更新询价记录状态
            Boolean updateInquiry = commodityPriceInquiryService.updateStatusAndSetQuotaStoreId(commodityPriceInquiryId, CommodityPriceInquiryStatusEnum.PRICE_QUOTE, storeId);
        }
        List<CommodityPriceQuote> needSave = new ArrayList<>();
        for (CommodityPriceQuoteDto dto : dtos) {
            // 新增或更新
            CommodityPriceQuote entity = new CommodityPriceQuote();
            entity.setCommodityPriceInquiryId(commodityPriceInquiryId);
            entity.setSupplierId(storeId);
            entity.setSupplierName(storeDetail.getCompanyName());
            entity.setSupplierQuotationAmount(dto.getSupplierQuotationAmount());
            entity.setDeliveryQuantity(dto.getDeliveryQuantity());
            entity.setDeliveryDate(dto.getDeliveryDate());
            entity.setDeliveryModel(dto.getDeliveryModel());
            entity.setSettlementModel(dto.getSettlementModel());
            entity.setCommodityListId(dto.getCommodityListId());
            entity.setCommoditySpecId(dto.getCommoditySpecId());
            entity.setStatus(CommodityPriceQuoteStatusEnum.PRICE_QUOTE.getCode());
            entity.setEarnestMoneyRatio(dto.getEarnestMoneyRatio());
            needSave.add(entity);
        }
        // 如果询价为全额, 不允许报定金
        if (SettlementModelEnum.FULL_PAYMENT.getCode().equals(inquiry.getSettlementModel())) {
            // 过滤出定金发货
            List<CommodityPriceQuote> outGoods = needSave.stream().filter(e -> SettlementModelEnum.DEPOSIT_SEND_OUT_GOODS.getCode().equals(e.getSettlementModel())).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(outGoods)) {
                throw new ServiceException("全额询价不允许定金报价");
            }
        }
        boolean remove = this.remove(Wrappers.<CommodityPriceQuote>lambdaUpdate()
                .eq(CommodityPriceQuote::getSupplierId, storeId)
                .eq(CommodityPriceQuote::getCommodityPriceInquiryId, commodityPriceInquiryId)
        );
        return saveBatch(needSave);
    }

    /**
     * 融资端用户我的询价记录
     * @param query
     * @param dto
     * @return
     */
    @Override
    public IPage<CommodityPriceInquiryVo> pageByQueryDto(PageVO query, CommodityPriceInquiryQueryDto dto) {
        IPage<CommodityPriceInquiryVo> page = commodityPriceInquiryService.pageByQueryDto(query, dto);
        List<CommodityPriceInquiryVo> records = page.getRecords();
        if (CollectionUtil.isEmpty(records)) {
            return page;
        }
        // 询价记录ids
        List<String> inquiryIds = records.stream().map(CommodityPriceInquiryVo::getId).collect(Collectors.toList());
        CommodityPriceQuoteVoQueryDto quoteVoQueryDto = new CommodityPriceQuoteVoQueryDto();
        quoteVoQueryDto.setInquiryIds(inquiryIds);
        List<CommodityPriceQuoteVo> quoteVoAllList = this.getCommodityPriceQuoteDtos(quoteVoQueryDto);
        if (CollectionUtil.isNotEmpty(quoteVoAllList)) {
            Map<String, List<CommodityPriceQuoteVo>> inquiryIdMap = quoteVoAllList.stream().collect(Collectors.groupingBy(CommodityPriceQuoteVo::getCommodityPriceInquiryId));
            for (CommodityPriceInquiryVo vo : records) {
                // 如果是已报价，则获取所有报价的数据
                if (CommodityPriceInquiryStatusEnum.PRICE_QUOTE.getCode().equals(vo.getStatus())) {
                    vo.setCommodityPriceQuoteList(inquiryIdMap.get(vo.getId()));
                }
                // 如果是确认报价，则只获取已确认报价的报价数据
                if (CommodityPriceInquiryStatusEnum.PRICE_CONFIRM.getCode().equals(vo.getStatus())) {
                    List<CommodityPriceQuoteVo> commodityPriceQuotes = inquiryIdMap.get(vo.getId());
                    List<CommodityPriceQuoteVo> collect = commodityPriceQuotes.stream().filter(e -> CommodityPriceQuoteStatusEnum.PRICE_CONFIRM.getCode().equals(e.getStatus())).collect(Collectors.toList());
                    vo.setCommodityPriceQuoteList(collect);
                }
            }
        }
        return page;
    }

    /**
     * 平台端已报价分页
     * @param query
     * @param storeId
     * @param dto
     * @return
     */
    @Override
    public IPage<CommodityPriceInquiryVo> quotePage(PageVO query, String storeId, CommodityPriceInquiryQueryDto dto) {
        CommodityPriceQuoteVoQueryDto quoteVoQueryDto = new CommodityPriceQuoteVoQueryDto();
        quoteVoQueryDto.setSupplierId(storeId);
        List<CommodityPriceQuoteVo> quoteVoAllList = this.getCommodityPriceQuoteDtos(quoteVoQueryDto);
        if (CollectionUtil.isEmpty(quoteVoAllList)) {
            return new Page<>(query.getPageNumber(), query.getPageSize());
        }
        Map<String, List<CommodityPriceQuoteVo>> inquiryIdMap = CollStreamUtil.groupBy(quoteVoAllList, CommodityPriceQuoteVo::getCommodityPriceInquiryId, Collectors.toList());
        List<String> inquiryIds = CollStreamUtil.toList(quoteVoAllList, CommodityPriceQuoteVo::getCommodityPriceInquiryId);
        dto.setInquiryIds(inquiryIds);
        dto.setCommodityListName(dto.getCommodityListName());
        IPage<CommodityPriceInquiryVo> commodityPriceInquiryVoIPage = commodityPriceInquiryService.pageByQueryDto(query, dto);
        for (CommodityPriceInquiryVo vo : commodityPriceInquiryVoIPage.getRecords()) {
            vo.setCommodityPriceQuoteList(inquiryIdMap.get(vo.getId()));
        }
        return commodityPriceInquiryVoIPage;
    }

    /**
     * 融资端用户确认报价
     * @param commodityPriceQuoteIds
     * @param userId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean confirmQuote(List<Long> commodityPriceQuoteIds, String userId) {
        CommodityPriceQuoteVoQueryDto quoteVoQueryDto = new CommodityPriceQuoteVoQueryDto();
        quoteVoQueryDto.setIds(commodityPriceQuoteIds);
        List<CommodityPriceQuoteVo> quoteVos = this.getCommodityPriceQuoteDtos(quoteVoQueryDto);
        if (CollectionUtil.isEmpty(quoteVos)) {
            throw new ServiceException("报价记录不存在");
        }
        String commodityPriceInquiryId = quoteVos.get(0).getCommodityPriceInquiryId();
        CommodityPriceInquiry inquiry = commodityPriceInquiryService.getById(commodityPriceInquiryId);
        if (ObjectUtil.isEmpty(inquiry)) {
            throw new ServiceException("询价记录不存在");
        }
        if (CommodityPriceInquiryStatusEnum.PRICE_CONFIRM.getCode().equals(inquiry.getStatus())) {
            throw new ServiceException("以确认报价，无法二次确认报价");
        }
        // 生成交易订单
        this.intoDB(quoteVos, inquiry);
        // 更新询价状态
        inquiry.setStatus(CommodityPriceInquiryStatusEnum.PRICE_CONFIRM.getCode());
        Boolean updateQuote = this.updateStatus(commodityPriceQuoteIds, CommodityPriceQuoteStatusEnum.PRICE_CONFIRM);
        Boolean updateInquiry = commodityPriceInquiryService.updateById(inquiry);
        return updateInquiry && updateQuote;
    }

    private void intoDB(List<CommodityPriceQuoteVo> quoteVos, CommodityPriceInquiry inquiry) {
        CommodityPriceQuoteVo commodityPriceQuoteVo = quoteVos.get(0);
        String supplierId = commodityPriceQuoteVo.getSupplierId();
        Store store = storeService.getById(supplierId);
        if (ObjectUtil.isEmpty(store)) {
            throw new ServiceException("报价店铺不存在");
        }

        AuthUser currentUser = UserContext.getCurrentUser();

        // 先构建基本订单信息，不包含运费和最终价格
        Order order = this.buildOrderWithoutFreight(quoteVos, inquiry, currentUser, store);

        // 商品sku
        List<Long> goodsSkuIds = CollStreamUtil.toList(quoteVos, CommodityPriceQuoteVo::getCommoditySpecId);
        List<GoodsSku> goodsSkus = goodsSkuService.listByIds(goodsSkuIds);
        Map<String, List<GoodsSku>> goodsSkuMap = CollStreamUtil.groupBy(goodsSkus, GoodsSku::getId, Collectors.toList());

        // 创建订单项列表
        List<OrderItem> orderItemList = new ArrayList<>();
        List<OrderLog> orderLogs = new ArrayList<>();

        // 先构建所有订单项
        for (CommodityPriceQuoteVo quoteVo : quoteVos) {
            OrderItem orderItem = this.buildOrderItem(quoteVo, order, inquiry, goodsSkuMap);
            orderItemList.add(orderItem);
            String message = "订单[" + orderItem.getSn() + "]创建";
            //记录日志
            orderLogs.add(new OrderLog(orderItem.getSn(), currentUser.getId(), UserContext.getCurrentUser().getRole().getRole(),
                    UserContext.getCurrentUser().getUsername(), message));
        }

        // 计算每个子订单的运费
        Map<String, Double> skuFreightMap = calculateItemFreight(orderItemList, order.getConsigneeAddressPath());

        // 更新每个子订单的运费和总价
        Double totalFreight = 0.0;
        for (OrderItem orderItem : orderItemList) {
            Double itemFreight = skuFreightMap.getOrDefault(orderItem.getSkuId(), 0.0);
            totalFreight = CurrencyUtil.add(totalFreight, itemFreight);

            // 更新子订单的运费和总价
            updateOrderItemWithFreight(orderItem, itemFreight);
        }

        // 使用子订单运费总和更新主订单
        updateOrderWithTotalFreight(order, totalFreight);

        // 保存订单和订单项
        inquiry.setOrderSn(order.getSn());
        orderService.save(order);
        orderItemService.saveBatch(orderItemList);
        //批量记录订单操作日志
        orderLogService.saveBatch(orderLogs);

        //发送订单未付款消息
        OrderMessage orderMessage = new OrderMessage();
        orderMessage.setOrderSn(order.getSn());
        orderMessage.setPaymentMethod(order.getPaymentMethod());
        orderMessage.setNewStatus(OrderStatusEnum.UNPAID);
        orderService.sendUpdateStatusMessage(orderMessage);
//        // 保存收据
//        this.intoReceipt(order, orderItemList);
    }

    private void intoReceipt(Order order, List<OrderItem> orderItemList) {

        // 设置抬头及纳税人识别号
        String memberId = order.getMemberId();
        CustomerInfo customerInfo = customerInfoService.getBuyCustomerId(memberId);
        if (ObjectUtil.isEmpty(customerInfo)) {
            throw new ServiceException("企业信息有误!");
        }
        // 统一社会信用代码
        String businessLicenceNumber = customerInfo.getBusinessLicenceNumber();
        // 公司名称
        String corpName = customerInfo.getCorpName();
        List<Receipt> receipts = new ArrayList<>();
        for (OrderItem orderItem : orderItemList) {
            // 创建收据
            Receipt receipt = new Receipt();
            receipt.setReceiptTitle(corpName);
            receipt.setTaxpayerId(businessLicenceNumber);
            receipt.setReceiptContent("商品明细");
            receipt.setMemberId(order.getMemberId());
            receipt.setMemberName(order.getMemberName());
            receipt.setStoreId(order.getStoreId());
            receipt.setStoreName(order.getStoreName());
            receipt.setOrderSn(order.getSn());
            receipt.setReceiptDetail(JSONUtil.toJsonStr(orderItem));
            receipt.setReceiptPrice(orderItem.getChangeFlowPrice().doubleValue());
            receipt.setReceiptStatus(ReceiptStatusEnum.UN_GENERATE.getCode());
            receipt.setOrderItemSn(orderItem.getSn());
            receipts.add(receipt);
        }
        //保存发票
        receiptService.saveBatch(receipts);
    }

    /**
     * 变更报价单状态
     * @param id
     * @param quoteStatusEnum
     * @return
     */
    @Override
    public Boolean updateStatus(Long id, CommodityPriceQuoteStatusEnum quoteStatusEnum) {
        return this.update(Wrappers.<CommodityPriceQuote>lambdaUpdate()
                .eq(CommodityPriceQuote::getId, id)
                .set(CommodityPriceQuote::getStatus, quoteStatusEnum.getCode())
        );
    }

    @Override
    public Boolean updateStatus(List<Long> ids, CommodityPriceQuoteStatusEnum quoteStatusEnum) {
        return this.update(Wrappers.<CommodityPriceQuote>lambdaUpdate()
                .in(CommodityPriceQuote::getId, ids)
                .set(CommodityPriceQuote::getStatus, quoteStatusEnum.getCode())
        );
    }

    @Override
    public List<CommodityPriceQuoteDetailVo> priceQuoteDetail(String commodityPriceInquiryId) {
        CommodityPriceInquiry inquiry = commodityPriceInquiryService.getById(commodityPriceInquiryId);
        CommodityPriceQuoteVoQueryDto quoteVoQueryDto = new CommodityPriceQuoteVoQueryDto();
        quoteVoQueryDto.setInquiryIds(Arrays.asList(commodityPriceInquiryId));
//        quoteVoQueryDto.setStatus(inquiry.getStatus());
        List<CommodityPriceQuoteVo> quoteVoAllList = this.getCommodityPriceQuoteDtos(quoteVoQueryDto);
        if (CollectionUtil.isEmpty(quoteVoAllList)) {
            return Collections.emptyList();
        }
        List<CommodityPriceQuoteDetailVo> list = new ArrayList<>();
        // 根据供应商id进行分组
        Map<String, List<CommodityPriceQuoteVo>> supplierIds = CollStreamUtil.groupBy(quoteVoAllList, CommodityPriceQuoteVo::getSupplierId, Collectors.toList());
        String commodityPriceQuoteIds = "";
        for (String supplierId : supplierIds.keySet()) {
            List<CommodityPriceQuoteVo> commodityPriceQuotes = supplierIds.get(supplierId);
            String supplierName = commodityPriceQuotes.get(0).getSupplierName();
            CommodityPriceQuoteDetailVo vo = new CommodityPriceQuoteDetailVo();
            vo.setSupplierName(supplierName);
            vo.setCommodityPriceQuoteList(commodityPriceQuotes);
            BigDecimal totalSupplierQuotationAmount = commodityPriceQuotes.stream().map(CommodityPriceQuoteVo::getSupplierQuotationAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            vo.setTotalSupplierQuotationAmount(totalSupplierQuotationAmount);
            for (CommodityPriceQuoteVo commodityPriceQuoteVo : commodityPriceQuotes) {
                commodityPriceQuoteIds = commodityPriceQuoteIds + commodityPriceQuoteVo.getId() + ",";
            }
            vo.setCommodityPriceQuoteIds(commodityPriceQuoteIds);
            vo.setStatus(commodityPriceQuotes.get(0).getStatus());
            vo.setStoreName(commodityPriceQuotes.get(0).getStoreName());
            vo.setCreateTime(commodityPriceQuotes.get(0).getCreateTime());
            list.add(vo);
        }
        return list;
    }

    @Override
    public List<CommodityPriceQuoteVo> getCommodityPriceQuoteDtos(CommodityPriceQuoteVoQueryDto dto) {
        return commodityPriceQuoteMapper.getCommodityPriceQuoteDtos(dto);
    }

    /**
     * 根据订单号查询
     * @param orderSn
     * @return
     */
    @Override
    public CommodityPriceInquiryVo getByOrderSn(String orderSn) {
        CommodityPriceInquiryVo vo = commodityPriceInquiryService.getByOrderSn(orderSn);
        if (ObjectUtil.isEmpty(vo)) {
            return vo;
        }
        // 询价记录ids
        CommodityPriceQuoteVoQueryDto quoteVoQueryDto = new CommodityPriceQuoteVoQueryDto();
        quoteVoQueryDto.setInquiryIds(Arrays.asList(vo.getId()));
        List<CommodityPriceQuoteVo> quoteVoAllList = this.getCommodityPriceQuoteDtos(quoteVoQueryDto);
        if (CollectionUtil.isNotEmpty(quoteVoAllList)) {
            Map<String, List<CommodityPriceQuoteVo>> inquiryIdMap = quoteVoAllList.stream().collect(Collectors.groupingBy(CommodityPriceQuoteVo::getCommodityPriceInquiryId));
            // 如果是已报价，则获取所有报价的数据
            if (CommodityPriceInquiryStatusEnum.PRICE_QUOTE.getCode().equals(vo.getStatus())) {
                vo.setCommodityPriceQuoteList(inquiryIdMap.get(vo.getId()));
            }
            // 如果是确认报价，则只获取已确认报价的报价数据
            if (CommodityPriceInquiryStatusEnum.PRICE_CONFIRM.getCode().equals(vo.getStatus())) {
                List<CommodityPriceQuoteVo> commodityPriceQuotes = inquiryIdMap.get(vo.getId());
                List<CommodityPriceQuoteVo> collect = commodityPriceQuotes.stream().filter(e -> CommodityPriceQuoteStatusEnum.PRICE_CONFIRM.getCode().equals(e.getStatus())).collect(Collectors.toList());
                vo.setCommodityPriceQuoteList(collect);
            }
        }
        return vo;
    }

    private OrderItem buildOrderItem(CommodityPriceQuoteVo quoteVo, Order order, CommodityPriceInquiry inquiry, Map<String, List<GoodsSku>> goodsSkuMap) {
        String sn = order.getSn();
        Double supplierQuotationAmount = quoteVo.getSupplierQuotationAmount().doubleValue();
        // 计算单价
        Double unitPrice = quoteVo.getSupplierQuotationAmount().divide(BigDecimal.valueOf(quoteVo.getDeliveryQuantity()), 2, RoundingMode.HALF_UP).doubleValue();
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderSn(sn);
        orderItem.setSn(SnowFlake.createStr("OI"));
        orderItem.setGoodsId(String.valueOf(quoteVo.getCommodityListId()));
        orderItem.setGoodsName(quoteVo.getCommodityListName() + " " + quoteVo.getCommoditySpecName());
        orderItem.setSkuId(String.valueOf(quoteVo.getCommoditySpecId()));
        orderItem.setSimpleSpecs(quoteVo.getCommoditySpecName());
        orderItem.setStoreId(quoteVo.getSupplierId());
        orderItem.setStoreName(quoteVo.getSupplierName());
        orderItem.setGoodsPrice(unitPrice);
        orderItem.setFlowPrice(supplierQuotationAmount);
        orderItem.setSubTotal(supplierQuotationAmount);
        orderItem.setUnitPrice(unitPrice);
        orderItem.setNum(quoteVo.getDeliveryQuantity());
        orderItem.setDeliveryDate(quoteVo.getDeliveryDate());
        orderItem.setDeliveryModel(DeliveryModelEnum.getEnumByCode(quoteVo.getDeliveryModel()).getDeliveryMethodEnum().name());
        orderItem.setSettlementModel(SettlementModelEnum.getEnumByCode(quoteVo.getSettlementModel()).name());
        orderItem.setImage(goodsSkuMap.get(String.valueOf(quoteVo.getCommoditySpecId())).get(0).getThumbnail());
        orderItem.setAfterSaleStatus(OrderItemAfterSaleStatusEnum.NEW.name());
        orderItem.setCommentStatus(CommentStatusEnum.NEW.name());
        orderItem.setComplainStatus(OrderComplaintStatusEnum.NEW.name());

        orderItem.setChangeDeliverNumber(quoteVo.getDeliveryQuantity());
        orderItem.setChangeFlowPrice(quoteVo.getSupplierQuotationAmount());
        orderItem.setEarnestMoneyRatio(quoteVo.getEarnestMoneyRatio());
        orderItem.setPaidAmount(BigDecimal.ZERO);
        orderItem.setChangeConfirmStatus(" ");
        orderItem.setApplyInvoicingStatus(ApplyInvoicingStatusEnum.NOT_APPLIED.name());

        PriceDetailDTO priceDetailDTO = new PriceDetailDTO();
        priceDetailDTO.setOriginalPrice(supplierQuotationAmount);
        priceDetailDTO.setGoodsPrice(supplierQuotationAmount);
        priceDetailDTO.setFlowPrice(supplierQuotationAmount);
        priceDetailDTO.setBillPrice(supplierQuotationAmount);
        orderItem.setPriceDetailDTO(priceDetailDTO);
        orderItem.setItemPaymentMethod(" ");
        return orderItem;
    }

    private Order buildOrderWithoutFreight(List<CommodityPriceQuoteVo> quoteVos, CommodityPriceInquiry inquiry, AuthUser currentUser, Store store) {
        BigDecimal totalAmount = BigDecimal.ZERO;
        Integer goodsNum = 0;
        for (CommodityPriceQuoteVo quoteVo : quoteVos) {
            totalAmount = totalAmount.add(quoteVo.getSupplierQuotationAmount());
            goodsNum = goodsNum + Optional.ofNullable(quoteVo.getDeliveryQuantity()).orElse(0);
        }

        Order order = new Order();
        order.setSn(SnowFlake.createStr("O"));
        order.setStoreId(store.getId());
        order.setStoreName(store.getStoreName());
        order.setMemberId(currentUser.getId());
        order.setMemberName(currentUser.getUsername());
        order.setOrderStatus(OrderStatusEnum.UNPAID.name());
        order.setOrderType(OrderTypeEnum.BAOJIA.name());
        order.setPayStatus(PayStatusEnum.UNPAID.name());
        order.setDeliverStatus(DeliverStatusEnum.UNDELIVERED.name());
        order.setPaymentMethod(PaymentMethodEnum.BANK_TRANSFER.name());
        order.setConsigneeName(inquiry.getContactName());
        order.setConsigneeMobile(inquiry.getContactPhone());
        order.setDeliveryMethod(DeliveryModelEnum.getEnumByCode(inquiry.getDeliveryModel()).getDeliveryMethodEnum().name());
        order.setConsigneeAddressPath(inquiry.getDeliveryPlace());
        order.setRemark(inquiry.getDetailedSescription());
        order.setLogisticsTime(inquiry.getDeliveryDate());
        order.setGoodsName(inquiry.getCommodityListName());
        order.setGoodsNum(goodsNum);
        order.setSimpleSpecs(inquiry.getCommoditySpec());
        order.setSettlementModel(SettlementModelEnum.getEnumByCode(inquiry.getSettlementModel()).name());

        // 设置商品总价
        order.setGoodsPrice(totalAmount.doubleValue());

        // 初始化价格详情，不包含运费
        PriceDetailDTO priceDetailDTO = new PriceDetailDTO();
        priceDetailDTO.setOriginalPrice(totalAmount.doubleValue());
        priceDetailDTO.setGoodsPrice(totalAmount.doubleValue());
        priceDetailDTO.setFreightPrice(0.0); // 初始化运费为0，后续更新
        priceDetailDTO.setFlowPrice(totalAmount.doubleValue()); // 初始流水价格等于商品价格，后续更新
        priceDetailDTO.setBillPrice(totalAmount.doubleValue()); // 初始结算价格等于商品价格，后续更新
        order.setPriceDetailDTO(priceDetailDTO);

        return order;
    }

    /**
     * 使用子订单运费总和更新主订单
     */
    private void updateOrderWithTotalFreight(Order order, Double totalFreight) {
        // 更新订单的运费
        order.setFreightPrice(totalFreight);

        // 更新订单的总价 = 商品价格 + 运费
        Double newFlowPrice = CurrencyUtil.add(order.getGoodsPrice(), totalFreight);
        order.setFlowPrice(newFlowPrice);

        // 更新订单的价格详情
        PriceDetailDTO priceDetailDTO = order.getPriceDetailDTO();
        priceDetailDTO.setFreightPrice(totalFreight);
        priceDetailDTO.setFlowPrice(newFlowPrice);
        priceDetailDTO.setBillPrice(newFlowPrice);
        order.setPriceDetailDTO(priceDetailDTO);
        order.setPriceDetail(JSONUtil.toJsonStr(priceDetailDTO));
    }


    /**
     * 计算运费
     */
    private Double countFreight(Double count, FreightTemplateChildDTO template) {
        try {
            Double finalFreight = template.getFirstPrice();
            //不满首重 / 首件
            if (template.getFirstCompany() >= count) {
                return finalFreight;
            }
            //如果续重/续件，费用不为空，则返回
            if (template.getContinuedCompany() == 0 || template.getContinuedPrice() == 0) {
                return finalFreight;
            }

            //计算 续重 / 续件 价格
            Double continuedCount = count - template.getFirstCompany();
            return CurrencyUtil.add(finalFreight,
                    CurrencyUtil.mul(Math.ceil(continuedCount / template.getContinuedCompany()), template.getContinuedPrice()));
        } catch (Exception e) {
            log.error("计算运费出错", e);
            return 0D;
        }
    }

    /**
     * 运费计算项，用于临时存储计算运费所需的信息
     */
    @Data
    private static class FreightCalculateItem {
        private String skuId;
        private Integer quantity;
        private Double weight;
    }

    /**
     * 更新子订单的运费相关字段
     * @param orderItem 子订单
     * @param freight 运费
     */
    private void updateOrderItemWithFreight(OrderItem orderItem, Double freight) {
        // 获取原价格详情
        PriceDetailDTO priceDetail = orderItem.getPriceDetailDTO();

        // 更新运费
        priceDetail.setFreightPrice(freight);

        // 更新总价
        Double newFlowPrice = CurrencyUtil.add(priceDetail.getGoodsPrice(), freight);
//        priceDetail.setFlowPrice(newFlowPrice);
//        priceDetail.setBillPrice(newFlowPrice);

        // 更新子订单的价格详情
        orderItem.setPriceDetailDTO(priceDetail);

        // 更新子订单的总价
        orderItem.setFlowPrice(newFlowPrice);
//        orderItem.setChangeFlowPrice(BigDecimal.valueOf(newFlowPrice));
    }

    /**
     * 计算每个子订单的运费
     * @param orderItemList 子订单列表
     * @param addressPath 收货地址路径
     * @return 子订单ID到运费的映射
     */
    private Map<String, Double> calculateItemFreight(List<OrderItem> orderItemList, String addressPath) {
        Map<String, Double> skuFreightMap = new HashMap<>();

        if (CharSequenceUtil.isEmpty(addressPath)) {
            return skuFreightMap;
        }

        // 解析收货地址路径，获取市级ID
        String[] addressPathArray = addressPath.split(",");
        if (addressPathArray.length < 2) {
            return skuFreightMap;
        }
        // 市级
        String cityStr = addressPathArray[1];
        Region region = regionService.getOne(Wrappers.<Region>lambdaQuery()
                .eq(Region::getName, cityStr)
        );
        if (ObjectUtil.isEmpty(region)) {
            return skuFreightMap;
        }
        String cityId = region.getId();
        // 按运费模板分组
        Map<String, List<FreightCalculateItem>> templateItemsMap = new HashMap<>();

        // 收集每个SKU的运费计算项
        for (OrderItem orderItem : orderItemList) {
            // 如果订单是买方自提则不计算运费
            if (DeliveryMethodEnum.SELF_PICK_UP.name().equals(orderItem.getDeliveryModel())) {
                continue;
            }
            String skuId = orderItem.getSkuId();
            GoodsSku sku = goodsSkuService.getById(skuId);

            if (sku == null || CharSequenceUtil.isEmpty(sku.getFreightTemplateId())) {
                skuFreightMap.put(skuId, 0.0);
                continue;
            }

            String templateId = sku.getFreightTemplateId();
            FreightCalculateItem item = new FreightCalculateItem();
            item.setSkuId(skuId);
            item.setQuantity(orderItem.getNum());
            item.setWeight(sku.getWeight() != null ? sku.getWeight() : 0.0);

            if (!templateItemsMap.containsKey(templateId)) {
                templateItemsMap.put(templateId, new ArrayList<>());
            }
            templateItemsMap.get(templateId).add(item);
        }

        // 计算每个运费模板的运费
        for (Map.Entry<String, List<FreightCalculateItem>> entry : templateItemsMap.entrySet()) {
            String templateId = entry.getKey();
            List<FreightCalculateItem> items = entry.getValue();

            // 获取运费模板
            FreightTemplateVO freightTemplate = freightTemplateService.getFreightTemplate(templateId);
            if (freightTemplate == null || freightTemplate.getFreightTemplateChildList() == null
                    || freightTemplate.getFreightTemplateChildList().isEmpty()) {
                continue;
            }

            // 免运费模板跳过
            if (FreightTemplateEnum.FREE.name().equals(freightTemplate.getPricingMethod())) {
                for (FreightCalculateItem item : items) {
                    skuFreightMap.put(item.getSkuId(), 0.0);
                }
                continue;
            }

            // 查找匹配的运费规则
            FreightTemplateChild freightTemplateChild = null;
            for (FreightTemplateChild templateChild : freightTemplate.getFreightTemplateChildList()) {
                if (templateChild.getAreaId() != null && templateChild.getAreaId().contains(cityId)) {
                    freightTemplateChild = templateChild;
                    break;
                }
            }

            // 如果没有匹配的规则，使用默认规则
            if (freightTemplateChild == null) {
                for (FreightTemplateChild templateChild : freightTemplate.getFreightTemplateChildList()) {
                    if (templateChild.getAreaId() != null && templateChild.getAreaId().contains("ALL")) {
                        freightTemplateChild = templateChild;
                        break;
                    }
                }
            }

            // 如果仍然没有匹配的规则，跳过
            if (freightTemplateChild == null) {
                continue;
            }

            // 创建运费规则DTO
            FreightTemplateChildDTO freightTemplateChildDTO = new FreightTemplateChildDTO(freightTemplateChild);
            freightTemplateChildDTO.setPricingMethod(freightTemplate.getPricingMethod());

            // 计算每个SKU的运费
            for (FreightCalculateItem item : items) {
                Double count;
                if (FreightTemplateEnum.NUM.name().equals(freightTemplateChildDTO.getPricingMethod())) {
                    count = (double) item.getQuantity();
                } else {
                    // 按重量计费
                    count = CurrencyUtil.mul(item.getQuantity(), item.getWeight());
                }

                // 计算运费
                Double freight = countFreight(count, freightTemplateChildDTO);
                skuFreightMap.put(item.getSkuId(), freight);
            }
        }

        return skuFreightMap;
    }
}
