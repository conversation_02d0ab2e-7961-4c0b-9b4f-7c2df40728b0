package cn.lili.modules.goods.entity.enums;

import cn.lili.common.exception.ServiceException;
import lombok.Getter;

/**
 * 结算方式
 * <AUTHOR>
 * @date 2025/4/8
 */
@Getter
public enum SettlementModelEnum {
    DEPOSIT_SEND_OUT_GOODS(1, "定金发货"),
    FULL_PAYMENT(2, "全额付款"),
    PERIODIZATION_PAYMENT(3, "分期付款"),
    ;
    private Integer code;
    private String desc;

    SettlementModelEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SettlementModelEnum getEnumByCode(Integer code) {
        for (SettlementModelEnum value : SettlementModelEnum.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        throw new ServiceException("暂无对应结算方式");
    }

}
