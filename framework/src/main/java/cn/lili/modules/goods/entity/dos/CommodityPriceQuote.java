package cn.lili.modules.goods.entity.dos;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 商品报价
 * <AUTHOR>
 * @date 2025/3/5
 */
@Data
@TableName("jrzh_commodity_price_quote")
public class CommodityPriceQuote extends BaseEntity {

    /**
     * 询价记录id
     */
    private Long commodityPriceInquiryId;

    /**
     * 供应商id
     * 供应商的用户id，也就是供应商用户的userId
     */
    private String supplierId;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商报价金额
     */
    private BigDecimal supplierQuotationAmount;
    /**
     * 商品交货数量
     * 如果是期货的时候才有
     */
    private Integer deliveryQuantity;
    /**
     * 交货时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryDate;
    /**
     * 配送方式
     * 1-买方自提 2-供方承运
     */
    private Integer deliveryModel;
    /**
     * 结算方式
     * 1-定金发货 2-全额付款 3-分期付款
     * @see cn.lili.modules.goods.entity.enums.SettlementModelEnum
     */
    private Integer settlementModel;
    /**
     * 供应商商品id
     * 对应li_goods表的id
     */
    private Long commodityListId;
    /**
     * 商品规格id
     * 对应li_goods_sku表的id
     */
    private Long commoditySpecId;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 定金比例
     * 如20% 传入20
     */
    private BigDecimal earnestMoneyRatio;
}
