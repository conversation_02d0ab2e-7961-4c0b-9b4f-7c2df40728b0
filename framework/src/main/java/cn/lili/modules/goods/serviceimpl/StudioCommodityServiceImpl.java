package cn.lili.modules.goods.serviceimpl;

import cn.lili.modules.goods.entity.dos.StudioCommodity;
import cn.lili.modules.goods.mapper.StudioCommodityMapper;
import cn.lili.modules.goods.service.StudioCommodityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 直播间-商品关联业务层实现
 *
 * <AUTHOR>
 * @since 2021/5/17 3:20 下午
 */
@Service
public class StudioCommodityServiceImpl extends ServiceImpl<StudioCommodityMapper, StudioCommodity> implements StudioCommodityService {
}
