package cn.lili.modules.goods.entity.enums;

import cn.lili.common.exception.ServiceException;
import cn.lili.modules.order.cart.entity.enums.DeliveryMethodEnum;
import lombok.Getter;

/**
 * 配送方式枚举
 * <AUTHOR>
 * @date 2025/4/8
 */
@Getter
public enum DeliveryModelEnum {
    BUYER_SELF_PICKUP(1, "买方自提", DeliveryMethodEnum.SELF_PICK_UP),
    SUPPLIER_CARRIAGE(2, "供方承运", DeliveryMethodEnum.LOGISTICS),
    ;
    private Integer code;
    private String desc;
    private DeliveryMethodEnum deliveryMethodEnum;

    DeliveryModelEnum(Integer code, String desc, DeliveryMethodEnum deliveryMethodEnum) {
        this.code = code;
        this.desc = desc;
        this.deliveryMethodEnum = deliveryMethodEnum;
    }

    public static DeliveryModelEnum getEnumByCode(Integer code) {
        for (DeliveryModelEnum value : DeliveryModelEnum.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        throw new ServiceException("暂无对应配送方式");
    }

}
