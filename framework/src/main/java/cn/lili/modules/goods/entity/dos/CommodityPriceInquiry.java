package cn.lili.modules.goods.entity.dos;

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mysql.cj.x.protobuf.MysqlxCrud;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;

/**
 * 商品询价
 * <AUTHOR>
 * @date 2025/3/4
 */
@Data
@TableName("jrzh_commodity_price_inquiry")
public class CommodityPriceInquiry extends BaseEntity {

    /**
     * 商品名称
     */
    private String commodityListName;
    /**
     * 规格型号
     */
    private String commoditySpec;
    /**
     * 融资用户
     * 会员id即li_member表的id
     */
    private String customerId;

    /**
     * 期望购买数量
     */
    private Integer purchaseQuantity;

    /**
     * 交货日期
     * yyyy-MM-dd
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date deliveryDate;

    /**
     * 交货方式
     * 1-现货交易 2-期货交易 3-混合交易
     */
    private Integer tradeModel;

    /**
     * 交货地点
     */
    private String deliveryPlace;
    /**
     * 报价截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date priceQuoteEndDate;

    /**
     * 配送方式
     * 1-买方自提 2-供方承运
     */
    private Integer deliveryModel;
    /**
     * 询价详情描述
     */
    private String detailedSescription;

    /**
     * 附件id
     */
    private String attchIds;
    /**
     * 结算方式
     * 1-定金发货 2-全额付款 3-分期付款
     */
    private Integer settlementModel;
    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 手机号
     */
    private String contactPhone;
    /**
     * 已报价供应商ids也就是店铺id
     */
    private String quoteSupplierStoreIds;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 定金比例
     * 如20% 传入20
     */
    private BigDecimal earnestMoneyRatio;
    /**
     * 订单sn
     */
    private String orderSn;
}
