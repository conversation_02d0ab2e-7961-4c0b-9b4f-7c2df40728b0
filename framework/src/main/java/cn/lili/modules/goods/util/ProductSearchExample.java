package cn.lili.modules.goods.util;

import cn.lili.cache.impl.RedisCache;
import cn.lili.common.utils.StringUtils;
import lombok.RequiredArgsConstructor;
import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.cn.smart.SmartChineseAnalyzer;
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.StringReader;
import java.util.*;

@Component
@RequiredArgsConstructor
public class ProductSearchExample {

    /**
     * key
     */
    private final static String PRODUCT = "product:";
    private final static String PRODUCT_KEY = "productKey:";

    /**
     * hash中的key
     */
    private final static String FIELD_PRODUCT_ID = "productId";
    private final static String FIELD_PRODUCT_NAME = "productName";

    private final Analyzer analyzer = new SmartChineseAnalyzer();
    private final RedisCache bladeRedis;

    public void addProduct(String productId, String productName) {
        // 将商品信息存储到Redis中
        bladeRedis.hSet(PRODUCT + productId, FIELD_PRODUCT_ID, productId);
        bladeRedis.hSet(PRODUCT + productId, FIELD_PRODUCT_NAME, productName);
        // 对产品名称进行分词
        List<String> words = segment(productName);

        // 将分词结果存储到Redis中
        for (String word : words) {
            bladeRedis.sAdd(PRODUCT_KEY + word.toLowerCase(), productId);
        }
    }

    public void updateProduct(String productId, String newProductName) {
        // 获取旧的产品名称
        String oldProductName = (String) bladeRedis.hGet(PRODUCT + productId, FIELD_PRODUCT_NAME);
        if (oldProductName == null) {
            return;
        }
        // 删除旧的分词索引
        List<String> oldWords = segment(oldProductName);
        for (String word : oldWords) {
            bladeRedis.sRem(PRODUCT_KEY + word.toLowerCase(), productId);
        }

        // 更新产品名称
        bladeRedis.hSet(PRODUCT + productId, FIELD_PRODUCT_NAME, newProductName);

        // 对新的产品名称进行分词
        List<String> newWords = segment(newProductName);

        // 将新的分词结果存储到Redis中，添加前缀 "word:"
        for (String word : newWords) {
            bladeRedis.sAdd(PRODUCT_KEY + word.toLowerCase(), productId);
        }
    }

    public void remove(String productId) {
        // 获取旧的产品名称
        String oldProductName = (String) bladeRedis.hGet(PRODUCT + productId, FIELD_PRODUCT_NAME);
        if (oldProductName == null) {
            return;
        }
        // 删除旧的分词索引
        List<String> oldWords = segment(oldProductName);
        for (String word : oldWords) {
            bladeRedis.sRem(PRODUCT_KEY + word.toLowerCase(), productId);
        }
        // 删除产品名称
        bladeRedis.hDel(PRODUCT + productId, FIELD_PRODUCT_NAME);
    }

    private List<String> segment(String text) {
        List<String> words = new ArrayList<>();
        try (TokenStream tokenStream = analyzer.tokenStream("content", new StringReader(text))) {
            CharTermAttribute charTermAttr = tokenStream.addAttribute(CharTermAttribute.class);
            tokenStream.reset();
            while (tokenStream.incrementToken()) {
                words.add(charTermAttr.toString());
            }
            tokenStream.end();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return words;
    }

    public List<Map<String, String>> searchProductsByName(String queryText) {
        // 对查询文本进行分词
        List<String> queryWords = segment(queryText);

        if (queryWords.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取第一个词对应的商品ID集合
        Set<String> productIds = bladeRedis.sMembers(PRODUCT_KEY + queryWords.get(0).toLowerCase());

        // 逐步交集其他词对应的商品ID集合
        for (int i = 1; i < queryWords.size(); i++) {
            productIds.retainAll(bladeRedis.sMembers(PRODUCT_KEY + queryWords.get(i).toLowerCase()));
        }

        // 根据商品ID获取商品详细信息
        List<Map<String, String>> products = new ArrayList<>();
        for (String productId : productIds) {
//            Map<String, String> productInfo = jedis.hgetAll("product:" + productId);
            Map<String, String> productInfo = bladeRedis.hGetAll(PRODUCT + productId);
            products.add(productInfo);
        }

        return products;
    }

    public List<String> searchProductIdsByName(String queryText) {
        if (StringUtils.isEmpty(queryText)) {
            return Collections.emptyList();
        }
        // 对查询文本进行分词
        List<String> queryWords = segment(queryText);

        if (queryWords.isEmpty()) {
            return Collections.emptyList();
        }

        // 获取第一个词对应的商品ID集合
        List<String> productIds = new ArrayList<>();
        // 逐步交集其他词对应的商品ID集合
        for (int i = 0; i < queryWords.size(); i++) {
            productIds.addAll(bladeRedis.sMembers(PRODUCT_KEY + queryWords.get(i).toLowerCase()));
        }

        return productIds;
    }

}



