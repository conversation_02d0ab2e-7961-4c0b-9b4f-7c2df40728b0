package cn.lili.modules.connect.config;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//



public class SsqCreateSignImg {
    private Integer type;
    private String accountId;
    private Integer customerType;
    private String imageData;
    private String imageName;

    public SsqCreateSignImg() {
    }

    public Integer getType() {
        return this.type;
    }

    public String getAccountId() {
        return this.accountId;
    }

    public Integer getCustomerType() {
        return this.customerType;
    }

    public String getImageData() {
        return this.imageData;
    }

    public String getImageName() {
        return this.imageName;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public void setCustomerType(Integer customerType) {
        this.customerType = customerType;
    }

    public void setImageData(String imageData) {
        this.imageData = imageData;
    }

    public void setImageName(String imageName) {
        this.imageName = imageName;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SsqCreateSignImg)) {
            return false;
        } else {
            SsqCreateSignImg other = (SsqCreateSignImg)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label71: {
                    Object this$type = this.getType();
                    Object other$type = other.getType();
                    if (this$type == null) {
                        if (other$type == null) {
                            break label71;
                        }
                    } else if (this$type.equals(other$type)) {
                        break label71;
                    }

                    return false;
                }

                Object this$customerType = this.getCustomerType();
                Object other$customerType = other.getCustomerType();
                if (this$customerType == null) {
                    if (other$customerType != null) {
                        return false;
                    }
                } else if (!this$customerType.equals(other$customerType)) {
                    return false;
                }

                label57: {
                    Object this$accountId = this.getAccountId();
                    Object other$accountId = other.getAccountId();
                    if (this$accountId == null) {
                        if (other$accountId == null) {
                            break label57;
                        }
                    } else if (this$accountId.equals(other$accountId)) {
                        break label57;
                    }

                    return false;
                }

                Object this$imageData = this.getImageData();
                Object other$imageData = other.getImageData();
                if (this$imageData == null) {
                    if (other$imageData != null) {
                        return false;
                    }
                } else if (!this$imageData.equals(other$imageData)) {
                    return false;
                }

                Object this$imageName = this.getImageName();
                Object other$imageName = other.getImageName();
                if (this$imageName == null) {
                    if (other$imageName == null) {
                        return true;
                    }
                } else if (this$imageName.equals(other$imageName)) {
                    return true;
                }

                return false;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SsqCreateSignImg;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        Object $customerType = this.getCustomerType();
        result = result * 59 + ($customerType == null ? 43 : $customerType.hashCode());
        Object $accountId = this.getAccountId();
        result = result * 59 + ($accountId == null ? 43 : $accountId.hashCode());
        Object $imageData = this.getImageData();
        result = result * 59 + ($imageData == null ? 43 : $imageData.hashCode());
        Object $imageName = this.getImageName();
        result = result * 59 + ($imageName == null ? 43 : $imageName.hashCode());
        return result;
    }

    public String toString() {
        return "SsqCreateSignImg(type=" + this.getType() + ", accountId=" + this.getAccountId() + ", customerType=" + this.getCustomerType() + ", imageData=" + this.getImageData() + ", imageName=" + this.getImageName() + ")";
    }
}
