package cn.lili.modules.connect.config;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//



import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

public class SSqCredential implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("用户证件号")
    private String identity;
    @ApiModelProperty("用户证件类型")
    private String identityType;
    @ApiModelProperty("联系手机")
    private String contactMobile;
    @ApiModelProperty("联系邮箱")
    private String contactMail;
    @ApiModelProperty("省份")
    private String province;
    @ApiModelProperty("城市")
    private String city;
    @ApiModelProperty("地址")
    private String address;

    public static SSqCredentialBuilder builder() {
        return new SSqCredentialBuilder();
    }

    public String getIdentity() {
        return this.identity;
    }

    public String getIdentityType() {
        return this.identityType;
    }

    public String getContactMobile() {
        return this.contactMobile;
    }

    public String getContactMail() {
        return this.contactMail;
    }

    public String getProvince() {
        return this.province;
    }

    public String getCity() {
        return this.city;
    }

    public String getAddress() {
        return this.address;
    }

    public void setIdentity(String identity) {
        this.identity = identity;
    }

    public void setIdentityType(String identityType) {
        this.identityType = identityType;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public void setContactMail(String contactMail) {
        this.contactMail = contactMail;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SSqCredential)) {
            return false;
        } else {
            SSqCredential other = (SSqCredential)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label95: {
                    Object this$identity = this.getIdentity();
                    Object other$identity = other.getIdentity();
                    if (this$identity == null) {
                        if (other$identity == null) {
                            break label95;
                        }
                    } else if (this$identity.equals(other$identity)) {
                        break label95;
                    }

                    return false;
                }

                Object this$identityType = this.getIdentityType();
                Object other$identityType = other.getIdentityType();
                if (this$identityType == null) {
                    if (other$identityType != null) {
                        return false;
                    }
                } else if (!this$identityType.equals(other$identityType)) {
                    return false;
                }

                Object this$contactMobile = this.getContactMobile();
                Object other$contactMobile = other.getContactMobile();
                if (this$contactMobile == null) {
                    if (other$contactMobile != null) {
                        return false;
                    }
                } else if (!this$contactMobile.equals(other$contactMobile)) {
                    return false;
                }

                label74: {
                    Object this$contactMail = this.getContactMail();
                    Object other$contactMail = other.getContactMail();
                    if (this$contactMail == null) {
                        if (other$contactMail == null) {
                            break label74;
                        }
                    } else if (this$contactMail.equals(other$contactMail)) {
                        break label74;
                    }

                    return false;
                }

                label67: {
                    Object this$province = this.getProvince();
                    Object other$province = other.getProvince();
                    if (this$province == null) {
                        if (other$province == null) {
                            break label67;
                        }
                    } else if (this$province.equals(other$province)) {
                        break label67;
                    }

                    return false;
                }

                Object this$city = this.getCity();
                Object other$city = other.getCity();
                if (this$city == null) {
                    if (other$city != null) {
                        return false;
                    }
                } else if (!this$city.equals(other$city)) {
                    return false;
                }

                Object this$address = this.getAddress();
                Object other$address = other.getAddress();
                if (this$address == null) {
                    if (other$address != null) {
                        return false;
                    }
                } else if (!this$address.equals(other$address)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SSqCredential;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $identity = this.getIdentity();
        result = result * 59 + ($identity == null ? 43 : $identity.hashCode());
        Object $identityType = this.getIdentityType();
        result = result * 59 + ($identityType == null ? 43 : $identityType.hashCode());
        Object $contactMobile = this.getContactMobile();
        result = result * 59 + ($contactMobile == null ? 43 : $contactMobile.hashCode());
        Object $contactMail = this.getContactMail();
        result = result * 59 + ($contactMail == null ? 43 : $contactMail.hashCode());
        Object $province = this.getProvince();
        result = result * 59 + ($province == null ? 43 : $province.hashCode());
        Object $city = this.getCity();
        result = result * 59 + ($city == null ? 43 : $city.hashCode());
        Object $address = this.getAddress();
        result = result * 59 + ($address == null ? 43 : $address.hashCode());
        return result;
    }

    public String toString() {
        return "SSqCredential(identity=" + this.getIdentity() + ", identityType=" + this.getIdentityType() + ", contactMobile=" + this.getContactMobile() + ", contactMail=" + this.getContactMail() + ", province=" + this.getProvince() + ", city=" + this.getCity() + ", address=" + this.getAddress() + ")";
    }

    public SSqCredential(String identity, String identityType, String contactMobile, String contactMail, String province, String city, String address) {
        this.identity = identity;
        this.identityType = identityType;
        this.contactMobile = contactMobile;
        this.contactMail = contactMail;
        this.province = province;
        this.city = city;
        this.address = address;
    }

    public SSqCredential() {
    }

    public static class SSqCredentialBuilder {
        private String identity;
        private String identityType;
        private String contactMobile;
        private String contactMail;
        private String province;
        private String city;
        private String address;

        SSqCredentialBuilder() {
        }

        public SSqCredentialBuilder identity(String identity) {
            this.identity = identity;
            return this;
        }

        public SSqCredentialBuilder identityType(String identityType) {
            this.identityType = identityType;
            return this;
        }

        public SSqCredentialBuilder contactMobile(String contactMobile) {
            this.contactMobile = contactMobile;
            return this;
        }

        public SSqCredentialBuilder contactMail(String contactMail) {
            this.contactMail = contactMail;
            return this;
        }

        public SSqCredentialBuilder province(String province) {
            this.province = province;
            return this;
        }

        public SSqCredentialBuilder city(String city) {
            this.city = city;
            return this;
        }

        public SSqCredentialBuilder address(String address) {
            this.address = address;
            return this;
        }

        public SSqCredential build() {
            return new SSqCredential(this.identity, this.identityType, this.contactMobile, this.contactMail, this.province, this.city, this.address);
        }

        public String toString() {
            return "SSqCredential.SSqCredentialBuilder(identity=" + this.identity + ", identityType=" + this.identityType + ", contactMobile=" + this.contactMobile + ", contactMail=" + this.contactMail + ", province=" + this.province + ", city=" + this.city + ", address=" + this.address + ")";
        }
    }
}
