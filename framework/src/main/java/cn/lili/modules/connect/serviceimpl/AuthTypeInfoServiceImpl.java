package cn.lili.modules.connect.serviceimpl;


import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import cn.lili.modules.connect.entity.AuthTypeInfo;
import cn.lili.modules.connect.mapper.AuthTypeInfoMapper;
import cn.lili.modules.connect.service.IAuthTypeInfoService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 认证方式信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-07
 */
@Service
public class AuthTypeInfoServiceImpl extends ServiceImpl<AuthTypeInfoMapper, AuthTypeInfo> implements IAuthTypeInfoService {
//
//    @Override
//    public IPage<AuthTypeInfoVO> selectAuthTypeInfoPage(IPage<AuthTypeInfoVO> page, AuthTypeInfoVO authTypeInfo) {
//        return page.setRecords(baseMapper.selectAuthTypeInfoPage(page, authTypeInfo));
//    }
//
    @Override
    public <T> T getResponse(Long customerId, Integer authType, String apiSupplier, Class<T> type) {
        AuthTypeInfo authTypeInfo = getOne(baseWrapper(customerId, authType, apiSupplier).last("limit 1"));
        if (ObjectUtil.isEmpty(authTypeInfo)) {
            return null;
        }
        return JSONUtil.toBean(authTypeInfo.getResponse(), type);
    }

    @Override
    public void updateStatus(Long customerId, Integer authType, String apiSupplier, Integer status) {
        update(Wrappers.<AuthTypeInfo>lambdaUpdate().eq(AuthTypeInfo::getCustomerId, customerId)
                .eq(AuthTypeInfo::getAuthType, authType).eq(AuthTypeInfo::getApiSupplier, apiSupplier)
                .orderByDesc(AuthTypeInfo::getCreateTime).set(AuthTypeInfo::getStatus, status));
    }

    @Override
    public AuthTypeInfo getAuthType(Long customerId, Integer authType, String apiSupplier) {
        LambdaQueryWrapper<AuthTypeInfo> wrapper = baseWrapper(customerId, authType, apiSupplier);
        return getOne(wrapper.last("limit 1"));
    }
//
//    @Override
//    public AuthTypeInfo getAuthType(Long customerId, List<Integer> authType, String apiSupplier) {
//        return getOne(Wrappers.<AuthTypeInfo>lambdaQuery().eq(AuthTypeInfo::getCustomerId, customerId)
//                .in(AuthTypeInfo::getAuthType, authType).eq(AuthTypeInfo::getApiSupplier, apiSupplier)
//                .orderByDesc(AuthTypeInfo::getCreateTime).last("limit 1"));
//    }
//
//    @Override
//    public AuthTypeInfo getByOrderNo(String orderNo) {
//        return getOne(Wrappers.<AuthTypeInfo>lambdaQuery().eq(AuthTypeInfo::getOrderNo, orderNo)
//                .orderByDesc(AuthTypeInfo::getCreateTime)
//                .last("limit 1"));
//    }
//
    private LambdaQueryWrapper<AuthTypeInfo> baseWrapper(Long customerId, Integer authType, String apiSupplier) {
        LambdaQueryWrapper<AuthTypeInfo> baseWrapper = Wrappers.<AuthTypeInfo>lambdaQuery().eq(AuthTypeInfo::getCustomerId, customerId)
                .eq(AuthTypeInfo::getAuthType, authType).eq(AuthTypeInfo::getApiSupplier, apiSupplier)
                .orderByDesc(AuthTypeInfo::getCreateTime);
        return baseWrapper;
    }
}

