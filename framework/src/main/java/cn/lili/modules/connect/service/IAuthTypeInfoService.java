package cn.lili.modules.connect.service;


import cn.lili.modules.connect.entity.AuthTypeInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 认证方式信息表 服务类
 *
 * <AUTHOR>
 * @since 2022-08-07
 */
public interface IAuthTypeInfoService extends IService<AuthTypeInfo> {
//
//    /**
//     * 自定义分页
//     *
//     * @param page
//     * @param authTypeInfo
//     * @return
//     */
//    IPage<AuthTypeInfoVO> selectAuthTypeInfoPage(IPage<AuthTypeInfoVO> page, AuthTypeInfoVO authTypeInfo);
//
    /**
     * 获取响应体
     *
     * @param customerId  客户编号
     * @param authType    认证方式
     * @param apiSupplier api供应商
     * @param type        返回类型
     * @return
     */
    <T> T getResponse(Long customerId, Integer authType, String apiSupplier, Class<T> type);

    /**
     * 更新实名方式状态
     *
     * @param customerId  客户编号
     * @param authType    认证方式
     * @param apiSupplier api供应商
     * @param status      实名状态
     */
    void updateStatus(Long customerId, Integer authType, String apiSupplier, Integer status);

    /**
     * 获取实名方式信息
     *
     * @param customerId  客户编号
     * @param authType    认证方式
     * @param apiSupplier api供应商
     * @return
     */
    AuthTypeInfo getAuthType(Long customerId, Integer authType, String apiSupplier);
//
//    /**
//     * 获取实名方式信息
//     *
//     * @param customerId  客户编号
//     * @param authType    认证方式
//     * @param apiSupplier api供应商
//     * @return
//     */
//    AuthTypeInfo getAuthType(Long customerId, List<Integer> authType, String apiSupplier);
//
//    AuthTypeInfo getByOrderNo(String orderNo);
}
