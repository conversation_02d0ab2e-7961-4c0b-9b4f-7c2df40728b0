/*
 *      Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.connect.mapper;

import cn.lili.modules.connect.entity.CustomerPersonInfo;
import cn.lili.modules.connect.entity.vo.CustomerPersonInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 个人客户信息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
public interface CustomerPersonInfoMapper extends BaseMapper<CustomerPersonInfo> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param customerPersonInfo
	 * @return
	 */
	List<CustomerPersonInfoVO> selectCustomerPersonInfoPage(IPage page, CustomerPersonInfoVO customerPersonInfo);

	@Delete("delete from jrzh_customer_person_info where  customer_id=#{userId}")
	void deleteReal(Long userId);
}
