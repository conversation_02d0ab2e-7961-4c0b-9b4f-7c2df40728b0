package cn.lili.modules.connect.entity.dto;

import cn.lili.modules.connect.entity.CustomerInfo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "CustomerAndPersonInfoDTO对象", description = "注册实名参数DTO")
public class CustomerAndPersonInfoDTO extends CustomerInfo {

    /**
     * 客户性别
     */
    private Integer sex;

    /**
     * 客户住址
     */
    private String address;

    /**
     * 民族
     */
    private String nation;

    /**
     * 生日
     */
    private String birth;

    /**
     * 签发单位
     */
    private String issue;

    /**
     * 有效期起
     */
    private String validFrom;

    /**
     * 有效期止
     */
    private String validTo;



}
