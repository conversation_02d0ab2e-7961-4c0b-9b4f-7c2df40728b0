package cn.lili.modules.connect.config;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//



public class SkyEysConfig {
    private String token;
    private String host;

    public static SkyEysConfigBuilder builder() {
        return new SkyEysConfigBuilder();
    }

    public String getToken() {
        return this.token;
    }

    public String getHost() {
        return this.host;
    }

    public SkyEysConfig setToken(String token) {
        this.token = token;
        return this;
    }

    public SkyEysConfig setHost(String host) {
        this.host = host;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SkyEysConfig)) {
            return false;
        } else {
            SkyEysConfig other = (SkyEysConfig)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$token = this.getToken();
                Object other$token = other.getToken();
                if (this$token == null) {
                    if (other$token != null) {
                        return false;
                    }
                } else if (!this$token.equals(other$token)) {
                    return false;
                }

                Object this$host = this.getHost();
                Object other$host = other.getHost();
                if (this$host == null) {
                    if (other$host != null) {
                        return false;
                    }
                } else if (!this$host.equals(other$host)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SkyEysConfig;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $token = this.getToken();
        result = result * 59 + ($token == null ? 43 : $token.hashCode());
        Object $host = this.getHost();
        result = result * 59 + ($host == null ? 43 : $host.hashCode());
        return result;
    }

    public String toString() {
        return "SkyEysConfig(token=" + this.getToken() + ", host=" + this.getHost() + ")";
    }

    public SkyEysConfig(String token, String host) {
        this.token = token;
        this.host = host;
    }

    public SkyEysConfig() {
    }

    public static class SkyEysConfigBuilder {
        private String token;
        private String host;

        SkyEysConfigBuilder() {
        }

        public SkyEysConfigBuilder token(String token) {
            this.token = token;
            return this;
        }

        public SkyEysConfigBuilder host(String host) {
            this.host = host;
            return this;
        }

        public SkyEysConfig build() {
            return new SkyEysConfig(this.token, this.host);
        }

        public String toString() {
            return "SkyEysConfig.SkyEysConfigBuilder(token=" + this.token + ", host=" + this.host + ")";
        }
    }
}
