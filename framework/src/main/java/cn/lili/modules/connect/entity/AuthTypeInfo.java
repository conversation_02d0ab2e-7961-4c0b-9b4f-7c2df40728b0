package cn.lili.modules.connect.entity;


import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

/**
 * 认证方式信息表实体类
 *
 * <AUTHOR>
 * @since 2022-08-07
 */
@Data
@TableName("jrzh_auth_type_info")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AuthTypeInfo对象", description = "认证方式信息表")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthTypeInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    private Integer status;

    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;
    /**
     * api供应商
     */
    @ApiModelProperty(value = "api供应商")
    private String apiSupplier;
    /**
     * 认证方式
     */
    @ApiModelProperty(value = "认证方式 1手机短信 2面部认证3银行卡认证")
    private Integer authType;
    /**
     * 请求
     */
    @ApiModelProperty(value = "认证信息")
    private String request;
    /**
     * 响应
     */
    private String response;
    /**
     * 认证类型 1个人 2企业
     */
    @ApiModelProperty(value = "认证类型 1个人 2企业")
    private Integer userType;
    /**
     * 订单唯一标识
     */
    private String orderNo;
}
