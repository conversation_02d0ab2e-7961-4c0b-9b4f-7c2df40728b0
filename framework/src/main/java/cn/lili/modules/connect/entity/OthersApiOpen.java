package cn.lili.modules.connect.entity;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//



import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@TableName("jrzh_others_api_open")
@ApiModel(
        value = "OthersApiOpen对象",
        description = "接口参数表"
)
public class OthersApiOpen  {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("接口名称")
    private String openApiName;
    @ApiModelProperty("接口请求地址")
    private String openApiUrl;
    @ApiModelProperty("有效次数 -1则无限使用")
    private Integer validCount;
    @ApiModelProperty("接口图片")
    private String logo;
    @ApiModelProperty("接口id")
    private Long apiId;
    @ApiModelProperty("备注")
    private String remark;

    public OthersApiOpen() {
    }

    public String getOpenApiName() {
        return this.openApiName;
    }

    public String getOpenApiUrl() {
        return this.openApiUrl;
    }

    public Integer getValidCount() {
        return this.validCount;
    }

    public String getLogo() {
        return this.logo;
    }

    public Long getApiId() {
        return this.apiId;
    }

    public String getRemark() {
        return this.remark;
    }

    public void setOpenApiName(String openApiName) {
        this.openApiName = openApiName;
    }

    public void setOpenApiUrl(String openApiUrl) {
        this.openApiUrl = openApiUrl;
    }

    public void setValidCount(Integer validCount) {
        this.validCount = validCount;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public void setApiId(Long apiId) {
        this.apiId = apiId;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String toString() {
        return "OthersApiOpen(openApiName=" + this.getOpenApiName() + ", openApiUrl=" + this.getOpenApiUrl() + ", validCount=" + this.getValidCount() + ", logo=" + this.getLogo() + ", apiId=" + this.getApiId() + ", remark=" + this.getRemark() + ")";
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof OthersApiOpen)) {
            return false;
        } else {
            OthersApiOpen other = (OthersApiOpen)o;
            if (!other.canEqual(this)) {
                return false;
            } else if (!super.equals(o)) {
                return false;
            } else {
                Object this$validCount = this.getValidCount();
                Object other$validCount = other.getValidCount();
                if (this$validCount == null) {
                    if (other$validCount != null) {
                        return false;
                    }
                } else if (!this$validCount.equals(other$validCount)) {
                    return false;
                }

                Object this$apiId = this.getApiId();
                Object other$apiId = other.getApiId();
                if (this$apiId == null) {
                    if (other$apiId != null) {
                        return false;
                    }
                } else if (!this$apiId.equals(other$apiId)) {
                    return false;
                }

                label71: {
                    Object this$openApiName = this.getOpenApiName();
                    Object other$openApiName = other.getOpenApiName();
                    if (this$openApiName == null) {
                        if (other$openApiName == null) {
                            break label71;
                        }
                    } else if (this$openApiName.equals(other$openApiName)) {
                        break label71;
                    }

                    return false;
                }

                label64: {
                    Object this$openApiUrl = this.getOpenApiUrl();
                    Object other$openApiUrl = other.getOpenApiUrl();
                    if (this$openApiUrl == null) {
                        if (other$openApiUrl == null) {
                            break label64;
                        }
                    } else if (this$openApiUrl.equals(other$openApiUrl)) {
                        break label64;
                    }

                    return false;
                }

                Object this$logo = this.getLogo();
                Object other$logo = other.getLogo();
                if (this$logo == null) {
                    if (other$logo != null) {
                        return false;
                    }
                } else if (!this$logo.equals(other$logo)) {
                    return false;
                }

                Object this$remark = this.getRemark();
                Object other$remark = other.getRemark();
                if (this$remark == null) {
                    if (other$remark != null) {
                        return false;
                    }
                } else if (!this$remark.equals(other$remark)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof OthersApiOpen;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = super.hashCode();
        Object $validCount = this.getValidCount();
        result = result * 59 + ($validCount == null ? 43 : $validCount.hashCode());
        Object $apiId = this.getApiId();
        result = result * 59 + ($apiId == null ? 43 : $apiId.hashCode());
        Object $openApiName = this.getOpenApiName();
        result = result * 59 + ($openApiName == null ? 43 : $openApiName.hashCode());
        Object $openApiUrl = this.getOpenApiUrl();
        result = result * 59 + ($openApiUrl == null ? 43 : $openApiUrl.hashCode());
        Object $logo = this.getLogo();
        result = result * 59 + ($logo == null ? 43 : $logo.hashCode());
        Object $remark = this.getRemark();
        result = result * 59 + ($remark == null ? 43 : $remark.hashCode());
        return result;
    }
}
