package cn.lili.modules.connect.entity.vo;

/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */


import cn.lili.modules.connect.entity.CustomerInfo;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 企业信息名称视图实体类
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Data
@ApiModel(value = "CustomerInfoVO对象", description = "企业信息名称")
public class CustomerInfoVO extends CustomerInfo {
    private static final long serialVersionUID = 1L;
    /**
     * 法人生日
     */
    private  String  corporationBirthDay;
    /**
     * 经手人生日
     */
    private  String operatorBirthDay;

    /***
     * 统一社会信用代码
     */
    private String creditCode;
    /**
     * 附件集合
     */
    Map<Long, String> attachMap;
    /**
     * 账户名
     */
    private String account;
    /***
     * 成立日期
     */
    private String estiblishTime;


}
