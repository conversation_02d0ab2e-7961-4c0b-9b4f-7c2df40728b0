package cn.lili.modules.connect.entity.vo;

/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */


import cn.lili.modules.connect.entity.CustomerPersonInfo;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * 个人客户信息表视图实体类
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CustomerPersonInfoVO对象", description = "个人客户信息表")
public class CustomerPersonInfoVO extends CustomerPersonInfo {
    private static final long serialVersionUID = 1L;

    /*
     生日
     */
    private String birthday;

    /*
     *实名状态
     */
    private Integer authStatus;
    /**
     * 手机号
     */
    private String phone;

    /**
     * 账号
     */
    private String account;
    /**
     * 附件集合
     */
    Map<Long, String> attachMap;
}
