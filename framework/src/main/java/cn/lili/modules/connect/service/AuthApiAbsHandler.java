package cn.lili.modules.connect.service;


import cn.hutool.extra.spring.SpringUtil;
import cn.lili.modules.jrzh_other.core.service.IOthersApiService;
import cn.lili.modules.jrzh_other.core_api.constant.OtherApiTypeEnum;
import cn.lili.modules.jrzh_other.core_api.dto.ApiParamDTO;

/**
 * 实名接口抽象类
 *
 * <AUTHOR>
 */
public abstract class AuthApiAbsHandler implements AuthApiHandler {
	final static String CODE = OtherApiTypeEnum.AUTH_API.getCode();

	/**
	 * 获取Api链接参数
	 *
	 * @return
	 */
	public ApiParamDTO getApiParams() {
		return SpringUtil.getBean(IOthersApiService.class).getSingleParamByTypeCode(CODE);
	}

}
