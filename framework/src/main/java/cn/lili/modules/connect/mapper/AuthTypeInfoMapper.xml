<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.authapi.mapper.AuthTypeInfoMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="authTypeInfoResultMap" type="org.springblade.customer.authapi.entity.AuthTypeInfo">
		<result column="id" property="id"/>
		<result column="tenant_id" property="tenantId"/>
		<result column="create_dept" property="createDept"/>
		<result column="status" property="status"/>
		<result column="create_time" property="createTime"/>
		<result column="update_time" property="updateTime"/>
		<result column="create_user" property="createUser"/>
		<result column="update_user" property="updateUser"/>
		<result column="is_deleted" property="isDeleted"/>
		<result column="customer_id" property="customerId"/>
		<result column="api_supplier" property="apiSupplier"/>
		<result column="auth_type" property="authType"/>
		<result column="request" property="request"/>
		<result column="response" property="response"/>
		<result column="user_type" property="userType"/>
	</resultMap>


	<select id="selectAuthTypeInfoPage" resultMap="authTypeInfoResultMap">
		select *
		from jrzh_auth_type_info
		where is_deleted = 0
	</select>

</mapper>
