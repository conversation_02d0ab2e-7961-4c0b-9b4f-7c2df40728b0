/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.connect.service;

import cn.lili.modules.connect.entity.CustomerPersonInfo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 个人客户信息表 服务类
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
public interface ICustomerPersonInfoService extends IService<CustomerPersonInfo> {
//
//    /**
//     * 自定义分页
//     *
//     * @param page
//     * @param customerPersonInfo
//     * @return
//     */
//    IPage<CustomerPersonInfoVO> selectCustomerPersonInfoPage(IPage<CustomerPersonInfoVO> page, CustomerPersonInfoVO customerPersonInfo);
//
//    /**
//     * 查找个人信息
//     *
//     * @param customerId 客户id
//     * @return
//     */
//    CustomerPersonInfoVO getPersonInfoByCustomerId(Long customerId);
//
//
//    /**
//     * 根据custonerId  获取个人脱敏信息
//     */
//    CustomerPersonInfoVO selectVagueInfoByCustomerId(Long customerId);
//
    /**
     * 根据身份证客户id获取实名中或已实名的信息
     *
     * @param idCard 身份证
     * @return
     */
    CustomerPersonInfo isAuthOrAuthing(String idCard);

    /**
     * 根据用户id获取正在实名或已实名信息
     *
     * @param customerId
     * @return
     */
    CustomerPersonInfo getAuthingOrAuthedByCustomerId(Long customerId);
//
//    /**
//     * 主动查询个人实名情况
//     *
//     * @return
//     */
//    PersonStatusAuthDTO queryPersonAuth();
//
//    /**
//     * 更新个人实名
//     *
//     * @param personInfo
//     * @return
//     */
//    Boolean updatePersonInfoAndUserInfo(CustomerPersonInfo personInfo);
//
//    /**
//     * 主动查询实名情况
//     *
//     * @param userId 用户id
//     * @return
//     */
//    R queryPersonAuthByUserId(Long userId);
//
//    /**
//     * 个人绑定企业
//     *
//     * @param entName    企业名称
//     * @param dev        客户系统中的账号或用以标识账号的ID
//     * @param ssqAccount 待绑定的上上签账号
//     * @param returnUrl  单点登录后重新向地址
//     * @return
//     */
//    String personBindCompany(String entName, String dev, String ssqAccount, String returnUrl);
//
//    /**
//     * 根据名称和联系方式进行查找
//     *
//     * @param name
//     * @param phone
//     * @return
//     */
//    CustomerPersonInfo getByNameAndPhone(String name, String phone);
//
    /**
     * 根据客户id查询实名信息
     */
    CustomerPersonInfo getByCustomerId(Long customerId);
//
//    /**
//     * 根据手机号查询实名信息
//     *
//     * @param phone
//     * @return
//     */
//    CustomerPersonInfo getByPhone(String phone);
//
//    void deleteReal(Long userId);
//
}
