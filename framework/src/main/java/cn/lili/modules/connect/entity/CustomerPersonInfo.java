package cn.lili.modules.connect.entity;

/*
 *      Copyright (c) 2018-2028, Chill Zhuang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */

import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 个人客户信息表实体类
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Data
@TableName("jrzh_customer_person_info")
@ApiModel(value = "CustomerPersonInfo对象", description = "个人客户信息表")
public class CustomerPersonInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 实名认证状态（0 ：未认证 1： 认证未通过 2：认证通过）
     */
    @ApiModelProperty(value = "实名认证状态（0 ：未认证 1： 认证未通过 2：认证通过）")
    private Integer authStatus;
    /**
     * 账号
     */
    @ApiModelProperty(value = "账号")
    private String account;
    /**
     * 姓名
     */
    @NotEmpty
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 身份证号
     */
    @NotEmpty
    @ApiModelProperty(value = "身份证号")
    private String identity;
    /**
     * 身份证正面文件id
     */
    @ApiModelProperty(value = "身份证正面文件id")
    @TableField("identity_facefile_attachId")
    private String identityFacefileAttachid;
    /**
     * 身份证反面文件id
     */
    @ApiModelProperty(value = "身份证反面文件id")
    @TableField("identity_backfile_attachId")
    private String identityBackfileAttachid;
    /**
     * 采集人脸视频存储id
     */
    @ApiModelProperty(value = "采集人脸视频存储id")
    @TableField("collected_face_video_attachId")
    private String collectedFaceVideoAttachid;
    /**
     * 银行卡号
     */
    @ApiModelProperty(value = "银行卡号")
    private String bankCardNo;
    /**
     * 银行预留手机
     */
    @ApiModelProperty(value = "银行预留手机")
    private String bankPhone;
    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户id")
    private Long customerId;

    /***
     * 客户性别
     */
    private Integer sex;

    /**
     * 客户住址
     */
    private String address;

    /**
     * 民族
     */
    private String nation;
    /**
     * 客户证件有效期
     */
    private String identityEffective;
    /**
     * 生日
     */
    private String birth;
    /**
     * 签发单位
     */
    private String issue;
    /**
     * 有效期起
     */
    private String validFrom;
    /**
     * 有效期止
     */
    private String validTo;
    /**
     * 实名方式 1手机短信 2面部认证3银行卡认证
     */
    private Integer authType;
    /**
     * 实名进度
     */
    private Integer process;
    /**
     * 接口供应商
     */
    private String apiSupplier;
    /**
     * 异步任务编号
     * 异步申请证书队列中的任务编号，在24小时内可用于查询异步申请状态，taskId过24小时后就失效
     */
    private String taskId;
}
