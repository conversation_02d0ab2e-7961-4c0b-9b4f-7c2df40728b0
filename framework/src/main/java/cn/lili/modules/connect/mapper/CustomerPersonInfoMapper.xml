<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.customer.mapper.CustomerPersonInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="customerPersonInfoResultMap" type="org.springblade.customer.entity.CustomerPersonInfo">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="auth_status" property="authStatus"/>
        <result column="account" property="account"/>
        <result column="name" property="name"/>
        <result column="identity" property="identity"/>
        <result column="authinfo_types" property="authinfoTypes"/>
        <result column="identity_facefile_attachId" property="identityFacefileAttachid"/>
        <result column="identity_backfile_attachId" property="identityBackfileAttachid"/>
        <result column="collected_face_video_attachId" property="collectedFaceVideoAttachid"/>
        <result column="bank_card_no" property="bankCardNo"/>
        <result column="bank_phone" property="bankPhone"/>
        <result column="customer_id" property="customerId"/>
        <result column="sex" property="sex"/>
        <result column="address" property="address"/>
        <result column="nation" property="nation"/>
        <result column="identity_effective" property="identityEffective"/>
    </resultMap>


    <select id="selectCustomerPersonInfoPage" resultMap="customerPersonInfoResultMap">
        select * from jrzh_customer_person_info where is_deleted = 0
    </select>

</mapper>
