package cn.lili.modules.connect.config;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//



import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;

public class SSq<PERSON><PERSON>Auth implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("用户帐号")
    private String account;
    @ApiModelProperty("用户名称")
    private String name;
    @ApiModelProperty("用户类型")
    private String userType;
    @ApiModelProperty("用户邮箱")
    private String mail;
    @ApiModelProperty("用户手机号")
    private String mobile;
    @ApiModelProperty("用户证件信息对象")
    private SSqCredential credential;
    @ApiModelProperty("是否申请证书")
    private String applyCert;

    public static SSqPersonAuthBuilder builder() {
        return new SSqPersonAuthBuilder();
    }

    public String getAccount() {
        return this.account;
    }

    public String getName() {
        return this.name;
    }

    public String getUserType() {
        return this.userType;
    }

    public String getMail() {
        return this.mail;
    }

    public String getMobile() {
        return this.mobile;
    }

    public SSqCredential getCredential() {
        return this.credential;
    }

    public String getApplyCert() {
        return this.applyCert;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setUserType(String userType) {
        this.userType = userType;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public void setCredential(SSqCredential credential) {
        this.credential = credential;
    }

    public void setApplyCert(String applyCert) {
        this.applyCert = applyCert;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SSqPersonAuth)) {
            return false;
        } else {
            SSqPersonAuth other = (SSqPersonAuth)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label95: {
                    Object this$account = this.getAccount();
                    Object other$account = other.getAccount();
                    if (this$account == null) {
                        if (other$account == null) {
                            break label95;
                        }
                    } else if (this$account.equals(other$account)) {
                        break label95;
                    }

                    return false;
                }

                Object this$name = this.getName();
                Object other$name = other.getName();
                if (this$name == null) {
                    if (other$name != null) {
                        return false;
                    }
                } else if (!this$name.equals(other$name)) {
                    return false;
                }

                Object this$userType = this.getUserType();
                Object other$userType = other.getUserType();
                if (this$userType == null) {
                    if (other$userType != null) {
                        return false;
                    }
                } else if (!this$userType.equals(other$userType)) {
                    return false;
                }

                label74: {
                    Object this$mail = this.getMail();
                    Object other$mail = other.getMail();
                    if (this$mail == null) {
                        if (other$mail == null) {
                            break label74;
                        }
                    } else if (this$mail.equals(other$mail)) {
                        break label74;
                    }

                    return false;
                }

                label67: {
                    Object this$mobile = this.getMobile();
                    Object other$mobile = other.getMobile();
                    if (this$mobile == null) {
                        if (other$mobile == null) {
                            break label67;
                        }
                    } else if (this$mobile.equals(other$mobile)) {
                        break label67;
                    }

                    return false;
                }

                Object this$credential = this.getCredential();
                Object other$credential = other.getCredential();
                if (this$credential == null) {
                    if (other$credential != null) {
                        return false;
                    }
                } else if (!this$credential.equals(other$credential)) {
                    return false;
                }

                Object this$applyCert = this.getApplyCert();
                Object other$applyCert = other.getApplyCert();
                if (this$applyCert == null) {
                    if (other$applyCert != null) {
                        return false;
                    }
                } else if (!this$applyCert.equals(other$applyCert)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SSqPersonAuth;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $account = this.getAccount();
        result = result * 59 + ($account == null ? 43 : $account.hashCode());
        Object $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Object $userType = this.getUserType();
        result = result * 59 + ($userType == null ? 43 : $userType.hashCode());
        Object $mail = this.getMail();
        result = result * 59 + ($mail == null ? 43 : $mail.hashCode());
        Object $mobile = this.getMobile();
        result = result * 59 + ($mobile == null ? 43 : $mobile.hashCode());
        Object $credential = this.getCredential();
        result = result * 59 + ($credential == null ? 43 : $credential.hashCode());
        Object $applyCert = this.getApplyCert();
        result = result * 59 + ($applyCert == null ? 43 : $applyCert.hashCode());
        return result;
    }

    public String toString() {
        return "SSqPersonAuth(account=" + this.getAccount() + ", name=" + this.getName() + ", userType=" + this.getUserType() + ", mail=" + this.getMail() + ", mobile=" + this.getMobile() + ", credential=" + this.getCredential() + ", applyCert=" + this.getApplyCert() + ")";
    }

    public SSqPersonAuth(String account, String name, String userType, String mail, String mobile, SSqCredential credential, String applyCert) {
        this.account = account;
        this.name = name;
        this.userType = userType;
        this.mail = mail;
        this.mobile = mobile;
        this.credential = credential;
        this.applyCert = applyCert;
    }

    public SSqPersonAuth() {
    }

    public static class SSqPersonAuthBuilder {
        private String account;
        private String name;
        private String userType;
        private String mail;
        private String mobile;
        private SSqCredential credential;
        private String applyCert;

        SSqPersonAuthBuilder() {
        }

        public SSqPersonAuthBuilder account(String account) {
            this.account = account;
            return this;
        }

        public SSqPersonAuthBuilder name(String name) {
            this.name = name;
            return this;
        }

        public SSqPersonAuthBuilder userType(String userType) {
            this.userType = userType;
            return this;
        }

        public SSqPersonAuthBuilder mail(String mail) {
            this.mail = mail;
            return this;
        }

        public SSqPersonAuthBuilder mobile(String mobile) {
            this.mobile = mobile;
            return this;
        }

        public SSqPersonAuthBuilder credential(SSqCredential credential) {
            this.credential = credential;
            return this;
        }

        public SSqPersonAuthBuilder applyCert(String applyCert) {
            this.applyCert = applyCert;
            return this;
        }

        public SSqPersonAuth build() {
            return new SSqPersonAuth(this.account, this.name, this.userType, this.mail, this.mobile, this.credential, this.applyCert);
        }

        public String toString() {
            return "SSqPersonAuth.SSqPersonAuthBuilder(account=" + this.account + ", name=" + this.name + ", userType=" + this.userType + ", mail=" + this.mail + ", mobile=" + this.mobile + ", credential=" + this.credential + ", applyCert=" + this.applyCert + ")";
        }
    }
}
