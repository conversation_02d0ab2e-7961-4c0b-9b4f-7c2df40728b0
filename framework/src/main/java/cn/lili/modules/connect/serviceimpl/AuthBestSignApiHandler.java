package cn.lili.modules.connect.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.modules.connect.entity.AuthStatus;
import cn.lili.modules.connect.entity.AuthTypeInfo;
import cn.lili.modules.connect.entity.BestSignAuthApiParamBuilder;
import cn.lili.modules.connect.entity.CustomerInfo;
import cn.lili.modules.connect.entity.dto.SSQAccountAudit;
import cn.lili.modules.connect.service.AuthApiAbsHandler;
import cn.lili.modules.connect.service.IAuthTypeInfoService;
import cn.lili.modules.jrzh_other.bestsign.config.AuthApiConfig;
import cn.lili.modules.jrzh_other.bestsign.dto.SSQResult;
import cn.lili.modules.jrzh_other.bestsign.service.IBestSignService;
import cn.lili.modules.jrzh_other.core_api.constant.ApiSupplier;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <AUTHOR>
 * 上上签实名接口实现类
 */
@Service("bestSignAuthApi")
@RequiredArgsConstructor
@Slf4j
public class AuthBestSignApiHandler extends AuthApiAbsHandler {
    private final IBestSignService bestSignService;
    private final IAuthTypeInfoService authTypeInfoService;
//    private final BladeRedis bladeRedis;
    private final static String VERIFY_SUCCESS = "1";
    private final static String COMMIT_SUCCESS = "1";
    private final static Integer PERSON_TYPE = 1;
    private final static Integer ENT_TYPE = 2;
    private final static String FACE_KEY = "face_key:";
    private final static String H5_FACE_KEY = "h5_face_key:";
    private final static String PERSON_USER = "person_user:";
    private final static String COMPANY_USER = "company_user";
    private final static String TENANT = "tenant:";
    private final static String WXAPPLET_FACE_KEY = "wxapplet_face_key:";
    private final AuthApiConfig authApiConfig;


//    @Value(value = "${blade.linkChange.preUrl}")
//    private String PRE_URL;
//
//    @Override
//    public void regPersonUser(CustomerPersonInfoVO customerPersonInfo) {
//        //设为上上签供应商
//        customerPersonInfo.setApiSupplier(ApiSupplier.BEST_SIGN_AUTH.getCode());
//        //注册电子签账号并设置任务id
//        SSqPersonAuth personAuth = BestSignAuthApiParamBuilder.regPersonParamBuild(customerPersonInfo);
//        String taskId = bestSignService.regPersonUser(personAuth);
//        customerPersonInfo.setTaskId(taskId);
//    }
//
//    @Override
//    public JSONObject commitPersonAuthInfoOnH5FaceContrast(Map<String, Object> request, CustomerPersonInfo personInfo, String tenantId) {
//        //缓存人脸链接
//        String key = H5_FACE_KEY + personInfo.getCustomerId();
//        if (!"true".equals(request.get("reGen"))) {
//            if (bladeRedis.exists(CacheUtil.formatCacheName(key, true))) {
//                return JSONUtil.parseObj(bladeRedis.get(CacheUtil.formatCacheName(key, true)));
//            }
//        }
//        AuthStatus.AUTH_TYPE h5Face = AuthStatus.AUTH_TYPE.H5_FACE;
//        // 构建请求数据
//        SSqPersonAuthFaceH5Param param = BestSignAuthApiParamBuilder.buildCommitPersonH5FaceContrast(personInfo);
//        SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnH5FaceContrast(param);
//        dealH5FaceResult(ssqResult);
//        JSONObject ssqData = JSONUtil.parseObj(ssqResult.getData());
//        String orderNo = ssqData.getStr("orderNo");
//        //保存到实名方式信息表中
//        saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(param), h5Face.getStatus(), personInfo.getCustomerId(), PERSON_TYPE, orderNo, tenantId);
//        // 缓存人脸链接
//        bladeRedis.setEx(CacheUtil.formatCacheName(key, true), ssqResult.getData(), Duration.ofSeconds(60));
//        return ssqData;
//    }
//
//    private void dealH5FaceResult(SSQResult ssqResult) {
//        if (ssqResult.getErrno() != 0) {
//            throw new ServiceException("实名信息有误，请核实");
//        }
//    }
//
//    /**
//     * 个人人脸对比
//     *
//     * @param request    请求参数
//     * @param personInfo 个人客户信息
//     * @return 验证结果
//     */
//    @Override
//    public Boolean commitPersonAuthInfoOnFaceContrast(Map<String, Object> request, CustomerPersonInfo personInfo, String tenantId) {
//        // 构建请求数据
//        SSqPersonAuthFaceContrastParam faceContrastParam = BestSignAuthApiParamBuilder.buildCommitPersonFaceContrast(request, personInfo);
//        SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnFaceContrast(faceContrastParam);
//        JSONObject ssqData = JSONUtil.parseObj(ssqResult.getData());
//        // 保存到实名方式信息表中
//        saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(faceContrastParam), AuthStatus.AUTH_TYPE.FACE_CONTRAST.getStatus(), personInfo.getCustomerId(), PERSON_TYPE, null, tenantId);
//        boolean success = VERIFY_SUCCESS.equals(ssqData.getStr("result"));
//        return success;
//    }
//
//
//    /**
//     * 微信小程序人脸识别
//     *
//     * @param request
//     * @param personInfo
//     * @return account 认证账号
//     * returnUrl 认证完成后的跳转地址
//     * name 真实姓名
//     * sid 认证流水号（请保持唯一）
//     * callbackHost 授权回调域名
//     * identity 身份证号
//     */
//    @Override
//    public JSONObject commitPersonAuthInfoOnWxAppletFace(Map<String, Object> request, CustomerPersonInfo personInfo, String tenantId) {
//        //缓存人脸链接
//        String key = WXAPPLET_FACE_KEY + personInfo.getCustomerId();
//        if (!"true".equals(request.get("reGen"))) {
//            if (bladeRedis.exists(CacheUtil.formatCacheName(key, true))) {
//                return JSONUtil.parseObj(bladeRedis.get(CacheUtil.formatCacheName(key, true)));
//            }
//        }
//        SSqPersonAuthFaceParam faceParam = BestSignAuthApiParamBuilder.buildCommitPersonWxAppletFace(personInfo);
//        SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnWxAppletFace(faceParam);
//        //保存到实名方式信息表中
//        saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(faceParam), AuthStatus.AUTH_TYPE.WXAPPLET_FACE.getStatus(), personInfo.getCustomerId(), PERSON_TYPE, null, tenantId);
//        JSONObject data = JSONUtil.parseObj(ssqResult.getData());
//        // 缓存人脸链接
//        bladeRedis.setEx(CacheUtil.formatCacheName(key, true), data, Duration.ofDays(1));
//        return data;
//    }
//
//    /**
//     * 个人银行卡校验
//     *
//     * @param personInfo 实名资料
//     */
//    @Override
//    public Boolean commitPersonAuthInfoOnBank(CustomerPersonInfo personInfo, String tenantId) {
//        SSqPersonAuthBankParam bankParam = BestSignAuthApiParamBuilder.buildCommitPersonBankReq(personInfo);
//        SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnBank(bankParam);
//        //保存到实名方式信息表中
//        saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(bankParam), AuthStatus.AUTH_TYPE.BANK.getStatus(), personInfo.getCustomerId(), PERSON_TYPE, null, tenantId);
//        return true;
//    }
//
//    @Override
//    public JSONObject commitPersonAuthInfoOnFace(Map<String, Object> request, CustomerPersonInfo personInfo, String tenantId) {
//        //缓存人脸链接
//        String key = TENANT + tenantId + PERSON_USER + FACE_KEY + personInfo.getCustomerId();
//        if (!"true".equals(request.get("reGen"))) {
//            if (bladeRedis.exists(CacheUtil.formatCacheName(key, true))) {
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.putOnce("url", bladeRedis.get(CacheUtil.formatCacheName(key, true)));
//                return jsonObject;
//            }
//        }
//        SSqPersonAuthFaceParam faceParam = BestSignAuthApiParamBuilder.buildCommitPersonFace(personInfo);
//        SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnFace(faceParam);
//        String orderNo = JSONUtil.parseObj(ssqResult.getData()).getStr("orderNo");
//        //保存到实名方式信息表中
//        saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(faceParam), AuthStatus.AUTH_TYPE.FACE.getStatus(), personInfo.getCustomerId(), PERSON_TYPE, orderNo, tenantId);
//        JSONObject data = JSONUtil.parseObj(ssqResult.getData());
//        //放入长链接连接池 返回短链接
//        longToShortUrl(data);
//        //放入緩衝池
//        bladeRedis.setEx(CacheUtil.formatCacheName(key, true), data.getStr("url"), Duration.ofHours(12));
//        return data;
//    }
//
//    @SneakyThrows
//    private void longToShortUrl(JSONObject data) {
//        String url = data.getStr("url");
//        String hashVal = MD5Utils.stringMD5(url);
//        bladeRedis.setEx(CacheUtil.formatCacheName(hashVal, true), url, Duration.ofHours(12));
//        String requestURL = PRE_URL + "/blade-otherApi/web-back/authApi/endpoint/transfer/shortToLongUrl/";
//        String path = requestURL + hashVal;
//        data.set("url", path);
//    }
//
//    @Override
//    public Boolean commitPersonAuthInfoOnPhone(CustomerPersonInfo personInfo, String tenantId) {
//        SSqPersonAuthPhoneParam phoneParam = BestSignAuthApiParamBuilder.buildCommitPersonPhoneReq(personInfo);
//        SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnPhone(phoneParam);
//        JSONObject resultData = JSONUtil.parseObj(ssqResult.getData());
//        //提交成功保存到实名方式信息表中
//        if (COMMIT_SUCCESS.equals(resultData.getStr("result"))) {
//            saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(phoneParam), AuthStatus.AUTH_TYPE.PHONE.getStatus(), personInfo.getCustomerId(), PERSON_TYPE, null, tenantId);
//        } else {
//            String errorMsg = resultData.getStr("msg");
//            if("查询无此记录".contains(errorMsg)){
//                throw new ServiceException("该手机号入网记录查询失败/手机号为副卡、虚拟号段");
//            }
//            throw new ServiceException(errorMsg);
//    }
//        return true;
//}
//
//    @Override
//    public Boolean verifyPersonAuthInfoOnH5Face(CustomerPersonInfo personInfo) {
//        //获取orderNo
//        Integer status = AuthStatus.AUTH_TYPE.H5_FACE.getStatus();
//        AuthTypeInfo authType = authTypeInfoService.getAuthType(personInfo.getCustomerId(), status, ApiSupplier.BEST_SIGN_AUTH.getCode());
//        if (ObjectUtil.isEmpty(authType)) {
//            throw new ServiceException("请先进行人脸验证");
//        }
//        Boolean success = authType.getStatus() == 2;
//        return success;
//    }
//
//    /**
//     * @param request
//     * @param personInfo
//     */
//    @Override
//    public Boolean verifyPersonAuthInfoOnBank(Map<String, Object> request, CustomerPersonInfo personInfo) {
//        //获取验证key
//        Map<String, String> response = authTypeInfoService.getResponse(personInfo.getCustomerId(), AuthStatus.AUTH_TYPE.PHONE.getStatus(), ApiSupplier.BEST_SIGN_AUTH.getCode(), Map.class);
//        if (ObjectUtil.isEmpty(response)) {
//            throw new ServiceException("请先进行银行认证");
//        }
//        //发送请求进行验证
//        String identity3Key = response.get("personalIdentity3Key");
//        request.put("personalIdentity3Key", identity3Key);
//        String vcode = request.get("vcode").toString();
//        SSQResult ssqResult = bestSignService.verifyPersonAuthInfoOnBank(identity3Key, vcode);
//
//        JSONObject ssqData = JSONUtil.parseObj(ssqResult.getData());
//        return VERIFY_SUCCESS.equals(ssqData.getStr("result"));
//    }
//
//    /**
//     * @param personInfo
//     */
//    @Override
//    public Boolean verifyPersonAuthInfoOnFace(CustomerPersonInfo personInfo) {
//        //获取orderNo
//        AuthTypeInfo authType = authTypeInfoService.getAuthType(personInfo.getCustomerId(), AuthStatus.AUTH_TYPE.FACE.getStatus(), ApiSupplier.BEST_SIGN_AUTH.getCode());
//        if (ObjectUtil.isEmpty(authType)) {
//            throw new ServiceException("请先进行人脸验证");
//        }
//        Boolean success = authType.getStatus() == 2;
//        JSONObject data = new JSONObject();
//        data.putOnce("verifyResult", success ? 1 : 0);
//        return success;
//    }
//
//    /**
//     * 个人手机号三要素验证码校验
//     *
//     * @param request
//     * @param personInfo
//     */
//    public Boolean verifyPersonAuthInfoOnPhone(Map<String, Object> request, CustomerPersonInfo personInfo) {
//        //获取验证key
//        Map<String, String> response = authTypeInfoService.getResponse(personInfo.getCustomerId(), AuthStatus.AUTH_TYPE.PHONE.getStatus(), ApiSupplier.BEST_SIGN_AUTH.getCode(), Map.class);
//        if (ObjectUtil.isEmpty(response)) {
//            throw new ServiceException("请先进行手机号验证");
//        }
//        //发送请求进行验证
//        String identity3Key = response.get("personalIdentity3Key");
//        String vcode = request.get("vcode").toString();
//        SSQResult ssqResult = bestSignService.verifyPersonAuthInfoOnPhone(identity3Key, vcode);
//        JSONObject ssqData = JSONUtil.parseObj(ssqResult.getData());
//        Boolean success = VERIFY_SUCCESS.equals(ssqData.getStr("result"));
//        return success;
//    }
//
//
    /**
     * 保存到实名方式表中
     *
     * @param ssqResult
     * @param req
     * @param authType
     * @param customerId
     */
    private void saveAuthTypeInfo(SSQResult ssqResult, String req, Integer authType, Long customerId, Integer userType, String orderNo) {
        AuthTypeInfo authTypeInfo = authTypeInfoService.getAuthType(customerId, authType, ApiSupplier.BEST_SIGN_AUTH.getCode());
        if (ObjectUtil.isEmpty(authTypeInfo)) {
            authTypeInfo = new AuthTypeInfo();
        }
        authTypeInfo.setApiSupplier(ApiSupplier.BEST_SIGN_AUTH.getCode());
        authTypeInfo.setUserType(userType);
        authTypeInfo.setCustomerId(customerId);
        authTypeInfo.setRequest(req);
        authTypeInfo.setResponse(ssqResult.getData());
        authTypeInfo.setAuthType(authType);
        authTypeInfo.setStatus(AuthStatus.PersonAuthStatus.AUTHING.getStatus());
//        authTypeInfo.setTenantId(tenantId);
        authTypeInfo.setOrderNo(orderNo);
        authTypeInfoService.saveOrUpdate(authTypeInfo);
    }
//
//    @Override
//    public void regEntUser(CustomerInfo customerInfo, String userPhone) {
//        SSqEntReg sSqEntReg = BestSignAuthApiParamBuilder.buildRegEntUserParam(customerInfo, userPhone);
//        String taskId = bestSignService.regEntUser(sSqEntReg);
//        customerInfo.setTaskId(taskId);
//    }
//
//    @Override
//    public JSONObject commitEntAuthInfoOnH5Face(Map<String, Object> request, CustomerInfo customerInfo, String tenantId) {
//        //缓存人脸链接
//        String key = H5_FACE_KEY + customerInfo.getCompanyId();
//        if (!"true".equals(request.get("reGen"))) {
//            if (bladeRedis.exists(CacheUtil.formatCacheName(key, true))) {
//                return JSONUtil.parseObj(bladeRedis.get(CacheUtil.formatCacheName(key, true)));
//            }
//        }
//        AuthStatus.AUTH_TYPE h5Face = AuthStatus.AUTH_TYPE.H5_FACE;
//        // 构建请求数据
//        SSqPersonAuthFaceH5Param param = BestSignAuthApiParamBuilder.buildCommitEntH5FaceContrast(request, customerInfo,customerInfo.getCompanyId());
//        SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnH5FaceContrast(param);
//        dealH5FaceResult(ssqResult);
//        JSONObject ssqData = JSONUtil.parseObj(ssqResult.getData());
//        String orderNo = ssqData.getStr("orderNo");
//        //保存到实名方式信息表中
//        saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(param), h5Face.getStatus(), customerInfo.getCompanyId(), ENT_TYPE, orderNo, tenantId);
//        // 缓存人脸链接
//        bladeRedis.setEx(CacheUtil.formatCacheName(key, true), ssqResult.getData(), Duration.ofSeconds(60));
//        return ssqData;
//    }
//
    @Override
    public JSONObject commitEntAuthInfoOnBank(Map<String, Object> request, CustomerInfo customerInfo) {
//        Map<String, String> paramMap = getApiParams().getParamMap();
        //======================================后门====================================//
        String areaCodeOld = request.get("bankAreaCode").toString();
        SSQAccountAudit accountAudit = BestSignAuthApiParamBuilder.buildCommitEntBack(request, customerInfo);
        //不足6位补0
        String areaCode = accountAudit.getBankAreaCode();
        accountAudit.setBankAreaCode((areaCode + "*********").substring(0, 6));
        //======================================后门====================================//
        if ("是".equals(authApiConfig.getIsTest())) {
            JSONObject obj = new JSONObject();
            obj.putOnce("result", "1");
            obj.putOnce("msg", "");
            obj.putOnce("bizNo", "");
            obj.putOnce("flowId", "");

            SSQResult ssqResult = new SSQResult();
            ssqResult.setData(obj.toString());
            //保存记录
            saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(accountAudit), AuthStatus.AUTH_TYPE.BANK.getStatus(), customerInfo.getCompanyId(), ENT_TYPE, null);
            //银行信息保存到实名表中
            setAccountAudit(areaCodeOld, accountAudit, customerInfo);
            return obj;
        }
        //查询请求是否未过期、若过期则重新生成、未过期则返回旧数据
        JSONObject validBankInfo = getValidBankInfo(accountAudit, customerInfo);
        if (ObjectUtil.isNotEmpty(validBankInfo)) {
            return validBankInfo;
        }

        SSQResult ssqResult = bestSignService.entCorporateAccountAudit(accountAudit);
        //保存记录
        saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(accountAudit), AuthStatus.AUTH_TYPE.BANK.getStatus(), customerInfo.getCompanyId(), ENT_TYPE, null);
        //银行信息保存到实名表中
        setAccountAudit(areaCodeOld, accountAudit, customerInfo);
        return JSONUtil.parseObj(ssqResult.getData());
    }

    /**
     * 保存银行信息到实名表中
     *
     * @param areaCode
     * @param accountAudit
     * @param customerInfo
     */
    private void setAccountAudit(String areaCode, SSQAccountAudit accountAudit, CustomerInfo customerInfo) {
        customerInfo.setBankCard(accountAudit.getBankCard());
        customerInfo.setBankAreaCode(areaCode);
        customerInfo.setBankName(accountAudit.getBankName());
        customerInfo.setBraBankName(accountAudit.getBraBankName());
    }

    /**
     * 获取未过期的打款记录
     *
     * @param accountAudit
     * @param customerInfo
     */
    private JSONObject getValidBankInfo(SSQAccountAudit accountAudit, CustomerInfo customerInfo) {
        //查询是否有记录
        JSONObject response = authTypeInfoService.getResponse(customerInfo.getCompanyId(), AuthStatus.AUTH_TYPE.BANK.getStatus(), ApiSupplier.BEST_SIGN_AUTH.getCode(), JSONObject.class);
        if (ObjectUtil.isEmpty(response)) {
            return null;
        }
        //是否为同一条记录
        if (!isOne(accountAudit, customerInfo)) {
            return null;
        }
        //根据流水号查询打款状态
        SSQResult ssqResult = bestSignService.entPayAuthQuery(response.getStr("bizNo"));
        JSONObject resultData = JSONUtil.parseObj(ssqResult.getData());
        //根据打款状态判断是否需要重新发起
        String status = resultData.getStr("status");
        if (needReCommit(status)) {
            return null;
        }
        return response;
    }

    /**
     * -1 已过期，需要重新发起打款；
     * 1：业务处理成功或提交成功，具体又有：①已到账；②已提交银行，到账情况以账号查询为准，若银行打款失败第二日结果会更新,请重新查询；③已提交银行，到账情况以账号查询为准；
     * 2：失败，打款失败，确认失败原因后重新发起打款请求；
     * 3: 处理中，银行间落地处理未完成，需要继续等待，如频繁查询会返回"请求频繁,请稍后查询"；
     * 4：系统异常，银行服务不可用，请稍后再试；
     * 6：其他情况，银行间处理的未知问题，建议重新发起打款；
     *
     * @param status
     * @return
     */
    private boolean needReCommit(String status) {
        if ("4".equals(status)) {
            throw new ServiceException("系统异常，银行服务不可用，请稍后再试");
        }
        return !("1".equals(status) || "3".equals(status));
    }
//
    private boolean isOne(SSQAccountAudit accountAudit, CustomerInfo customerInfo) {
        return accountAudit.getBankCard().equals(customerInfo.getBankCard()) && accountAudit.getBankAreaCode().equals(customerInfo.getBankAreaCode()) && accountAudit.getBankName().equals(customerInfo.getBankName()) && accountAudit.getBraBankName().equals(customerInfo.getBraBankName());
    }
//
//    @Override
//    public JSONObject commitEntAuthInfoOnFace(Map<String, Object> request, CustomerInfo customerInfo, String tenantId) {
//        //缓存人脸链接
//        String key = TENANT + AuthUtil.getTenantId() + COMPANY_USER + FACE_KEY + customerInfo.getCompanyId();
//        if (!"true".equals(request.get("reGen"))) {
//            if (bladeRedis.exists(CacheUtil.formatCacheName(key, true))) {
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.putOnce("url", bladeRedis.get(CacheUtil.formatCacheName(key, true)));
//                return jsonObject;
//            }
//        }
//        SSqPersonAuthFaceParam faceParam = BestSignAuthApiParamBuilder.buildCommitEntFace(request, customerInfo);
//        SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnFace(faceParam);
//        String orderNo = JSONUtil.parseObj(ssqResult.getData()).getStr("orderNo");
//        //保存到实名方式信息表中
//        saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(faceParam), AuthStatus.AUTH_TYPE.FACE.getStatus(), customerInfo.getCompanyId(), ENT_TYPE, orderNo, tenantId);
//        JSONObject data = JSONUtil.parseObj(ssqResult.getData());
//        //放入长链接连接池 返回短链接
//        longToShortUrl(data);
//        //放入緩衝池
//        bladeRedis.setEx(CacheUtil.formatCacheName(key, true), data.getStr("url"), Duration.ofHours(12));
//        return data;
//    }
//
//    @Override
//    public Boolean commitEntAuthInfoOnPhone(Map<String, Object> request, CustomerInfo customerInfo, String tenantId) {
//        SSqPersonAuthPhoneParam phoneParam = BestSignAuthApiParamBuilder.buildCommitEntPhone(request, customerInfo);
//        SSQResult ssqResult = bestSignService.commitPersonAuthInfoOnPhone(phoneParam);
//        JSONObject resultData = JSONUtil.parseObj(ssqResult.getData());
//        //提交成功保存
//        if (COMMIT_SUCCESS.equals(resultData.getStr("result"))) {
//            saveAuthTypeInfo(ssqResult, JSONUtil.toJsonStr(phoneParam), AuthStatus.AUTH_TYPE.PHONE.getStatus(), customerInfo.getCompanyId(), ENT_TYPE, null, tenantId);
//        } else {
//            throw new ServiceException(resultData.getStr("msg"));
//        }
//        //将手机号回填到实名表中
//        customerInfo.setMobile(request.get("mobile").toString());
//        return true;
//    }
//
//
//    @Override
//    public PersonEAuthInfoDTO getPersonalCredential(String account) {
//        return bestSignService.getPersonalCredential(account);
//
//    }
//
//    @Override
//    public EntEAuthInfoDTO getEntCredential(String account) {
//        return bestSignService.getEntCredential(account);
//
//    }
//
    @Override
    public JSONObject verifyEntAuthInfoOnBank(Map<String, Object> request, CustomerInfo customerInfo) {

        SSQResult ssqResult = bestSignService.payAuthVerify(request.get("bankCard").toString(), request.get("transactionAmount").toString());
        JSONObject resultData = JSONUtil.parseObj(ssqResult.getData());
        return resultData;
    }
//
//    @Override
//    public Boolean verifyEntAuthInfoOnFace(CustomerInfo customerInfo) {
//        //获取orderNo
//        if (customerInfo == null) {
//            return null;
//        }
//        AuthTypeInfo authType = authTypeInfoService.getAuthType(customerInfo.getCompanyId(), AuthStatus.AUTH_TYPE.FACE.getStatus(), ApiSupplier.BEST_SIGN_AUTH.getCode());
//        if (ObjectUtil.isEmpty(authType)) {
//            throw new ServiceException("请先进行人脸验证");
//        }
//        Boolean success = authType.getStatus() == 2;
//        return success;
//    }
//
//    @Override
//    public Boolean verifyEntAuthInfoOnH5Face(CustomerInfo customerInfo) {
//        //获取orderNo
//        Integer status = AuthStatus.AUTH_TYPE.H5_FACE.getStatus();
//        AuthTypeInfo authType = authTypeInfoService.getAuthType(customerInfo.getCompanyId(), status, ApiSupplier.BEST_SIGN_AUTH.getCode());
//        if (ObjectUtil.isEmpty(authType)) {
//            throw new ServiceException("请先进行人脸验证");
//        }
//        Boolean success = authType.getStatus() == 2;
//        return success;
//    }
//
//    @Override
//    public Boolean verifyEntAuthInfoOnPhone(Map<String, Object> request, CustomerInfo customerInfo) {
//        //获取验证key
//        Map<String, String> response = authTypeInfoService.getResponse(customerInfo.getCompanyId(), AuthStatus.AUTH_TYPE.PHONE.getStatus(), ApiSupplier.BEST_SIGN_AUTH.getCode(), Map.class);
//        if (ObjectUtil.isEmpty(response)) {
//            throw new ServiceException("请先进行手机号验证");
//        }
//        //发送请求进行验证
//        String vcode = request.get("vcode").toString();
//        String identity3Key = response.get("personalIdentity3Key");
//        request.put("personalIdentity3Key", identity3Key);
//        SSQResult ssqResult = bestSignService.verifyPersonAuthInfoOnPhone(identity3Key, vcode);
//
//        JSONObject ssqData = JSONUtil.parseObj(ssqResult.getData());
//        Boolean success = VERIFY_SUCCESS.equals(ssqData.getStr("result"));
//        return success;
//    }
//
////    public R payAuthQuery(Map<String, Object> request) {
////        SSQResult ssqResult = connector.post(BestSignPathEnum.ENT_PAY_AUTH_QUERY.getPath(), JSONUtil.toJsonStr(request), true);
////        return R.data(JSONUtil.parseObj(ssqResult.getData()));
////    }
//
////    @Transactional(rollbackFor = Exception.class)
////    public void updateEntAuthProcess(CustomerInfo customerInfo, Integer authType, String apiSupplier) {
////        //更新实名方式表
////        Integer passStatus = AuthStatus.CompanyAuthStatus.AUTH_PASS.getStatus();
////        authTypeInfoService.updateStatus(customerInfo.getCompanyId(), passStatus, apiSupplier, passStatus);
////        //若企业未完成实名 更新企业实名信息
////        if (!AuthStatus.CompanyAuthStatus.AUTH_PASS.getStatus().equals(customerInfo.getAuthStatus())) {
////            customerInfo.setProcess(AuthProcess.ENT_PROCESS.SIGN_PROCEE.getProcess());
////            customerInfo.setApiSupplier(apiSupplier);
////            customerInfo.setProcess(AuthProcess.ENT_PROCESS.SIGN_PROCEE.getProcess());
////            customerInfo.setAuthType(authType);
////            customerInfo.setAuthStatus(passStatus);
////            SpringUtil.getBean(ICustomerInfoService.class).updateEntInfoAndUserInfo(customerInfo);
////        }
////    }
//
////    /**
////     * 异步证书申请状态
////     */
////    private R checkAsynCertStatus(String account, String taskId) {
////        JSONObject req = new JSONObject();
////        req.putOnce("account", account);
////        req.putOnce("taskId", taskId);
////        SSQResult ssqResult = connector.post(BestSignPathEnum.ASYNC_APPLY_CERT_STATUS.getPath(), JSONUtil.toJsonStr(req));
////        return R.data(JSONUtil.parseObj(ssqResult.getData()));
////    }
}
