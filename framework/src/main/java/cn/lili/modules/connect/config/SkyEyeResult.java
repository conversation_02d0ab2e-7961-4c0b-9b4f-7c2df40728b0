package cn.lili.modules.connect.config;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//



public class SkyEyeResult {
    private String reason;
    private String result;
    private Integer error_code;

    public SkyEyeResult() {
    }

    public String getReason() {
        return this.reason;
    }

    public String getResult() {
        return this.result;
    }

    public Integer getError_code() {
        return this.error_code;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public void setError_code(Integer error_code) {
        this.error_code = error_code;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SkyEyeResult)) {
            return false;
        } else {
            SkyEyeResult other = (SkyEyeResult)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label47: {
                    Object this$error_code = this.getError_code();
                    Object other$error_code = other.getError_code();
                    if (this$error_code == null) {
                        if (other$error_code == null) {
                            break label47;
                        }
                    } else if (this$error_code.equals(other$error_code)) {
                        break label47;
                    }

                    return false;
                }

                Object this$reason = this.getReason();
                Object other$reason = other.getReason();
                if (this$reason == null) {
                    if (other$reason != null) {
                        return false;
                    }
                } else if (!this$reason.equals(other$reason)) {
                    return false;
                }

                Object this$result = this.getResult();
                Object other$result = other.getResult();
                if (this$result == null) {
                    if (other$result != null) {
                        return false;
                    }
                } else if (!this$result.equals(other$result)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SkyEyeResult;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $error_code = this.getError_code();
        result = result * 59 + ($error_code == null ? 43 : $error_code.hashCode());
        Object $reason = this.getReason();
        result = result * 59 + ($reason == null ? 43 : $reason.hashCode());
        Object $result = this.getResult();
        result = result * 59 + ($result == null ? 43 : $result.hashCode());
        return result;
    }

    public String toString() {
        return "SkyEyeResult(reason=" + this.getReason() + ", result=" + this.getResult() + ", error_code=" + this.getError_code() + ")";
    }
}
