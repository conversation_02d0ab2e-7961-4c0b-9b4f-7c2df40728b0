package cn.lili.modules.connect.service;


import cn.hutool.json.JSONObject;
import cn.lili.modules.connect.entity.CustomerInfo;
import cn.lili.modules.connect.entity.CustomerPersonInfo;
import cn.lili.modules.connect.entity.vo.CustomerPersonInfoVO;

import java.util.Map;

/**
 * <AUTHOR>
 * 实名接口
 */
public interface AuthApiHandler {
//    /**
//     * 个人注册电子签账号
//     *
//     * @param customerPersonInfo 系统个人信息
//     * @return 任务id taskId
//     */
//    void regPersonUser(CustomerPersonInfoVO customerPersonInfo);
//
//    /**
//     * 企业注册电子签账号
//     *
//     * @param customerInfo 系统企业信息
//     * @return
//     */
//    void regEntUser(CustomerInfo customerInfo, String userPhone);
//
//    /**
//     * 获取个人注册信息
//     *
//     * @param account 用户账号
//     * @return
//     */
////    PersonEAuthInfoDTO getPersonalCredential(String account);
//
//    /**
//     * 获取企业注册信息
//     *
//     * @param companyId
//     * @return
//     */
////    EntEAuthInfoDTO getEntCredential(String companyId);
//
//    /**
//     * 个人手机号三要素验证码获取
//     *
//     * @param personInfo
//     * @param tenantId
//     * @return
//     */
//    Boolean commitPersonAuthInfoOnPhone(CustomerPersonInfo personInfo, String tenantId);
//
//    /**
//     * 支付宝刷脸认证请求认证url
//     *
//     * @param request
//     * @param personInfo
//     * @param tenantId
//     * @return
//     */
//    JSONObject commitPersonAuthInfoOnFace(Map<String, Object> request, CustomerPersonInfo personInfo, String tenantId);
//
//    /**
//     * 银行卡多要素校验及发送验证码
//     *
//     * @param personInfo
//     * @param tenantId
//     * @return
//     */
//    Boolean commitPersonAuthInfoOnBank(CustomerPersonInfo personInfo, String tenantId);
//
//    /**
//     * 微信小程序刷脸认证请求认证
//     *
//     * @param request
//     * @param personInfo
//     * @param tenantId
//     * @return 具体看实现类
//     */
//    JSONObject commitPersonAuthInfoOnWxAppletFace(Map<String, Object> request, CustomerPersonInfo personInfo, String tenantId);
//
//    /**
//     * 个人人脸比对验证
//     *
//     * @param request
//     * @param personInfo
//     * @param tenantId
//     * @return
//     */
//    Boolean commitPersonAuthInfoOnFaceContrast(Map<String, Object> request, CustomerPersonInfo personInfo, String tenantId);
//
//    /**
//     * 腾讯云H5刷脸认证
//     *
//     * @param request    请求参数
//     * @param personInfo 个人客户信息
//     * @return 人脸链接
//     */
//    JSONObject commitPersonAuthInfoOnH5FaceContrast(Map<String, Object> request, CustomerPersonInfo personInfo, String tenantId);
//
//    /**
//     * 个人手机号三要素验证码校验
//     *
//     * @param request
//     * @param personInfo
//     */
//    Boolean verifyPersonAuthInfoOnPhone(Map<String, Object> request, CustomerPersonInfo personInfo);
//
//    Boolean verifyPersonAuthInfoOnFace(CustomerPersonInfo personInfo);
//
//    Boolean verifyPersonAuthInfoOnBank(Map<String, Object> request, CustomerPersonInfo personInfo);
//
//    Boolean verifyPersonAuthInfoOnH5Face(CustomerPersonInfo personInfo);
//
//    Boolean commitEntAuthInfoOnPhone(Map<String, Object> request, CustomerInfo customerInfo, String tenantId);
//
//    JSONObject commitEntAuthInfoOnFace(Map<String, Object> request, CustomerInfo customerInfo, String tenantId);
//
//    JSONObject commitEntAuthInfoOnBank(Map<String, Object> request, CustomerInfo customerInfo, String tenantId);
    JSONObject commitEntAuthInfoOnBank(Map<String, Object> request, CustomerInfo customerInfo);
//
//    JSONObject commitEntAuthInfoOnH5Face(Map<String, Object> request, CustomerInfo customerInfo, String tenantId);
//
//    Boolean verifyEntAuthInfoOnPhone(Map<String, Object> request, CustomerInfo customerInfo);
//
//    Boolean verifyEntAuthInfoOnFace(CustomerInfo customerInfo);
//
    JSONObject verifyEntAuthInfoOnBank(Map<String, Object> request, CustomerInfo customerInfo);
//
//    Boolean verifyEntAuthInfoOnH5Face(CustomerInfo customerInfo);
}
