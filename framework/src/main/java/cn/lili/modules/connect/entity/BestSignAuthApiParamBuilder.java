package cn.lili.modules.connect.entity;



import cn.hutool.core.util.StrUtil;
import cn.lili.modules.connect.config.SSqCredential;
import cn.lili.modules.connect.config.SSqEntCredential;
import cn.lili.modules.connect.config.SSqEntReg;
import cn.lili.modules.connect.config.SSqPersonAuth;
import cn.lili.modules.connect.entity.dto.SSQAccountAudit;
import cn.lili.modules.connect.entity.vo.CustomerPersonInfoVO;
import lombok.experimental.UtilityClass;

import java.util.Map;
/**
 * 上上签参数构造器
 *
 * <AUTHOR>
 */
@UtilityClass
public class BestSignAuthApiParamBuilder {


    /**
     * 企业实名信息构建
     *
     * @param customerInfo
     * @param userPhone
     */
    public SSqEntReg buildRegEntUserParam(CustomerInfo customerInfo, String userPhone) {
        SSqEntCredential entCredential = new SSqEntCredential();
        entCredential.setEnterpriseIdentityType("8");
        entCredential.setRegCode(customerInfo.getBusinessLicenceNumber());
        entCredential.setOrgCode("");
        entCredential.setTaxCode("");
        //经办人填写经办人信息 法人填写法人信息
        if (AuthStatus.LegalPersonFlag.MANAGER.getStatus().equals(customerInfo.getLegalPersonFlag())) {
            entCredential.setLegalPerson(customerInfo.getOperatorName());
            entCredential.setLegalPersonIdentity(customerInfo.getOperatorIdcard());
            entCredential.setLegalPersonMobile(customerInfo.getOperatorPhone());
        } else {
            entCredential.setLegalPerson(customerInfo.getCorporationName());
            entCredential.setLegalPersonIdentity(customerInfo.getCorporationIdCardNumber());
            entCredential.setLegalPersonMobile(customerInfo.getMobile());
        }
        entCredential.setLegalPersonIdentityType("0");
        entCredential.setContactMobile(userPhone);
        entCredential.setContactMail("");
        entCredential.setProvince("");
        entCredential.setCity("");
        entCredential.setAddress("");
        SSqEntReg build = SSqEntReg.builder()
                .account(customerInfo.getCompanyId().toString())
                .name(customerInfo.getCorpName())
                .userType("2")
                .mail("")
                .mobile("")
                .applyCert("1")
                .credential(entCredential).build();
        return build;
    }

    public SSqPersonAuth regPersonParamBuildOld(CustomerInfo customerPersonInfo) {

        SSqCredential credential = SSqCredential.builder()
                .identity(customerPersonInfo.getOperatorIdcard())
                .identityType("0")
                .contactMobile(customerPersonInfo.getOperatorPhone())
                .contactMail("")
                .province("")
                .city("")
                .address("").build();
        SSqPersonAuth personAuth = SSqPersonAuth.builder()
                .account(customerPersonInfo.getCustomerId().toString())
                .name(customerPersonInfo.getOperatorName())
                .userType("1")
                .mail("")
                .mobile(customerPersonInfo.getOperatorPhone())
                .applyCert("1")
                .credential(credential).build();
        return personAuth;
    }
    /**
     * 个人注册请求
     *
     * @param customerPersonInfo
     * @return
     */
    public SSqPersonAuth regPersonParamBuild(CustomerPersonInfoVO customerPersonInfo) {
        SSqCredential credential = SSqCredential.builder()
                .identity(customerPersonInfo.getIdentity())
                .identityType("0")
                .contactMobile(customerPersonInfo.getAccount())
                .contactMail("")
                .province("")
                .city("")
                .address("").build();
        SSqPersonAuth personAuth = SSqPersonAuth.builder()
                .account(customerPersonInfo.getCustomerId().toString()+"person")
                .name(customerPersonInfo.getName())
                .userType("1")
                .mail("")
                .mobile(customerPersonInfo.getAccount())
                .applyCert("1")
                .credential(credential).build();
        return personAuth;
    }

    /**
     * 企业打款参数构建
     *
     * @param request
     * @param customerInfo
     */
    public SSQAccountAudit buildCommitEntBack(Map<String, Object> request, CustomerInfo customerInfo) {
        SSQAccountAudit ssqAccountAudit = new SSQAccountAudit();
        ssqAccountAudit.setAccount(customerInfo.getCompanyId().toString());
        ssqAccountAudit.setBankCardName(customerInfo.getCorpName());
        ssqAccountAudit.setBankCard(request.get("bankCard").toString());
        ssqAccountAudit.setBankName(request.get("bankName").toString());
        ssqAccountAudit.setBankAreaCode(StrUtil.subAfter(request.get("bankAreaCode").toString(),",",true));
        ssqAccountAudit.setBraBankName(request.get("braBankName").toString());
        return ssqAccountAudit;
    }


}
