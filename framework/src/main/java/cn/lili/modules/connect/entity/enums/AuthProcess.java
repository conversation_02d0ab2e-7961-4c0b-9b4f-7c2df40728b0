package cn.lili.modules.connect.entity.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 实名进度
 */
public interface AuthProcess {
	Integer getProcess();

	String getDesc();

	@Getter
	@AllArgsConstructor
	enum PERSON_PROCESS implements AuthProcess {
		/**
		 * 未实名
		 */
		NO_AUTH(0, "未实名"),
		/**
		 * 实名阶段
		 */
		AUTH_START(1, "实名阶段"),
		/**
		 * 实名通过
		 */
		AUTH_SUCCESS(2, "实名通过"),
		/**
		 * 实名失败
		 */
		AUTH_FAIL(3, "实名失败");
		private Integer process;
		private String desc;
	}

	@Getter
	@AllArgsConstructor
	enum ENT_PROCESS implements AuthProcess {
		/**
		 * 未实名
		 */
		NO_AUTH(0, "未实名"),
		/**
		 * 实名阶段
		 */
		AUTH_START(1, "实名阶段"),
		/**
		 * 签署经办人授权书
		 */
		SIGN_PROCEE(2, "签署经办人授权书"),
		/**
		 * 准入审批
		 */
		ENTER_AUTH(3, "准入审批"),
		/**
		 * 实名通过
		 */
		AUTH_SUCCESS(4, "实名通过"),
		/**
		 * 实名失败
		 */
		AUTH_FAIL(5, "实名失败"),
		/**
		 * 实名驳回
		 */
		AUTH_REJECT(6, "实名驳回"),
		;
		private Integer process;
		private String desc;
	}
}
