package cn.lili.modules.connect.config;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//


import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

public class SSqEntCredential implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty("证件类型")
    private String enterpriseIdentityType;
    @ApiModelProperty("统一社会信用代码")
    private String regCode;
    @ApiModelProperty("组织机构代码")
    private String orgCode;
    @ApiModelProperty("税务登记证号")
    private String taxCode;
    @ApiModelProperty("经办人（或法定代表人）姓名")
    private String legalPerson;
    @ApiModelProperty("经办人（或法定代表人）证件号")
    private String legalPersonIdentity;
    @ApiModelProperty("经办人（或法定代表人）证件类型")
    private String legalPersonIdentityType;
    @ApiModelProperty("法定代表人或经办人手机号")
    private String legalPersonMobile;
    @ApiModelProperty("联系手机")
    private String contactMobile;
    @ApiModelProperty("联系邮箱")
    private String contactMail;
    @ApiModelProperty("省份")
    private String province;
    @ApiModelProperty("城市")
    private String city;
    @ApiModelProperty("地址")
    private String address;

    public static SSqEntCredentialBuilder builder() {
        return new SSqEntCredentialBuilder();
    }

    public String getEnterpriseIdentityType() {
        return this.enterpriseIdentityType;
    }

    public String getRegCode() {
        return this.regCode;
    }

    public String getOrgCode() {
        return this.orgCode;
    }

    public String getTaxCode() {
        return this.taxCode;
    }

    public String getLegalPerson() {
        return this.legalPerson;
    }

    public String getLegalPersonIdentity() {
        return this.legalPersonIdentity;
    }

    public String getLegalPersonIdentityType() {
        return this.legalPersonIdentityType;
    }

    public String getLegalPersonMobile() {
        return this.legalPersonMobile;
    }

    public String getContactMobile() {
        return this.contactMobile;
    }

    public String getContactMail() {
        return this.contactMail;
    }

    public String getProvince() {
        return this.province;
    }

    public String getCity() {
        return this.city;
    }

    public String getAddress() {
        return this.address;
    }

    public void setEnterpriseIdentityType(String enterpriseIdentityType) {
        this.enterpriseIdentityType = enterpriseIdentityType;
    }

    public void setRegCode(String regCode) {
        this.regCode = regCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public void setTaxCode(String taxCode) {
        this.taxCode = taxCode;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public void setLegalPersonIdentity(String legalPersonIdentity) {
        this.legalPersonIdentity = legalPersonIdentity;
    }

    public void setLegalPersonIdentityType(String legalPersonIdentityType) {
        this.legalPersonIdentityType = legalPersonIdentityType;
    }

    public void setLegalPersonMobile(String legalPersonMobile) {
        this.legalPersonMobile = legalPersonMobile;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public void setContactMail(String contactMail) {
        this.contactMail = contactMail;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SSqEntCredential)) {
            return false;
        } else {
            SSqEntCredential other = (SSqEntCredential)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label167: {
                    Object this$enterpriseIdentityType = this.getEnterpriseIdentityType();
                    Object other$enterpriseIdentityType = other.getEnterpriseIdentityType();
                    if (this$enterpriseIdentityType == null) {
                        if (other$enterpriseIdentityType == null) {
                            break label167;
                        }
                    } else if (this$enterpriseIdentityType.equals(other$enterpriseIdentityType)) {
                        break label167;
                    }

                    return false;
                }

                Object this$regCode = this.getRegCode();
                Object other$regCode = other.getRegCode();
                if (this$regCode == null) {
                    if (other$regCode != null) {
                        return false;
                    }
                } else if (!this$regCode.equals(other$regCode)) {
                    return false;
                }

                label153: {
                    Object this$orgCode = this.getOrgCode();
                    Object other$orgCode = other.getOrgCode();
                    if (this$orgCode == null) {
                        if (other$orgCode == null) {
                            break label153;
                        }
                    } else if (this$orgCode.equals(other$orgCode)) {
                        break label153;
                    }

                    return false;
                }

                Object this$taxCode = this.getTaxCode();
                Object other$taxCode = other.getTaxCode();
                if (this$taxCode == null) {
                    if (other$taxCode != null) {
                        return false;
                    }
                } else if (!this$taxCode.equals(other$taxCode)) {
                    return false;
                }

                label139: {
                    Object this$legalPerson = this.getLegalPerson();
                    Object other$legalPerson = other.getLegalPerson();
                    if (this$legalPerson == null) {
                        if (other$legalPerson == null) {
                            break label139;
                        }
                    } else if (this$legalPerson.equals(other$legalPerson)) {
                        break label139;
                    }

                    return false;
                }

                Object this$legalPersonIdentity = this.getLegalPersonIdentity();
                Object other$legalPersonIdentity = other.getLegalPersonIdentity();
                if (this$legalPersonIdentity == null) {
                    if (other$legalPersonIdentity != null) {
                        return false;
                    }
                } else if (!this$legalPersonIdentity.equals(other$legalPersonIdentity)) {
                    return false;
                }

                label125: {
                    Object this$legalPersonIdentityType = this.getLegalPersonIdentityType();
                    Object other$legalPersonIdentityType = other.getLegalPersonIdentityType();
                    if (this$legalPersonIdentityType == null) {
                        if (other$legalPersonIdentityType == null) {
                            break label125;
                        }
                    } else if (this$legalPersonIdentityType.equals(other$legalPersonIdentityType)) {
                        break label125;
                    }

                    return false;
                }

                label118: {
                    Object this$legalPersonMobile = this.getLegalPersonMobile();
                    Object other$legalPersonMobile = other.getLegalPersonMobile();
                    if (this$legalPersonMobile == null) {
                        if (other$legalPersonMobile == null) {
                            break label118;
                        }
                    } else if (this$legalPersonMobile.equals(other$legalPersonMobile)) {
                        break label118;
                    }

                    return false;
                }

                Object this$contactMobile = this.getContactMobile();
                Object other$contactMobile = other.getContactMobile();
                if (this$contactMobile == null) {
                    if (other$contactMobile != null) {
                        return false;
                    }
                } else if (!this$contactMobile.equals(other$contactMobile)) {
                    return false;
                }

                label104: {
                    Object this$contactMail = this.getContactMail();
                    Object other$contactMail = other.getContactMail();
                    if (this$contactMail == null) {
                        if (other$contactMail == null) {
                            break label104;
                        }
                    } else if (this$contactMail.equals(other$contactMail)) {
                        break label104;
                    }

                    return false;
                }

                label97: {
                    Object this$province = this.getProvince();
                    Object other$province = other.getProvince();
                    if (this$province == null) {
                        if (other$province == null) {
                            break label97;
                        }
                    } else if (this$province.equals(other$province)) {
                        break label97;
                    }

                    return false;
                }

                Object this$city = this.getCity();
                Object other$city = other.getCity();
                if (this$city == null) {
                    if (other$city != null) {
                        return false;
                    }
                } else if (!this$city.equals(other$city)) {
                    return false;
                }

                Object this$address = this.getAddress();
                Object other$address = other.getAddress();
                if (this$address == null) {
                    if (other$address != null) {
                        return false;
                    }
                } else if (!this$address.equals(other$address)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SSqEntCredential;
    }

    public int hashCode() {
        boolean PRIME = true;
        int result = 1;
        Object $enterpriseIdentityType = this.getEnterpriseIdentityType();
        result = result * 59 + ($enterpriseIdentityType == null ? 43 : $enterpriseIdentityType.hashCode());
        Object $regCode = this.getRegCode();
        result = result * 59 + ($regCode == null ? 43 : $regCode.hashCode());
        Object $orgCode = this.getOrgCode();
        result = result * 59 + ($orgCode == null ? 43 : $orgCode.hashCode());
        Object $taxCode = this.getTaxCode();
        result = result * 59 + ($taxCode == null ? 43 : $taxCode.hashCode());
        Object $legalPerson = this.getLegalPerson();
        result = result * 59 + ($legalPerson == null ? 43 : $legalPerson.hashCode());
        Object $legalPersonIdentity = this.getLegalPersonIdentity();
        result = result * 59 + ($legalPersonIdentity == null ? 43 : $legalPersonIdentity.hashCode());
        Object $legalPersonIdentityType = this.getLegalPersonIdentityType();
        result = result * 59 + ($legalPersonIdentityType == null ? 43 : $legalPersonIdentityType.hashCode());
        Object $legalPersonMobile = this.getLegalPersonMobile();
        result = result * 59 + ($legalPersonMobile == null ? 43 : $legalPersonMobile.hashCode());
        Object $contactMobile = this.getContactMobile();
        result = result * 59 + ($contactMobile == null ? 43 : $contactMobile.hashCode());
        Object $contactMail = this.getContactMail();
        result = result * 59 + ($contactMail == null ? 43 : $contactMail.hashCode());
        Object $province = this.getProvince();
        result = result * 59 + ($province == null ? 43 : $province.hashCode());
        Object $city = this.getCity();
        result = result * 59 + ($city == null ? 43 : $city.hashCode());
        Object $address = this.getAddress();
        result = result * 59 + ($address == null ? 43 : $address.hashCode());
        return result;
    }

    public String toString() {
        return "SSqEntCredential(enterpriseIdentityType=" + this.getEnterpriseIdentityType() + ", regCode=" + this.getRegCode() + ", orgCode=" + this.getOrgCode() + ", taxCode=" + this.getTaxCode() + ", legalPerson=" + this.getLegalPerson() + ", legalPersonIdentity=" + this.getLegalPersonIdentity() + ", legalPersonIdentityType=" + this.getLegalPersonIdentityType() + ", legalPersonMobile=" + this.getLegalPersonMobile() + ", contactMobile=" + this.getContactMobile() + ", contactMail=" + this.getContactMail() + ", province=" + this.getProvince() + ", city=" + this.getCity() + ", address=" + this.getAddress() + ")";
    }

    public SSqEntCredential(String enterpriseIdentityType, String regCode, String orgCode, String taxCode, String legalPerson, String legalPersonIdentity, String legalPersonIdentityType, String legalPersonMobile, String contactMobile, String contactMail, String province, String city, String address) {
        this.enterpriseIdentityType = enterpriseIdentityType;
        this.regCode = regCode;
        this.orgCode = orgCode;
        this.taxCode = taxCode;
        this.legalPerson = legalPerson;
        this.legalPersonIdentity = legalPersonIdentity;
        this.legalPersonIdentityType = legalPersonIdentityType;
        this.legalPersonMobile = legalPersonMobile;
        this.contactMobile = contactMobile;
        this.contactMail = contactMail;
        this.province = province;
        this.city = city;
        this.address = address;
    }

    public SSqEntCredential() {
    }

    public static class SSqEntCredentialBuilder {
        private String enterpriseIdentityType;
        private String regCode;
        private String orgCode;
        private String taxCode;
        private String legalPerson;
        private String legalPersonIdentity;
        private String legalPersonIdentityType;
        private String legalPersonMobile;
        private String contactMobile;
        private String contactMail;
        private String province;
        private String city;
        private String address;

        SSqEntCredentialBuilder() {
        }

        public SSqEntCredentialBuilder enterpriseIdentityType(String enterpriseIdentityType) {
            this.enterpriseIdentityType = enterpriseIdentityType;
            return this;
        }

        public SSqEntCredentialBuilder regCode(String regCode) {
            this.regCode = regCode;
            return this;
        }

        public SSqEntCredentialBuilder orgCode(String orgCode) {
            this.orgCode = orgCode;
            return this;
        }

        public SSqEntCredentialBuilder taxCode(String taxCode) {
            this.taxCode = taxCode;
            return this;
        }

        public SSqEntCredentialBuilder legalPerson(String legalPerson) {
            this.legalPerson = legalPerson;
            return this;
        }

        public SSqEntCredentialBuilder legalPersonIdentity(String legalPersonIdentity) {
            this.legalPersonIdentity = legalPersonIdentity;
            return this;
        }

        public SSqEntCredentialBuilder legalPersonIdentityType(String legalPersonIdentityType) {
            this.legalPersonIdentityType = legalPersonIdentityType;
            return this;
        }

        public SSqEntCredentialBuilder legalPersonMobile(String legalPersonMobile) {
            this.legalPersonMobile = legalPersonMobile;
            return this;
        }

        public SSqEntCredentialBuilder contactMobile(String contactMobile) {
            this.contactMobile = contactMobile;
            return this;
        }

        public SSqEntCredentialBuilder contactMail(String contactMail) {
            this.contactMail = contactMail;
            return this;
        }

        public SSqEntCredentialBuilder province(String province) {
            this.province = province;
            return this;
        }

        public SSqEntCredentialBuilder city(String city) {
            this.city = city;
            return this;
        }

        public SSqEntCredentialBuilder address(String address) {
            this.address = address;
            return this;
        }

        public SSqEntCredential build() {
            return new SSqEntCredential(this.enterpriseIdentityType, this.regCode, this.orgCode, this.taxCode, this.legalPerson, this.legalPersonIdentity, this.legalPersonIdentityType, this.legalPersonMobile, this.contactMobile, this.contactMail, this.province, this.city, this.address);
        }

        public String toString() {
            return "SSqEntCredential.SSqEntCredentialBuilder(enterpriseIdentityType=" + this.enterpriseIdentityType + ", regCode=" + this.regCode + ", orgCode=" + this.orgCode + ", taxCode=" + this.taxCode + ", legalPerson=" + this.legalPerson + ", legalPersonIdentity=" + this.legalPersonIdentity + ", legalPersonIdentityType=" + this.legalPersonIdentityType + ", legalPersonMobile=" + this.legalPersonMobile + ", contactMobile=" + this.contactMobile + ", contactMail=" + this.contactMail + ", province=" + this.province + ", city=" + this.city + ", address=" + this.address + ")";
        }
    }
}
