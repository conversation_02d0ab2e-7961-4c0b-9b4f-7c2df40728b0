/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.connect.serviceimpl;

import cn.hutool.core.util.DesensitizedUtil;
import cn.lili.modules.connect.entity.AuthStatus;
import cn.lili.modules.connect.entity.CustomerPersonInfo;
import cn.lili.modules.connect.mapper.CustomerPersonInfoMapper;
import cn.lili.modules.connect.service.ICustomerPersonInfoService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 个人客户信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-03-03
 */
@Service
@AllArgsConstructor
public class CustomerPersonInfoServiceImpl extends ServiceImpl<CustomerPersonInfoMapper, CustomerPersonInfo> implements ICustomerPersonInfoService {
//
//    private final IAttachService attachService;
//
//    @Override
//    public IPage<CustomerPersonInfoVO> selectCustomerPersonInfoPage(IPage<CustomerPersonInfoVO> page, CustomerPersonInfoVO customerPersonInfo) {
//        return page.setRecords(baseMapper.selectCustomerPersonInfoPage(page, customerPersonInfo));
//    }
//
//    @Override
//    public CustomerPersonInfoVO getPersonInfoByCustomerId(Long customerId) {
//        CustomerPersonInfoVO customerPersonInfoVO = BeanUtil.copy(this.lambdaQuery().eq(CustomerPersonInfo::getCustomerId, customerId).one(), CustomerPersonInfoVO.class);
//        if (ObjectUtil.isEmpty(customerPersonInfoVO)) {
//            return customerPersonInfoVO;
//        }
//        //身份证识别
//        customerPersonInfoVO.setBirthday(IdCardUtils.cusBirthday(customerPersonInfoVO.getIdentity()));
//        return customerPersonInfoVO;
//    }
//
//    @Override
//    public CustomerPersonInfoVO selectVagueInfoByCustomerId(Long customerId) {
//        CustomerPersonInfoVO customerPersonInfoVO = BeanUtil.copy(this.lambdaQuery().eq(CustomerPersonInfo::getCustomerId, customerId).one(), CustomerPersonInfoVO.class);
//        if (ObjectUtil.isEmpty(customerPersonInfoVO)) {
//            return customerPersonInfoVO;
//        }
//        customerPersonInfoVO.setBirthday(IdCardUtils.cusBirthday(customerPersonInfoVO.getIdentity()));
//        customerPersonInfoVO.setIdentity(DesensitizedUtil.idCardNum(customerPersonInfoVO.getIdentity(), 3, 2));
//        customerPersonInfoVO.setAddress(DesensitizedUtil.idCardNum(customerPersonInfoVO.getAddress(), 3, 2));
//        customerPersonInfoVO.setBankPhone(DesensitizedUtil.idCardNum(customerPersonInfoVO.getBankPhone(), 3, 2));
//        customerPersonInfoVO.setBankCardNo(DesensitizedUtil.idCardNum(customerPersonInfoVO.getBankCardNo(), 3, 2));
//        return customerPersonInfoVO;
//
//    }
//
    @Override
    public CustomerPersonInfo isAuthOrAuthing(String idCard) {
        CustomerPersonInfo customerPersonInfo = this.getOne(Wrappers.<CustomerPersonInfo>lambdaQuery().eq(CustomerPersonInfo::getIdentity, idCard)
                .ne(CustomerPersonInfo::getAuthStatus, AuthStatus.PersonAuthStatus.NO_AUTH));
        return customerPersonInfo;
    }


    @Override
    public CustomerPersonInfo getAuthingOrAuthedByCustomerId(Long customerId) {
        CustomerPersonInfo personInfo = getOne(Wrappers.<CustomerPersonInfo>lambdaQuery()
                .eq(CustomerPersonInfo::getCustomerId, customerId)
                .ne(CustomerPersonInfo::getAuthStatus, AuthStatus.PersonAuthStatus.NO_AUTH)
                .last("limit 1"));
        return personInfo;
    }
//
//    @Override
//    public PersonStatusAuthDTO queryPersonAuth() {
//        return null;
//    }
//
//    @Override
//    public Boolean updatePersonInfoAndUserInfo(CustomerPersonInfo personInfo) {
//        personInfo.setAuthStatus(SSQPersonAuthStatus.AUTH_PASS.getCode());
//        saveOrUpdate(personInfo);
//        Long userId = personInfo.getCustomerId();
//        String name = personInfo.getName();
//        SpringUtil.getBean(ICustomerFrontUserTypeService.class).success(userId);
//        ICustomerService bean = SpringUtil.getBean(ICustomerService.class);
//        Customer byUserId = bean.getByUserId(userId);
//        if (ObjectUtil.isNotEmpty(byUserId)) {
//            //0-未实名 1-实名
//            byUserId.setStatus(1);
//            byUserId.setName(name);
//            bean.updateById(byUserId);
//        }
//        return true;
//    }
//
//    @Override
//    public R queryPersonAuthByUserId(Long userId) {
//        CustomerPersonInfo personInfo = getByCustomerId(userId);
//        if (ObjectUtil.isEmpty(personInfo)) {
//            return R.data(null);
//        }
//        //实名成功返回
//        CustomerPersonInfoVO personInfoVO = CustomerPersonInfoWrapper.build().entityVO(personInfo);
//        if (AuthProcess.PERSON_PROCESS.AUTH_SUCCESS.getProcess().equals(personInfoVO.getProcess())) {
//            return R.data(personInfoVO);
//        }
//        //回显旧数据供用户修改
//        List<Long> attachIds = new ArrayList<>(2);
//        attachIds.add(Func.toLong(personInfo.getIdentityFacefileAttachid()));
//        attachIds.add(Func.toLong(personInfo.getIdentityBackfileAttachid()));
//        Map<Long, String> attachMap = StreamUtil.toMap(attachService.listByIds(attachIds), Attach::getId, Attach::getLink);
//        personInfoVO.setAttachMap(attachMap);
//        return R.data(personInfoVO);
//    }
//
//    @Override
//    public String personBindCompany(String entName, String dev, String ssqAccount, String returnUrl) {
//        return null;
//    }
//
//    @Override
//    public CustomerPersonInfo getByNameAndPhone(String name, String phone) {
//        CustomerPersonInfo one = getOne(Wrappers.<CustomerPersonInfo>lambdaQuery().eq(CustomerPersonInfo::getName, name)
//                .eq(CustomerPersonInfo::getAccount, phone)
//                //TODO 实名修改结束后打开
//                .last("limit 1"));
//        return one;
//    }
//
    @Override
    public CustomerPersonInfo getByCustomerId(Long customerId) {
        CustomerPersonInfo personInfo = getOne(Wrappers.<CustomerPersonInfo>lambdaQuery()
                .eq(CustomerPersonInfo::getCustomerId, customerId)
                .last("limit 1"));
        return personInfo;
    }
//
//    @Override
//    public CustomerPersonInfo getByPhone(String phone) {
//        CustomerPersonInfo personInfo = getOne(Wrappers.<CustomerPersonInfo>lambdaQuery()
//                .eq(CustomerPersonInfo::getAccount, phone)
//                .ne(CustomerPersonInfo::getAuthStatus, AuthStatus.PersonAuthStatus.NO_AUTH)
//                .last("limit 1"));
//        return personInfo;
//    }
//
//    @Override
//    public void deleteReal(Long userId) {
//        baseMapper.deleteReal(userId);
//    }
//
}
