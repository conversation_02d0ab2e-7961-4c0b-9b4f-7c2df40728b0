/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.modules.connect.entity;

import cn.lili.modules.connect.entity.vo.CustomerPersonInfoVO;
import cn.lili.mybatis.BaseEntity;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;


/**
 * 企业信息名称实体类
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "CustomerInfo对象", description = "企业信息名称")
@TableName("li_customer_info")
@AllArgsConstructor
@NoArgsConstructor
public class CustomerInfo extends BaseEntity {

	private static final long serialVersionUID = 1L;



	/**
	 * 会员表id
	 */
	@ApiModelProperty(value = "会员id")
	private Long customerId;
	/**
	 * 企业名称
	 */

	@ApiModelProperty(value = "企业名称")
	private String corpName;
	/**
	 * 工商注册号
	 */

	@ApiModelProperty(value = "工商注册号")
	private String businessLicenceNumber;
	/**
	 * 工商号图片id
	 */

	@ApiModelProperty(value = "工商号图片id")
	private String registPicAttchId;
	/**
	 * 法人姓名
	 */

	@ApiModelProperty(value = "法人姓名")
	private String corporationName;
	/**
	 * 法人证件号
	 */

	@ApiModelProperty(value = "法人证件号")
	private String corporationIdCardNumber;
	/**
	 * 法人性别;0 男 1 女  2 不展示
	 */

	@ApiModelProperty(value = "法人性别;0 男 1 女  2 不展示")
	private Integer corporationSex;
	/**
	 * 法人国家
	 */

	@ApiModelProperty(value = "法人国家")
	private String corporationCountry;
	/**
	 * 法人名族
	 */

	@ApiModelProperty(value = "法人名族")
	private String corporationNation;
	/**
	 * 法人证件有效期
	 */

	@ApiModelProperty(value = "法人证件有效期")
	private String corporationValidTime;
	/**
	 * 法人居住地
	 */

	@ApiModelProperty(value = "法人居住地")
	private String corporationAddress;
	/**
	 * 法人手机号
	 */

	@ApiModelProperty(value = "法人手机号")
	private String mobile;
	/**
	 * 法人证件号附件id
	 */

	@ApiModelProperty(value = "法人证件号附件id")
	private String leagalNoAttachId;
	/**
	 * 经办人姓名
	 */

	@ApiModelProperty(value = "经办人姓名")
	private String operatorName;
	/**
	 * 经办人证件号
	 */

	@ApiModelProperty(value = "经办人证件号")
	private String operatorIdcard;
	/**
	 * 经办人性别;0 男 1 女  2 不展示
	 */

	@ApiModelProperty(value = "经办人性别;0 男 1 女  2 不展示")
	@TableField(updateStrategy = FieldStrategy.IGNORED)
	private Integer operatorSex;
	/**
	 * 经办人国籍
	 */

	@ApiModelProperty(value = "经办人国籍")
	private String operatorCountry;
	/**
	 * 经办人名族
	 */

	@ApiModelProperty(value = "经办人名族")
	private String operatorNation;
	/**
	 * 经办人证件有效期
	 */

	@ApiModelProperty(value = "经办人证件有效期")
	private String operatorValidTime;
	/**
	 * 经办人居住地
	 */

	@ApiModelProperty(value = "经办人居住地")
	private String operatorAddress;
	/**
	 * 经办人手机号
	 */

	@ApiModelProperty(value = "经办人手机号")
	private String operatorPhone;
	/**
	 * 经办人附件id
	 */

	@ApiModelProperty(value = "经办人附件id")
	private String operatorAttachId;
	/**
	 * 法人证件类型-若证件类型为“0”、法人证件号码为身份证号码，证件类型为“1”、法人证件号码为护照号码
	 */

	@ApiModelProperty(value = "法人证件类型-若证件类型为“0”、法人证件号码为身份证号码，证件类型为“1”、法人证件号码为护照号码")
	private String corporationIdType;


	/**
	 * 法人邮件
	 */

	@ApiModelProperty(value = "法人邮件")
	private String corporationEmail;


	/**
	 * 法人证件正面id
	 */

	@ApiModelProperty(value = "法人证件正面id")
	private String corporationFaceAttachId;
	/**
	 * 法人证件反面id
	 */

	@ApiModelProperty(value = "法人证件反面id")
	private String corporationBackAttachId;


	/**
	 * 经手人证件正面id
	 */

	@ApiModelProperty(value = "经手人证件正面id")
	private String operatorFaceAttachId;


	/**
	 * 经手人证件反面id
	 */

	@ApiModelProperty(value = "经手人证件反面id")
	private String operatorBackAttachId;


	/**
	 * 公司营业证
	 */

	@ApiModelProperty(value = "公司营业证附件id")
	private String businessLicenceAttachId;
	/**
	 * 公司营业证id
	 */

	@ApiModelProperty(value = "公司营业证id")
	private Long businessLicenceId;
	/**
	 * 公司经营状况
	 */

	@ApiModelProperty(value = "公司经营状况")
	private String operationStatus;


	/**
	 * 经营期限自
	 */

	@ApiModelProperty(value = " 经营期限自")
	private String operationFrom;

	/**
	 * 经营期限至
	 */

	@ApiModelProperty(value = "经营期限至")
	private String operationTo;


	@ApiModelProperty(value = "企业id")

	private Long companyId;
	/**
	 * 企业实名状态 0：未认证； 1： 审核中 ；2：认证通过 ；3：认证不通过 ；4:意愿性认证中
	 */
	@ApiModelProperty(value = "企业实名状态")
	private Integer authStatus;
	/**
	 * 是否为法人认证 1：是；0:否
	 */
	@ApiModelProperty(value = "是否为法人认证")
	private Integer legalPersonFlag;
	/**
	 * 企业认证类型 2 融资企业 3 核心企业
	 */
	private Integer entAuthType;
	/**
	 * 企业logo
	 */
	private String logo;
	/**
	 * 经办人是否签署授权书 1 未签署 2 签署
	 */
	private Integer startSign;
	/**
	 * 合同编号
	 */
	private String contractNo;
	/**
	 * 工商信息id
	 */
	private Long businessInfoId;
	/**
	 * 准入状态 1 未通过 2 通过
	 */
	private Integer enterStatus;
	/**
	 * 实名方式
	 */
	private Integer authType;
	/**
	 * 异步任务id
	 */
	private String taskId;
	/**
	 * 开通进度
	 */
	private Integer process;
	/**
	 * api供应商
	 */
	private String apiSupplier;
	/**
	 * 对公银行卡号
	 */
	private String bankCard;
	/**
	 * 银行名称
	 */
	private String bankName;
	/**
	 * 银行区划编号
	 */
	private String bankAreaCode;
	/**
	 * 银行支行
	 */
	private String braBankName;

	/**
	 * 省份拼音简写
	 */
	private String base;


	/**
	 * 个人实名异步任务id
	 */
	private String userTaskId;
}
