package cn.lili.modules.connect.entity.dto;


public class SSQAccountAudit {
    private String account;
    private String bankCard;
    private String bankCardName;
    private String bankName;
    private String bankAreaCode;
    private String braBankName;

    public SSQAccountAudit() {
    }

    public String getAccount() {
        return this.account;
    }

    public String getBankCard() {
        return this.bankCard;
    }

    public String getBankCardName() {
        return this.bankCardName;
    }

    public String getBankName() {
        return this.bankName;
    }

    public String getBankAreaCode() {
        return this.bankAreaCode;
    }

    public String getBraBankName() {
        return this.braBankName;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public void setBankCard(String bankCard) {
        this.bankCard = bankCard;
    }

    public void setBankCardName(String bankCardName) {
        this.bankCardName = bankCardName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public void setBankAreaCode(String bankAreaCode) {
        this.bankAreaCode = bankAreaCode;
    }

    public void setBraBankName(String braBankName) {
        this.braBankName = braBankName;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof SSQAccountAudit)) {
            return false;
        } else {
            SSQAccountAudit other = (SSQAccountAudit)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                Object this$account = this.getAccount();
                Object other$account = other.getAccount();
                if (this$account == null) {
                    if (other$account != null) {
                        return false;
                    }
                } else if (!this$account.equals(other$account)) {
                    return false;
                }

                Object this$bankCard = this.getBankCard();
                Object other$bankCard = other.getBankCard();
                if (this$bankCard == null) {
                    if (other$bankCard != null) {
                        return false;
                    }
                } else if (!this$bankCard.equals(other$bankCard)) {
                    return false;
                }

                Object this$bankCardName = this.getBankCardName();
                Object other$bankCardName = other.getBankCardName();
                if (this$bankCardName == null) {
                    if (other$bankCardName != null) {
                        return false;
                    }
                } else if (!this$bankCardName.equals(other$bankCardName)) {
                    return false;
                }

                Object this$bankName = this.getBankName();
                Object other$bankName = other.getBankName();
                if (this$bankName == null) {
                    if (other$bankName != null) {
                        return false;
                    }
                } else if (!this$bankName.equals(other$bankName)) {
                    return false;
                }

                Object this$bankAreaCode = this.getBankAreaCode();
                Object other$bankAreaCode = other.getBankAreaCode();
                if (this$bankAreaCode == null) {
                    if (other$bankAreaCode != null) {
                        return false;
                    }
                } else if (!this$bankAreaCode.equals(other$bankAreaCode)) {
                    return false;
                }

                Object this$braBankName = this.getBraBankName();
                Object other$braBankName = other.getBraBankName();
                if (this$braBankName == null) {
                    if (other$braBankName != null) {
                        return false;
                    }
                } else if (!this$braBankName.equals(other$braBankName)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof SSQAccountAudit;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Object $account = this.getAccount();
        result = result * 59 + ($account == null ? 43 : $account.hashCode());
        Object $bankCard = this.getBankCard();
        result = result * 59 + ($bankCard == null ? 43 : $bankCard.hashCode());
        Object $bankCardName = this.getBankCardName();
        result = result * 59 + ($bankCardName == null ? 43 : $bankCardName.hashCode());
        Object $bankName = this.getBankName();
        result = result * 59 + ($bankName == null ? 43 : $bankName.hashCode());
        Object $bankAreaCode = this.getBankAreaCode();
        result = result * 59 + ($bankAreaCode == null ? 43 : $bankAreaCode.hashCode());
        Object $braBankName = this.getBraBankName();
        result = result * 59 + ($braBankName == null ? 43 : $braBankName.hashCode());
        return result;
    }

    public String toString() {
        return "SSQAccountAudit(account=" + this.getAccount() + ", bankCard=" + this.getBankCard() + ", bankCardName=" + this.getBankCardName() + ", bankName=" + this.getBankName() + ", bankAreaCode=" + this.getBankAreaCode() + ", braBankName=" + this.getBraBankName() + ")";
    }
}
