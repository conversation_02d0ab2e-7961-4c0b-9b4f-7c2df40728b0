package cn.lili.modules.connect.config;

//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//



import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.oss.ServiceException;

import org.springframework.stereotype.Component;
import java.util.HashMap;
import java.util.Map;

@Component
public class SkyEyeConnector {


    private static final String HOST = "http://open.api.tianyancha.com";

    private static final String TOKEN = "e5a08037-848c-4c6d-ab4b-6d3a410171f0";

    private static final String AUTHORIZATION = "Authorization";
    private static final Integer RESULT_SUCCESS_CODE = 0;
//    private final IOthersApiService othersApiService;
//
//
    private SkyEysConfig getBestSignCommon() {

        SkyEysConfig skyEysConfig = SkyEysConfig.builder().host(HOST).token(TOKEN).build();
        return skyEysConfig;
    }

    private Integer getSuccess(SkyEyeResult returnResult) {
        return RESULT_SUCCESS_CODE.equals(returnResult.getError_code()) ? 1 : 0;
    }



    private void dealErrorResult(SkyEyeResult ssqResult) {
        Integer success = this.getSuccess(ssqResult);
        if (success == 0) {
            throw new ServiceException(ssqResult.getReason());
        }
    }

    public SkyEyeResult get(String url, Map<String, Object> requestData) {
        return this.get(url, requestData, true);
    }


    public SkyEyeResult get(String url, Map<String, Object> requestData, Boolean dealError) {
        SkyEysConfig skyEysConfig = getBestSignCommon();
        String host = skyEysConfig.getHost();
        String reallyUrl = host + url;
        //获取头部参数 发送请求
        String result1 = null;
       // requestData.put("Authorization", TOKEN);
        Map<String, String> headers = getHeader(HOST, TOKEN);
         result1 = HttpUtil.createGet(reallyUrl)
                .addHeaders(headers)
                .form(requestData)
                .execute()
                .body();

       // result = HttpUtil.get(reallyUrl, requestData);
        SkyEyeResult skyEyeResult = (SkyEyeResult) JSONUtil.toBean(result1, SkyEyeResult.class, true);
        if (dealError) {
            this.dealErrorResult(skyEyeResult);
        }
        return skyEyeResult;
    }

    private Map<String, String> getHeader(String host,String token) {
        Map<String, String> header = new HashMap();
        header.put("Authorization", token);
        return header;
    }

//    public SkyEyeConnector(IOthersApiService othersApiService) {
//        this.othersApiService = othersApiService;
//
//    }
}
