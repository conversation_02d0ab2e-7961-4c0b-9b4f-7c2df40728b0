package cn.lili.modules.connect.entity;



import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 实名认证状态
 *
 * <AUTHOR>
 */

public interface AuthStatus {

    @AllArgsConstructor
    @Getter
    enum PersonAuthStatus {
       /*
         0 ：未认证 1： 认证中 2：认证通过 -1：处理中
        */

        NO_AUTH(0, "未认证"),

        AUTHING(1, "认证中"),


        AUTH_PASS(2, "认证通过"),

        AUTH_PROCESSING(-1, "认证处理中");
        private Integer status;

        private String desc;

    }

    @AllArgsConstructor
    @Getter
    enum CompanyAuthStatus {
       /*
         0 ：未认证 1： 认证中 2：认证通过 -1：处理中 4 认证失败
        */

        NO_AUTH(0, "未认证"),

        AUTHING(1, "认证中"),


        AUTH_PASS(2, "认证通过"),


        AUTH_PROCESSING(-1, "认证处理中");


        private Integer status;

        private String desc;

    }

    /**
     * 实名人
     */
    @AllArgsConstructor
    @Getter
    enum LegalPersonFlag {
        /**
         * 未开始
         */
        LEGAL_PERSON(1, "法人实名"),
        /**
         * 开始
         */
        MANAGER(0, "经办人实名");
        private Integer status;

        private String desc;
    }

    /**
     * 取消开通状态
     */
    @AllArgsConstructor
    @Getter
    enum CANCEL_STATUS {
        /**
         * 未取消
         */
        NO_CANCEL(1, "未取消"),
        /**
         * 取消
         */
        CANCEL(2, "取消");
        private Integer status;
        private String desc;
    }

    /**
     * 是否通过审批 1 未通过 2 通过
     */
    @AllArgsConstructor
    @Getter
    enum ENTER_STATUS {
        /**
         * 未通过
         */
        NOT_ENTER(1, "未通过"),
        /**
         * 通过
         */
        ENTER(2, "2通过");
        private Integer status;
        private String desc;
    }

    /**
     * 开通状态 1开通中 2 已开通 3开通失败
     */
    @AllArgsConstructor
    @Getter
    enum OPEN_STATUS {
        /**
         * 开通中
         */
        OPENGING(1, "开通中"),
        /**
         * 已开通
         */
        OPENED(2, "已开通"),
        /**
         * 开通失败
         */
        OPEN_FAIL(3, "开通失败");
        private Integer status;
        private String desc;
    }

    /**
     * 实名方式
     */
    @AllArgsConstructor
    @Getter
    enum AUTH_TYPE {
        /**
         * 手机短信
         */
        PHONE(1, "手机短信", "phone"),
        /**
         * 面部认证
         */
        FACE(2, "面部认证", "face"),
        /**
         * 银行认证
         */
        BANK(3, "银行卡认证", "bank"),
        /**
         * 微信小程序人脸识别
         */
        WXAPPLET_FACE(4, "微信小程序人脸识别", "wxapplet-face"),
        /**
         * 人脸对比
         */
        FACE_CONTRAST(4, "人脸对比", "face-contrast"),
        /**
         * h5人脸认证
         */
        H5_FACE(6, "H5人脸认证", "h5-face"),
        ;


        private Integer status;
        private String desc;
        private String code;

        public static String matchEnum(Integer status) {
            for (AUTH_TYPE statusEnum : AUTH_TYPE.values()) {
                if (statusEnum.getStatus().equals(status)) {
                    return statusEnum.getCode();
                }
            }
            return null;
        }
    }

    /**
     * 当前节点 1已实名，2待审批,3准入失败，4准入成功
     */
    @AllArgsConstructor
    @Getter
    enum CURRENT_NODE {
        /**
         * 待实名
         */
        READY_TO_AUTH(1, "1已实名"),
        /**
         * 正在实名
         */
        AUTHING(2, "2待审批"),
        /**
         * 3已实名
         */
        AUTHED(3, "3准入失败"),
        /**
         * 实名失败
         */
        AUTH_FAIL(4, "4准入成功");
        private Integer status;
        private String desc;
    }
}
