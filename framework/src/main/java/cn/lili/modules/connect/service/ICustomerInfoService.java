package cn.lili.modules.connect.service;

/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */


import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.connect.entity.CustomerInfo;
import cn.lili.modules.connect.entity.dto.CustomerAndPersonInfoDTO;
import cn.lili.modules.connect.entity.vo.CompanyAndBankVO;
import cn.lili.modules.connect.entity.vo.CustomerPersonInfoVO;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * 企业信息名称 服务类
 *
 * <AUTHOR>
 * @since 2022-01-06
 */
public interface ICustomerInfoService extends IService<CustomerInfo> {

 ResultMessage commitEntAuthInfo(CustomerAndPersonInfoDTO customerInfo, Long userId, String returnUrl) throws Throwable;

//天眼查企业三要素
 boolean companyThreeElementsIsTure(String creditCode, String companyName, String legalPersonName);

// SupplierBusinessInfo saveBusinessInfo(String var1, Duration var2);

 /**
  * 选择实名方式提交资料进行企业实名认证
  *
  * @param request 请求参数
  * @param type    1、短信 2、人脸 3、银行 6、h5人脸识别
  * @return
  */
 ResultMessage commitEntAuthInfoByType(Map<String, Object> request, Integer type);

 /**
  * 主动查询企业实名情况
  *
  * @param customerId 用户id
  * @param id         实名id
  * @return
  */
 ResultMessage queryEntAuthByCustomerId(Long customerId, Long id);

 /**
  * 更新企业实名信息 并创建企业
  *
  * @param customerInfo
  * @return
  */
 CustomerInfo updateEntInfoAndUserInfo(CustomerInfo customerInfo);

 /**
  * 根据会员id查询客户信息
  *
  * @param customerId
  * @return
  */
 CustomerInfo getBuyCustomerId(String customerId);

 /**
  * 查询是否需要签署授权书通过CustomerId
  *
  * @param customerId 用户id
  * @param id         实名id
  * @return
  */
// ResultMessage checkNeedSignByCustomerId(Long customerId, Long id);

 /**
  * 根据合同模板名称生成合同 该名称在上上签平台是唯一的 否则返回最新一条
  *
  * @param contractName 合同模板名称
  * @param dataSource   字段填充数据源
  * @param bizNo        合同流水号 标识该合同业务 支持64位
  * @param account      与上上签账号绑定的账户信息
  * @return 合同编号 用于跳转签署
  */
// String generateContractByNameAndDataSource(String contractName, Map<String, Object> dataSource, String bizNo, String account, CustomerInfo customerInfo);

 /**
  * 提交个人基础实名信息
  *
  * @param customerPersonInfo 实名信息
  * @return
  */
 ResultMessage commitPersonAuthInfo(CustomerPersonInfoVO customerPersonInfo);

 /**
  * 提交资料进行
  *
  * @param request 请求参数
  * @param type    1、短信 2、人脸 3、银行 6、h5人脸
  * @return
  */
 ResultMessage verifyEntAuthInfoByType(Map<String, Object> request, Long customerId, Integer type);

 /**
  * 查询当前用户企业、银行信息
  *
  * @param userId 会员Id
  */
 CompanyAndBankVO getCompanyAndBankInfo(String userId);

}
