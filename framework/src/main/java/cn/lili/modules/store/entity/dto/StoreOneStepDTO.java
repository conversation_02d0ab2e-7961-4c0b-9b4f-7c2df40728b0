package cn.lili.modules.store.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.*;

/**
 * 店铺入驻只有一步信息
 *
 * <AUTHOR>
 * @since 2020/12/7 16:16
 */
@Data
public class StoreOneStepDTO {
    @Size(min = 2, max = 200, message = "店铺名称个数需要在6-200位")
    @NotBlank(message = "店铺名称不能为空")
    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺logo")
    private String storeLogo;

    @Size(min = 6, max = 200, message = "店铺简介个数需要在6-200位")
    @NotBlank(message = "店铺简介不能为空")
    @ApiModelProperty(value = "店铺简介")
    private String storeDesc;

    @ApiModelProperty(value = "经纬度")
    @NotEmpty
    private String storeCenter;

    @NotBlank(message = "店铺经营类目不能为空")
    @ApiModelProperty(value = "店铺经营类目")
    private String goodsManagementCategory;

    @NotBlank(message = "地址不能为空")
    @ApiModelProperty(value = "地址名称， '，'分割")
    private String storeAddressPath;

    @NotBlank(message = "地址ID不能为空")
    @ApiModelProperty(value = "地址id，'，'分割 ")
    private String storeAddressIdPath;

    @NotBlank(message = "地址详情")
    @ApiModelProperty(value = "地址详情")
    private String storeAddressDetail;

    @Length(min = 1, max = 200)
    @NotBlank(message = "结算银行开户行名称不能为空")
    @ApiModelProperty(value = "结算银行开户行名称")
    private String settlementBankAccountName;

    @Length(min = 1, max = 200)
    @NotBlank(message = "结算银行开户账号不能为空")
    @ApiModelProperty(value = "结算银行开户账号")
    private String settlementBankAccountNum;

    @ApiModelProperty(value = "结算银行开户支行名称")
    private String settlementBankBranchName;

    @Length(min = 1, max = 50)
    @NotBlank(message = "结算银行支行联行号不能为空")
    @ApiModelProperty(value = "结算银行支行联行号")
    private String settlementBankJointName;

    @ApiModelProperty(value = "开户银行许可证电子版")
    private String settlementBankLicencePhoto;

    @Length(min = 2, max = 20)
    @NotBlank(message = "联系人姓名为空")
    @ApiModelProperty(value = "联系人姓名")
    private String linkName;

    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^[1][3,4,5,6,7,8,9][0-9]{9}$", message = "手机号格式有误")
    @ApiModelProperty(value = "联系人电话")
    private String linkPhone;

    @Email
    @ApiModelProperty(value = "电子邮箱")
    private String companyEmail;
}
