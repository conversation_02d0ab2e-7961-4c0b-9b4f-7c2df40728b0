package cn.lili.modules.store.entity.enums;

/**
 * 店铺银行状态枚举
 *
 * <AUTHOR>
 */
public enum StoreBankStatusEnum {
    /**
     * 审核中
     */
    APPLYING("审核中"),

    /**
     * 已通过
     */
    SUCCESS("已通过"),

    /**
     * 审核拒绝
     */
    REFUSED("审核拒绝"),

    ;

    private final String description;

    StoreBankStatusEnum(String des) {
        this.description = des;
    }

    public String description() {
        return this.description;
    }

    public String value() {
        return this.name();
    }
}
