package cn.lili.modules.store.entity.vos;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class StoreCompanyAndBankVO {

    /***
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String companyName;

    /**
     * 注册地址
     */
    @ApiModelProperty(value = "注册地址")
    private String regLocation;

    /**
     * 参保人员
     */
    @ApiModelProperty(value = "参保人员")
    private String employeeNum;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码")
    private String companyPhone;

    /**
     * 注册资本
     */
    @ApiModelProperty(value = "注册资本")
    private String registeredCapital;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String licenseNum;

    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围")
    private String scope;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalName;

    /**
     * 法定代表人身份证号码
     */
    @ApiModelProperty(value = "法定代表人身份证号码")
    private String legalId;

    /**
     * 法定代表人身份证照片
     */
    @ApiModelProperty(value = "法人身份证照片")
    private String legalPhoto;

    /**
     * 营业执照电子版
     */
    @ApiModelProperty(value = "营业执照电子版")
    private String licencePhoto;

    @ApiModelProperty(value = "结算银行开户行名称")
    private String settlementBankAccountName;

    @ApiModelProperty(value = "结算银行开户账号")
    private String settlementBankAccountNum;

    @ApiModelProperty(value = "结算银行开户支行名称")
    private String settlementBankBranchName;

    @ApiModelProperty(value = "结算银行支行联行号")
    private String settlementBankJointName;

}
