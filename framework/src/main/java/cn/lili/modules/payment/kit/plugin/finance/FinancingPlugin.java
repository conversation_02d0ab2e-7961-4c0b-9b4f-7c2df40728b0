package cn.lili.modules.payment.kit.plugin.finance;

import cn.lili.common.enums.ResultCode;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.payment.entity.enums.PaymentMethodEnum;
import cn.lili.modules.payment.kit.Payment;
import cn.lili.modules.payment.kit.dto.PayParam;
import cn.lili.modules.payment.kit.dto.PaymentSuccessParams;
import cn.lili.modules.payment.service.PaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;


/**
 * 融资支付
 *
 * <AUTHOR>
 * 2025/6/4
 */
@Slf4j
@Component
public class FinancingPlugin implements Payment {

    /**
     * 支付日志
     */
    @Autowired
    private PaymentService paymentService;

    @Override
    public ResultMessage<Object> financingPay(HttpServletRequest request, PayParam payParam) {
        savePaymentLog(payParam);
        return ResultUtil.success(ResultCode.PAY_SUCCESS);
    }

    /**
     * 保存支付日志
     *
     * @param payParam 支付参数
     */
    private void savePaymentLog(PayParam payParam) {
        try {
            this.callBack(payParam, 1.0);
        } catch (Exception e){
            log.info("融资支付异常", e);
            throw new ServiceException(String.valueOf(e));
        }
    }

    /**
     * 支付订单
     *
     * @param payParam 支付参数
     */
    public void callBack(PayParam payParam, Double price) {
        try {
            PaymentSuccessParams paymentSuccessParams = new PaymentSuccessParams(
                    PaymentMethodEnum.FINANCING.name(),
                    "",
                    price,
                    payParam
            );
            paymentService.success(paymentSuccessParams);
            log.info("支付回调通知：融资支付：{}", payParam);
        } catch (Exception e) {
            log.info("融资支付异常", e);
            throw new ServiceException(ResultCode.PAY_ERROR);
        }

    }


}
