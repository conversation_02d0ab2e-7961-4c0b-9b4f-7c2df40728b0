/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.controller.contract;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_contract.contract_api.dto.ContractTemplateDTO;
import cn.lili.modules.jrzh_contract.contract_api.dto.UpdateStatusDTO;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateVO;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateService;
import cn.lili.mybatis.util.PageUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.extern.slf4j.Slf4j;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 控制器
 *
 * <AUTHOR>
 * @since 2021-12-20
 */
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/manager/contract/contractTemplate")
@Api(value = "平台合同模板", tags = "接口")
public class ContractTemplateManagerController {

    private final IContractTemplateService contractTemplateService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "详情", notes = "传入contractTemplate")
    public ResultMessage<ContractTemplateVO> detail(String templateId) {
        return ResultUtil.data(contractTemplateService.details(templateId));
    }


    @PostMapping("/list")
    @ApiOperationSupport(order = 2)
    @ApiOperation(value = "分页", notes = "传入contractTemplate")
    public ResultMessage<IPage<ContractTemplate>> list(ContractTemplateDTO dto) {
        PageVO pageVO = BeanUtil.copyProperties(dto, PageVO.class);
        String templateName = dto.getTemplateName();
        dto.setTemplateName(null);
        ContractTemplate template = BeanUtil.copyProperties(dto, ContractTemplate.class);
        QueryWrapper<ContractTemplate> objectQueryWrapper = PageUtil.initWrapper(template);
        // 模板名称模糊查询
        if (CharSequenceUtil.isNotBlank(templateName)) {
            objectQueryWrapper.like("template_name", templateName);
        }
        // 如果查询默认合同，添加特殊查询条件
        if (Boolean.TRUE.equals(dto.getIsDefault())) {
            objectQueryWrapper.isNotNull("default_contract_type");
        }
        IPage<ContractTemplate> pages = contractTemplateService.page(PageUtil.initPage(pageVO), objectQueryWrapper.orderByDesc("create_time"));
        return ResultUtil.data(pages);
    }

//    @PostMapping("/syncContractTemplate")
//    public R syncContractTemplate(Query query) {
//        return R.status(contractTemplateService.syncBestSign(query));
//    }

    /**
     * 自定义分页
     */
//    @GetMapping("/page")
//    @ApiOperationSupport(order = 3)
//    @ApiOperation(value = "分页", notes = "传入contractTemplate")
//    public R<IPage<ContractTemplateVO>> page(ContractTemplateVO contractTemplate, Query query) {
//        IPage<ContractTemplateVO> pages = contractTemplateService.selectContractTemplatePage(Condition.getPage(query), contractTemplate);
//        return R.data(pages);
//    }

    /**
     * 新增或修改
     */
    @PostMapping("/submit")
    @ApiOperationSupport(order = 6)
    @ApiOperation(value = "新增或修改", notes = "传入contractTemplate")
    @Transactional(rollbackFor = Exception.class)
    public ResultMessage submit(@Valid @RequestBody ContractTemplateDTO contractTemplateDTO) {
        return ResultUtil.data(contractTemplateService.saveOrUpdateContractTemplateDTO(contractTemplateDTO));
    }


    /**
     * 删除
     */
    @PostMapping("/remove")
    @ApiOperationSupport(order = 7)
    @ApiOperation(value = "逻辑删除", notes = "传入ids")
    @Transactional(rollbackFor = Exception.class)
    public ResultMessage remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return ResultUtil.data(contractTemplateService.remove(ids));

    }

    /**
     * 跳转到模板
     *
     * @return
     */
//    @PostMapping("skipToTemplate")
//    public R getSkipUrl() {
//        return R.data(contractTemplateService.skipToTemplate());
//    }


    @PostMapping("updateStatus")
    @ApiOperationSupport(order = 8)
    @ApiOperation(value = "启用禁用")
    public ResultMessage<?> updateStatus(@RequestBody UpdateStatusDTO updateStatusDTO) {
        return ResultUtil.data(this.contractTemplateService.enableDisable(updateStatusDTO));
    }
//
//    @GetMapping("findById")
//    @ApiOperationSupport(order = 11)
//    @ApiOperation(value = "根据id查找模板", notes = "传入合同模板id")
//    public R<ContractTemplate> findById(@RequestParam Long id) {
//        return R.data(contractTemplateService.lambdaQuery().eq(ContractTemplate::getId, id)
//                .eq(ContractTemplate::getStatus, ContractEnum.contract.CONTRACT_TEMPLATE_STATE_ABLE.getStatus()).one());
//    }

    /**
     * 预览模板
     *
     * @param contractTemplate
     * @return
     */
    @PostMapping("getTemplatePicUrl")
    public ResultMessage<?> getTemplatePicUrl(@RequestBody ContractTemplate contractTemplate) {
        String url = contractTemplateService.preView(contractTemplate.getTemplateId());
        JSONObject returnObj = new JSONObject();
        returnObj.put("contractGenType", contractTemplateService.getByTemplateId(contractTemplate.getTemplateId()).getContractGenType());
        returnObj.put("url", url);
        return ResultUtil.data(returnObj);
    }
//
//    /**
//     * 查看所有开启的模板
//     *
//     * @return
//     */
//    @GetMapping("/all")
//    public R<List<ContractTemplate>> all() {
//        return R.data(contractTemplateService.listEnableAll());
//    }
//
//    @GetMapping("/listAll")
//    @ApiOperation("查询所有合同模板无论开启关闭")
//    public R<List<ContractTemplate>> getAll() {
//        return R.data(contractTemplateService.list());
//    }
//
//
//    /**
//     * 根据类型查询所有合同模板
//     *
//     * @param type 类型
//     * @return 产品类型关联的合同模板
//     */
//    @GetMapping("/allByGoodType")
//    @ApiOperation("根据产品类型查询所有合同模板")
//    public R<List<ContractTemplate>> allByGoodType(@RequestParam Integer type) {
//        List<ContractTemplateConfig> templateConfigList = contractTemplateConfigService.list(Wrappers
//                .<ContractTemplateConfig>lambdaQuery()
//                .select(ContractTemplateConfig::getTemplateId)
//                .apply("find_in_set({0}, goods_type)", type));
//        List<String> templateIds = templateConfigList.stream().map(ContractTemplateConfig::getTemplateId).collect(Collectors.toList());
//        return R.data(contractTemplateService.list(Wrappers.<ContractTemplate>lambdaQuery()
//                .eq(ContractTemplate::getStatus, 1)
//                .in(CollectionUtil.isNotEmpty(templateIds), ContractTemplate::getTemplateId, templateIds)));
//    }
//
//    /**
//     * 查询所有可作为子合同的合同模板
//     */
//    @ApiOperation("查询所有可作为子合同的合同模板")
//    @GetMapping("/list_all_not_contain_self")
//    public R listAllNotContainSelf(String excludeTemplateIds) {
//        return R.data(contractTemplateService.list(Wrappers.<ContractTemplate>lambdaQuery()
//                .notIn(StringUtil.isNotBlank(excludeTemplateIds), ContractTemplate::getTemplateId, excludeTemplateIds)));
//    }
//
//    /**
//     * 根据合同id查询签署节点
//     *
//     * @param id
//     * @return
//     */
//    @ApiOperation("根据合同id查询签署节点")
//    @GetMapping("/getNodeById")
//    public R<String> getNodeById(@RequestParam String id) {
//        if (StringUtil.isBlank(id)) {
//            return null;
//        }
//        ContractTemplate contract = contractTemplateService.getById(id);
//        if (contract.getStatus() == 0) {
//            throw new ServiceException("合同未启用");
//        }
//        String signNode = contract.getSignNode();
//        return R.data(signNode);
//    }
}
