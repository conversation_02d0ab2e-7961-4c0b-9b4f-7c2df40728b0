package cn.lili.controller.contract;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateCategoryConfig;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplateGoodsConfig;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateCategoryConfigVO;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractTemplateGoodsConfigVO;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateConfigService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 合同模板配置管理控制器
 *
 * <AUTHOR>
 * @since 2024-06-18
 */
@RestController
@RequestMapping("/manager/contract/template-config")
@Api(tags = "管理端-合同模板配置管理")
@RequiredArgsConstructor
public class ContractTemplateConfigManagerController {

    private final IContractTemplateConfigService contractTemplateConfigService;

    @ApiOperation("分页查询分类模板配置")
    @GetMapping("/category/page")
    public ResultMessage<IPage<ContractTemplateCategoryConfigVO>> getCategoryConfigPage(
            @RequestParam(defaultValue = "1") Integer pageNumber,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String categoryId,
            @RequestParam(required = false) String templateId) {

        Page<ContractTemplateCategoryConfig> page = new Page<>(pageNumber, pageSize);
        IPage<ContractTemplateCategoryConfigVO> result = contractTemplateConfigService.getCategoryConfigPage(page, categoryId, templateId);
        return ResultUtil.data(result);
    }

    @ApiOperation("分页查询商品模板配置")
    @GetMapping("/goods/page")
    public ResultMessage<IPage<ContractTemplateGoodsConfigVO>> getGoodsConfigPage(
            @RequestParam(defaultValue = "1") Integer pageNumber,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam(required = false) String goodsId,
            @RequestParam(required = false) String templateId) {

        Page<ContractTemplateGoodsConfig> page = new Page<>(pageNumber, pageSize);
        IPage<ContractTemplateGoodsConfigVO> result = contractTemplateConfigService.getGoodsConfigPage(page, goodsId, templateId);
        return ResultUtil.data(result);
    }

    @ApiOperation("保存或更新分类模板配置")
    @PostMapping("/category")
    public ResultMessage<Boolean> saveOrUpdateCategoryConfig(@Valid @RequestBody ContractTemplateCategoryConfig config) {
        boolean result = contractTemplateConfigService.saveOrUpdateCategoryConfig(config);
        return ResultUtil.data(result);
    }

    @ApiOperation("保存分类模板配置（向后兼容）")
    @PostMapping("/category/save")
    public ResultMessage<Boolean> saveCategoryConfig(@Valid @RequestBody ContractTemplateCategoryConfig config) {
        boolean result = contractTemplateConfigService.saveCategoryConfig(config);
        return ResultUtil.data(result);
    }

    @ApiOperation("保存或更新商品模板配置")
    @PostMapping("/goods")
    public ResultMessage<Boolean> saveOrUpdateGoodsConfig(@Valid @RequestBody ContractTemplateGoodsConfig config) {
        boolean result = contractTemplateConfigService.saveOrUpdateGoodsConfig(config);
        return ResultUtil.data(result);
    }

    @ApiOperation("保存商品模板配置（向后兼容）")
    @PostMapping("/goods/save")
    public ResultMessage<Boolean> saveGoodsConfig(@Valid @RequestBody ContractTemplateGoodsConfig config) {
        boolean result = contractTemplateConfigService.saveGoodsConfig(config);
        return ResultUtil.data(result);
    }

    @ApiOperation("删除分类模板配置")
    @DeleteMapping("/category/{configId}")
    public ResultMessage<Boolean> deleteCategoryConfig(@PathVariable String configId) {
        boolean result = contractTemplateConfigService.deleteCategoryConfig(configId);
        return ResultUtil.data(result);
    }

    @ApiOperation("删除商品模板配置")
    @DeleteMapping("/goods/{configId}")
    public ResultMessage<Boolean> deleteGoodsConfig(@PathVariable String configId) {
        boolean result = contractTemplateConfigService.deleteGoodsConfig(configId);
        return ResultUtil.data(result);
    }

    @ApiOperation("批量配置分类模板")
    @PostMapping("/category/batch")
    public ResultMessage<Boolean> batchConfigCategoryTemplate(
            @RequestParam List<String> categoryIds,
            @RequestParam String templateId) {
        boolean result = contractTemplateConfigService.batchConfigCategoryTemplate(categoryIds, templateId);
        return ResultUtil.data(result);
    }

    @ApiOperation("根据商品ID获取最佳匹配模板")
    @GetMapping("/best-match/{goodsId}")
    public ResultMessage<String> getBestMatchTemplate(@PathVariable String goodsId) {
        ContractTemplate template = contractTemplateConfigService.getBestMatchTemplate(goodsId);
        return ResultUtil.data(template != null ? template.getTemplateId() : null);
    }

    @ApiOperation("根据商品ID获取可用的合同模板列表")
    @GetMapping("/available-templates/{goodsId}")
    public ResultMessage<List<ContractTemplate>> getAvailableTemplatesForGoods(@PathVariable String goodsId) {
        List<ContractTemplate> templates = contractTemplateConfigService.getAvailableTemplatesForGoods(goodsId);
        return ResultUtil.data(templates);
    }

    @ApiOperation("根据分类路径获取可用的合同模板列表")
    @GetMapping("/available-templates-by-category")
    public ResultMessage<List<ContractTemplate>> getAvailableTemplatesByCategoryPath(@RequestParam String categoryPath) {
        List<ContractTemplate> templates = contractTemplateConfigService.getAvailableTemplatesByCategoryPath(categoryPath);
        return ResultUtil.data(templates);
    }

    @ApiOperation("查询商品绑定的合同模板ID")
    @GetMapping("/bound-template/{goodsId}")
    public ResultMessage<String> getGoodsBoundTemplateId(@PathVariable String goodsId) {
        String templateId = contractTemplateConfigService.getGoodsBoundTemplateId(goodsId);
        return ResultUtil.data(templateId);
    }

    @ApiOperation("绑定商品合同模板")
    @PostMapping("/bind-goods-template")
    public ResultMessage<Boolean> bindGoodsTemplate(@RequestBody ContractTemplateGoodsConfig config) {
        boolean result = contractTemplateConfigService.saveOrUpdateGoodsConfig(config);
        return ResultUtil.data(result);
    }
}
