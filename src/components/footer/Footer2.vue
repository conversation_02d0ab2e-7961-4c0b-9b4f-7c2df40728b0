<template>
  <footer class="footer">
    <div class="footer-top">
      <div class="item">
        <div class="logo">
          <img src="../../assets/images/footer/ca.png" alt="logo" />
        </div>
        <div class="item-name">
          <span>企业CA认证</span>
          <span class="item-name-sub"
            >入驻企业真实身份核验，实现企业合法登记</span
          >
        </div>
      </div>
      <div class="item">
        <div class="logo">
          <img
            class="img"
            src="../../assets/images/footer/trade.png"
            alt="logo"
          />
        </div>
        <div class="item-name">
          <span>真实交易</span>
          <span class="item-name-sub">电子合同、签章，保障交易真实性</span>
        </div>
      </div>
      <div class="item">
        <div class="logo">
          <img src="../../assets/images/footer/service.png" alt="logo" />
        </div>
        <div class="item-name">
          <span>专业服务</span>
          <span class="item-name-sub"
            >企业授信，交易货物风险评估，为每笔交易保驾护航</span
          >
        </div>
      </div>
    </div>
    <div class="footer-bottom">
      <div class="footer-bottom-top">
        <span class="copyright">
          <a href="https://beian.miit.gov.cn/" target="_blank"
            ><img class="zhizhao" src="@/assets/images/zhizhao.jpg" mode="" />{{
              config.icpMessage
            }}</a
          >
        </span>
        <span>
          <a :href="config.company.href" target="_blank">{{
            config.company.name
          }}</a>
        </span>
      </div>
      <div>
        <span>© 2015JINGRUIIT精锐纵横. All Right Reserved.</span>
      </div>
    </div>
  </footer>
</template>
<script>
export default {
  name: "Footer",
  data() {
    return {
      config: require("@/config"),
      year: new Date().getFullYear(), // 当前年份
    };
  },
  methods: {
    goArticle() {
      this.$router.push({ path: "/article" });
    },
  },
};
</script>
<style scoped lang="scss">
.footer {
  .footer-top {
    height: 100px;
    width: 1200px;
    // background-color: #f5f5f5;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 100px;
    grid-gap: 10px;
    .item {
      // background-color: red;
      color: #333;
      display: flex;
      justify-content: center;
      align-items: center;
      .logo {
        margin-right: 10px;
        img {
          width: 44px;
          height: 44px;
        }
        .img {
          width: 60px;
          height: 60px;
        }
      }
      .item-name {
        font-size: 16px;
        font-weight: 700;
        display: flex;
        flex-direction: column;
        justify-content: center;
        // align-items: center;
        .item-name-sub {
          font-size: 12px;
          font-weight: 700;
          color: #666;
        }
      }
    }
  }
  .footer-bottom {
    width: 100%;
    height: 80px;
    border-top: 1px solid $border_color;
    margin: 0 auto;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .footer-bottom-top {
      margin-bottom: 10px;
      .copyright {
        position: relative;
        margin-right: 30px;

        .zhizhao {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          position: absolute;
          top: -4px;
          left: -24px;
        }
      }
    }
  }
}
</style>
