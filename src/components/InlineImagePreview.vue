<template>
  <div class="inline-image-preview" v-if="imageList.length > 0">
    <!-- 主图片显示区域 -->
    <div class="main-image-wrapper">
      <img
        :src="currentImage"
        class="preview-image"
        @load="onImageLoad"
        @error="onImageError"
      />

      <!-- 左右切换按钮 -->
      <div class="nav-buttons" v-if="imageList.length > 1">
        <el-button
          class="nav-btn prev-btn"
          icon="el-icon-arrow-left"
          @click="prevImage"
          :disabled="currentIndex === 0"
          circle
          size="small"
        ></el-button>
        <el-button
          class="nav-btn next-btn"
          icon="el-icon-arrow-right"
          @click="nextImage"
          :disabled="currentIndex === imageList.length - 1"
          circle
          size="small"
        ></el-button>
      </div>

      <!-- 图片信息显示 -->
      <div class="image-info" v-if="imageList.length > 1">
        {{ currentIndex + 1 }} / {{ imageList.length }}
      </div>
    </div>

    <!-- 缩略图列表 -->
    <div class="thumbnail-list" v-if="imageList.length > 1">
      <div
        v-for="(image, index) in imageList"
        :key="index"
        class="thumbnail-item"
        :class="{ active: index === currentIndex }"
        @click="setCurrentImage(index)"
      >
        <img :src="image" class="thumbnail-image" />
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button icon="el-icon-search" size="small" @click="viewFullImage">
        预览当前{{ btnText }}
      </el-button>
      <el-button size="small" @click="downloadCurrent" icon="el-icon-download">
        下载当前{{ btnText }}
      </el-button>
      <el-button
        size="small"
        @click="downloadAll"
        v-if="imageList.length > 1"
        icon="el-icon-folder-opened"
      >
        下载全部{{ btnText }}
      </el-button>
    </div>

    <ImagePreview :url="imageUrl" />
  </div>
</template>

<script>
import ImagePreview from "@/components/ImagePreview.vue";
export default {
  name: "InlineImagePreview",
  components: {
    ImagePreview,
  },
  props: {
    // 图片URL列表
    urlList: {
      type: Array,
      default: () => [],
    },
    btnText: {
      type: String,
      default: "凭证",
    },
  },
  data() {
    return {
      // 当前显示的图片索引
      currentIndex: 0,
      // 图片列表
      imageList: [],
      imageUrl: "",
    };
  },
  computed: {
    // 当前显示的图片URL
    currentImage() {
      if (
        this.imageList &&
        this.imageList.length > 0 &&
        this.currentIndex >= 0
      ) {
        return this.imageList[this.currentIndex] || this.imageList[0] || "";
      }
      return "";
    },
  },
  watch: {
    // 监听 urlList 变化
    urlList: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.imageList = [...newVal];
          this.currentIndex = 0;
          // 确保在下一个tick中更新，让DOM有时间渲染
          this.$nextTick(() => {
            // 强制触发图片加载
            if (this.currentImage) {
              // 可以在这里添加额外的处理逻辑
            }
          });
        } else {
          this.imageList = [];
          this.currentIndex = 0;
        }
      },
      immediate: true, // 组件初始化时立即执行
    },
  },
  methods: {
    // 查看完整图片
    viewFullImage() {
      // 这里可以实现查看完整图片的逻辑
      // 比如打开新窗口或者放大显示
      console.log("currentImage:", this.currentImage);
      this.imageUrl =
        this.currentImage + "?time=" + new Date().getMilliseconds();
    },
    // 设置当前显示的图片
    setCurrentImage(index) {
      this.currentIndex = index;
    },

    // 上一张图片
    prevImage() {
      if (this.currentIndex > 0) {
        this.currentIndex--;
      }
    },

    // 下一张图片
    nextImage() {
      if (this.currentIndex < this.imageList.length - 1) {
        this.currentIndex++;
      }
    },

    // 图片加载成功
    onImageLoad() {
      // 可以在这里添加加载成功的处理逻辑
    },

    // 图片加载失败
    onImageError() {
      this.$message.error(`${this.btnText}加载失败`);
    },

    // 下载当前图片
    async downloadCurrent() {
      if (!this.currentImage) {
        this.$message.warning(`没有可下载的${this.btnText}`);
        return;
      }
      await this.downloadImage(
        this.currentImage,
        `${this.btnText}${this.currentIndex + 1}`
      );
    },

    // 下载全部图片
    async downloadAll() {
      if (this.imageList.length === 0) {
        this.$message.warning(`没有可下载的${this.btnText}`);
        return;
      }

      this.$message.info(`开始下载全部${this.btnText}，请稍候...`);

      for (let i = 0; i < this.imageList.length; i++) {
        try {
          await this.downloadImage(
            this.imageList[i],
            `${this.btnText}${i + 1}`
          );
          // 添加延迟避免浏览器阻止多个下载
          if (i < this.imageList.length - 1) {
            await new Promise((resolve) => setTimeout(resolve, 500));
          }
        } catch (error) {
          console.error(`下载第${i + 1}张${this.btnText}失败:`, error);
        }
      }

      this.$message.success(`全部${this.btnText}下载完成`);
    },

    // 下载单张图片
    async downloadImage(imageUrl, fileName) {
      if (!imageUrl) {
        this.$message.warning(`该${this.btnText}不支持下载`);
        return;
      }

      try {
        // 发送请求获取图片数据
        const response = await fetch(imageUrl);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 将响应数据转换为 Blob 对象
        const blob = await response.blob();

        // 创建一个临时的 URL
        const url = window.URL.createObjectURL(blob);

        // 创建一个 <a> 元素
        const a = document.createElement("a");
        a.href = url;

        // 提取 URL 的后缀名
        const lastDotIndex = imageUrl.lastIndexOf(".");
        const fileExtension =
          lastDotIndex !== -1
            ? imageUrl
                .slice(lastDotIndex + 1)
                .split("?")[0]
                .toLowerCase()
            : "jpg";

        // 生成文件名
        const finalFileName = `${fileName}.${fileExtension}`;
        a.download = finalFileName;

        // 模拟点击 <a> 元素触发下载
        a.click();

        // 释放临时 URL
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error("下载图片失败:", error);
        this.$message.error(`下载${this.btnText}失败，请稍后重试`);
      }
    },
  },
};
</script>

<style scoped>
.inline-image-preview {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 10px 0;
}

.main-image-wrapper {
  position: relative;
  width: 100%;
  max-height: 40vh;
  min-height: 40vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
}

.preview-image {
  max-width: 100%;
  max-height: 40vh;
  object-fit: contain;
  display: block;
  border-radius: 4px;
}

.nav-buttons {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  pointer-events: none;
}

.nav-btn {
  pointer-events: all;
  background-color: rgba(0, 0, 0, 0.5);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.nav-btn:hover:not(:disabled) {
  background-color: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.nav-btn:disabled {
  background-color: rgba(0, 0, 0, 0.2);
  cursor: not-allowed;
}

.image-info {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.thumbnail-list {
  display: flex;
  gap: 8px;
  max-width: 100%;
  overflow-x: auto;
  padding: 5px 0;
  justify-content: center;
  flex-wrap: wrap;
}

.thumbnail-item {
  width: 40px;
  height: 40px;
  border: 2px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.thumbnail-item:hover {
  border-color: #409eff;
  transform: scale(1.05);
}

.thumbnail-item.active {
  border-color: #409eff;
  box-shadow: 0 0 8px rgba(64, 158, 255, 0.3);
}

.thumbnail-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 10px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-image-wrapper {
    max-height: 40vh;
  }

  .preview-image {
    max-height: 40vh;
  }

  .thumbnail-item {
    width: 50px;
    height: 50px;
  }

  .thumbnail-list {
    gap: 6px;
  }

  .action-buttons {
    flex-direction: column;
  }
}

/* 查看按钮样式 */
.view-button-wrapper {
  position: absolute;
  top: 25px;
  right: 50px;
  transform: translate(-50%, -50%);
  z-index: 10;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
}

.view-btn {
  background-color: rgba(0, 0, 0, 0.6);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.view-btn:hover {
  background-color: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}
</style>
