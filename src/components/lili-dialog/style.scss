.wrapper {
  width: 100%;
  height: 100%;
  display: flex;

  .wap-list {
    flex: 2;
    text-align: center;
    overflow-y: auto;
    height: 100%;
  }

  >.wap-list,

  .wap-content {
    flex: 8;
  }
}

.wap-sku {
  font-size: 12px;
  color: #999;
  overflow: hidden;
  display: flex;
  align-items: center;
  text-overflow: ellipsis;

  white-space: nowrap;
}

.query-wrapper {
  display: flex;
  margin: 8px 0;

  >.query-item {
    display: flex;
    align-items: center;

    >* {
      margin: 0 4px;
    }
  }
}

/deep/ .ivu-scroll-container {
  width: 100% !important;
  height: 400px !important;
}

/deep/ .ivu-scroll-content {
  /* */
  display: flex;
  flex-wrap: wrap;
}

.wap-content-list {
  overflow-y: auto;
}

.wap-item {
  padding: 10px 0;
  cursor: pointer;
}

.wap-item:hover {
  background: #ededed;
}

.pageration {
  text-align: right;
  padding-right: 20px;
}
.wap-content-item {

  cursor: pointer;

  display: flex;
  height: 90px;
  padding: 2px;
  overflow: hidden;
  align-items: center;
  margin: 10px;

  /deep/ img {
    width: 60px;
    height: 60px;
    text-align: center;
  }

  .wap-content-desc {
    width: 180px;
    padding: 8px;

    >.wap-content-desc-title {
      display: -webkit-box;
      font-size: 12px;
      color: #666;

      -webkit-box-orient: vertical;
      overflow: hidden;
      -webkit-line-clamp: 2;
    }

    >.wap-content-desc-bottom {
      font-size: 12px;
      padding: 4px 0;
      color: #999;
      display: flex;
      justify-content: space-between;

      >div:nth-of-type(1) {
        color: $theme_color;
      }
    }
  }
}
