<template>
  <div>
    <!-- 图片预览弹窗 -->
    <el-dialog
      :visible.sync="isPreviewing"
      class="image-preview-dialog"
      width="50%"
      center
      @close="closePreview"
      append-to-body
    >
      <img :src="url" class="preview-image" />
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="downLoad">下 载</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "SingleImagePreviewer",
  props: {
    // 传入的图片 URL
    url: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      // 控制弹窗显示状态
      isPreviewing: false,
    };
  },
  watch: {
    // 监听 url 变化
    url(newVal) {
      if (newVal) {
        // 延迟 200ms 显示弹窗，避免在图片加载完成前显示弹窗导致回显上一张图片
        setTimeout(() => {
          this.isPreviewing = true; // 如果有 url，则显示弹窗
        }, 150);
      } else {
        this.isPreviewing = false; // 如果 url 为空，则关闭弹窗
      }
    },
  },
  methods: {
    // 关闭图片预览
    closePreview() {
      this.$emit("update:url", null); // 清空 url 以关闭预览
    },

    // 下载图片
    async downLoad() {
      if (!this.url) {
        this.$message.warning("该图片不支持下载");
        return;
      }
      try {
        // 发送请求获取图片数据
        const response = await fetch(this.url);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        // 将响应数据转换为 Blob 对象
        const blob = await response.blob();
        // 创建一个临时的 URL
        const url = window.URL.createObjectURL(blob);
        // 创建一个 <a> 元素
        const a = document.createElement("a");
        a.href = url;
        // 提取 URL 的后缀名
        const lastDotIndex = this.url.lastIndexOf(".");
        const fileExtension =
          lastDotIndex !== -1
            ? this.url
                .slice(lastDotIndex + 1)
                .split("?")[0]
                .toLowerCase()
            : "jpg";

        // 生成文件名，这里简单用时间戳作为文件名主体
        const fileName = `下载图片.${fileExtension}`;
        a.download = fileName;
        // 模拟点击 <a> 元素触发下载
        a.click();
        // 释放临时 URL
        window.URL.revokeObjectURL(url);
      } catch (error) {
        console.error("下载图片失败:", error);
        this.$message.error("下载图片失败，请稍后重试");
      }
    },
  },
};
</script>

<style scoped>
.preview-image {
  max-width: 100%;
  max-height: 100%;
  display: block;
  margin: 0 auto;
}

::v-deep .dialog-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 0px;
}
</style>
