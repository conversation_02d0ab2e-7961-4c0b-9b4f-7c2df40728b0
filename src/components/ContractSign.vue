<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :modal="true"
    :fullscreen="isFullscreen"
    :append-to-body="true"
    custom-class="pre-dialog"
    @close="handleClose"
    top="5vh"
    width="60%"
  >
    <div slot="title">
      <span class="el-dialog__title">文件预览</span>
      <div class="dialog__menu">
        <i @click="handleFullScreen" class="el-icon-full-screen"></i>
      </div>
    </div>

    <div
      class="container__xmg"
      v-loading="loading"
      element-loading-text="合同签署中，请稍等..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <div class="left__xmg">
        <div class="contract__name__list">
          <div
            v-for="(item, index) in contractList"
            :key="item.url"
            @click="handleChange(item, index)"
            :class="{
              contract__name: true,
              active: currentContractIndex === index,
              'read-completed':
                signStatus == 'sign' &&
                contractReadingStatus[index] &&
                contractReadingStatus[index].isReadCompleted,
              reading:
                signStatus == 'sign' &&
                contractReadingStatus[index] &&
                contractReadingStatus[index].isReading,
              disabled: signStatus == 'sign' && !canSwitchToContract(index),
            }"
          >
            <span class="contract-name">{{ item.name }}</span>
            <span
              v-if="
                signStatus == 'sign' &&
                contractReadingStatus[index] &&
                contractReadingStatus[index].remainingTime > 0
              "
              class="reading-time"
            >
              {{ contractReadingStatus[index].remainingTime }}s
            </span>
            <i
              v-if="
                signStatus == 'sign' &&
                contractReadingStatus[index] &&
                contractReadingStatus[index].isReadCompleted
              "
              class="el-icon-check read-check"
            ></i>
          </div>
        </div>
        <div class="sign__btn">
          <div
            v-if="signStatus == 'sign'"
            class="sign"
            @click="handleSign"
            :style="{
              backgroundColor: canSign ? '#409eff' : '#aaa',
              cursor: canSign ? 'pointer' : 'not-allowed',
            }"
          >
            {{ getSignButtonText }}
          </div>
        </div>
      </div>
      <div class="right__xmg">
        <iframe
          v-if="dialogVisible"
          :src="pdfUrl"
          frameborder="no"
          class="pdf-iframe"
        ></iframe>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { sign } from "@/api/contract";
export default {
  name: "ContractSign",
  props: {
    // url: {
    //   type: String,
    //   required: true,
    // },
    contractList: {
      type: Array,
      required: true,
    },
    signStatus: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
      isFullscreen: false,
      pdfUrl: undefined,
      loading: false,
      canSign: true,
      remainingTime: 0,
      timer: null,
      currentContractIndex: 0, // 当前合同索引
      contractReadingStatus: {}, // 每个合同的阅读状态
    };
  },
  computed: {
    getSignButtonText() {
      if (this.canSign) {
        return "立即签署";
      }

      const currentStatus =
        this.contractReadingStatus[this.currentContractIndex];
      if (currentStatus && currentStatus.remainingTime > 0) {
        const currentContract = this.contractList[this.currentContractIndex];
        const contractName =
          (currentContract && currentContract.name) || "合同";
        // return `请阅读《${contractName}》 ${currentStatus.remainingTime} 秒`;
        return `请阅读当前合同 ${currentStatus.remainingTime} 秒`;
      }

      // 检查还有多少合同未完成阅读
      const unreadCount = this.contractList.filter((_, index) => {
        const status = this.contractReadingStatus[index];
        return !status || !status.isReadCompleted;
      }).length;

      if (unreadCount > 0) {
        return `还有 ${unreadCount} 份合同未完成阅读`;
      }

      return "请完成所有合同阅读";
    },
  },
  watch: {
    // url(newUrl) {
    //   if (newUrl != "") {
    //     this.dialogVisible = true;
    //     // const baseUrl = window.location.origin;
    //     // this.pdfUrl = `${baseUrl}/cdn/pdfjs/web/viewer.html?file=${newUrl}`;
    //     this.pdfUrl = `/contractCdn/pdfjs/generic/web/viewer.html?file=${newUrl}`;
    //   }
    // },
  },
  methods: {
    async handleSign() {
      // 检查是否所有合同都已完成阅读
      const allCompleted = this.contractList.every((_, index) => {
        const status = this.contractReadingStatus[index];
        return status && status.isReadCompleted;
      });

      if (!allCompleted) {
        this.$message.error("请先完成所有合同的阅读");
        return;
      }

      // 检查是否可以签署
      if (!this.canSign) {
        this.$message.error("请等待当前合同阅读完成");
        return;
      }

      let contractId = this.contractList
        .map((item) => item.contractId)
        .join(",");
      let params = {
        code: {
          id: "",
        },
        contractId,
        verifyType: "",
      };

      // 调用接口
      this.loading = true;

      // 签署
      const res = await sign(params);

      if (res.success) {
        this.loading = false;
        this.$message.success("签署成功");

        this.handleClose();
      } else {
        this.loading = false;
      }

      this.$emit("signCompleted", res.success);
    },
    handleChange(item, index) {
      // 检查是否可以切换到该合同
      if (!this.canSwitchToContract(index) && this.signStatus == "sign") {
        this.$message.warning("请按顺序完成合同阅读");
        return;
      }

      // 切换到新合同
      this.switchToContract(item, index);
    },

    // 检查是否可以切换到指定合同
    canSwitchToContract(targetIndex) {
      // 第一个合同总是可以访问
      if (targetIndex === 0) return true;

      // 检查前面的合同是否都已完成阅读
      if (this.signStatus == "sign") {
        for (let i = 0; i < targetIndex; i++) {
          const status = this.contractReadingStatus[i];
          if (!status || !status.isReadCompleted) {
            return false;
          }
        }
      } else {
        return true;
      }
      return true;
    },

    // 切换到指定合同
    switchToContract(item, index) {
      // 停止当前合同的计时器
      this.stopCurrentTimer();

      // 更新当前合同索引
      this.currentContractIndex = index;

      // 加载新合同
      const targetUrl = item.url + "?time=" + new Date().getMilliseconds();
      this.pdfUrl = `/contractCdn/pdfjs/generic/web/viewer.html?file=${targetUrl}`;
      item.isView = true; // 标记为已查看

      // 初始化合同阅读状态
      if (!this.contractReadingStatus[index]) {
        this.$set(this.contractReadingStatus, index, {
          isReading: false,
          isReadCompleted: false,
          remainingTime: 0,
          timer: null,
        });
      }

      // 开始阅读计时
      this.startReadingTimer(item, index);
    },

    // 开始阅读计时
    startReadingTimer(item, index) {
      const forceReadingSecond = item.forceReadingSecond || 0;

      // 如果该合同已经完成阅读，不需要重新计时
      if (this.contractReadingStatus[index].isReadCompleted) {
        this.updateSignButtonStatus();
        return;
      }

      if (forceReadingSecond > 0) {
        // 设置阅读状态
        this.contractReadingStatus[index].isReading = true;
        this.contractReadingStatus[index].remainingTime = forceReadingSecond;

        // 开始倒计时
        this.contractReadingStatus[index].timer = setInterval(() => {
          this.contractReadingStatus[index].remainingTime--;

          if (this.contractReadingStatus[index].remainingTime <= 0) {
            // 阅读完成
            clearInterval(this.contractReadingStatus[index].timer);
            this.contractReadingStatus[index].isReading = false;
            this.contractReadingStatus[index].isReadCompleted = true;
            this.contractReadingStatus[index].timer = null;

            if (this.signStatus == "sign") {
              this.$message.success(`《${item.name}》阅读完成`);
            }

            this.updateSignButtonStatus();
          }
        }, 1000);
      } else {
        // 没有强制阅读时间，直接标记为完成
        this.contractReadingStatus[index].isReadCompleted = true;
        this.updateSignButtonStatus();
      }
    },

    // 停止当前计时器
    stopCurrentTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }

      // 停止所有合同的计时器
      Object.values(this.contractReadingStatus).forEach((status) => {
        if (status.timer) {
          clearInterval(status.timer);
          status.timer = null;
        }
      });
    },

    // 更新签署按钮状态
    updateSignButtonStatus() {
      // 检查是否所有合同都已完成阅读
      const allCompleted = this.contractList.every((_, index) => {
        const status = this.contractReadingStatus[index];
        return status && status.isReadCompleted;
      });

      this.canSign = allCompleted;

      if (allCompleted) {
        this.remainingTime = 0;
      } else {
        // 计算还需要阅读的时间
        const currentStatus =
          this.contractReadingStatus[this.currentContractIndex];
        this.remainingTime =
          (currentStatus && currentStatus.remainingTime) || 0;
      }
    },
    handleFullScreen() {
      this.isFullscreen = !this.isFullscreen;
    },
    handleClose() {
      // 停止所有计时器
      this.stopCurrentTimer();

      // 重置状态
      this.dialogVisible = false;
      this.pdfUrl = undefined;
      this.loading = false;
      this.canSign = true;
      this.remainingTime = 0;
      this.timer = null;
      this.currentContractIndex = 0;
      this.contractReadingStatus = {};
    },
    handleOpen() {
      this.dialogVisible = true;

      // 重置状态
      this.currentContractIndex = 0;
      this.contractReadingStatus = {};
      this.canSign = false;
      this.remainingTime = 0;

      this.$nextTick(() => {
        // 初始化第一个合同
        if (this.contractList.length > 0) {
          this.switchToContract(this.contractList[0], 0);
        }
      });
    },
  },
};
</script>

<style lang="scss">
.pre-dialog {
  height: 84vh;
  margin-top: 8vh !important;
  border-radius: 4px;
}

.pre-dialog .dialog__menu {
  float: right;
  padding-right: 24px;
  cursor: pointer;
}

.pre-dialog .dialog__menu:hover {
  color: #409eff;
}

.pre-dialog .el-dialog__body {
  height: calc(100% - 54px);
  padding: 0 10px 10px;
}

.pre-dialog.is-fullscreen {
  margin-top: 0 !important;
  border-radius: 0 !important;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

.pdf-iframe body,
.pdf-iframe .toolbarContainer {
  background-color: white;
}

.container__xmg {
  display: flex;
  height: 100%;
}

.left__xmg {
  min-width: 200px;
  background-color: #fff;
  margin-right: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;

  .contract__name__list {
    width: 100%;
    height: calc(100% - 80px);
    overflow-y: auto;
    padding-top: 10px;
    box-sizing: border-box;
    .contract__name {
      width: 100%;
      height: 60px;
      line-height: 60px;
      text-align: center;
      background: #fff;
      margin-bottom: 20px;
      cursor: pointer;
      color: #000;
      border-radius: 10px;
      border: #ccc 1px solid;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      .contract-name {
        flex: 1;
      }

      .reading-time {
        position: absolute;
        right: 10px;
        font-size: 12px;
        color: #fff;
        font-weight: bold;
      }

      .read-check {
        position: absolute;
        right: 10px;
        color: #67c23a;
        font-size: 16px;
      }
    }

    .contract__name.active {
      background-color: #409eff;
      color: white;
      border-color: #409eff;
    }

    .contract__name.read-completed {
      background-color: #f0f9ff;
      border-color: #67c23a;
      color: #67c23a;
    }

    .contract__name.read-completed.active {
      background-color: #67c23a;
      color: white;
    }

    .contract__name.reading {
      background-color: #fff7e6;
      border-color: #ff9800;
      color: #ff9800;
    }

    .contract__name.reading.active {
      background-color: #ff9800;
      color: white;
    }

    .contract__name.disabled {
      background-color: #f5f5f5;
      color: #ccc;
      cursor: not-allowed;
      border-color: #e0e0e0;
    }

    .contract__name.disabled:hover {
      background-color: #f5f5f5;
      color: #ccc;
    }
  }

  .sign__btn {
    height: 80px;
    background: #fff !important;
    width: 100%;

    .sign {
      position: absolute;
      bottom: 10px;
      left: 50%;
      transform: translateX(-50%);
      width: 100%;
      height: 50px;
      background: #409eff;
      color: #fff;
      border-radius: 25px;
      line-height: 50px;
      text-align: center;
      cursor: pointer;
    }
  }
}
.right__xmg {
  flex: 1;
}
</style>
