<template>
  <div class="information">
    <vue-j-scroll
      :autoplay="true"
      :steep="0.3"
      :direction="'up'"
      style="height: 200px"
      :data="list"
      :isRoller="true"
      v-if="list.length > 0"
      ref="scroll"
    >
      <div v-for="(item, index) in list" :key="index" class="list">
        <div class="list_item">
          <span class="list_item_left">
            <span class="company_name">{{ item.goodsName }}</span>
            <span class="data_name">
              <span>成交量&nbsp;&nbsp;</span>
              <span style="font-size: 18px; color: #f31947; font-weight: bold"
                >{{ item.goodsNum }}&nbsp;&nbsp;</span
              >
              <span>{{ item.simpleSpecs }}</span>
            </span>
          </span>
          <span class="list_item_right data_time">
            <span>成交日期：{{ item.createTime }}</span>
          </span>
        </div>
      </div>
    </vue-j-scroll>
    <el-empty
      v-else
      style="height: 200px"
      description="暂无数据"
      :image-size="50"
    />
  </div>
</template>
<script>
import { orderConfirmedPage } from "@/api/common";
export default {
  name: "CreditInformation",
  data() {
    return {
      list: [],
    };
  },
  methods: {
    // 初始化
    async init() {
      const res = await orderConfirmedPage({ pageNumber: 1, pageSize: 100 });

      if (res.success) {
        this.list = res.result.records;
      }
      console.log(res, "---res");
    },
  },
  mounted() {
    this.init();
  },
};
</script>
<style lang="scss" scoped>
.information {
  height: 100%;
  overflow: hidden;
  .list {
    margin-bottom: 10px;
    .list_item {
      height: 25px;
      line-height: 25px;
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      padding: 0 10px;
      /* 使用 border-image 实现自定义虚线，虚线长度 6px，间隔 8px */
      border-bottom: 1px solid transparent;
      border-image: repeating-linear-gradient(
          90deg,
          #fff,
          #eee 6px,
          transparent 6px,
          transparent 8px
        )
        1;
      .company_name {
        font-size: 15px;
        font-weight: 700;
        line-height: 17px;
        height: 17px;
        color: #333;
        width: 200px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .data_name {
        font-size: 12px;
        font-weight: 400;
        line-height: 17px;
        height: 17px;
        color: #333;
      }
      .data_time {
        font-size: 12px;
        color: #333;
      }
    }
  }
}
</style>
