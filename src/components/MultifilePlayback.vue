<template>
  <el-dialog
    :visible.sync="isPreviewing"
    class="multifile-playback-dialog"
    width="800px"
    @close="closePreview"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    top="5vh"
  >
    <div slot="title">
      <span class="el-dialog__title">{{ title }}</span>
    </div>

    <el-upload
      action="/"
      :file-list="fileListData"
      :on-preview="handlePreview"
      disabled
      class="upload-demo"
    >
    </el-upload>

    <FilePreview :url="fileUrl" />
    <InlineImagePreview :urlList="imageUrlList" />
  </el-dialog>
</template>
<script>
import FilePreview from "@/components/FilePreview.vue";
import InlineImagePreview from "@/components/InlineImagePreview.vue";
export default {
  name: "MultifilePlayback",
  components: {
    FilePreview,
    InlineImagePreview,
  },
  props: {
    fileListData: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: "文件列表",
    },
  },
  data() {
    return {
      fileUrl: "", // pdf文件预览地址
      imageUrlList: [], // 图片预览地址数组
      isPreviewing: false,
    };
  },
  watch: {
    fileListData(newVal) {
      if (newVal.length > 0) {
        // 过滤出图片文件的URL
        this.imageUrlList = newVal
          .filter((item) => {
            const fileName = item.name || "";
            const lowerCaseFileName = fileName.toLowerCase();
            return (
              lowerCaseFileName.endsWith(".jpg") ||
              lowerCaseFileName.endsWith(".jpeg") ||
              lowerCaseFileName.endsWith(".png") ||
              lowerCaseFileName.endsWith(".gif") ||
              lowerCaseFileName.endsWith(".webp") ||
              lowerCaseFileName.endsWith(".bmp")
            );
          })
          .map((item) => item.url);
        setTimeout(() => {
          this.isPreviewing = true;
        }, 200);
      } else {
        this.isPreviewing = false;
        this.imageUrlList = [];
      }
    },
  },
  methods: {
    handlePreview(file, fileList) {
      // 重置预览状态
      this.fileUrl = "";

      const fileName = file.name;
      const lowerCaseFileName = fileName.toLowerCase();
      const targetUrl = file.response.result;

      if (lowerCaseFileName.endsWith(".pdf")) {
        // PDF文件预览
        this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
        // 清空图片列表，关闭图片预览
        // this.imageUrlList = [];
      } else {
        // 图片文件预览 - 找到当前图片在列表中的位置，重新排序让它显示在第一位
        const currentImageUrl = targetUrl;
        const allImageUrls = this.fileListData
          .filter((item) => {
            const itemFileName = item.name || "";
            const itemLowerCaseFileName = itemFileName.toLowerCase();
            return (
              itemLowerCaseFileName.endsWith(".jpg") ||
              itemLowerCaseFileName.endsWith(".jpeg") ||
              itemLowerCaseFileName.endsWith(".png") ||
              itemLowerCaseFileName.endsWith(".gif") ||
              itemLowerCaseFileName.endsWith(".webp") ||
              itemLowerCaseFileName.endsWith(".bmp")
            );
          })
          .map((item) => item.url);

        // 将当前点击的图片放在第一位
        const currentIndex = allImageUrls.indexOf(currentImageUrl);
        if (currentIndex > -1) {
          const reorderedImages = [
            ...allImageUrls.slice(currentIndex),
            ...allImageUrls.slice(0, currentIndex),
          ];
          this.imageUrlList = reorderedImages;
        } else {
          // 如果没找到，就单独预览这张图片
          this.imageUrlList = [
            currentImageUrl + "?time=" + new Date().getMilliseconds(),
          ];
        }
      }
    },
    // 关闭图片预览
    closePreview() {
      this.$emit("closePreview", null); // 清空 fileListData 以关闭预览
    },
    handleFullScreen() {
      this.isFullscreen = !this.isFullscreen;
    },
  },
};
</script>
<style scoped>
::v-deep .el-upload {
  display: none;
}

::v-deep .el-dialog__body {
  padding-top: 0;
}
</style>
