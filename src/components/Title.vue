<template>
  <div class="title">
    <div class="item">{{ title }}</div>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default() {
        return "";
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.title {
  margin-bottom: 10px;
  border-left: 5px solid #0d55cf;
  height: 30px;
  padding-left: 10px;
  .item {
    font-size: 20px;
    color: #0d55cf;
  }
}
</style>
