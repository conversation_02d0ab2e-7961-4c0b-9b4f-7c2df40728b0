<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :modal="true"
    :fullscreen="isFullscreen"
    :append-to-body="true"
    custom-class="pre-dialog"
  >
    <div slot="title">
      <span class="el-dialog__title">文件预览</span>
      <div class="dialog__menu">
        <span class="download" @click="downLoad">下 载</span>
        <i @click="handleFullScreen" class="el-icon-full-screen"></i>
      </div>
    </div>

    <iframe
      v-if="dialogVisible && pdfUrl"
      :src="pdfUrl"
      frameborder="no"
      class="pdf-iframe"
    ></iframe>
  </el-dialog>
</template>

<script>
export default {
  name: "VueFilePreview",
  props: {
    url: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
      isFullscreen: false,
      pdfUrl: undefined,
    };
  },
  watch: {
    url(newUrl) {
      if (newUrl != "") {
        this.dialogVisible = true;

        // const baseUrl = window.location.origin;

        // this.pdfUrl = `${baseUrl}/cdn/pdfjs/web/viewer.html?file=${newUrl}`;
        this.pdfUrl = `/contractCdn/pdfjs/generic/web/viewer.html?file=${newUrl}`
      }
    },
  },
  methods: {
    handleFullScreen() {
      this.isFullscreen = !this.isFullscreen;
    },

    downLoad() {
      if (!this.url) {
        console.warn('没有可用的下载链接');
        this.$message.warning('没有可用的下载链接'); // 假设使用 Element UI 的消息提示
        return;
      }

      // 显示加载状态
      const loadingInstance = this.$loading({
        lock: true,
        text: '正在下载...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      const xhr = new XMLHttpRequest();
      xhr.open('GET', this.url, true);
      xhr.responseType = 'blob';

      xhr.onload = () => {
        if (xhr.status === 200) {
          const blob = new Blob([xhr.response], { type: 'application/pdf' });
          const url = window.URL.createObjectURL(blob);
          const link = document.createElement('a');
          link.href = url;
          // 尝试从响应头获取文件名，若没有则使用默认名
          const disposition = xhr.getResponseHeader('Content-Disposition');
          let fileName = '下载.pdf';
          if (disposition && disposition.indexOf('attachment') !== -1) {
            const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
            const matches = filenameRegex.exec(disposition);
            if (matches != null && matches[1]) { 
              fileName = matches[1].replace(/['"]/g, '');
            }
          }
          link.download = fileName;
          link.click();
          // 释放 URL 对象
          window.URL.revokeObjectURL(url);
        } else {
          console.error('下载失败，状态码: ', xhr.status);
          this.$message.error('下载失败，请稍后重试');
        }
        // 关闭加载状态
        loadingInstance.close();
      };

      xhr.onerror = () => {
        console.error('下载过程中发生网络错误');
        this.$message.error('下载过程中发生网络错误，请检查网络连接');
        // 关闭加载状态
        loadingInstance.close();
      };

      xhr.send();
    }
  },
};
</script>

<style>
.pre-dialog {
  height: 84vh;
  margin-top: 8vh !important;
  border-radius: 4px;
}

.pre-dialog .dialog__menu {
  float: right;
  padding-right: 24px;
  cursor: pointer;
}

.pre-dialog .dialog__menu:hover {
  color: #409eff;
}

.pre-dialog .el-dialog__body {
  height: calc(100% - 54px);
  padding: 0 10px 10px;
}

.pre-dialog.is-fullscreen {
  margin-top: 0 !important;
  border-radius: 0 !important;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

.pdf-iframe body,
.pdf-iframe .toolbarContainer {
  background-color: white;
}

.download {
  margin-right: 10px;
  padding: 10px;
  cursor: pointer;
  border-radius: 10px;
  color: #fff;
  border: 1px solid #ebeef5;
  box-sizing: border-box;
  background-color: #409eff;
}
</style>
