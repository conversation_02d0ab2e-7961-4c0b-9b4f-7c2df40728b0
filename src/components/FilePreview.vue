<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :modal="true"
    :fullscreen="isFullscreen"
    :append-to-body="true"
    custom-class="pre-dialog"
  >
    <div slot="title">
      <span class="el-dialog__title">文件预览</span>
      <div class="dialog__menu">
        <i @click="handleFullScreen" class="el-icon-full-screen"></i>
      </div>
    </div>

    <iframe
      v-if="dialogVisible && pdfUrl"
      :src="pdfUrl"
      frameborder="no"
      class="pdf-iframe"
    ></iframe>
  </el-dialog>
</template>

<script>
export default {
  name: 'VueFilePreview',
  props: {
    url: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
      isFullscreen: false,
      pdfUrl: undefined,
    }
  },
  watch: {
    url(newUrl) {
      if (newUrl != '') {
        // console.log(newUrl, 'newUrl')
        this.dialogVisible = true
        this.pdfUrl = 'cdn/pdfjs/web/viewer.html?file=' + newUrl
        // console.log(this.pdfUrl, '1')
      }
    },
  },
  methods: {
    handleFullScreen() {
      this.isFullscreen = !this.isFullscreen
    },
  },
}
</script>

<style>
.pre-dialog {
  height: 84vh;
  margin-top: 8vh !important;
  border-radius: 4px;
}

.pre-dialog .dialog__menu {
  float: right;
  padding-right: 24px;
  cursor: pointer;
}

.pre-dialog .dialog__menu:hover {
  color: #409eff;
}

.pre-dialog .el-dialog__body {
  height: calc(100% - 54px);
  padding: 0 10px 10px;
}

.pre-dialog.is-fullscreen {
  margin-top: 0 !important;
  border-radius: 0 !important;
}

.pdf-iframe {
  width: 100%;
  height: 100%;
  border: 0;
}

.pdf-iframe body,
.pdf-iframe .toolbarContainer {
  background-color: white;
}
</style>
