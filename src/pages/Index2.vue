<template>
  <div class="container">
    <!-- 头部 包括登录，我的订单等 -->
    <BaseHeader></BaseHeader>
    <!-- 搜索框、logo -->
    <!-- <Search></Search> -->

    <!-- 播报 -->
    <!-- <div class="news-scroll">
      <div class="news-scroll-title">
        <img src="../assets/images/notice.png" />
      </div>
      <div class="news-scroll-wrapper">
        <ul class="news-scroll-list">
          <li v-for="(item, index) in newsList" :key="index">{{ item }}</li>
        </ul>
        <ul class="news-scroll-list">
          <li v-for="(item, index) in newsList" :key="index + newsList.length">
            {{ item }}
          </li>
        </ul>
      </div>
    </div> -->

    <!-- 商品分类 -->
    <div class="container-item">
      <div class="left">
        <cateNav
          :showAlways="true"
          v-if="showNav"
          :large="carouselLarge"
          :opacity="carouselOpacity"
          :showNavBar="true"
        ></cateNav>
        <!-- <div class="left-item-name">成交动态</div>
        <div class="left-item">
          <div class="left-item-list" v-for="item in dealList">
            <div class="left-item-list-top">
              <span class="left-item-list-top-name">{{ item.goodsName }}</span>
              <span>
                <span>成交量 </span>
                <span style="color: #f31947; font-size: 16px">{{
                  item.num
                }}</span>
                <span> {{ item.unit }}</span>
              </span>
            </div>
            <div>
              <span>成交日期：{{ item.time }}</span>
            </div>
          </div>
        </div> -->
      </div>
      <div class="right">
        <Search></Search>
        <!-- 询价 -->
        <div class="right-item">
          <div class="right-item-left">
            <div class="left-item-name">询价</div>
            <div class="right-item-left-entrance" @click="goPriceInquiry" title="点击询价">
              <img src="../assets/images/entrance.png" />
              <span>我 要 询 价</span>
            </div>
          </div>
          <div class="right-item-right">
            <div class="left-item-name left-item-name2">
              <span class="name">成交动态</span>
            </div>
            <div class="right-item-right-entrance">
              <CreditInformation />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 商品 -->
    <div class="popular-products">
      <div class="left-item-name">热门市场</div>
      <div class="products-list" v-if="productsList.length">
        <!--  -->
        <div
          class="products-list-item"
          v-for="item in productsList"
          :key="item.id"
        >
          <div class="img" @click="goGoodsDetail(item.url)">
            <img :src="item.img" title="点击查看详情" />
          </div>
          <div class="content" @click="goGoodsDetail(item.url)" title="点击查看详情">
            <div class="content-name">{{ item.name }}</div>
            <div class="content-price">
              <span>单价：</span>
              <span
                style="font-size: 18px; color: #f31947; font-weight: bold"
                >{{ item.price }}</span
              >
              <span>&nbsp;元</span>
            </div>
            <div class="content-unit">
              <span>规 格：</span>
              <span>{{ item.unit }}</span>
            </div>
            <div class="shadow">
              <img src="../assets/images/cat.png" />
            </div>
          </div>
        </div>
      </div>
      <el-empty
        v-else
        style="height: 200px"
        description="暂无数据"
        :image-size="50"
      />
    </div>

    <!-- 底部栏 -->
    <BaseFooter></BaseFooter>
    <!-- 侧边栏 -->
    <fixedBar
      class="fixed-bar"
      :class="{ 'show-fixed': topSearchShow }"
    ></fixedBar>

    <ImagePreview :url="imageUrl" />
  </div>
</template>

<script>
import Search from "@/components/Search2";
import ModelForm from "@/components/indexDecorate/ModelForm";
import HoverSearch from "@/components/header/hoverSearch";
import fixedBar from "@/components/fixed/index";
import storage from "@/plugins/storage";
import { indexData, getAutoCoup } from "@/api/index.js";
import { seckillByDay } from "@/api/promotion";
import CreditInformation from "@/components/CreditInformation2.vue";
import ImagePreview from "@/components/ImagePreview.vue";
export default {
  name: "Index",
  mounted() {
    this.getIndexData();
    let that = this;
    window.onscroll = function () {
      let top = document.documentElement.scrollTop || document.body.scrollTop;
      if (top > 300) {
        that.topSearchShow = true;
      } else {
        that.topSearchShow = false;
      }
    };
    if (storage.getItem("userInfo")) {
      this.getAutoCoup();
    }
  },
  data() {
    return {
      autoCoupList: [],
      showCpmodel: false,
      modelForm: { list: [] }, // 楼层装修数据
      topAdvert: {}, // 顶部广告
      showNav: false, // 是否展示分类栏
      topSearchShow: false, // 滚动后顶部搜索栏展示
      carouselLarge: false, // 不同轮播分类尺寸
      carouselOpacity: false, // 不同轮播分类样式
      carouselList: [], // 轮播图
      productsList: [], // 商品
      newsList: [
        "1、入驻企业真实身份核验，实现企业合法登记",
        "2、电子合同、签章，保障交易真实性",
        "3、企业授信，交易货物风险评估，为每笔交易保驾护航",
      ],
      imageUrl: "", // 图片预览
    };
  },
  props: {
    pageData: {
      type: null,
      default: "",
    },
  },
  // created(){

  // },
  methods: {
    previewImg(img) {
      // this.imageUrl = img + "?time=" + new Date().getMilliseconds();
    },
    goGoodsDetail(url) {
      console.log(url);
      this.$router.push({
        path: url,
      });
    },
    goPriceInquiry() {
      this.$router.push({
        path: "/priceInquiry",
      });
    },

    // 优惠券可用范围
    useScope(type, storeName) {
      let shop = "平台";
      let goods = "全部商品";
      if (storeName !== "platform") shop = storeName;
      switch (type) {
        case "ALL":
          goods = "全部商品";
          break;
        case "PORTION_GOODS":
          goods = "部分商品";
          break;
        case "PORTION_GOODS_CATEGORY":
          goods = "部分分类商品";
          break;
      }
      return `${shop}${goods}可用`;
    },
    getAutoCoup() {
      let data = new Date();
      let datas = data.getDate();
      let hours = data.getHours();
      let flagCoup = storage.getItem("getTimes"); //缓存
      if (flagCoup && flagCoup != undefined && flagCoup != null) {
        //判断当前是否有缓存
        if (Number(datas) > Number(flagCoup)) {
          //判断缓存是否小于当前天数
          if (Number(hours) >= 6) {
            //超过或等于6时清楚缓存
            storage.setItem("getTimes", datas); //存储缓存
            this.getcps();
          }
        }
      } else {
        // window.localStorage.setItem('getTimes',datas)//存储缓存
        this.getcps();
      }
    },
    getcps() {
      console.log(123123);
      let data = new Date();
      let datas = data.getDate();
      getAutoCoup().then((res) => {
        //调用自动发券
        if (res.success) {
          this.autoCoupList.push(...res.result);
          let objs = {};
          this.autoCoupList = this.autoCoupList.reduce((cur, next) => {
            //对象去重
            if (next.id != undefined) {
              objs[next.id] ? "" : (objs[next.id] = true && cur.push(next));
            }
            return cur;
          }, []);
          if (this.autoCoupList != "" && this.autoCoupList.length > 0) {
            this.showCpmodel = true;
          }
          storage.setItem("getTimes", datas); //存储缓存
        }
      });
    },
    handleReachBottom() {},
    getIndexData() {
      if (this.pageData) {
        this.parsePageData(JSON.stringify(this.pageData));
      } else {
        // 获取首页装修数据
        indexData({ clientType: "PC" }).then(async (res) => {
          if (res.success && res.result) {
            this.parsePageData(res.result.pageData);
          }
        });
      }
    },

    async parsePageData(pageData) {
      let dataJson = JSON.parse(pageData);
      // 秒杀活动不是装修的数据，需要调用接口判断是否有秒杀商品
      // 轮播图根据不同轮播，样式不同
      for (let i = 0; i < dataJson.list.length; i++) {
        let type = dataJson.list[i].type;
        if (type === "carousel2") {
          this.carouselLarge = true;
        } else if (type === "carousel1") {
          this.carouselLarge = true;
          this.carouselOpacity = true;
        } else if (type === "seckill") {
          let seckill = await this.getListByDay();
          dataJson.list[i].options.list = seckill;
        }
      }
      this.modelForm = dataJson;
      storage.setItem("navList", dataJson.list[1]);
      this.showNav = true;
      this.topAdvert = dataJson.list[0];

      // console.log(dataJson.list[4].options.list, "--this.modelForm");
      // 轮播图
      this.carouselList = dataJson.list[2].options.list;
      // 商品
      this.productsList = dataJson.list[4].options.list[0]
        .slice(0, 6)
        .map((item) => {
          return {
            ...item,
            name: item.name.split(" ")[0],
            unit: item.name.split(" ")[1],
          };
        });
      console.log(this.productsList, "--this.productsList");
    },

    async getListByDay() {
      // 当天秒杀活动
      const res = await seckillByDay();
      if (res.success && res.result.length) {
        return res.result;
      } else {
        return [];
      }
    },
  },
  components: {
    Search,
    ModelForm,
    HoverSearch,
    fixedBar,
    CreditInformation,
    ImagePreview,
  },
};
</script>

<style scoped lang="scss">
@import "../assets/styles/coupon.scss";
.container {
  @include sub_background_color($light_background_color);

  background-color: #fff;
}

.left-item-name {
  font-size: 16.8px;
  font-weight: 700;
  color: #333333;
  letter-spacing: 0px;
  border-left: 5px solid $theme_color;
  padding-left: 10px;
  margin-bottom: 10px;
}

.container-item {
  display: flex;
  width: 1280px;
  margin: 0 auto;
  // margin-top: 20px;

  .left-item-name2 {
    display: flex;
    .name {
      width: calc(52% - 8px);
    }
    .name2 {
      border-left: 5px solid $theme_color;
      padding-left: 10px;
    }
  }
  .left {
    width: 280px;
    box-sizing: border-box;
    margin-top: 30px;
    .left-item {
      width: 100%;
      height: 300px;
      background: linear-gradient(to bottom, #ace3fb, #fff);
      border-radius: 10px;
      padding: 10px 20px;

      .left-item-list {
        width: 100%;
        height: 60px;
        background-color: #fff;
        border-radius: 5px;
        margin-bottom: 10px;
        padding: 5px 10px;
        box-sizing: border-box;
        .left-item-list-top {
          display: flex;
          justify-content: space-between;
          margin-bottom: 6px;
          .left-item-list-top-name {
            font-size: 15px;
            font-weight: 700;
            color: #333333;
            letter-spacing: 0px;
          }
        }
      }
    }
  }
  .right {
    flex: 1;
    padding-left: 65px;
    box-sizing: border-box;
    .right-item {
      width: calc(100% - 65px);
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;

      .right-item-left,
      .right-item-right {
        width: 25%;
        height: 220px;
        // background-color: #ccc;
        border-radius: 10px;
        // padding: 10px;
        box-sizing: border-box;
      }
      .right-item-right {
        width: 70%;
      }
      .right-item-right .right-item-right-entrance {
        height: calc(100% - 20px);
        // background: url("../assets/images/m_bg.jpg") no-repeat center;
        // background-size: 100%;
        background: linear-gradient(to bottom, #ace3fb, #fff);
        border-radius: 10px;
        padding: 10px;
        box-sizing: border-box;
        cursor: all-scroll;
        // display: flex;
        // justify-content: space-between;
        // align-items: center;

        // .right-item-right-entrance-left,
        // .right-item-right-entrance-right {
        //   width: 48%;
        //   height: 100%;
        //   background: url("../assets/images/instock.png") no-repeat center;
        //   background-size: 100%;
        //   cursor: pointer;
        // }
        // .right-item-right-entrance-right {
        //   background: url("../assets/images/future.png") no-repeat center;
        //   background-size: 100%;
        // }
      }
      .right-item-left .right-item-left-entrance {
        height: calc(100% - 20px);
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        background: linear-gradient(to bottom, #ace3fb, #fff);
        border-radius: 10px;
        box-sizing: border-box;
        // box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);

        img {
          margin-bottom: 20px;
        }
        span {
          font-size: 16px;
          color: #03aaf3;
          font-weight: bold;
        }
      }
    }
    .right-item2 {
      width: calc(100% - 65px);
      height: 300px;
      background-color: #ccc;
      border-radius: 10px;
    }
  }
}

.popular-products {
  width: 1280px;
  margin: 0 auto;
  margin-top: 30px;
  margin-bottom: 30px;

  .products-list {
    height: 800px;
    // background-color: #ccc;
    border-radius: 10px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 40px 100px;
    padding: 10px;
    box-sizing: border-box;
    .products-list-item {
      background-color: #fff;
      border-radius: 10px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
      cursor: pointer;

      .img {
        width: 100%;
        height: 220px;
        border-radius: 10px 10px 0 0;
        // cursor: zoom-in;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 10px 10px 0 0;
        }
      }
      .content {
        height: calc(100% - 220px);
        padding: 20px;
        box-sizing: border-box;
        position: relative;
        background: linear-gradient(to bottom, #fff, #ace3fb);
        border-radius: 0 0 10px 10px;

        .content-name {
          font-size: 16px;
          color: #333;
          font-weight: bold;
          margin-bottom: 10px;
          line-height: 1.5;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          letter-spacing: 2px;
        }

        .content-price {
          letter-spacing: 2px;
          margin-bottom: 10px;
        }

        .shadow {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 200px;
          height: 100px;
          background: linear-gradient(to bottom, #fff, #ace3fb);
          border-radius: 10px 10px 0px 0px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}

.news-scroll {
  width: 1200px;
  height: 40px;
  margin: 0 auto;
  overflow: hidden;
  background-color: #f8f9fa;
  // border: 1px solid #dee2e6;
  border-radius: 10px;
  // margin-bottom: 20px;
  position: relative;
}

.news-scroll-title {
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  background: #fff;
  height: 40px;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-right: 10px;
}

.news-scroll-wrapper {
  display: flex;
  height: 100%;
}

.news-scroll-list {
  display: flex;
  align-items: center;
  list-style: none;
  margin: 0;
  padding: 0 20px;
  animation: scroll 20s linear infinite; /* 滚动动画，20s 滚动一次，可按需调整 */

  li {
    white-space: nowrap;
    margin-right: 40px;
  }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
</style>
<style>
.hover-search {
  width: 100%;
  height: 60px;
  transform: translateY(-200px);
  background-color: #fff;
  position: fixed;
  top: 0;
  z-index: 9999;
  box-shadow: 0 0 10px 2px rgb(90 90 90 / 60%);
  transition: 0.35s;
}
.show {
  transform: translateY(0);
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
  top: 0;
}
.fixed-bar {
  opacity: 0 !important;
  transform: translateY(-10px);
  transition: 0.35s;
  z-index: 999999;
  height: 0px !important;
  overflow: hidden;
}
.show-fixed {
  height: 354px !important;
  opacity: 1 !important;
  transform: translateY(0);
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}

/* 2K */
@media screen and (min-width: 2561px) and (max-width: 3840px) {
  /* 样式 */
  .fixed-bar {
    position: fixed;
    right: 900px;
    top: 500px;
  }
}

/* 1080p */
@media screen and (max-width: 2560px) {
  /* 样式 */
  .fixed-bar {
    position: fixed;
    right: 300px;
    top: 500px;
  }
}

@media screen and (max-width: 2025px) {
  /* 样式 */
  .fixed-bar {
    position: fixed;
    right: 150px;
    top: 300px;
  }
}
</style>
