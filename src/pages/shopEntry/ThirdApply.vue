<template>
  <div class="person-msg">
    <Form ref="thirdForm" :model="form" :rules="rules" :label-width="260">
      <h4>基础信息</h4>
      <FormItem prop="storeName" label="店铺名称">
        <Input
          type="text"
          v-model="form.storeName"
          placeholder="请填写店铺名称"
        />
      </FormItem>

      <FormItem prop="storeLogo" label="店铺logo">
        <Upload
          ref="uploadLogo"
          :show-upload-list="false"
          :on-success="handleSuccess"
          :format="['jpg', 'jpeg', 'png', 'gif']"
          :max-size="2048"
          :before-upload="beforeUpload"
          :on-format-error="handleFormatError"
          :on-exceeded-size="handleMaxSize"
          :on-error="uploadErr"
          multiple
          :action="action"
          :headers="accessToken"
        >
          <Button type="info" :loading="uploadLoading">上传logo</Button>
        </Upload>
        <div class="describe">请压缩图片在2M以内，格式为gif，jpg，png</div>
        <div
          class="img-list"
          v-for="(item, index) in form.storeLogo"
          :key="index"
        >
          <img :src="item" width="100" height="" alt="" />
          <div class="cover">
            <Icon
              type="ios-eye-outline"
              @click.native="handleView(item)"
            ></Icon>
            <Icon
              type="ios-trash-outline"
              @click.native="handleRemove(index, 'storeLogo')"
            ></Icon>
          </div>
        </div>
      </FormItem>
      <FormItem prop="goodsManagementCategory" label="店铺经营类目">
        <Select
          v-model="form.goodsManagementCategory"
          multiple
          style="width: 300px"
        >
          <Option
            v-for="item in categoryList"
            :value="item.id"
            :key="item.id"
            >{{ item.name }}</Option
          >
        </Select>
      </FormItem>

      <FormItem prop="storeAddressIdPath" label="店铺所在地">
        <span>{{ form.storeAddressPath || '暂无地址' }}</span>
        <Button type="default" style="margin-left: 10px;" @click="$refs.map.open()">选择</Button>
      </FormItem>
      <FormItem prop="storeAddressDetail" label="店铺详细地址">
        <Input
          type="text"
          v-model="form.storeAddressDetail"
          placeholder="请填写店铺详细地址"
        />
      </FormItem>
      <FormItem prop="storeDesc" label="店铺简介">
        <Input
          type="textarea"
          v-model="form.storeDesc"
          maxlength="200"
          show-word-limit
          :rows="4"
          placeholder="请输入店铺简介"
        />
      </FormItem>

      <FormItem prop="settlementBankAccountName" label="银行开户名">
        <Input
          type="text"
          v-model="form.settlementBankAccountName"
          placeholder="请填写银行开户名称"
        />
      </FormItem>
      <FormItem prop="settlementBankAccountNum" label="银行账号">
        <Input
          type="text"
          v-model="form.settlementBankAccountNum"
          placeholder="请填写银行账号"
        />
      </FormItem>

      <FormItem prop="settlementBankJointName" label="支行联行号">
        <Input
          type="text"
          v-model="form.settlementBankJointName"
          placeholder="请填写支行联行号"
        />
      </FormItem>

      <FormItem prop="linkName" label="联系人姓名">
        <Input
          type="text"
          v-model="form.linkName"
          placeholder="请填写联系人姓名"
        />
      </FormItem>
      <FormItem prop="linkPhone" label="联系人电话">
        <Input
          type="text"
          v-model="form.linkPhone"
          placeholder="请填写联系人电话"
        />
      </FormItem>
      <FormItem prop="companyEmail" label="电子邮箱">
        <Input
          type="text"
          v-model="form.companyEmail"
          placeholder="请填写电子邮箱"
        />
      </FormItem>

      <FormItem label="">
        <Checkbox v-model="checked" @on-change="onChange">我已同意以上协议</Checkbox>
        <span @click="showAgreement = true" class="hover-pointer-color">《店铺入驻协议》</span>
      </FormItem>

      <FormItem>
        <!-- <Button @click="$emit('change', 1)">返回</Button> -->
        <Button @click="$router.push('/')">返回</Button>
        &nbsp;&nbsp;&nbsp;
        <Button type="primary" :loading="loading" @click="next"
          >提交平台审核</Button
        >
      </FormItem>
    </Form>

    <Modal title="店铺入驻协议" v-model="showAgreement" width="1200" :closable="true" :mask-closable="false">
      <Scroll :on-reach-bottom="handleReachBottom">
        <div class="agreeent-con" v-html="agreementCon"></div>
      </Scroll>

      <div slot="footer" style="text-align: center"></div>
    </Modal>

    <Modal title="View Image" v-model="visible">
      <img :src="previewPicture" v-if="visible" style="width: 100%" />
    </Modal>
    <multipleMap ref="map" @callback="getAddress" />
  </div>
</template>
<script>
import { applyThird, agreement, applyOne } from '@/api/shopentry';
import { getCategory } from '@/api/goods';

import storage from '@/plugins/storage';
import { commonUrl } from '@/plugins/request.js';


import multipleMap from "@/components/map/multiple-map";


export default {
  props: {
    content: {
      default: {},
      type: Object
    }
  },
  components: { multipleMap },
  data () {
    return {
      loading: false, // 加载状态
      uploadLoading: false, // 上传加载状态
      action: commonUrl + '/common/common/upload/file', // 上传地址
      accessToken: {}, // 验证token
      previewPicture: '', // 预览图片
      agreementCon: "", // 协议内容
      checked: false, // 选中协议
      showAgreement: false, // 协议显示

      visible: false, // 图片预览
      form: { // 表单数据
        storeLogo: []
      },
      rules: { // 验证规则
        goodsManagementCategory: [
          { required: true, message: '请选择店铺经营类目' }
        ],
        storeName: [{ required: true, message: '请填写店铺名称' }],
        storeLogo: [{ required: true, message: '请上传店铺logo' }],
        storeDesc: [{ required: true, message: '请填写店铺简介' }],
        storeCenter: [{ required: true, message: '请选择店铺位置' }],
        storeAddressIdPath: [{ required: true, message: '请选择店铺位置' }],
        storeAddressDetail: [{ required: true, message: '请输入店铺详细地址' }],
        // 验证规则
        settlementBankAccountName: [
          { required: true, message: "请填写银行开户名称" },
        ],
        settlementBankAccountNum: [
          { required: true, message: "请填写银行账号" },
        ],

        settlementBankJointName: [
          { required: true, message: "请填写支行联行号" },
        ],
        linkName: [{ required: true, message: '请填写联系人姓名' }],
        linkPhone: [
          { required: true, message: '请填写联系人电话' },
          { pattern: RegExp.mobile, message: '请填写正确的号码' }
        ],
        companyPhone: [
          { required: true, message: '请填写公司电话' },
          // { pattern: RegExp.mobile, message: '请填写正确的号码' }
        ],
        companyEmail: [
          { required: true, message: '请填写电子邮箱' },
          { type: 'email', message: '请输入正确的邮箱' }
        ],
      },
      categoryList: [] // 分类数据
    };
  },
  methods: {
    onChange(val) {
      this.checked = val;
    },
    getArticle() {
      // 入驻协议
      agreement().then((res) => {
        this.agreementCon = res.result.content;
      });
    },
    handleReachBottom(){

    },
    // 下一步
    next () {
      this.$refs.thirdForm.validate((valid) => {
        if (valid) {

          if (!this.checked) {
            this.$Message.warning('请勾选同意协议')
            return
          }

          this.loading = true;
          let params = JSON.parse(JSON.stringify(this.form));
          params.storeLogo = this.form.storeLogo.toString();
          params.goodsManagementCategory = this.form.goodsManagementCategory.toString();
          applyOne(params)
            .then((res) => {
              this.loading = false;
              if (res.success) this.$emit('change', 1);
              this.$parent.getData()
            })
            .catch(() => {
              this.loading = false;
            });
        } else {
          console.log('error');
        }
      });
    },
    // 上传之前
    beforeUpload () {
      this.uploadLoading = true;
      if (this.form.storeLogo.length >= 3) {
        this.$Message.warning('最多上传三张图片')
        return false;
      }
    },
    // 上传成功回调
    handleSuccess (res, file) {
      this.uploadLoading = false;
      this.form.storeLogo.push(res.result);
    },
    // 上传格式错误
    handleFormatError (file) {
      this.uploadLoading = false;
      this.$Notice.warning({
        title: 'The file format is incorrect',
        desc: '上传文件格式不正确'
      });
    },
    // 上传大小限制
    handleMaxSize (file) {
      this.uploadLoading = false;
      this.$Notice.warning({
        title: 'Exceeding file size limit',
        desc: '文件大小不能超过2M'
      })
    },
    // 上传失败
    uploadErr () {
      this.uploadLoading = false;
    },
    // 查看图片
    handleView (item) {
      this.previewPicture = item;
      this.visible = true;
    },
    // 删除图片
    handleRemove (index, listName) {
      this.form[listName].splice(index, 1);
    },
    // 选择坐标回调
    getAddress (val) {
      if(val.type === 'select'){
        const paths = val.data.map(item => item.name).join(',')
        const ids = val.data.map(item => item.id).join(',')
        this.$set(this.form, "storeAddressPath", paths);
        this.$set(this.form, "storeAddressIdPath", ids);
        this.form.storeCenter = val.data[val.data.length - 1].center
      }else{
        this.$set(this.form, "storeAddressPath", val.data.addr);
        this.$set(this.form, "storeAddressIdPath", val.data.addrId);
        this.$set(
          this.form,
          'storeCenter',
          val.data.position.lng + ',' + val.data.position.lat
        );
      }
    },
    // 获取商品分类
    getCategoryList () {
      getCategory(0).then((res) => {
        if (res.success) this.categoryList = res.result;
      });
    },


  },
  mounted () {
    this.getArticle();
    this.accessToken.accessToken = storage.getItem('accessToken');
    this.getCategoryList();
    if (this.content != {}) {
      this.form = JSON.parse(JSON.stringify(this.content));
      if (this.form.storeLogo) {
        this.form.storeLogo = this.content.storeLogo.split(',');
        this.form.goodsManagementCategory = this.content.goodsManagementCategory.split(
          ','
        );
      } else {
        this.form.storeLogo = [];
      }
      this.$forceUpdate();
    }
    this.$refs.thirdForm.resetFields()
  }
};
</script>
<style lang="scss" scoped>
h4 {
  margin-bottom: 10px;
  padding: 0 10px;
  border: 1px solid #ddd;
  background-color: #f8f8f8;
  font-weight: bold;
  color: #333;
  font-size: 14px;
  line-height: 40px;
  text-align: left;
}
.ivu-input-wrapper {
  width: 300px;
}
.img-list {
  display: inline-block;
  margin: 10px;
  width: 100px;
  height: auto;
  position: relative;
  .cover {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
    width: inherit;
    height: inherit;
    align-items: center;
    justify-content: space-around;
    i {
      color: #fff;
      font-size: 30px;
      cursor: pointer;
    }
  }
  &:hover .cover {
    display: flex;
  }
}
.describe {
  font-size: 12px;
  color: #999;
}
</style>
