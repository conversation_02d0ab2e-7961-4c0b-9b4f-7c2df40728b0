<template>
  <div class="PriceInquiry">
    <div class="inquiry-title">
      <div class="title-box">
        <div class="back" @click="$router.replace('/')">首页</div>
        <div>询价单</div>
        <div></div>
      </div>
    </div>
    <div class="container">
      <div class="inquiry-form">
        <el-form
          :model="formData"
          :rules="rules"
          ref="formRef"
          label-width="auto"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <Title title="产品信息" />
            </el-col>

            <el-col :span="8">
              <el-form-item label="商品名称：" prop="commodityListName">
                <el-input
                  v-model="formData.commodityListName"
                  placeholder="请输入商品名称"
                  size="large"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="规格型号：" prop="commoditySpec">
                <el-input
                  v-model="formData.commoditySpec"
                  placeholder="请输入规格型号"
                  size="large"
                >
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="购买数量：" prop="purchaseQuantity">
                <el-input-number
                  v-model="formData.purchaseQuantity"
                  size="large"
                  style="width: 100%"
                  :min="1"
                />
              </el-form-item>
            </el-col>

            <!-- <el-col :span="16">占位</el-col> -->
            <el-col :span="24">
              <Title title="求购需求" />
            </el-col>

            <el-col :span="8">
              <el-form-item label="交货方式：" prop="tradeModel">
                <el-select
                  v-model="formData.tradeModel"
                  placeholder="请选择交货方式"
                  size="large"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in tradeModelDict"
                    :key="item.dictKey"
                    :label="item.dictValue"
                    :value="item.dictKey"
                  />
                </el-select>
              </el-form-item>
            </el-col>

            <!-- <el-col :span="16">占位</el-col> -->

            <el-col :span="8">
              <el-form-item label="交货日期：" prop="deliveryDate">
                <DatePicker
                  size="large"
                  v-model:value="formData.deliveryDate"
                  placeholder="期望交货日期"
                  format="yyyy-MM-dd"
                  type="date"
                  style="width: 100%"
                  @on-change="
                    (time, type) => changeTime(time, type, 'deliveryDate')
                  "
                  :options="{
                    disabledDate: (time) =>
                      time.getTime() < Date.now() - 8.64e7,
                  }"
                ></DatePicker>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item prop="companyAddressIdPath" label="收货地区：">
                <span :class="{ mr_20: formData.companyAddressPath }">{{
                  formData.companyAddressPath || ""
                }}</span>
                <el-button type="default" @click="$refs.map.open()"
                  >选择</el-button
                >
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="详细地址：" prop="deliveryPlace">
                <el-input
                  v-model="formData.deliveryPlace"
                  style="width: 100%"
                  :rows="4"
                  type="textarea"
                  placeholder="请输入交货地点"
                  size="large"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报价截止时间：" prop="priceQuoteEndDate">
                <DatePicker
                  size="large"
                  v-model:value="formData.priceQuoteEndDate"
                  placeholder="报价截止时间"
                  format="yyyy-MM-dd"
                  type="date"
                  style="width: 100%"
                  @on-change="
                    (time, type) => changeTime(time, type, 'priceQuoteEndDate')
                  "
                  :options="{
                    disabledDate: (time) =>
                      time.getTime() < Date.now() - 8.64e7,
                  }"
                ></DatePicker>
              </el-form-item>
            </el-col>

            <!-- <el-col :span="16">占位</el-col> -->

            <el-col :span="8">
              <el-form-item label="配送方式：" prop="deliveryModel">
                <el-select
                  v-model="formData.deliveryModel"
                  placeholder="请选择交货方式"
                  size="large"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in deliveryModelDict"
                    :key="item.dictKey"
                    :label="item.dictValue"
                    :value="item.dictKey"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="询价详情描述：" prop="detailedSescription">
                <el-input
                  v-model="formData.detailedSescription"
                  style="width: 100%"
                  :rows="4"
                  type="textarea"
                  placeholder="请输入询价详情描述"
                  size="large"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="附件：" prop="attchUrl">
                <div class="file-upload-container">
                  <Upload
                    multiple
                    type="drag"
                    :action="action"
                    :headers="accessToken"
                    :on-success="handleSuccess"
                    :before-upload="handleBeforeUpload"
                    :on-preview="handlePreview"
                    :on-remove="handleFileRemove"
                    :on-error="handleError"
                  >
                    <div style="padding: 20px 0">
                      <Icon
                        type="ios-cloud-upload"
                        size="52"
                        style="color: #3399ff"
                      ></Icon>
                      <p class="text">将文件拖到此处,或<em>点击上传</em></p>
                    </div>
                  </Upload>
                </div>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <Title title="交易信息" />
            </el-col>

            <el-col :span="8">
              <el-form-item label="结算方式：" prop="settlementModel">
                <el-select
                  v-model="formData.settlementModel"
                  placeholder="请选择结算方式"
                  size="large"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in settlementModelDict"
                    :key="item.dictKey"
                    :label="item.dictValue"
                    :value="item.dictKey"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="formData.settlementModel == 1">
              <el-form-item label="定金比例：" prop="earnestMoneyRatio">
                <el-input
                  v-model="formData.earnestMoneyRatio"
                  placeholder="请输入定金比例"
                >
                  <template #append>%</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="16"></el-col>
            <el-col :span="24">
              <Title title="联系人" />
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系人：" prop="contactName">
                <el-input
                  v-model="formData.contactName"
                  placeholder="请输入联系人"
                  size="large"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="联系电话：" prop="contactPhone">
                <el-input
                  v-model="formData.contactPhone"
                  placeholder="请输入联系电话"
                  size="large"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>

        <!-- 询价结果 -->
        <footer>
          <el-button size="large" type="primary" round @click="handleInquiry"
            >立即询价</el-button
          >
        </footer>
      </div>
    </div>
    <!-- 底部栏 -->
    <BaseFooter></BaseFooter>
    <!-- 侧边栏 -->
    <fixedBar
      class="fixed-bar"
      :class="{ 'show-fixed': topSearchShow }"
    ></fixedBar>

    <FilePreview :url="fileUrl" />
    <ImagePreview :url="imageUrl" />

    <multipleMap ref="map" @callback="getAddress" />
  </div>
</template>
<script>
import fixedBar from "@/components/fixed/index";
import FilePreview from "@/components/FilePreview.vue";
import ImagePreview from "@/components/ImagePreview.vue";
import Title from "@/components/Title.vue";
import { commonUrl } from "@/plugins/request.js";
import storage from "@/plugins/storage";
import { submit } from "@/api/priceInquiry.js";
import multipleMap from "@/components/map/multiple-map";

export default {
  name: "PriceInquiry",
  components: {
    fixedBar,
    FilePreview,
    ImagePreview,
    Title,
    multipleMap,
  },
  data() {
    return {
      action: commonUrl + "/common/common/upload/file", // 上传地址
      accessToken: {}, // 验证token
      topSearchShow: false,
      formData: {
        commodityListName: "", // 商品名称
        commoditySpec: "", // 规格型号
        purchaseQuantity: 1, // 购买数量
        tradeModel: "", // 交货方式
        deliveryPlace: "", // 交货地点
        deliveryDate: "", // 交货日期
        priceQuoteEndDate: "", // 报价截止时间
        deliveryModel: "", // 配送方式
        detailedSescription: "", // 询价详情描述
        attchUrl: "", // 附件id
        settlementModel: "", // 结算方式
        earnestMoneyRatio: "", // 定金比例
        contactName: "", // 联系人
        contactPhone: "", // 联系电话
      },
      rules: {
        commodityListName: [
          { required: true, message: "请输入商品名称", trigger: "blur" },
        ],
        commoditySpec: [
          { required: true, message: "请输入规格型号", trigger: "blur" },
        ],
        purchaseQuantity: [
          { required: true, message: "请输入购买数量", trigger: "blur" },
        ],
        tradeModel: [
          { required: true, message: "请选择交货方式", trigger: "blur" },
        ],
        companyAddressIdPath: [
          { required: true, message: "请输入收货地区", trigger: "blur" },
        ],
        deliveryPlace: [
          { required: true, message: "请输入详细地址", trigger: "blur" },
        ],
        deliveryDate: [
          { required: true, message: "请输入交货日期", trigger: "blur" },
        ],
        priceQuoteEndDate: [
          { required: true, message: "请输入报价截止时间", trigger: "blur" },
        ],
        deliveryModel: [
          { required: true, message: "请选择配送方式", trigger: "blur" },
        ],
        detailedSescription: [
          { required: false, message: "请输入询价详情描述", trigger: "blur" },
        ],
        attchUrl: [{ required: false, message: "请上传附件", trigger: "blur" }],
        settlementModel: [
          { required: true, message: "请选择结算方式", trigger: "blur" },
        ],
        earnestMoneyRatio: [
          { required: true, message: "请输入定金比例", trigger: "blur" },
        ],
        contactName: [
          { required: true, message: "请输入联系人", trigger: "blur" },
        ],
        contactPhone: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
        ],
      },
      formRef: null,
      tradeModelDict: [
        {
          dictKey: 1,
          dictValue: "现货交易",
        },
        {
          dictKey: 2,
          dictValue: "期货交易",
        },
        {
          dictKey: 3,
          dictValue: "混合交易",
        },
      ],
      deliveryModelDict: [
        {
          dictKey: 1,
          dictValue: "买方自提",
        },
        {
          dictKey: 2,
          dictValue: "供方承运",
        },
      ],
      settlementModelDict: [
        {
          dictKey: 1,
          dictValue: "定金发货",
        },
        {
          dictKey: 2,
          dictValue: "全额付款",
        },
        // {
        //   dictKey: 3,
        //   dictValue: "分期付款",
        // },
      ],
      fileListData: [],
      fileUrl: "",
      imageUrl: "",
    };
  },
  computed: {
    userInfo() {
      // 用户信息
      if (storage.getItem("userInfo")) {
        return JSON.parse(storage.getItem("userInfo"));
      } else {
        return {};
      }
    },
  },
  methods: {
    // 获取店铺地址
    getAddress(val) {
      if (val.type === "select") {
        const paths = val.data.map((item) => item.name).join(",");
        const ids = val.data.map((item) => item.id).join(",");
        this.$set(this.formData, "companyAddressIdPath", ids);
        this.$set(this.formData, "companyAddressPath", paths);
      } else {
        this.$set(this.formData, "companyAddressIdPath", val.data.addrId);
        this.$set(this.formData, "companyAddressPath", val.data.addr);
      }
    },
    // 格式化时间
    changeTime(time, type, name) {
      if (time) {
        if (name === "deliveryDate") {
          this.formData.deliveryDate = time;
        } else {
          this.formData.priceQuoteEndDate = time;
        }
      }
    },
    async handleInquiry() {
      try {
        await this.$refs.formRef.validate();
        console.log(this.formData, "formData");

        console.log(this.fileListData, "----this.fileListData");

        if (this.fileListData.length > 0) {
          this.formData.attchUrl = this.fileListData
            .map((item) => item.response.result)
            .join(",");
        }

        let params = {
          ...this.formData,
          deliveryPlace:
            this.formData.companyAddressPath +
            "," +
            this.formData.deliveryPlace +
            "",
        };

        const res = await submit(params);

        console.log(res, "----");
        if (res.success) {
          // this.$message.success("询价成功");

          this.$confirm("询价成功, 是否前往询价列表查看？", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "继续询价",
            type: "warning",
          })
            .then(() => {
              this.$router.push({ path: "/home/<USER>" });
            })
            .catch(() => {
              window.location.reload();
            });
        }
      } catch (e) {
        console.log(e);
      }
    },
    handleError(file, fileList) {
      console.log(file, "file");
      console.log(fileList, "fileList");
      this.$message.error(fileList.message);
    },
    handleSuccess(response, file, fileList) {
      console.log(fileList, "fileList");

      this.fileListData = fileList;
    },

    handleFileRemove(file, fileList) {
      // uploadForm.value.contractInfoList = fileList
      console.log(file, "file");
      console.log(fileList, "fileList");
      // this.fileListData = [];
    },
    handlePreview(file) {
      console.log(file, "--------------");
      const fileName = file.name;
      const lowerCaseFileName = fileName.toLowerCase();
      const targetUrl = file.response.result;
      if (lowerCaseFileName.endsWith(".pdf")) {
        this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      } else {
        this.imageUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      }
    },
    handleBeforeUpload(file) {
      console.log(file, "---file");
      const type = file.type;
      const isOver50MB = file.size > 50 * 1024 * 1024;
      if (
        ["image/jpg", "image/jpeg", "image/png", "application/pdf"].includes(
          type
        )
      ) {
        if (isOver50MB) {
          this.$message.error("文件大小不能超过50M");
          return false;
        }
      } else {
        this.$message.error("文件格式错误");
        return false;
      }
    },
    goUserCenter() {
      // 跳转我的订单，我的足迹、收藏等
      if (this.accessToken.accessToken) {
        if (this.userInfo.status == 4) {
          // 已企业实名
        } else {
          this.$Modal.confirm({
            title: "请先企业实名",
            content: "<p>请企业实名后执行此操作</p>",
            okText: "立即实名",
            cancelText: "继续浏览",
            onOk: () => {
              this.$router.push({
                path: "/signUp",
                query: {
                  rePath: this.$router.history.current.path,
                  query: JSON.stringify(this.$router.history.current.query),
                },
              });
            },
            onCancel: () => {
              this.$router.push({ path: "/" });
            },
          });
        }
      } else {
        this.$Modal.confirm({
          title: "请登录",
          content: "<p>请登录后执行此操作</p>",
          okText: "立即登录",
          cancelText: "继续浏览",
          onOk: () => {
            this.$router.push({
              path: "/login",
              query: {
                rePath: this.$router.history.current.path,
                query: JSON.stringify(this.$router.history.current.query),
              },
            });
          },
          onCancel: () => {
            this.$router.push({ path: "/" });
          },
        });
      }
    },
  },
  mounted() {
    this.accessToken.accessToken = storage.getItem("accessToken");
    let that = this;
    window.onscroll = function () {
      let top = document.documentElement.scrollTop || document.body.scrollTop;
      if (top > 300) {
        that.topSearchShow = true;
      } else {
        that.topSearchShow = false;
      }
    };
    // 判断是否登录
    this.goUserCenter();
  },
};
</script>
<style lang="scss" scoped>
.PriceInquiry {
  height: 100%;
  // background-color: #ffffff;
  .inquiry-title {
    font-size: 24px;
    color: #333;
    text-align: center;
    background-color: #333;
    height: 60px;
    line-height: 60px;

    .title-box {
      display: flex;
      width: 1200px;
      margin: 0 auto;
      justify-content: space-between;
      color: #fff;

      .back {
        font-size: 13px;
        cursor: pointer;
        color: #eee;
      }
    }
  }
  .container {
    width: 1200px;
    margin: 0 auto;

    .inquiry-form {
      margin-top: 48px;
    }

    footer {
      margin-top: 48px;
      text-align: center;
      margin-bottom: 48px;
      .el-button {
        width: 300px;
        font-size: 24px;
        height: 60px;
      }
    }
  }
}

.file-upload-container {
  .text {
    em {
      color: #3399ff;
    }
  }
}

.fixed-bar {
  opacity: 0 !important;
  transform: translateY(-10px);
  transition: 0.35s;
  z-index: 999999;
  height: 0px !important;
  overflow: hidden;
}
.show-fixed {
  height: 354px !important;
  opacity: 1 !important;
  transform: translateY(0);
  -webkit-transform: translateZ(0);
  -moz-transform: translateZ(0);
  -ms-transform: translateZ(0);
  -o-transform: translateZ(0);
  transform: translateZ(0);
}
/* 2K */
@media screen and (min-width: 2561px) and (max-width: 3840px) {
  /* 样式 */
  .fixed-bar {
    position: fixed;
    right: 900px;
    top: 500px;
  }
}

/* 1080p */
@media screen and (max-width: 2560px) {
  /* 样式 */
  .fixed-bar {
    position: fixed;
    right: 300px;
    top: 500px;
  }
}

@media screen and (max-width: 2025px) {
  /* 样式 */
  .fixed-bar {
    position: fixed;
    right: 150px;
    top: 300px;
  }
}
</style>
