<template>
  <div class="search">
    <card _Title="结算单" :_Size="16" />

    <!-- 搜索 筛选 -->
    <div class="mb_24">
      <div class="searchList">
        <Input
          class="mr_20"
          enter-button
          v-model="searchForm.orderSn"
          @on-search="getDataList"
          placeholder="请输入订单编号搜索"
        />
        <Input
          class="mr_20"
          enter-button
          v-model="searchForm.goodsName"
          @on-search="getDataList"
          placeholder="请输入商品名称搜索"
        />
        <Button @click="handleSearch" type="primary" class="search-btn mr_20"
          >搜索</Button
        >
        <Button @click="handleReset" class="search-btn">重置</Button>
      </div>
    </div>
    <Table
      :loading="loading"
      border
      :columns="columns"
      :data="data"
      ref="table"
    >
      <template slot-scope="{ row }" slot="pdfUrlSlot">
        <div class="preview-btn">
          <span
            class="mr_10 hover-pointer-color"
            @click="previewBtn(row, 'preview')"
            >预览</span
          >
          <span class="hover-pointer-color" @click="previewBtn(row, 'download')"
            >下载</span
          >
        </div>
      </template>
    </Table>
    <Row type="flex" justify="end" class="mt_10">
      <Page
        :current="searchForm.pageNumber"
        :total="total"
        :page-size="searchForm.pageSize"
        @on-change="changePage"
        @on-page-size-change="changePageSize"
        :page-size-opts="[10, 20, 50]"
        size="small"
        show-total
        show-elevator
        show-sizer
      ></Page>
    </Row>

    <FilePreview :url="fileUrl" />
  </div>
</template>

<script>
import FilePreview from "@/components/FilePreview.vue";
import { orderItemSettlement } from "@/api/order";

export default {
  name: "SettlementDoc",
  components: {
    FilePreview,
  },
  data() {
    return {
      loading: true, // 表单加载状态
      searchForm: {
        // 搜索框初始化对象
        pageNumber: 1, // 当前页数
        pageSize: 10, // 页面大小
        // sort: "", // 默认排序字段
        // order: "", // 默认排序方式
        // startDate: "", // 起始时间
        // endDate: "", // 终止时间
        // orderSn: "",
        // buyerName: "",
        // orderStatus: "",
        // orderType: "NORMAL",
      },
      selectDate: null,
      columns: [
        // {
        //   title: "会员id",
        //   key: "memberId",
        //   minWidth: 200,
        //   tooltip: true,
        // },
        // {
        //   title: "店铺id",
        //   key: "storeId",
        //   minWidth: 200,
        //   tooltip: true,
        // },
        {
          title: "订单编号",
          key: "orderSn",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "企业名称",
          key: "companyName",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "店铺名称",
          key: "storeName",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "商品名称",
          key: "goodsName",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "合同编号",
          key: "mainContractNo",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "结算单",
          key: "pdfUrl",
          minWidth: 120,
          slot: "pdfUrlSlot",
          fixed: "right",
          align: "center",
        },
      ],
      data: [], // 表单数据
      total: 0, // 表单数据总数

      fileUrl: "",
    };
  },
  methods: {
    // 下载文件
    previewBtn(v, type) {
      // let url = "https://test.jingruiit.com:9002/supplychain/1919938508597243906.pdf";
      let url = v.pdfUrl;
      if (!url) return this.$message.error("文件不存在");
      if (type == "preview") {
        this.fileUrl = url + "?time=" + new Date().getMilliseconds();
      } else {
        // window.open(
        //   url + "?attname=&response-content-type=application/octet-stream"
        // );
        // 使用 fetch 发起请求下载文件
        fetch(url, {
          headers: {
            "Content-Type": "application/octet-stream",
          },
        })
          .then((response) => {
            if (!response.ok) {
              throw new Error("网络响应失败");
            }
            return response.blob();
          })
          .then((blob) => {
            // 创建临时 URL
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement("a");
            a.href = url;
            // 可根据实际情况设置文件名
            a.download = "结算单.pdf";
            a.click();
            // 释放临时 URL
            window.URL.revokeObjectURL(url);
          })
          .catch((error) => {
            this.$message.error(`文件下载失败: ${error.message}`);
          });
      }
    },
    // 初始化数据
    init() {
      this.getDataList();
    },
    // 清理搜索表单中的空值
    cleanSearchForm() {
      const fields = ["storeId", "memberId"];
      fields.forEach((field) => {
        if (!this.searchForm[field]) {
          delete this.searchForm[field];
        }
      });
    },
    // 改变页码
    changePage(v) {
      this.searchForm.pageNumber = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 改变页数
    changePageSize(v) {
      this.searchForm.pageSize = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 搜索订单
    handleSearch() {
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 重置
    handleReset() {
      this.searchForm = {
        pageNumber: 1,
        pageSize: 10,
        storeId: "",
        memberId: "",
      };
      // 重新加载数据
      this.cleanSearchForm();
      this.getDataList();
    },
    // 获取表格数据
    getDataList() {
      this.loading = true;
      orderItemSettlement(this.searchForm).then((res) => {
        this.loading = false;
        if (res.success) {
          this.data = res.result.records;
          this.total = res.result.total;
        }
      });
    },
  },
  mounted() {
    this.init();
  },
  // 页面缓存处理，从该页面离开时，修改KeepAlive为false，保证进入该页面是刷新
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false;
    next();
  },
};
</script>
<style lang="scss" scoped>
.searchList {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
