<template>
  <div class="search">
    <!-- <card _Title="可开票订单" :_Size="16"></card> -->
    <!-- 搜索 筛选 -->
    <div class="mb_10">
      <div class="searchList">
        <Input
          class="mr_20"
          enter-button
          v-model="searchForm.orderSn"
          @on-search="getDataList"
          placeholder="请输入订单编号搜索"
        />
        <Input
          class="mr_20"
          enter-button
          v-model="searchForm.orderItemSn"
          @on-search="getDataList"
          placeholder="请输入子订单编号搜索"
        />
        <Input
          class="mr_20"
          enter-button
          v-model="searchForm.storeName"
          @on-search="getDataList"
          placeholder="请输入店铺名称搜索"
        />
        <Button @click="handleSearch" type="primary" class="search-btn mr_20"
          >搜索</Button
        >
        <Button @click="handleReset" class="search-btn">重置</Button>
      </div>
      <div class="searchList mt_10">
        <Button
          icon="ios-cloud-download-outline"
          @click="handleBatchExport"
          type="success"
          class="search-btn mr_20"
          >批量开票</Button
        >
      </div>
    </div>
    <Table
      :loading="loading"
      border
      :columns="columns"
      :data="data"
      ref="table"
      @on-selection-change="selectChange"
    >
      <!-- 添加开票操作列 -->
      <template slot-scope="{ row }" slot="actionSlot">
        <div class="action-btn">
          <span
            v-if="row.applyInvoicingStatus == 'NOT_APPLIED'"
            class="mr_10 hover-pointer-color"
            @click="openInvoiceModal(row)"
            >申请开票</span
          >
          <span v-else>--</span>
        </div>
      </template>
    </Table>
    <Row type="flex" justify="end" class="mt_10">
      <Page
        :current="searchForm.pageNumber"
        :total="total"
        :page-size="searchForm.pageSize"
        @on-change="changePage"
        @on-page-size-change="changePageSize"
        :page-size-opts="[10, 20, 50, 100]"
        size="small"
        show-total
        show-elevator
        show-sizer
      ></Page>
    </Row>

    <!-- 添加开票弹窗 -->
    <el-dialog
      append-to-body
      :visible.sync="invoiceModalVisible"
      width="30%"
      title="申请开票"
      @close="closeInvoiceModal"
    >
      <div class="invoice-form">
        <el-Form
          :model="invoiceForm"
          ref="invoiceFormRef"
          :rules="invoiceRules"
          label-position="left"
          label-width="auto"
        >
          <el-form-item label="发票类型" prop="receiptType">
            <RadioGroup
              v-model="invoiceForm.receiptType"
              type="button"
              button-style="solid"
            >
              <Radio label="ELECTRONIC_INVOICE">电子普通发票</Radio>
              <Radio label="SPECIAL_VAT_INVOICE">增值税专用发票</Radio>
            </RadioGroup>
          </el-form-item>

          <el-form-item label="发票抬头" prop="receiptTitle">
            <el-input
              v-model="invoiceForm.receiptTitle"
              placeholder="请输入发票抬头"
              :disabled="invoiceForm.receiptTitle ? true : false"
            />
          </el-form-item>

          <el-form-item label="纳税人识别号" prop="taxpayerId">
            <el-input
              v-model="invoiceForm.taxpayerId"
              placeholder="请输入纳税人识别号"
              :disabled="invoiceForm.taxpayerId ? true : false"
            />
          </el-form-item>

          <el-form-item label="发票内容" prop="receiptContent">
            <RadioGroup
              v-model="invoiceForm.receiptContent"
              type="button"
              button-style="solid"
            >
              <Radio label="商品内容">商品内容</Radio>
              <Radio label="商品类别">商品类别</Radio>
            </RadioGroup>
          </el-form-item>
        </el-Form>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="closeInvoiceModal">取 消</el-button>
        <el-button
          type="primary"
          :loading="submitLoading"
          @click="submitInvoice"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <FilePreview :url="fileUrl" />
    <ImagePreview :url="imageUrl" />
    <MultifilePlayback
      :fileListData="MultiFileListData"
      @closePreview="closePreview"
      :title="MultifilePlaybackTitle"
    />
  </div>
</template>

<script>
import FilePreview from "@/components/FilePreview.vue";
import ImagePreview from "@/components/ImagePreview.vue";
import MultifilePlayback from "@/components/MultifilePlayback.vue";
import { pendingInvoicingPage, applyReceipt } from "@/api/receipt";

export default {
  name: "PendingReceipt",
  components: {
    FilePreview,
    ImagePreview,
    MultifilePlayback,
  },
  data() {
    return {
      loading: true, // 表单加载状态
      searchForm: {
        pageNumber: 1,
        pageSize: 10,
        sn: "",
        storeName: "",
      },
      columns: [
        {
          type: "selection",
          width: 60,
          align: "center",
        },
        {
          title: "订单编号",
          key: "orderSn",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "子订单编号",
          key: "sn",
          minWidth: 240,
          tooltip: true,
        },
        // {
        //   title: "企业名称",
        //   key: "companyName",
        //   minWidth: 240,
        //   tooltip: true,
        // },
        {
          title: "店铺名称",
          key: "storeName",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "商品名称",
          key: "goodsName",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "订单金额",
          key: "goodsPrice",
          width: 120,
          tooltip: true,
        },
        {
          title: "操作",
          key: "action",
          minWidth: 120,
          slot: "actionSlot",
          fixed: "right",
          align: "center",
        },
      ],
      data: [], // 表单数据
      total: 0, // 表单数据总数

      fileUrl: "",
      imageUrl: "",
      MultifilePlaybackTitle: "发票凭证", // 多文件回显标题
      MultiFileListData: [], // 多文件回显数据

      // 开票弹窗相关数据
      invoiceModalVisible: false,
      invoiceForm: {
        receiptType: "ELECTRONIC_INVOICE",
        receiptTitle: "",
        taxpayerId: "",
        receiptContent: "商品内容",
        orderItemSnList: [],
      },
      invoiceRules: {
        receiptType: [
          { required: true, message: "请选择发票类型", trigger: "change" },
        ],
        receiptTitle: [
          { required: true, message: "请输入发票抬头", trigger: "blur" },
        ],
        // 只有当发票类型为2时才验证纳税人识别号
        taxpayerId: [
          { required: true, message: "请输入纳税人识别号", trigger: "blur" },
        ],
        receiptContent: [
          { required: true, message: "请选择发票内容", trigger: "change" },
        ],
      },
      submitLoading: false,
      selectionList: [], // 选中的订单列表
      orderItemSnList: [], // 选中的订单编号列表
    };
  },
  methods: {
    // 选中
    selectChange(selection) {
      this.selectionList = selection;
    },
    // 批量开票
    handleBatchExport() {
      if (this.selectionList.length === 0) {
        this.$Message.warning("请选择要开票的订单");
        return;
      }

      // 检查所有选中行是否来自同一个店铺
      const storeIds = new Set();
      const notAppliedOrders = [];
      this.orderItemSnList = [];

      // 遍历选中的行，收集 storeId 和检查订单状态
      this.selectionList.forEach((row) => {
        storeIds.add(row.storeName);
        this.orderItemSnList.push(row.sn);

        // 检查订单是否已申请开票
        if (row.applyInvoicingStatus !== "NOT_APPLIED") {
          notAppliedOrders.push(row.sn);
        }
      });

      // 检查是否来自同一个店铺
      if (storeIds.size > 1) {
        this.$Modal.warning({
          title: "提示",
          content: "批量开票只能选择同一个店铺的订单，请重新选择",
        });
        return;
      }

      // 检查是否有已申请开票的订单
      if (notAppliedOrders.length > 0) {
        this.$Modal.warning({
          title: "提示",
          content: `以下订单已申请开票，不能重复申请：${notAppliedOrders.join(
            ", "
          )}`,
        });
        return;
      }

      let row = this.selectionList[0];

      // 所有检查通过，打开批量开票弹窗
      this.openInvoiceModal(row);
    },
    // 开票弹窗相关方法
    openInvoiceModal(row) {
      // 确保所有字段都有初始值
      this.invoiceForm = {
        receiptType: "ELECTRONIC_INVOICE",
        receiptTitle: row.corpName || "",
        taxpayerId: row.businessLicenceNumber || "",
        receiptContent: "商品内容",
        orderItemSnList: row.sn.split(","),
      };

      this.invoiceModalVisible = true;
    },

    closeInvoiceModal() {
      this.invoiceModalVisible = false;
      this.$refs.invoiceFormRef && this.$refs.invoiceFormRef.resetFields();
      this.invoiceForm = {
        receiptType: "ELECTRONIC_INVOICE",
        receiptTitle: "",
        taxpayerId: "",
        receiptContent: "商品内容",
        orderItemSnList: "",
      };
      this.orderItemSnList = [];
    },

    submitInvoice() {
      this.$refs.invoiceFormRef.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;

          try {
            // 构建提交参数
            let snListTemp = [];
            if (this.orderItemSnList.length) {
              snListTemp = this.orderItemSnList;
            } else {
              snListTemp = this.invoiceForm.orderItemSnList;
            }
            const params = {
              ...this.invoiceForm,
              orderItemSnList: snListTemp,
            };

            // 调用申请开票API
            const res = await applyReceipt(params);

            if (res.success) {
              this.closeInvoiceModal();
              this.getDataList(); // 刷新列表
              // 提示成功
              this.$message.success("申请开票成功");
            } else {
              this.$Message.error(res.message || "申请开票失败");
            }
          } catch (error) {
            console.error("申请开票异常:", error);
            this.$Message.error("系统异常，请稍后重试");
          } finally {
            this.submitLoading = false;
          }
        }
      });
    },

    // 关闭预览,清空 MultiFileListData
    closePreview(data) {
      if (!data) {
        this.MultiFileListData = [];
      }
    },
    // 初始化数据
    init() {
      this.getDataList();
    },
    // 清理搜索表单中的空值
    cleanSearchForm() {
      const fields = ["sn", "storeName"];
      fields.forEach((field) => {
        if (!this.searchForm[field]) {
          delete this.searchForm[field];
        }
      });
    },
    // 改变页码
    changePage(v) {
      this.searchForm.pageNumber = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 改变页数
    changePageSize(v) {
      this.searchForm.pageSize = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 搜索订单
    handleSearch() {
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 重置
    handleReset() {
      this.searchForm = {
        pageNumber: 1,
        pageSize: 10,
        sn: "",
        storeName: "",
      };
      // 重新加载数据
      this.cleanSearchForm();
      this.getDataList();
    },
    // 获取表格数据
    getDataList() {
      this.loading = true;
      pendingInvoicingPage(this.searchForm).then((res) => {
        this.loading = false;
        if (res.success) {
          this.data = res.result.records;
          this.total = res.result.total;
        }
      });
    },
  },
  mounted() {
    this.init();
  },
  // 页面缓存处理，从该页面离开时，修改KeepAlive为false，保证进入该页面是刷新
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false;
    next();
  },
};
</script>
<style lang="scss" scoped>
.searchList {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// 添加开票弹窗样式
.invoice-form {
  .readonly-field {
    padding: 7px 0;
    color: #515a6e;
  }

  .upload-tip {
    margin-top: 5px;
    font-size: 12px;
    color: #999;
  }
}

.hover-pointer-color {
  cursor: pointer;
  color: #2d8cf0;

  &:hover {
    color: #57a3f3;
    text-decoration: underline;
  }
}
</style>
