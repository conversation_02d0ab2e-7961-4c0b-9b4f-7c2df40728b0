<template>
  <div class="priceInquiry-list">
    <card
      _Title="我的询价"
      :_Tabs="status"
      :_Size="16"
      @_Change="statusChange"
    />

    <!-- 搜索 筛选 -->
    <div class="mb_24">
      <div class="searchList">
        <Input
          class="mr_20"
          enter-button
          v-model="params.commodityListName"
          @on-search="getList"
          placeholder="请输入商品名称搜索"
        />
        <Select
          class="mr_20"
          v-model="params.tradeModel"
          placeholder="请选择交货方式"
          clearable
          style="width: 100%"
        >
          <Option value="1">现货交易</Option>
          <Option value="2">期货交易</Option>
          <Option value="3">混合交易</Option>
        </Select>
        <Button @click="handleSearch" type="primary" class="search-btn mr_20"
          >搜索</Button
        >
        <Button @click="handleReset" class="search-btn">重置</Button>
      </div>
    </div>

    <Table :columns="columns" :data="tableData.records" border>
      <template slot="attchIdsSlot" slot-scope="{ row }">
        <div style="color: #409eff; cursor: pointer">附件</div>
      </template>
      <template slot="actionSlot" slot-scope="{ row }">
        <span
          v-if="row.status != 0"
          style="color: #409eff; cursor: pointer"
          @click="handleDetail(row)"
        >
          报价详情
        </span>
      </template>
    </Table>
    <!-- 分页 -->
    <Page
      style="float: right; margin-top: 10px"
      :current="params.pageNumber"
      :total="tableData.total"
      :page-size="params.pageSize"
      @on-change="changePage"
      @on-page-size-change="changePageSize"
      :page-size-opts="[10, 20, 50]"
      size="small"
      show-total
      show-elevator
    ></Page>

    <!-- <Button @click="handleTest">测试合同签署</Button> -->

    <SelectQuota
      ref="selectQuotaRef"
      :list="quotaList"
      :status="quotaStatus"
      @confirmSuccess="() => getList()"
    ></SelectQuota>

    <!-- <FilePreview :url="fileUrl" /> -->
  </div>
</template>
<script>
import { getPriceInquiryList, priceQuoteDetail } from "@/api/priceInquiry.js";
import SelectQuota from "./components/selectQuota2.vue";
import FilePreview from "@/components/FilePreview.vue";
export default {
  name: "PriceInquiryList",
  components: {
    FilePreview,
    SelectQuota,
  },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "title",
      },
      tableData: {}, // 消息数据
      status: ["全部", "询价中", "已报价", "已确认报价"],
      params: {
        // 请求参数
        pageNumber: 1,
        pageSize: 10,
      },
      columns: [
        // table展示数据
        {
          title: "询价时间",
          key: "createTime",
          minWidth: 160,
        },
        // {
        //   title: "报价时间",
        //   key: "updateTime",
        //   minWidth: 160,
        // },
        {
          title: "商品名称",
          key: "commodityListName",
          tooltip: true,
          minWidth: 250,
        },
        {
          title: "报价截止时间",
          key: "priceQuoteEndDate",
          minWidth: 120,
          tooltip: true,
        },
        {
          title: "期望交货日期",
          key: "deliveryDate",
          minWidth: 140,
        },
        // {
        //   title: "附件",
        //   key: "attchIds",
        //   minWidth: 80,
        //   slot: "attchIdsSlot",
        // },
        {
          title: "规格型号",
          key: "commoditySpec",
          minWidth: 100,
          tooltip: true,
        },
        {
          title: "数量",
          key: "purchaseQuantity",
          minWidth: 100,
        },
        {
          title: "交货地点",
          key: "deliveryPlace",
          minWidth: 240,
        },
        {
          title: "交货方式",
          key: "tradeModelStr",
          tooltip: true,
          minWidth: 140,
          render: (h, params) => {
            if (params.row.tradeModel == "1") {
              return h("div", {}, "现货交易");
            } else if (params.row.tradeModel == "2") {
              return h("div", {}, "期货交易");
            } else if (params.row.tradeModel == "3") {
              return h("div", {}, "混合交易");
            } else {
              return h("div", {}, params.row.tradeModel);
            }
          },
        },
        {
          title: "配送方式",
          key: "deliveryModel",
          minWidth: 100,
          render: (h, params) => {
            if (params.row.deliveryModel == "1") {
              return h("div", {}, "买方自提");
            } else if (params.row.deliveryModel == "2") {
              return h("div", {}, "供方承运");
            } else {
              return h("div", {}, params.row.deliveryModel);
            }
          },
        },
        {
          title: "结算方式",
          key: "settlementModel",
          minWidth: 160,
          render: (h, params) => {
            if (params.row.settlementModel == "1") {
              return h(
                "div",
                {},
                `定金发货（${params.row.earnestMoneyRatio}%）`
              );
            } else if (params.row.settlementModel == "2") {
              return h("div", {}, "全额付款");
            } else {
              return h("div", {}, params.row.settlementModel);
            }
          },
        },
        {
          title: "状态",
          key: "status",
          minWidth: 120,
          render: (h, params) => {
            if (params.row.status == "0") {
              return h("div", [
                h("tag", { props: { color: "magenta" } }, "询价中"),
              ]);
            } else if (params.row.status == "1") {
              return h("div", [
                h("tag", { props: { color: "cyan" } }, "已报价"),
              ]);
            } else if (params.row.status == "2") {
              return h("div", [
                h("tag", { props: { color: "green" } }, "已确认"),
              ]);
            } else if (params.row.status == "3") {
              return h("div", [
                h("tag", { props: { color: "red" } }, "已过期"),
              ]);
            } else {
              return h("div", {}, params.row.status);
            }
          },
        },
        {
          title: "联系人姓名",
          key: "contactName",
          minWidth: 130,
          tooltip: true,
        },
        {
          title: "手机号",
          key: "contactPhone",
          minWidth: 160,
          tooltip: true,
        },
        {
          title: "操作",
          key: "action",
          align: "center",
          fixed: "right",
          minWidth: 150,
          slot: "actionSlot",
        },
      ],
      quotaList: [],
      quotaStatus: 0, // 状态

      fileUrl: "", // 附件地址
    };
  },
  methods: {
    // handleTest() {
    //   const targetUrl =
    //     "https://test.jingruiit.com:9002/supplychain/upload/20250422/aa896fc2e4222116d4d6127aefd181e5.pdf";
    //   this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();

    //   console.log(this.fileUrl, "url");
    // },
    // 报价详情
    async handleDetail(record) {
      console.log(record, "--------");
      this.quotaStatus = record.status;
      let params = {
        commodityPriceInquiryId: record.id,
      };
      const res = await priceQuoteDetail(params);
      console.log(res.result, "--------");
      if (res.success) {
        this.$refs.selectQuotaRef.handleOpen();

        // 将status == 2 的数据放在最前面
        const sortedList = res.result.sort((a, b) => {
          if (a.status === 2) return -1;
          if (b.status === 2) return 1;
          return 0;
        });

        this.quotaList = sortedList || [];
      }
    },
    // 状态发生变化
    statusChange(index) {
      const statusMap = {
        1: "0",
        2: "1",
        3: "2",
      };

      if (index === 0) {
        delete this.params.status;
      } else {
        this.params.status = statusMap[index];
      }

      this.params.pageNumber = 1;
      this.getList();
    },
    // 修改页码
    changePage(v) {
      this.params.pageNumber = v;
      this.cleanSearchForm();
      this.getList();
    },
    // 修改页数
    changePageSize(v) {
      this.params.pageSize = v;
      this.cleanSearchForm();
      this.getList();
    },

    // 清理搜索表单中的空值
    cleanSearchForm() {
      const fields = ["commodityListName", "tradeModel"];
      fields.forEach((field) => {
        if (!this.params[field]) {
          delete this.params[field];
        }
      });
    },

    // 搜索订单
    handleSearch() {
      this.params.pageNumber = 1;
      this.params.pageSize = 10;
      this.cleanSearchForm();
      this.getList();
    },
    // 重置
    handleReset() {
      this.params = {
        pageNumber: 1,
        pageSize: 10,
        commodityListName: "",
        tradeModel: "",
        status: this.params.status,
      };
      this.cleanSearchForm();
      this.getList();
    },
    getList() {
      // 获取消息列表
      getPriceInquiryList(this.params).then((res) => {
        if (res.success) {
          this.tableData = res.result;
        }
      });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>
<style lang="scss" scoped>
.searchList {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
