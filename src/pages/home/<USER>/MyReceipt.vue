<template>
  <div class="my-receipt">
    <card _Title="我的发票" :_Size="16" />
    <!-- Tabs 导航 -->
    <div class="tabs-container">
      <div class="tabs-nav">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          :class="['tab-item', { active: activeTab === tab.key }]"
          @click="switchTab(tab.key)"
        >
          {{ tab.label }}
        </div>
      </div>

      <!-- Tabs 内容区域 -->
      <div class="tabs-content">
        <div v-show="activeTab === 'pending'" class="tab-pane">
          <!-- 待开票列表内容 -->
          <PendingReceipt ref="pendingReceiptRef" />
        </div>

        <div v-show="activeTab === 'invoices'" class="tab-pane">
          <!-- 发票列表内容 -->
          <Receipt ref="receiptRef" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PendingReceipt from "./PendingReceipt.vue";
import Receipt from "./Receipt.vue";

export default {
  name: "MyReceipt",
  components: {
    PendingReceipt,
    Receipt,
  },
  data() {
    return {
      activeTab: "pending", // 当前激活的标签页
      tabs: [
        { key: "pending", label: "待开发票" },
        { key: "invoices", label: "我的发票" },
      ],
    };
  },
  methods: {
    // 切换标签页
    switchTab(tabKey) {
      this.activeTab = tabKey;
      // 这里可以添加切换时的逻辑，比如加载对应数据
      this.loadTabData(tabKey);
    },
    // 加载对应标签页的数据
    loadTabData(tabKey) {
      // console.log(`加载 ${tabKey} 标签页数据`);
      if (tabKey == "invoices") {
        this.$refs.receiptRef.init();
      } else if (tabKey == "pending") {
        this.$refs.pendingReceiptRef.init();
      }
    },
  },

  mounted() {
    // 组件挂载后加载默认标签页数据
    this.loadTabData(this.activeTab);
  },
};
</script>

<style lang="scss" scoped>
.my-receipt {
  // padding: 20px;

  .tabs-container {
    background: #fff;
    border-radius: 8px;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  .tabs-nav {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .tab-item {
      flex: 1;
      padding: 16px 20px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      color: #666;
      background: #f8f9fa;
      border-right: 1px solid #e9ecef;
      transition: all 0.3s ease;
      position: relative;

      &:last-child {
        border-right: none;
      }

      &:hover {
        background: #e9ecef;
        color: #333;
      }

      &.active {
        background: #fff;
        color: #409eff;
        font-weight: 500;

        &::after {
          content: "";
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 3px;
          background: #409eff;
        }
      }

      .tab-count {
        margin-left: 4px;
        font-size: 12px;
        color: #999;
      }

      &.active .tab-count {
        color: #409eff;
      }
    }
  }

  .tabs-content {
    min-height: 400px;

    .tab-pane {
      // padding: 20px;
      padding-top: 20px;
    }
  }
}

::v-deep ._Card {
  margin-bottom: 0 !important;
}
</style>
