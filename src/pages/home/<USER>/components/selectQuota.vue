<template>
  <Modal v-model="modal1" title="选择报价" @on-cancel="handleClose">
    <div class="list__container">
      <RadioGroup v-model="radioValue" vertical>
        <div
          class="item__container"
          v-for="item of list"
          :key="item.commodityPriceQuoteIds"
        >
          <Radio
            :value="item.commodityPriceQuoteIds"
            :label="item.supplierName"
          >
          </Radio>
        </div>
      </RadioGroup>
    </div>
    <div slot="footer">
      <el-button size="large" @click="handleClose">取消</el-button>
      <el-button
        @click="handleView"
        :disabled="radioValue === ''"
        type="primary"
        size="large"
        >查看报价</el-button
      >
      <el-button
        v-if="status != 2"
        @click="handleConfirm"
        :disabled="radioValue === ''"
        type="primary"
        size="large"
        >确认</el-button
      >
    </div>

    <ViewQuota ref="viewQuotaRef" :list="viewQuotaList"></ViewQuota>
  </Modal>
</template>
<script>
import ViewQuota from "./viewQuota.vue";
import { confirmQuote } from "@/api/priceInquiry.js";
export default {
  name: "SelectQuota",
  components: {
    ViewQuota,
  },
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    status: {
      type: Number,
      default: 0, 
    }
  },
  data() {
    return {
      modal1: false,
      radioValue: "",
      viewQuotaList: [],
    };
  },
  methods: {
    handleOpen() {
      this.modal1 = true;
    },
    handleClose() {
      this.modal1 = false;
      this.radioValue = "";
    },
    handleView() {
      this.viewQuotaList = [];

      const selectedItem = this.list.find(
        (item) => item.supplierName == this.radioValue
      );

      if (selectedItem) {
        this.viewQuotaList = selectedItem.commodityPriceQuoteList;
        this.$refs.viewQuotaRef.handleOpen();
      }
    },
    handleConfirm() {
      let params = [];

      const selectedItem = this.list.find(
        (item) => item.supplierName == this.radioValue
      );

      if (selectedItem) {
        params = selectedItem.commodityPriceQuoteList.map(
          (subItem) => subItem.id
        );
      }

      this.$Modal.confirm({
        title: "提示",
        content: "确认选中的报价吗？",
        onOk: async () => {
          const res = await confirmQuote(params);

          if (res.success) {
            this.$message.success("确认成功");
            this.handleClose();

            this.$emit("confirmSuccess");
          }
        },
        onCancel: () => {},
      });
    },
  },
};
</script>
<style scoped lang="scss">
.list__container {
  font-family: PingFangSC-Semibold, PingFang SC;
  ::v-deep .ivu-radio-group {
    width: 100%;
    & .ivu-radio-wrapper {
      align-items: center;
      width: 100%;

      font-size: 16px;
      font-weight: 700;

      .ivu-radio {
        margin-right: 10px;
        font-size: 16px;
      }
    }
  }

  .item__container {
    width: 100%;
    border-radius: 16px;
    margin-bottom: 16px;
    background: #f5f5f5;
    padding: 10px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}
</style>
