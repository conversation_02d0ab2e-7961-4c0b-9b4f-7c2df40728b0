<template>
  <el-dialog
    title="询价详情"
    :visible.sync="priceDialog"
    class="priceDialog"
    @close="handleClose"
    width="60%"
  >
    <card _Title="我的询价"></card>
    <el-row class="container">
      <el-col :span="12" class="item">
        <span class="item-label">商品名称: </span>
        <span class="item-value">{{ priceData.commodityListName }}</span>
      </el-col>
      <el-col :span="12" class="item">
        <span class="item-label">规格型号: </span>
        <span class="item-value">{{ priceData.commoditySpec }}</span>
      </el-col>
      <el-col :span="12" class="item">
        <span class="item-label">期望购买数量: </span>
        <span class="item-value">{{ priceData.purchaseQuantity }}&nbsp;{{ priceData.commoditySpec }}</span>
      </el-col>
      <el-col :span="12" class="item">
        <span class="item-label">交货日期: </span>
        <span class="item-value">{{ priceData.deliveryDate }}</span>
      </el-col>
      <el-col :span="12" class="item">
        <span class="item-label">交货方式: </span>
        <span class="item-value">{{
          priceData.tradeModel == 1
            ? "现货交易"
            : priceData.tradeModel == 2
            ? "期货交易"
            : "混合交易"
        }}</span>
      </el-col>
      <el-col :span="12" class="item">
        <span class="item-label">配送方式: </span>
        <span class="item-value">{{
          priceData.deliveryModel == 1 ? "买方自提" : "供方承运"
        }}</span>
      </el-col>
      <!-- <el-col :span="12" class="item">
          <span class="item-label">询价描述: </span>
          <span class="item-value">{{ priceData.detailedSescription }}</span>
        </el-col>
        <el-col :span="12" class="item">
          <span class="item-label">附件: </span>
          <span class="item-value">{{ priceData.attchIds }}</span>
        </el-col> -->
      <el-col :span="12" class="item">
        <span class="item-label">结算方式: </span>
        <span class="item-value">{{
          priceData.settlementModel == 1
            ? "定金发货"
            : priceData.settlementModel == 2
            ? "全额付款"
            : "分期付款"
        }}</span>
      </el-col>
      <el-col :span="12" class="item" v-if="priceData.settlementModel != 2">
        <span class="item-label">定金比例: </span>
        <span class="item-value">{{ priceData.earnestMoneyRatio }}%</span>
      </el-col>
      <el-col :span="12" class="item">
        <span class="item-label">联系人姓名: </span>
        <span class="item-value">{{ priceData.contactName }}</span>
      </el-col>
      <el-col :span="12" class="item">
        <span class="item-label">手机号: </span>
        <span class="item-value">{{ priceData.contactPhone }}</span>
      </el-col>
    </el-row>
    <card _Title="商家报价"></card>
    <el-row class="container">
      <el-table :data="priceData.commodityPriceQuoteList" border>
        <el-table-column prop="supplierName" label="供应商名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="supplierQuotationAmount" label="报价金额" />
        <el-table-column prop="commodityListName" label="商品名称" />
        <el-table-column prop="deliveryQuantity" label="交货数量" />
        <el-table-column prop="commoditySpecName" label="规格型号" />
        <el-table-column prop="deliveryDate" label="交货时间" />
        <el-table-column prop="deliveryModel" label="配送方式">
          <template slot-scope="{ row }">
            <span>{{ row.deliveryModel == 1 ? "买方自提" : "供方承运" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="settlementModel" label="结算方式">
          <template slot-scope="{ row }">
            <span>{{
              priceData.settlementModel == 1
                ? "定金发货"
                : priceData.settlementModel == 2
                ? "全额付款"
                : "分期付款"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="earnestMoneyRatio" label="定金比例%">
          <template slot-scope="{ row }">
            <span>{{ row.earnestMoneyRatio || "--" }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-row>
  </el-dialog>
</template>
<script>
export default {
  name: "priceDialog",
  props: {
    priceData: {
      type: Object,
      default: {},
    },
  },
  data() {
    return {
      priceDialog: false,
    };
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    openPriceDialog(val) {
      this.priceDialog = val;
    },
  },
};
</script>
<style scoped lang="scss">
.priceDialog {
  ::v-deep .el-dialog__body {
    padding-top: 0 !important;
  }

  .container {
    .item {
      .item-label {
        display: inline-block;
        width: 100px;
        font-size: 14px;
        color: #666;
        margin-bottom: 10px;
        text-align: right;
        margin-right: 10px;
      }
      .item-value {
        font-size: 16px;
        color: #333;
      }
      .item-value2 {
        font-size: 16px;
        // color: #333;
      }
    }
  }
}
</style>
