<template>
  <Modal v-model="modal1" title="选择报价" width="80%" @on-cancel="handleClose">
    <div class="quota-container">
      <!-- 左侧供应商列表 -->
      <div class="supplier-list">
        <h3 class="section-title">供应商列表</h3>
        <div class="supplier-items">
          <div
            v-for="item of list"
            :key="item.commodityPriceQuoteIds"
            class="supplier-item"
            :class="{
              active:
                selectedSupplierId === item.commodityPriceQuoteIds ||
                item.status === 2,
            }"
            :data-status="item.status"
            @click="selectSupplier(item)"
          >
            <div v-if="item.status === 2" class="icon">
              <img
                style="width: 20px; height: 20px"
                src="@/assets/images/iconSuccess.png"
              />
            </div>
            <div class="supplier-name">{{ item.supplierName }}</div>
            <div class="supplier-info">
              <span>报价时间：{{ item.createTime }}</span>
            </div>
            <!-- <div class="supplier-info" v-if="item.contactPerson">
              <span>联系人：{{ item.contactPerson }}</span>
            </div>
            <div class="supplier-info" v-if="item.contactPhone">
              <span>联系电话：{{ item.contactPhone }}</span>
            </div> -->
          </div>
        </div>
      </div>

      <!-- 右侧报价详情 -->
      <div class="quote-details">
        <h3 class="section-title">报价详情</h3>
        <div v-if="selectedSupplierId" class="quote-content">
          <el-table :data="currentQuoteList" border stripe style="width: 100%">
            <el-table-column
              prop="storeName"
              label="店铺名称"
              min-width="150"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="commodityListName"
              label="商品名称"
              min-width="150"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              prop="commoditySpecName"
              label="规格型号"
              min-width="100"
            ></el-table-column>
            <el-table-column
              prop="deliveryQuantity"
              label="交货数量"
              width="80"
            ></el-table-column>
            <el-table-column
              prop="supplierQuotationAmount"
              label="报价金额(元)"
              width="120"
            ></el-table-column>
            <el-table-column prop="deliveryModel" label="配送方式" width="100">
              <template slot-scope="scope">
                {{ getDeliveryModelText(scope.row.deliveryModel) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="settlementModel"
              label="结算方式"
              width="140"
            >
              <template slot-scope="scope">
                {{
                  getSettlementModelText(
                    scope.row.settlementModel,
                    scope.row.earnestMoneyRatio
                  )
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="deliveryDate"
              label="交货时间"
              width="100"
            ></el-table-column>
          </el-table>
        </div>
        <div v-else class="no-data">
          <el-empty description="请选择左侧供应商查看报价详情"></el-empty>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button size="large" @click="handleClose">取消</el-button>
      <el-button
        v-if="status != 2"
        @click="handleConfirm"
        :disabled="selectedSupplierId === ''"
        type="primary"
        size="large"
        >确认选择</el-button
      >
    </div>
  </Modal>
</template>
<script>
import { confirmQuote } from "@/api/priceInquiry.js";

export default {
  name: "SelectQuota",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    status: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      modal1: false,
      selectedSupplierId: "",
      currentQuoteList: [],
      deliveryModelDict: [
        { dictKey: 1, dictValue: "买方自提" },
        { dictKey: 2, dictValue: "供方承运" },
      ],
      settlementModelDict: [
        { dictKey: 1, dictValue: "定金发货" },
        { dictKey: 2, dictValue: "全额付款" },
      ],
    };
  },
  methods: {
    handleOpen() {
      this.modal1 = true;
      this.selectedSupplierId = "";
      this.currentQuoteList = [];

      // 默认选中第一个供应商
      this.$nextTick(() => {
        if (this.list && this.list.length > 0) {
          this.selectSupplier(this.list[0]);
        }
      });
    },
    handleClose() {
      this.modal1 = false;
      this.selectedSupplierId = "";
      this.currentQuoteList = [];
    },
    selectSupplier(item) {
      this.selectedSupplierId = item.commodityPriceQuoteIds;
      this.currentQuoteList = item.commodityPriceQuoteList || [];
    },
    getDeliveryModelText(value) {
      const item = this.deliveryModelDict.find((item) => item.dictKey == value);
      return item ? item.dictValue : "--";
    },
    getSettlementModelText(value, earnestMoneyRatio) {
      const item = this.settlementModelDict.find(
        (item) => item.dictKey == value
      );
      if (earnestMoneyRatio && value == 1) {
        return item ? `${item.dictValue}（${earnestMoneyRatio}%）` : "--";
      } else {
        return item ? item.dictValue : "--";
      }
    },
    handleConfirm() {
      if (!this.selectedSupplierId) {
        this.$message.warning("请先选择供应商");
        return;
      }

      let params = [];
      const selectedItem = this.list.find(
        (item) => item.commodityPriceQuoteIds == this.selectedSupplierId
      );

      if (selectedItem) {
        params = selectedItem.commodityPriceQuoteList.map(
          (subItem) => subItem.id
        );
      }

      this.$Modal.confirm({
        title: "提示",
        content: "确认选中的报价吗？",
        onOk: async () => {
          const res = await confirmQuote(params);

          if (res.success) {
            this.$message.success("确认成功");
            this.handleClose();
            this.$emit("confirmSuccess");
          }
        },
        onCancel: () => {},
      });
    },
  },
};
</script>
<style scoped lang="scss">
.quota-container {
  display: flex;
  height: 500px;

  .section-title {
    font-size: 16px;
    font-weight: bold;
    margin-top: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
  }

  .supplier-list {
    min-width: 300px;
    padding-right: 20px;
    border-right: 1px solid #eee;
    overflow-y: auto;

    .supplier-items {
      max-height: 450px;
      overflow-y: auto;
    }

    .supplier-item {
      padding: 15px;
      margin-bottom: 10px;
      background-color: #f5f5f5;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      position: relative;

      & .icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
      }

      &:hover {
        background-color: #e8f4ff;
      }

      &.active {
        background-color: #19be6b;
        color: white;
      }

      &.active[data-status="2"] {
        background-color: #409eff;
      }

      .supplier-name {
        font-size: 16px;
        font-weight: bold;
        // margin-bottom: 8px;
      }

      .supplier-info {
        font-size: 13px;
        color: inherit;
        opacity: 0.8;
        margin-top: 5px;
      }
    }
  }

  .quote-details {
    flex: 1;
    padding-left: 20px;
    overflow-y: auto;

    .quote-content {
      height: 450px;
      overflow-y: auto;
    }

    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
    }
  }
}

@media screen and (max-width: 768px) {
  .quota-container {
    flex-direction: column;

    .supplier-list {
      width: 100%;
      padding-right: 0;
      border-right: none;
      border-bottom: 1px solid #eee;
      padding-bottom: 15px;
      margin-bottom: 15px;
      max-height: 200px;
    }

    .quote-details {
      padding-left: 0;
    }
  }
}
</style>
