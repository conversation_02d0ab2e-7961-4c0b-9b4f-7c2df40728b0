<template>
  <Modal width="80%" v-model="modal2" title="查看报价" @on-cancel="handleClose">
    <div class="view-quota-container">
      <el-table :data="list" border stripe style="width: 100%">
        <el-table-column
          v-for="(item, index) in options"
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
          :align="item.align"
          :min-width="item.minWidth"
          :show-overflow-tooltip="item.showOverflowTooltip"
        >
          <template
            v-if="
              item.prop === 'settlementModel' || item.prop === 'deliveryModel'
            "
            #default="{ row }"
          >
            {{ getDisplayValue(item.prop, row) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div slot="footer">
      <el-button size="large" @click="handleClose">取消</el-button>
    </div>
  </Modal>
</template>
<script>
export default {
  name: "CompareQuota",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      options: [
        {
          label: "供应商名称",
          prop: "supplierName",
          align: "center",
          minWidth: 360,
          showOverflowTooltip: true,
        },
        {
          label: "交货数量",
          prop: "deliveryQuantity",
          align: "center",
          minWidth: 140,
          showOverflowTooltip: false,
        },
        {
          label: "规格型号",
          prop: "commoditySpecName",
          align: "center",
          minWidth: 100,
          showOverflowTooltip: false,
        },
        {
          label: "报价金额（元）",
          prop: "supplierQuotationAmount",
          align: "center",
          minWidth: 140,
          showOverflowTooltip: false,
        },
        {
          label: "配送方式",
          prop: "deliveryModel",
          align: "center",
          minWidth: 140,
          showOverflowTooltip: false,
        },
        {
          label: "结算方式",
          prop: "settlementModel",
          align: "center",
          minWidth: 140,
          showOverflowTooltip: false,
        },
        {
          label: "交货时间",
          prop: "deliveryDate",
          align: "center",
          minWidth: 140,
          showOverflowTooltip: false,
        },
      ],
      deliveryModelDict: [
        {
          dictKey: 1,
          dictValue: "买方自提",
        },
        {
          dictKey: 2,
          dictValue: "供方承运",
        },
      ],
      settlementModelDict: [
        {
          dictKey: 1,
          dictValue: "定金发货",
        },
        {
          dictKey: 2,
          dictValue: "全额付款",
        },
      ],
      modal2: false,
    };
  },
  methods: {
    handleOpen() {
      this.modal2 = true;
    },
    handleClose() {
      this.modal2 = false;
    },
    getLabelByValue(dict, value, earnestMoneyRatio) {
      const item = dict.find((item) => item.dictKey == value);
      console.log(item);
      // return item ? item.dictValue : "";
      if (earnestMoneyRatio && value == 1) {
        return item ? `${item.dictValue}（${earnestMoneyRatio}%）` : "";
      } else {
        return item ? item.dictValue : "";
      }
    },
    getDisplayValue(prop, row) {
      if (prop === "settlementModel") {
        return (
          this.getLabelByValue(
            this.settlementModelDict,
            row.settlementModel,
            row.earnestMoneyRatio
          ) || "--"
        );
      } else if (prop === "deliveryModel") {
        return (
          this.getLabelByValue(this.deliveryModelDict, row.deliveryModel, false) ||
          "--"
        );
      }
      return "";
    },
  },
};
</script>
