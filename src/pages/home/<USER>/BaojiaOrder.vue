<template>
  <div class="wrapper">
    <!-- 卡片组件 -->
    <card
      _Title="我的订单"
      :_Size="16"
      :_Tabs="changeWay"
      @_Change="change"
      v-if="!homePage"
    ></card>
    <card
      _Title="商品订单"
      :_Size="16"
      :_Tabs="changeWay"
      @_Change="change"
      _Src="/home/<USER>"
      v-else
    ></card>
    <!-- 搜索 筛选 -->
    <div class="mb_24 box" v-if="!homePage">
      <div class="global_float_right">
        <Input
          class="width_300"
          search
          enter-button
          v-model="params.keywords"
          @on-search="getList"
          placeholder="请输入订单号搜索"
        />
      </div>
    </div>
    <!-- 订单列表 -->
    <empty v-if="orderList.length === 0" />
    <div class="order-content" v-else>
      <div
        class="order-list"
        v-for="(order, onderIndex) in orderList"
        :key="onderIndex"
      >
        <div class="order-header">
          <div>
            <div>{{ filterOrderStatus(order.orderStatus) }}</div>
            <div>
              订单号：{{ order.sn }} &nbsp; &nbsp; &nbsp;{{
                order.createTime
              }}&nbsp; &nbsp; &nbsp;{{
                order.settlementModel == "DEPOSIT_SEND_OUT_GOODS"
                  ? "定金发货&emsp;&emsp;"
                  : "全额付款发货"
              }}
              &nbsp; &nbsp; &nbsp;
              <!-- <span
                v-if="order.paymentMethod == 'FINANCING'"
                @click="financingDetail(order.sn)"
                class="hover-pointer-color_2"
                >融资详情&nbsp; &nbsp; &nbsp;
              </span> -->
              <span
                v-if="order.orderType == 'BAOJIA'"
                class="hover-pointer-color"
                @click="getByOrderSn(order.sn)"
                >询价订单
              </span>
              <span v-else>普通订单</span>
              &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
              <span
                class="hover-pointer-color"
                @click="shopPage(order.storeId)"
                >{{ order.storeName }}</span
              >
            </div>
          </div>
          <div>
            <Tooltip placement="top" content="删除订单">
              <Button
                v-if="order.orderStatus === 'COMPLETED'"
                @click="delOrder(order.sn)"
                class="del-btn mr_10 fontsize_16"
                style="margin-top: -5px"
                type="text"
                icon="ios-trash-outline"
                size="small"
              ></Button>
            </Tooltip>
            <span class="global_color fontsize_16">{{
              order.flowPrice | unitPrice("￥")
            }}</span>
          </div>
        </div>
        <div class="order-body">
          <div class="goods-list">
            <div
              v-for="(goods, goodsIndex) in order.orderItems"
              :key="goodsIndex"
            >
              <img
                @click="goodsDetail(goods.skuId, goods.goodsId)"
                class="hover-color"
                :src="goods.image"
                alt=""
              />
              <div>
                <div
                  class="hover-color"
                  @click="goodsDetail(goods.skuId, goods.goodsId)"
                >
                  {{ goods.name }}
                </div>
                <div class="mt_10">
                  <span class="global_color"
                    >{{ goods.goodsPrice | unitPrice("￥") }} </span
                  >x {{ goods.num }}
                  <span style="margin-left: 10px; color: #ff9900">{{
                    refundPriceList(goods.isRefund)
                  }}</span>
                  <!-- 已付金额 -->
                  <span style="margin-left: 10px"
                    >已付金额：
                    <span>
                      <span class="global_color">{{
                        goods.paidAmount | unitPrice("￥")
                      }}</span>
                      <span
                        v-if="Number(goods.paidAmount)"
                        style="margin-left: 5px"
                      >
                        ({{
                          goods.itemPaymentMethod === "FINANCING"
                            ? "含融资支付"
                            : "线下付款"
                        }})
                      </span>
                    </span>
                  </span>
                  <!-- 付款方式 -->
                  <span
                    style="margin-left: 10px"
                    v-if="
                      Number(goods.paidAmount) &&
                      goods.itemPaymentMethod == 'FINANCING'
                    "
                  >
                    <span
                      @click="financingDetail(goods.sn)"
                      class="hover-pointer-color"
                      >融资详情</span
                    >
                  </span>
                  <!-- 订单状态 -->
                  <span
                    v-if="goods.deliverNumber == goods.num"
                    style="margin-left: 10px"
                  >
                    {{ goods.changeConfirmStatus ? "订单状态：" : "" }}
                    <span class="global_color">
                      {{ confirmText(goods.changeConfirmStatus) }}
                    </span>
                    <span
                      :style="{
                        color:
                          goods.changeConfirmStatus == 'CONFIRM'
                            ? '#409eff'
                            : '#F31947',
                      }"
                    >
                      {{ confirmStatus(goods.changeConfirmStatus) }}
                    </span>
                  </span>
                  <!-- 申请售后 -->
                  <span
                    v-if="
                      goods.deliverNumber == goods.num &&
                      goods.changeConfirmStatus != 'UN_CONFIRM' &&
                      goods.changeConfirmStatus != 'CONFIRM'
                    "
                    class="hover-pointer-color_2 ml_10"
                    @click="adjustReceived(goods)"
                    >申请售后</span
                  >
                  <span
                    v-if="
                      goods.deliverNumber == goods.num &&
                      goods.changeConfirmStatus != 'UN_CONFIRM' &&
                      goods.changeConfirmStatus != 'CONFIRM'
                    "
                    class="hover-pointer-color ml_10"
                    @click="confirmNum(goods)"
                    >确认收货</span
                  >
                </div>
                <!-- <Button
                  v-if="goods.commentStatus == 'UNFINISHED'"
                  @click="comment(order.sn, goodsIndex)"
                  size="small"
                  type="success"
                  class="fontsize_12"
                  style="
                    position: relative;
                    top: -22px;
                    left: 190px;
                    margin-right: 10px;
                  "
                  >评价</Button
                >
                <Button
                  v-if="goods.complainStatus == 'NO_APPLY'"
                  @click="complain(order.sn, goodsIndex)"
                  type="warning"
                  class="fontsize_12"
                  size="small"
                  style="position: relative; top: -22px; left: 190px"
                  >投诉</Button
                > -->

                <!-- <Button
                  v-if="
                    goods.deliverNumber == goods.num &&
                    goods.changeConfirmStatus != 'UN_CONFIRM' &&
                    goods.changeConfirmStatus != 'CONFIRM'
                  "
                  size="small"
                  type="text"
                  class="fontsize_12 hover-pointer-color"
                  style="
                    position: relative;
                    top: -22px;
                    left: 580px;
                    margin-right: 10px;
                  "
                  @click="adjustReceived(goods)"
                  >申请售后</Button
                >
                <Button
                  v-if="
                    goods.deliverNumber == goods.num &&
                    goods.changeConfirmStatus != 'UN_CONFIRM' &&
                    goods.changeConfirmStatus != 'CONFIRM'
                  "
                  type="text"
                  class="fontsize_12 hover-pointer-color"
                  size="small"
                  style="position: relative; top: -22px; left: 580px"
                  @click="confirmNum(goods)"
                  >确认收货</Button
                > -->
              </div>
            </div>
          </div>
          <div>
            <!-- <span @click="shopPage(order.storeId)">{{ order.storeName }}</span> -->
          </div>
          <div style="text-align: right">
            <!-- 订单基础操作 -->
            <Button @click="orderDetail(order.sn)" type="info" size="small"
              >订单详情</Button
            >
            <!-- <Button
              v-if="order.paymentMethod == 'FINANCING'"
              @click="financingDetail(order.sn)"
              type="info"
              size="small"
              >融资详情</Button
            > -->
            <!-- 合同按钮 -->
            <template v-if="order.orderStatus != 'CANCELLED'">
              <Button
                v-if="order.buyerSignStatus == 5"
                @click="signContrart(order, 'view')"
                type="success"
                size="small"
                >查看合同</Button
              >
              <Button
                v-else
                @click="signContrart(order, 'sign')"
                type="warning"
                size="small"
                >签署合同</Button
              >
            </template>
            <Button
              @click="handleCancelOrder(order.sn)"
              type="error"
              v-if="order.allowOperationVO.cancel"
              size="small"
              >取消订单</Button
            >
            <!-- <Button
              @click="goPay(order.sn)"
              size="small"
              type="success"
              v-if="
                order.allowOperationVO.pay &&
                order.buyerSignStatus == 5 &&
                order.sellerSignStatus == 5
              "
              >去支付</Button
            > -->
            <!-- @click="uploadVoucher(order.sn, order.storeId)" -->
            <Button
              @click="subUploadVoucher(order)"
              size="small"
              type="success"
              v-if="
                order.allowOperationVO.pay &&
                order.buyerSignStatus == 5 &&
                order.sellerSignStatus == 5 &&
                order.settlementModel == 'FULL_PAYMENT'
              "
              >去支付</Button
            >
            <Button
              @click="subUploadVoucher(order)"
              size="small"
              type="success"
              v-if="
                order.allowOperationVO.pay &&
                order.buyerSignStatus == 5 &&
                order.sellerSignStatus == 5 &&
                order.settlementModel == 'DEPOSIT_SEND_OUT_GOODS'
              "
              >去支付</Button
            >
            <!-- @click="received(order.sn)" -->
            <!-- <Button
              @click="adjustReceived(order)"
              size="small"
              type="warning"
              v-if="order.allowOperationVO.rog"
              >确认到货数量</Button
            > -->
            <!-- <Button
              @click="received(order.sn)"
              size="small"
              type="warning"
              v-if="order.allowOperationVO.rog && order.payStatus == 'PAID'"
              >确认收货</Button
            > -->
            <!-- 售后 -->
            <!-- <Button
              v-if="
                order.groupAfterSaleStatus &&
                (order.groupAfterSaleStatus.includes('NOT_APPLIED') ||
                  order.groupAfterSaleStatus.includes('PART_AFTER_SALE'))
              "
              @click="applyAfterSale(order.orderItems)"
              size="small"
              >申请售后</Button
            > -->
          </div>
        </div>
      </div>
      <!-- <Spin size="large" fix v-if="spinShow"></Spin> -->
    </div>
    <!-- 分页 -->
    <div class="page-size">
      <Page
        :total="total"
        @on-change="changePageNum"
        @on-page-size-change="changePageSize"
        :page-size="params.pageSize"
        show-total
        show-sizer
      >
      </Page>
    </div>
    <!-- 选择售后商品 -->
    <Modal v-model="afterSaleModal" title="请选择申请售后的商品">
      <div>
        <Table
          border
          :columns="afterSaleColumns"
          :data="afterSaleArr"
          @on-row-click="afterSaleSelect"
        >
        </Table>
      </div>
      <div slot="footer"></div>
    </Modal>
    <Modal
      v-model="cancelAvail"
      title="请选择取消订单原因"
      @on-ok="sureCancel"
      @on-cancel="cancelAvail = false"
    >
      <RadioGroup
        v-model="cancelParams.reason"
        vertical
        type="button"
        button-style="solid"
      >
        <Radio :label="item.reason" v-for="item in cancelReason" :key="item.id">
          {{ item.reason }}
        </Radio>
      </RadioGroup>
    </Modal>

    <ContractSign
      :contractList="contractList"
      ref="contractSignRef"
      :signStatus="signStatus"
      @signCompleted="() => getList()"
    />

    <!-- 上传支付凭证 -->
    <el-dialog
      :title="paymentTitle"
      :visible.sync="dialogVisible"
      width="45%"
      @close="handleClose"
      top="5vh"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="auto"
        label-position="left"
      >
        <el-form-item label="付款方式" prop="paymentMethod">
          <el-select
            v-model="formData.paymentMethod"
            placeholder="请选择付款方式"
            @change="getPaymentMethod"
          >
            <el-option
              v-for="item in paymentMethodData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>

        <!-- 线下支付 -->
        <el-row
          :gutter="20"
          v-if="formData.paymentMethod == 1 && changeBankStatus == 'SUCCESS'"
        >
          <el-col :span="24">
            <el-form-item label="收款人名称" prop="companyName">
              <el-input v-model="formData.companyName" disabled>
                <template slot="append">
                  <span
                    class="hover-pointer-color"
                    @click="copy(formData.companyName)"
                    >复 制</span
                  >
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款银行" prop="settlementBankBranchName">
              <el-input v-model="formData.settlementBankBranchName" disabled>
                <template slot="append">
                  <span
                    class="hover-pointer-color"
                    @click="copy(formData.settlementBankBranchName)"
                    >复 制</span
                  >
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="收款账号" prop="settlementBankAccountNum">
              <el-input v-model="formData.settlementBankAccountNum" disabled>
                <template slot="append">
                  <span
                    class="hover-pointer-color"
                    @click="copy(formData.settlementBankAccountNum)"
                    >复 制</span
                  >
                </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="应付金额" prop="payableAmount">
              <el-input v-model="payableAmount" disabled>
                <template slot="append"> 元 </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="已付金额" prop="paidAmount">
              <el-input v-model="paidAmount" disabled>
                <template slot="append"> 元 </template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="支付凭证" prop="paymentVoucherUrl">
              <div class="file-upload-container">
                <el-upload
                  class="upload-demo"
                  drag
                  :action="action"
                  :headers="accessToken"
                  multiple
                  :on-success="handleSuccess"
                  :before-upload="handleBeforeUpload"
                  :on-remove="handleFileRemove"
                  :on-error="handleError"
                  accept=".jpg,.jpeg,.png,.pdf"
                  :file-list="fileListData"
                  :on-preview="handlePreview"
                >
                  <i class="el-icon-upload"></i>
                  <div class="el-upload__text">
                    将文件拖到此处，或<em>点击上传</em>
                  </div>
                  <div class="el-upload__tip" slot="tip">
                    只能上传jpg、jepg、png、pdf文件，且不超过100M
                  </div>
                </el-upload>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 融资信息 -->
        <template v-if="formData.paymentMethod == 2">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="应付金额" prop="payableAmount">
                <el-input v-model="payableAmount" disabled>
                  <template slot="append"> 元 </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="已付金额" prop="paidAmount">
                <el-input v-model="paidAmount" disabled>
                  <template slot="append"> 元 </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-divider v-if="quotaAmount" content-position="center"
            ><i class="el-icon-postcard"></i
          ></el-divider>
          <el-col :span="12" v-if="quotaAmount">
            <el-form-item label="可用授信额度" prop="quotaAmount">
              <el-input v-model="quotaAmount" disabled>
                <template slot="append"> 元 </template>
              </el-input>
            </el-form-item>
          </el-col>
          <div
            v-else
            :class="{
              'quota-message': quotaMessage,
              'quota-message--warning': quotaMessage,
            }"
          >
            {{ quotaMessage }}
          </div>
        </template>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button :loading="loadingBtn" type="primary" @click="submit">{{
          submitButtonText
        }}</el-button>
      </span>
    </el-dialog>

    <!-- 申请售后 -->
    <el-dialog
      title="申请售后"
      :visible.sync="receiptVisible"
      width="50%"
      @close="handleReceiptClose"
    >
      <el-form
        :model="receiptFormData"
        :rules="receiptRules"
        ref="receiptFormRef"
        label-width="auto"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="收货数量" prop="num">
              <el-input
                v-model="receiptFormData.num"
                type="number"
                :min="1"
                placeholder="请输入收货数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调整价格" prop="flowPrice">
              <el-input
                v-model="receiptFormData.flowPrice"
                type="number"
                placeholder="请输入调整价格"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述" prop="changeDesc">
              <el-input
                v-model="receiptFormData.changeDesc"
                type="textarea"
                placeholder="请输入描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上传凭证" prop="changeUrl">
              <el-upload
                class="upload-demo"
                drag
                :action="action"
                :headers="accessToken"
                multiple
                :on-success="handleSuccessReceipt"
                :before-upload="handleBeforeUploadReceipt"
                :on-remove="handleFileRemoveReceipt"
                :on-error="handleErrorReceipt"
                accept=".jpg,.jpeg,.png,.pdf"
                :file-list="receiptFileListData"
                :on-preview="handlePreviewReceipt"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  只能上传jpg、jepg、png、pdf文件，且不超过100M
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleReceiptClose">取 消</el-button>
        <el-button
          :loading="loadingBtn"
          type="primary"
          @click="handleReceiptSubmit"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <!-- 子订单 -->
    <el-dialog title="订单详情" :visible.sync="subOrderVisible">
      <Table border :columns="subColumns" :data="subData">
        <template slot="earnestMoneyRatioSlot" slot-scope="{ row }">
          <span>{{ row.earnestMoneyRatio || "--" }}</span>
        </template>
        <template slot="actionSlot" slot-scope="{ row }">
          <template v-if="row.settlementModel == 'FULL_PAYMENT'">
            <span
              v-if="
                row.itemPaymentStatus == 'FIRST_PAYMENT_REFUSE' ||
                row.itemPaymentStatus == null
              "
              class="hover-pointer-color"
              @click="payDeposit(row)"
              >去支付</span
            >
          </template>
          <template v-else>
            <span
              v-if="
                row.itemPaymentStatus == 'FIRST_PAYMENT_REFUSE' ||
                row.itemPaymentStatus == null
              "
              class="hover-pointer-color"
              @click="payDeposit(row)"
              >{{ "去支付" || "支付定金" }}</span
            >
            <span
              @click="payFinal(row)"
              v-if="row.itemPaymentStatus == 'FIRST_PAYMENT_CONFIRM'"
              class="hover-pointer-color"
              >{{ "去支付" || "支付尾款" }}</span
            >
          </template>
        </template>
      </Table>
    </el-dialog>

    <!-- 询价详情 -->
    <PriceDialog
      :priceData="priceData"
      ref="priceDialogRef"
      @close="
        () => {
          $refs.priceDialogRef.openPriceDialog(false);
          priceData = {};
        }
      "
    />

    <!-- 用户名输入弹窗 -->
    <el-dialog
      title="融资支付"
      :visible.sync="userNameDialogVisible"
      width="450px"
      @close="handleUserNameDialogClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div style="margin-bottom: 20px;">
        <div style="color: #f56c6c; margin-bottom: 10px; font-size: 14px;">
          {{ userNameErrorMessage }}
        </div>
        <p style="color: #606266; font-size: 14px;">请填写新用户名</p>
      </div>
      <el-form
        :model="userNameForm"
        :rules="userNameRules"
        ref="userNameFormRef"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="userName">
          <el-input
            v-model="userNameForm.userName"
            placeholder="请输入新用户名"
            clearable
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleUserNameDialogClose">取 消</el-button>
        <el-button
          :loading="userNameLoading"
          type="primary"
          @click="confirmUserName"
        >
          确 认
        </el-button>
      </span>
    </el-dialog>

    <FilePreview :url="fileUrl" />
    <ImagePreview :url="imageUrl" />
  </div>
</template>

<script>
import {
  getOrderList,
  sureReceived,
  cancelOrder,
  delOrder,
  uploadPaymentVoucher,
  changeOrderItem,
  detail,
  orderDetail as orderDetailApi,
  // uploadFirstPaymentVoucherUrl as orderItemPayment,
  orderItemPayment,
  uploadBalancePaymentVoucherUrl,
  payableByItemSn as payableByItemSnApi,
  getGoodsAndQuota as getGoodsAndQuotaApi,
  getByOrderSn as getByOrderSnApi,
  changeConfirmStatus as changeConfirmStatusApi,
} from "@/api/order";
import { afterSaleReason } from "@/api/member";
import { orderStatusList } from "../enumeration.js";
import { getContractTemplate, genContract } from "@/api/contract";
import ContractSign from "@/components/ContractSign.vue";
import { Loading } from "element-ui";
import { Upload } from "view-design";
import { commonUrl } from "@/plugins/request.js";
import FilePreview from "@/components/FilePreview.vue";
import ImagePreview from "@/components/ImagePreview.vue";
import storage from "@/plugins/storage";
import * as apiLogin from "@/api/login.js";
import { accountConnectionApi, orderPushApi } from "@/api/common.js";
import PriceDialog from "./components/priceDialog.vue";

export default {
  name: "MyOrder",
  components: {
    ContractSign,
    FilePreview,
    ImagePreview,
    PriceDialog,
  },
  props: {
    homePage: {
      // 判断是否个人中心首页展示内容
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loadingBtn: false,
      paymentTitle: "请选择付款方式", // 支付弹窗标题
      orderList: [], // 订单列表
      orderItems: [], // 子订单列表
      params: {
        // 请求参数
        pageNumber: 1,
        pageSize: 10,
        // orderStatus: 'ALL',
        keywords: "",
        tag: "ALL",
        orderType: "",
      },
      cancelParams: {
        // 取消售后参数
        orderSn: "",
        reason: "",
      },
      // 状态数组
      orderStatusList,
      changeWay: ["全部订单", "待付款", "待收货", "已完成"], // 订单状态
      total: 0, // 数据总数
      spinShow: false, // 加载状态
      afterSaleModal: false, // 选择售后商品模态框
      afterSaleColumns: [
        // 售后商品表头
        { title: "商品名称", key: "name" },
        { title: "价格", key: "goodsPrice" },
      ],
      afterSaleArr: [], // 售后商品列表
      cancelAvail: false, // 取消订单modal控制
      cancelReason: [], // 取消订单原因

      contractList: [], // 合同列表
      signStatus: "", // 签署状态

      // 上传支付凭证
      dialogVisible: false,
      action: commonUrl + "/common/common/upload/file", // 上传地址
      accessToken: {}, // 验证token
      topSearchShow: false,
      formData: {
        paymentMethod: null, // 支付方式
        paymentVoucherUrl: [], // 支付凭证
        settlementBankAccountNum: null, // 对公账户
        companyName: null, // 对公账户
        settlementBankBranchName: null, // 所属银行
      },
      rules: {
        paymentMethod: [
          { required: true, message: "请选择支付方式", trigger: "change" },
        ],
        paymentVoucherUrl: [
          { required: true, message: "请上传支付凭证", trigger: "change" },
        ],
      },
      formRef: null,
      fileListData: [],
      fileUrl: "",
      imageUrl: "",
      orderSn: "", // 订单号

      receiptVisible: false, // 确认收货弹窗
      receiptFormRef: null, // 确认收货表单ref
      receiptFormData: {
        num: "", // 数量
        flowPrice: "", // 调整的价格
        orderItemSn: "", // 子订单商品id
        changeDesc: "", // 描述
        changeUrl: "", // 凭证
      },
      receiptFileListData: [],
      receiptRules: {
        num: [{ required: true, message: "请输入收货数量", trigger: "blur" }],
        flowPrice: [
          { required: true, message: "请输入调整的价格", trigger: "blur" },
        ],
        changeDesc: [
          { required: true, message: "请输入描述", trigger: "blur" },
        ],
        changeUrl: [{ required: true, message: "请上传附件", trigger: "blur" }],
      },
      // receiptColumns: [
      //   // 确认收货表格
      //   { type: "selection", width: 60, align: "center" },
      //   { title: "商品名称", key: "name" },
      //   { title: "商品数量", key: "num" },
      //   { title: "商品总价", key: "goodsPrice" },
      // ],
      selectReceipt: [], // 确认收货选中的商品
      paymentMethodData: [
        { id: 1, name: "线下付款" },
        { id: 2, name: "融资付款" }, // 暂时去掉
      ], // 支付方式列表
      // 子订单列表
      subOrderVisible: false,
      subColumns: [
        { title: "商品名称", key: "goodsName" },
        { title: "商品数量", key: "num" },
        { title: "商品规格", key: "simpleSpecs" },
        { title: "商品总价", key: "subTotal" },
        { title: "运费", key: "freightPrice" },
        {
          title: "结算方式",
          key: "settlementModel",
          render: (h, params) => {
            if (params.row.settlementModel == "DEPOSIT_SEND_OUT_GOODS") {
              return h("div", [
                h("tag", { props: { color: "blue" } }, "定金发货"),
              ]);
            } else {
              return h("div", [
                h("tag", { props: { color: "volcano" } }, "全额付款"),
              ]);
            }
          },
          width: 120,
        },
        { title: "已付金额", key: "paidAmount" },
        {
          title: "定金比例%",
          key: "earnestMoneyRatio",
          slot: "earnestMoneyRatioSlot",
          align: "center",
        },
        {
          title: "操作",
          key: "action",
          slot: "actionSlot",
          align: "center",
        },
      ],
      subData: [],
      orderItemSn: "",

      paymentType: 1, // 支付方式 1 全额付款 2 定金付款 3 尾款付款

      // 应付金额接口
      itemPaymentStatus: "", // 支付状态
      payableAmount: "0", // 应付金额
      paidAmount: "0", // 已付金额
      settlementModel: "", // 结算方式

      // 融资产品信息
      quotaAmount: "", // 可授信额度
      quotaMessage: "",

      // 询价详情
      priceDialog: false,
      priceData: {},

      subOrderStoreId: "", // 子订单店铺id
      changeBankStatus: "", // 结算银行状态

      // 用户名输入对话框
      userNameDialogVisible: false,
      userNameForm: {
        userName: ""
      },
      userNameRules: {
        userName: [
          { required: true, message: "请输入账号", trigger: "blur" },
          { min: 3, max: 20, message: "账号长度在 3 到 20 个字符", trigger: "blur" }
        ]
      },
      userNameLoading: false,
      userNameErrorMessage: "", // 用于显示错误信息
    };
  },
  mounted() {
    this.accessToken.accessToken = storage.getItem("accessToken");
    if (this.homePage) this.params.pageSize = 5;
    this.getList();
  },
  computed: {
    // 提交按钮文本
    submitButtonText() {
      // 当支付方式为2（融资支付）且额度列表为空时，显示"去申请"
      let text = "确 认";
      if (this.formData.paymentMethod === 2 && this.quotaAmount) {
        text = "去融资";
      } else if (this.formData.paymentMethod === 2 && !this.quotaAmount) {
        text = "去申请";
      } else {
        text = "确 认";
      }
      return text;
    },
  },
  methods: {
    // 商家确认数量状态
    confirmStatus(changeConfirmStatus) {
      const statusMap = {
        CONFIRM: "已确认",
        REFUSE: "已拒绝",
        UN_CONFIRM: "审核中",
      };
      // 使用对象的属性访问来获取对应状态的描述，若不存在则返回默认值 ''
      return statusMap[changeConfirmStatus] || "";
    },
    // 商家确认数量描述
    confirmText(changeConfirmStatus) {
      const statusMap = {
        CONFIRM: "",
        REFUSE: "商家",
        UN_CONFIRM: "商家",
      };
      // 使用对象的属性访问来获取对应状态的描述，若不存在则返回默认值 ''
      return statusMap[changeConfirmStatus] || "";
    },
    // 数量不符
    adjustReceived(order) {
      // 确认收货数量
      this.receiptVisible = true;
      this.receiptFormData.orderItemSn = order.sn;
    },
    // 确认数量
    confirmNum(order) {
      this.$confirm("请确认到货数量，该操作不可逆。是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          const loading = this.$loading({
            lock: true,
            text: "请稍等...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          let orderItemSn = order.sn;
          let changeConfirmStatus = "CONFIRM";

          try {
            const res = await changeConfirmStatusApi(
              orderItemSn,
              changeConfirmStatus
            );

            if (res.success) {
              loading.close();
              this.$message.success("操作成功");
              this.getList();
            }
          } catch (error) {
            loading.close();
          }
        })
        .catch(() => {});
    },
    // 询价详情
    async getByOrderSn(sn) {
      const res = await getByOrderSnApi({ orderSn: sn });
      if (res.success) {
        // this.priceDialog = true;
        this.$refs.priceDialogRef.openPriceDialog(true);
        this.priceData = res.result;
      }
    },
    // 支付方式切换
    async getPaymentMethod(value) {
      if (value == 1) {
        this.paymentTitle = "线下支付";
        await this.bankDetail(this.subOrderStoreId);
      } else if (value == 2) {
        this.paymentTitle = "融资支付";
        await this.getGoodsAndQuota(); // 暂时禁用
      }
    },
    // 银行信息 copy
    copy(value) {
      console.log(value, "+银行信息");
      // 检查浏览器是否支持 Clipboard API
      if (navigator.clipboard) {
        navigator.clipboard
          .writeText(value)
          .then(() => {
            this.$message.success("复制成功");
          })
          .catch((err) => {
            console.error("复制失败:", err);
            this.$message.error("复制失败，请重试");
          });
      } else {
        // 旧版浏览器的兼容方案
        const textarea = document.createElement("textarea");
        textarea.value = value;
        document.body.appendChild(textarea);
        textarea.select();
        try {
          document.execCommand("copy");
          this.$message.success("复制成功");
        } catch (err) {
          console.error("复制失败:", err);
          this.$message.error("复制失败，请重试");
        } finally {
          document.body.removeChild(textarea);
        }
      }
    },
    // 银行信息
    async bankDetail(storeId) {
      try {
        const res = await detail(storeId);
        if (res.success) {
          this.formData.settlementBankAccountNum =
            res.result.settlementBankAccountNum;
          this.formData.settlementBankBranchName =
            res.result.settlementBankAccountName;
          this.formData.companyName = res.result.companyName;
          this.changeBankStatus = res.result.changeBankStatus;

          if (this.changeBankStatus !== "SUCCESS") {
            this.$message.error("店铺银行信息正在审批中，请稍后再试！");
          }
        }
      } catch (error) {
        console.error("bankDetail error:", error);
      }
    },
    async getOrderDetailApi(sn) {
      try {
        // 订单详情接口
        const res = await orderDetailApi(sn);
        if (res.success) {
          this.subData = res.result.orderItems.map((item) => {
            return {
              ...item,
              paidAmount: item.paidAmount || "0",
              freightPrice: item.priceDetailDTO.freightPrice || "0",
            };
          });
        }
      } catch (error) {
        console.error("getOrderDetailApi error:", error);
      }
    },

    // 子订单应付金额
    async payableByItemSn(orderItemSn) {
      try {
        const res = await payableByItemSnApi(orderItemSn);
        if (res.success) {
          this.payableAmount = res.result.payableAmount || "0";
          this.paidAmount = res.result.paidAmount || "0";
          this.itemPaymentStatus = res.result.itemPaymentStatus;
          this.settlementModel = res.result.settlementModel;
        }
      } catch (error) {
        console.error("payableByItemSn error:", error);
      }
    },
    // 子订单融资信息
    async getGoodsAndQuota() {
      try {
        const res = await getGoodsAndQuotaApi();
        if (res.success) {
          this.quotaAmount = res.result;
          // this.quotaAmount = [{}, {}];

          if (res.message == "0") {
            this.quotaMessage =
              "无可用融资产品，是否注册并跳转供应链金融平台进行额度申请?";
          } else if (res.message == "1") {
            this.quotaMessage =
              "无可用融资产品，是否跳转供应链金融平台进行额度申请?";
          } else {
            this.quotaMessage = "";
          }
        } else {
          this.$message.error(res.message);
        }
      } catch (error) {
        console.error("getGoodsAndQuota error:", error);
      }
    },

    // 定金
    async payDeposit(subOrder) {
      // await this.bankDetail(subOrder.storeId);
      this.subOrderStoreId = subOrder.storeId;
      await this.payableByItemSn(subOrder.sn);
      // await this.getGoodsAndQuota(); // 暂时禁用

      this.orderItemSn = subOrder.sn;
      this.orderSn = subOrder.orderSn;
      this.dialogVisible = true;
      this.paymentType = 2;
    },
    // 尾款
    async payFinal(subOrder) {
      // await this.bankDetail(subOrder.storeId);
      this.subOrderStoreId = subOrder.storeId;
      await this.payableByItemSn(subOrder.sn);
      // await this.getGoodsAndQuota();  // 暂时禁用

      this.orderItemSn = subOrder.sn;
      this.orderSn = subOrder.orderSn;
      this.dialogVisible = true;
      this.paymentType = 3;
    },
    // 定金支付弹窗
    async subUploadVoucher(order) {
      // 订单详情接口
      await this.getOrderDetailApi(order.sn);
      this.subOrderVisible = true;
    },
    selectReceiptMethod(selected) {
      this.selectReceipt = selected;
    },

    handleReceiptClose() {
      this.receiptVisible = false;
      // this.receiptFormData.changeUrl = [];
      this.receiptFileListData = [];

      this.$refs.receiptFormRef.resetFields();
    },
    handleErrorReceipt(file, fileList) {
      console.log(file, "file");
      console.log(fileList, "fileList");
      this.$message.error(fileList.message);
    },
    handleSuccessReceipt(response, file, fileList) {
      console.log(fileList, "fileList");

      this.receiptFileListData = fileList;
      this.receiptFormData.changeUrl = fileList;
    },

    handleFileRemoveReceipt(file, fileList) {
      // uploadForm.value.contractInfoList = fileList
      console.log(file, "file");
      console.log(fileList, "fileList");
      this.receiptFormData.changeUrl = fileList;
      this.receiptFileListData = fileList;
    },
    handlePreviewReceipt(file) {
      console.log(file, "--------------");
      const fileName = file.name;
      const lowerCaseFileName = fileName.toLowerCase();
      const targetUrl = file.response.result;
      if (lowerCaseFileName.endsWith(".pdf")) {
        this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      } else {
        this.imageUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      }
    },
    handleBeforeUploadReceipt(file) {
      console.log(file, "---file");
      const type = file.type;
      const isOver50MB = file.size > 50 * 1024 * 1024;
      if (
        ["image/jpg", "image/jpeg", "image/png", "application/pdf"].includes(
          type
        )
      ) {
        if (isOver50MB) {
          this.$message.error("文件大小不能超过50M");
          return false;
        }
      } else {
        this.$message.error("文件格式错误");
        return false;
      }
    },
    // 数量不符确认
    handleReceiptSubmit() {
      this.$refs.receiptFormRef.validate(async (valid) => {
        if (valid) {
          this.loadingBtn = true;
          if (this.receiptFileListData.length > 0) {
            this.receiptFormData.changeUrl = this.receiptFileListData
              .map((item) => item.response.result)
              .join(",");
          }

          // 防止上传组件校验错误
          if (this.receiptFileListData.length == 0) {
            this.loadingBtn = false;
            return this.$message.error("请上传凭证");
          }

          let params = {
            ...this.receiptFormData,
          };

          try {
            const res = await changeOrderItem(params);
            if (res.success) {
              this.loadingBtn = false;
              this.$message.success("操作成功");
              this.getList();
              this.handleReceiptClose();
            }
          } catch (error) {
            this.loadingBtn = false;
          }
        }
      });
    },
    // 上传支付凭证
    submit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          this.loadingBtn = true;

          let params = {};

          let res = "";

          if (this.formData.paymentMethod === 2) {
            apiLogin
              .getMemberMsg()
              .then(async (resMember) => {
                if (resMember.success) {
                  // 如果 connectSysType 不为 "1"，先调用 accountConnectionApi
                  if (resMember.result.connectSysType !== "1") {
                    try {
                      const accountRes = await accountConnectionApi(null);
                      if (!accountRes.success) {
                        // 检查是否是状态码20035
                        if (accountRes.code === 20035) {
                          // 弹出输入框让用户输入新的账号
                          this.showUserNameInputDialog(accountRes.message);
                          return;
                        } else {
                          // 其他错误正常处理
                          this.$message.error(accountRes.message || "账号打通失败");
                          return;
                        }
                      }
                    } catch (error) {
                      // 检查是否是状态码20035的异常
                      if (error.code === 20035) {
                        // 弹出输入框让用户输入新的账号
                        this.showUserNameInputDialog(error.message);
                        return;
                      } else {
                        // 其他异常正常处理
                        this.$message.error(error.message || "账号打通失败");
                        return;
                      }
                    }
                  }
                  // 调用 orderPushApi 获取订单信息
                  await this.handleOrderPushAndMessage("product");
                  this.loadingBtn = false;
                }
              })
              .catch(() => {
                this.loadingBtn = false;
              });
            return;
          }

          // 线下支付
          if (this.changeBankStatus !== "SUCCESS") {
            this.loadingBtn = false;
            this.$message.error("店铺银行信息正在审批中，请稍后再试！");
            return;
          }

          if (this.formData.paymentMethod === 1) {
            let paymentVoucherUrls = this.formData.paymentVoucherUrl.map(
              (item) => item.response.result
            );

            // 防止上传组件校验错误
            if (paymentVoucherUrls.length == 0) {
              this.loadingBtn = false;
              this.$message.error("请上传凭证");
              return;
            }

            orderItemPayment(paymentVoucherUrls, this.orderItemSn)
              .then((res) => {
                if (res.success) {
                  this.loadingBtn = false;
                  this.$message.success("操作成功");
                  this.getList();
                  this.handleClose();
                }
              })
              .catch(() => {
                this.loadingBtn = false;
              });
            return;
          }

          // 判断支付方式
          // if (this.paymentType == 1) {
          //   // 全款
          //   params.orderSn = this.orderSn;
          //   params.paymentVoucherUrl = this.formData.paymentVoucherUrl
          //     .map((item) => item.response.result)
          //     .join(",");
          //   res = await uploadPaymentVoucher(params);
          // } else if (this.paymentType == 2) {
          //   // 定金
          //   params.orderItemSn = this.orderItemSn;
          //   params.firstPaymentVoucherUrl = this.formData.paymentVoucherUrl
          //     .map((item) => item.response.result)
          //     .join(",");
          //   res = await uploadFirstPaymentVoucherUrl(params);
          //   await this.getOrderDetailApi(this.orderSn);
          // } else if (this.paymentType == 3) {
          //   // 尾款
          //   params.orderItemSn = this.orderItemSn;
          //   params.balancePaymentVoucherUrl = this.formData.paymentVoucherUrl
          //     .map((item) => item.response.result)
          //     .join(",");
          //   res = await uploadBalancePaymentVoucherUrl(params);
          //   await this.getOrderDetailApi(this.orderSn);
          // }

          // console.log(params, "params");

          // const res = await uploadPaymentVoucher(params);

          // if (res.success) {
          //   this.$message.success("操作成功");
          //   this.getList();
          //   this.handleClose();
          // }
        }
      });
      // .catch(() => {
      //   this.loadingBtn = false;
      // });
    },
    async financingDetail(sn) {
      this.orderItemSn = sn;
      await this.handleOrderPushAndMessage("financing");
    },
    // 封装调用 orderPushApi 并发送消息的逻辑
    async handleOrderPushAndMessage(menu) {
      // 调用 orderPushApi 获取订单信息
      const orderRes = await orderPushApi({ sn: this.orderItemSn });
      if (orderRes.success) {
        const data = orderRes.result;
        const params = {
          id: data.id, // 订单id
          typeId: data.typeId,
          uuid: data.uuid,
        };

        // 封装打开新窗口并发送消息的逻辑
        this.openAndPostMessage(params, menu);

        if (menu === "product") {
          this.handleClose();
        }
        // window.location.reload();
      }
    },
    // 封装打开新窗口并发送消息的逻辑
    openAndPostMessage(params, menu) {
      // const BASE_URL = "http://218.17.186.140:38002";
      const BASE_URL = "http://192.168.0.196:8080";
      // const BASE_URL = "http://192.168.0.100:8080";
      const PATH_MAP = {
        product: "/user/product",
        financing: "/user/financing?menu=financing",
      };

      const targetUrl = BASE_URL + (PATH_MAP[menu] || PATH_MAP["product"]);

      const bWindow = window.open(targetUrl, "_blank");

      // 等待 B 网页加载完成
      setTimeout(() => {
        if (bWindow) {
          // 发送消息，实际使用时替换 '*' 为目标源 URL
          bWindow.postMessage(params, targetUrl);
        }
      }, 1000);
    },
    handleClose() {
      this.paymentTitle = "请选择付款方式";
      this.dialogVisible = false;
      this.fileListData = [];
      // this.formData.paymentVoucherUrl = [];
      this.formData.paymentMethod = null;
      this.$refs.formRef.resetFields();
      this.loadingBtn = false;
      this.changeBankStatus = "";
      this.quotaAmount = "";
    },
    handleError(file, fileList) {
      console.log(file, "file");
      console.log(fileList, "fileList");
      this.$message.error(fileList.message);
    },
    handleSuccess(response, file, fileList) {
      console.log(fileList, "fileList");

      this.fileListData = fileList;
      this.formData.paymentVoucherUrl = fileList;
    },

    handleFileRemove(file, fileList) {
      // uploadForm.value.contractInfoList = fileList
      console.log(file, "file");
      console.log(fileList, "fileList");
      // this.fileListData = [];
      this.formData.paymentVoucherUrl = fileList;
      this.fileListData = fileList;
    },
    handlePreview(file) {
      console.log(file, "--------------");
      const fileName = file.name;
      const lowerCaseFileName = fileName.toLowerCase();
      const targetUrl = file.response.result;
      if (lowerCaseFileName.endsWith(".pdf")) {
        this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      } else {
        this.imageUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      }
    },
    handleBeforeUpload(file) {
      console.log(file, "---file");
      const type = file.type;
      const isOver50MB = file.size > 50 * 1024 * 1024;
      if (
        ["image/jpg", "image/jpeg", "image/png", "application/pdf"].includes(
          type
        )
      ) {
        if (isOver50MB) {
          this.$message.error("文件大小不能超过50M");
          return false;
        }
      } else {
        this.$message.error("文件格式错误");
        return false;
      }
    },
    // 退款状态枚举
    refundPriceList(status) {
      switch (status) {
        case "ALL_REFUND":
          return "全部退款";
        case "PART_REFUND":
          return "部分退款";
        case "NO_REFUND":
          return "";
        case "REFUNDING":
          return "退款中";
        default:
          return "";
      }
    },
    goodsDetail(skuId, goodsId) {
      // 跳转商品详情
      let routeUrl = this.$router.resolve({
        path: "/goodsDetail",
        query: { skuId, goodsId },
      });
      window.open(routeUrl.href, "_blank");
    },
    // 切换订单状态
    change(index) {
      switch (index) {
        case 0:
          this.params.tag = "ALL";
          break;
        case 1:
          this.params.tag = "WAIT_PAY";
          break;
        case 2:
          this.params.tag = "WAIT_ROG";
          break;
        case 3:
          this.params.tag = "COMPLETE";
          break;
      }
      this.getList();
    },
    // 跳转店铺首页
    shopPage(id) {
      let routeUrl = this.$router.resolve({
        path: "/Merchant",
        query: { id: id },
      });
      window.open(routeUrl.href, "_blank");
    },
    orderDetail(sn) {
      // 跳转订单详情
      this.$router.push({ name: "OrderDetail", query: { sn } });
    },
    async signContrart(order, type) {
      const loading = Loading.service({
        lock: true,
        text: "合同生成中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 查看或签署
      this.signStatus = type;

      try {
        // 获取子订单编号
        await this.getOrderDetailApi(order.sn);
        let bizNos = this.subData.map((item) => item.sn).join(",");

        // 签署合同 只考虑一份合同
        const res = await getContractTemplate({ bigBizNos: order.sn, bizNos });
        if (res.success) {
          this.contractList = [];

          let urlsList = [];

          // 过滤出没有 contractId 的项目，并转换为所需格式
          let templateIdArr = res.result
            .filter((e) => !e.contractId)
            .map((e) => ({
              templateId: e.templateId,
              contractBizNo: e.bizNo,
              bigBizNo: order.sn,
            }));

          // 如果有需要生成合同的项目
          if (templateIdArr.length) {
            // 生成合同
            urlsList = [];
            for (const item of templateIdArr) {
              const genRes = await genContract(item);
              if (genRes.success) {
                urlsList.push(genRes.result);
              } else {
                loading.close();
              }
            }

            // 创建一个 Map 来快速查找 forceReadingSecond
            // const forceReadingMap = new Map(
            //   res.result.map((item) => [
            //     item.contractId,
            //     item.forceReadingSecond,
            //   ])
            // );

            // urlsList = urlsList.map((e) => {
            //   return {
            //     name: e.contractName, // 优先使用 contractName
            //     url: e.url,
            //     contractId: e.contractId,
            //     forceReadingSecond: forceReadingMap.get(e.contractId) || 0, // 从 Map 中获取，默认为 0
            //   };
            // });

            // 生成合同接口完成后从合同模板接口中获取合同
            const resTemplate = await getContractTemplate({
              bigBizNos: order.sn,
              bizNos,
            });
            // 从模板中获取合同
            urlsList = resTemplate.result.map((e) => {
              return {
                name: e.templateName,
                url: e.pdfUrl,
                contractId: e.contractId,
                forceReadingSecond: e.forceReadingSecond,
              };
            });

            // 将处理好的url赋给contractList
            this.contractList = urlsList;
            loading.close();
            this.$refs.contractSignRef.handleOpen();
          } else {
            // 不调用生成合同接口；从模板中获取合同
            urlsList = res.result.map((e) => {
              return {
                name: e.templateName,
                url: e.pdfUrl,
                contractId: e.contractId,
                forceReadingSecond: e.forceReadingSecond,
              };
            });

            // 将处理好的url赋给contractList
            this.contractList = urlsList;
            loading.close();
            this.$refs.contractSignRef.handleOpen();
          }
        } else {
          loading.close();
        }
      } catch (error) {
        loading.close();
      }
    },
    // adjustReceived(order) {
    //   // 确认收货数量
    //   this.receiptVisible = true;

    //   this.orderItems = order.orderItems;
    // },
    received(sn) {
      sureReceived(sn).then((res) => {
        if (res.success) {
          this.$Message.success("确认收货成功");
          this.getList();
        }
      });
    },
    goPay(sn) {
      // 去支付
      this.$router.push({
        path: "/payment",
        query: { orderType: "ORDER", sn },
      });
    },
    async uploadVoucher(sn, storeId) {
      await this.bankDetail(storeId);
      this.orderSn = sn;
      this.dialogVisible = true;
      this.paymentType = 1;
    },
    applyAfterSale(goodsItem) {
      // 申请售后
      let arr = [];
      goodsItem.forEach((e) => {
        if (
          e.afterSaleStatus === "NOT_APPLIED" ||
          e.afterSaleStatus === "PART_AFTER_SALE"
        ) {
          arr.push(e);
        }
      });
      if (arr.length === 1) {
        this.$router.push({ name: "ApplyAfterSale", query: { sn: arr[0].sn } });
      } else {
        this.afterSaleArr = arr;
        this.afterSaleModal = true;
      }
    },
    // 申请售后
    afterSaleSelect(item) {
      this.$router.push({ name: "ApplyAfterSale", query: { sn: item.sn } });
    },
    comment(sn, goodsIndex) {
      // 评价
      this.$router.push({
        path: "/home/<USER>",
        query: { sn, index: goodsIndex },
      });
    },
    complain(sn, goodsIndex) {
      // 投诉
      this.$router.push({ name: "Complain", query: { sn, index: goodsIndex } });
    },
    delOrder(sn) {
      // 删除订单
      this.$Modal.confirm({
        title: "删除订单",
        content: "<p>确认删除当前订单吗？</p>",
        onOk: () => {
          delOrder(sn).then((res) => {
            if (res.success) {
              this.$Message.success("删除成功");
              this.getList();
            }
          });
        },
        onCancel: () => {},
      });
    },
    getList() {
      // 获取订单列表
      this.spinShow = true;
      let params = JSON.parse(JSON.stringify(this.params));
      if (params.orderStatus === "ALL") {
        delete params.orderStatus;
      }
      getOrderList(params).then((res) => {
        this.spinShow = false;
        if (res.success) {
          this.orderList = res.result.records;
          this.total = res.result.total;
        }
      });
    },
    changePageNum(val) {
      // 修改页码
      this.params.pageNumber = val;
      this.getList();
    },
    changePageSize(val) {
      // 修改页数
      this.params.pageNumber = 1;
      this.params.pageSize = val;
      this.getList();
    },
    handleCancelOrder(sn) {
      // 取消订单
      this.cancelParams.orderSn = sn;
      afterSaleReason("CANCEL").then((res) => {
        if (res.success) {
          this.cancelReason = res.result;
          this.cancelAvail = true;
          this.cancelParams.reason = this.cancelReason[0].reason;
        }
      });
    },
    sureCancel() {
      // 确定取消
      cancelOrder(this.cancelParams).then((res) => {
        if (res.success) {
          this.$Message.success("取消订单成功");
          this.getList();
          this.cancelAvail = false;
        }
      });
    },
    filterOrderStatus(status) {
      // 获取订单状态中文
      const ob = this.orderStatusList.filter((e) => {
        return e.status === status;
      });
      return ob && ob[0] ? ob[0].name : status;
    },

    // 显示用户名输入弹窗
    showUserNameInputDialog(errorMessage) {
      this.userNameDialogVisible = true;
      this.userNameForm.userName = "";
      this.userNameErrorMessage = errorMessage || "请填写新用户名";
    },

    // 关闭用户名输入弹窗
    handleUserNameDialogClose() {
      this.userNameDialogVisible = false;
      this.userNameForm.userName = "";
      this.userNameLoading = false;
      this.userNameErrorMessage = "";
      if (this.$refs.userNameFormRef) {
        this.$refs.userNameFormRef.resetFields();
      }
    },

    // 确认用户名并重新调用accountConnection接口
    async confirmUserName() {
      this.$refs.userNameFormRef.validate(async (valid) => {
        if (valid) {
          this.userNameLoading = true;
          try {
            const accountRes = await accountConnectionApi(this.userNameForm.userName);
            if (accountRes.success) {
              this.$message.success("账号打通成功");
              this.handleUserNameDialogClose();
              // 调用 orderPushApi 获取订单信息
              await this.handleOrderPushAndMessage("product");
              this.loadingBtn = false;
            } else {
              this.$message.error(accountRes.message || "账号打通失败");
              this.userNameLoading = false;
            }
          } catch (error) {
            this.$message.error(error.message || "账号打通失败");
            this.userNameLoading = false;
          }
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.wrapper {
  margin-bottom: 40px;
}
.box {
  overflow: hidden;
}
.page-size {
  margin: 15px 0px;
  text-align: right;
}
/** 订单列表 */
.order-list {
  border: 1px solid #ddd;
  border-radius: 3px;
  margin-bottom: 10px;

  &:hover {
    .del-btn {
      visibility: visible;
    }
  }
  .del-btn {
    visibility: hidden;
  }

  .order-header {
    display: flex;
    align-items: center;
    padding: 10px;
    justify-content: space-between;
    border-bottom: 1px solid #ddd;
    > div:nth-child(1) > div:nth-child(2) {
      font-size: 12px;
      color: #999;
      margin-top: 3px;
    }
  }
  .order-body {
    display: flex;
    justify-content: space-between;
    color: #999;
    padding: 10px;

    .goods-list > div {
      width: 720px;
      display: flex;
      margin-bottom: 10px;
      img {
        width: 60px;
        height: 60px;
        margin-right: 10px;
      }
      > div {
        flex: 1;
      }
    }

    > div:nth-child(2) {
      width: 150px;
      text-align: center;
      span {
        color: #438cde;
        cursor: pointer;
        &:hover {
          color: $theme_color;
        }
      }
      .ivu-icon {
        color: #ff8f23;
        cursor: pointer;
        &:hover {
          color: $theme_color;
        }
      }
    }

    > div:nth-child(3) {
      width: 100px;
      .ivu-btn {
        margin-bottom: 10px;
      }
    }
  }
}

::v-deep .el-upload {
  width: 100%;
  .el-upload-dragger {
    width: 100%;
  }
}

.priceDialog {
  ::v-deep .el-dialog__body {
    padding-top: 0 !important;
  }

  .container {
    .item {
      .item-label {
        display: inline-block;
        width: 100px;
        font-size: 14px;
        color: #666;
        margin-bottom: 10px;
        text-align: right;
        margin-right: 10px;
      }
      .item-value {
        font-size: 16px;
        color: #333;
      }
      .item-value2 {
        font-size: 16px;
        // color: #333;
      }
    }
  }
}

.quota-list {
  padding: 20px 0;
}

.quota-message {
  padding: 16px 20px;
  margin: 12px 0;
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;
  border: 1px solid #e1e8ed;
  background-color: #f8f9fa;
  color: #495057;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}
/* 添加图标位置预留 */
.quota-message::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  vertical-align: middle;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.quota-message--warning {
  background-color: #fff7e6;
  border-color: #ff9800;
  color: #ff9800;
}

.quota-message--warning::before {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff9800'%3E%3Cpath d='M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z'/%3E%3C/svg%3E");
}
</style>
