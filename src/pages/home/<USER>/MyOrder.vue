<template>
  <div class="wrapper">
    <!-- 卡片组件 -->
    <card
      _Title="商品订单"
      :_Size="16"
      :_Tabs="changeWay"
      @_Change="change"
      v-if="!homePage"
    ></card>
    <card
      _Title="商品订单"
      :_Size="16"
      :_Tabs="changeWay"
      @_Change="change"
      _More="全部订单"
      _Src="/home/<USER>"
      v-else
    ></card>
    <!-- 搜索 筛选 -->
    <div class="mb_24 box" v-if="!homePage">
      <div class="global_float_right">
        <Input
          class="width_300"
          search
          enter-button
          v-model="params.keywords"
          @on-search="getList"
          placeholder="请输入订单号搜索"
        />
      </div>
    </div>
    <!-- 订单列表 -->
    <empty v-if="orderList.length === 0" />
    <div class="order-content" v-else>
      <div
        class="order-list"
        v-for="(order, onderIndex) in orderList"
        :key="onderIndex"
      >
        <div class="order-header">
          <div>
            <div>{{ filterOrderStatus(order.orderStatus) }}</div>
            <div>
              订单号：{{ order.sn }} &nbsp; &nbsp; &nbsp;{{ order.createTime }}
            </div>
          </div>
          <div>
            <Button
              v-if="order.orderStatus === 'COMPLETED'"
              @click="delOrder(order.sn)"
              class="del-btn mr_10 fontsize_16"
              style="margin-top: -5px"
              type="text"
              icon="ios-trash-outline"
              size="small"
            ></Button>
            <span>{{ order.flowPrice | unitPrice("￥") }}</span>
          </div>
        </div>
        <div class="order-body">
          <div class="goods-list">
            <div
              v-for="(goods, goodsIndex) in order.orderItems"
              :key="goodsIndex"
            >
              <img
                @click="goodsDetail(goods.skuId, goods.goodsId)"
                class="hover-color"
                :src="goods.image"
                alt=""
              />
              <div>
                <div
                  class="hover-color"
                  @click="goodsDetail(goods.skuId, goods.goodsId)"
                >
                  {{ goods.name }}
                </div>
                <div class="mt_10">
                  <span class="global_color"
                    >{{ goods.goodsPrice | unitPrice("￥") }} </span
                  >x {{ goods.num }}
                  <span style="margin-left: 10px; color: #ff9900">{{
                    refundPriceList(goods.isRefund)
                  }}</span>
                </div>
                <!-- <Button
                  v-if="goods.commentStatus == 'UNFINISHED'"
                  @click="comment(order.sn, goodsIndex)"
                  size="small"
                  type="success"
                  class="fontsize_12"
                  style="
                    position: relative;
                    top: -22px;
                    left: 190px;
                    margin-right: 10px;
                  "
                  >评价</Button
                >
                <Button
                  v-if="goods.complainStatus == 'NO_APPLY'"
                  @click="complain(order.sn, goodsIndex)"
                  type="warning"
                  class="fontsize_12"
                  size="small"
                  style="position: relative; top: -22px; left: 190px"
                  >投诉</Button
                > -->
              </div>
            </div>
          </div>
          <div>
            <span @click="shopPage(order.storeId)">{{ order.storeName }}</span>
          </div>
          <div>
            <!-- 订单基础操作 -->
            <Button @click="orderDetail(order.sn)" type="info" size="small"
              >订单详情</Button
            >
            <!-- 合同按钮 -->
            <template v-if="order.orderStatus != 'CANCELLED'">
              <Button
                v-if="order.buyerSignStatus == 5"
                @click="signContrart(order.sn, 'view')"
                type="success"
                size="small"
                >查看合同</Button
              >
              <Button
                v-else
                @click="signContrart(order.sn, 'sign')"
                type="warning"
                size="small"
                >签署合同</Button
              >
            </template>
            <Button
              @click="handleCancelOrder(order.sn)"
              type="error"
              v-if="order.allowOperationVO.cancel"
              size="small"
              >取消订单</Button
            >
            <!-- <Button
              @click="goPay(order.sn)"
              size="small"
              type="success"
              v-if="
                order.allowOperationVO.pay &&
                order.buyerSignStatus == 5 &&
                order.sellerSignStatus == 5
              "
              >去支付</Button
            > -->
            <Button
              @click="uploadVoucher(order.sn, order.storeId)"
              size="small"
              type="success"
              v-if="
                order.allowOperationVO.pay &&
                order.buyerSignStatus == 5 &&
                order.sellerSignStatus == 5
              "
              >去支付</Button
            >
            <Button
              @click="received(order.sn)"
              size="small"
              type="warning"
              v-if="order.allowOperationVO.rog"
              >确认收货</Button
            >
            <!-- 售后 -->
            <!-- <Button
              v-if="
                order.groupAfterSaleStatus &&
                (order.groupAfterSaleStatus.includes('NOT_APPLIED') ||
                  order.groupAfterSaleStatus.includes('PART_AFTER_SALE'))
              "
              @click="applyAfterSale(order.orderItems)"
              size="small"
              >申请售后</Button
            > -->
          </div>
        </div>
      </div>
      <Spin size="large" fix v-if="spinShow"></Spin>
    </div>
    <!-- 分页 -->
    <div class="page-size" v-if="!homePage">
      <Page
        :total="total"
        @on-change="changePageNum"
        @on-page-size-change="changePageSize"
        :page-size="params.pageSize"
        show-total
        show-sizer
      >
      </Page>
    </div>
    <!-- 选择售后商品 -->
    <Modal v-model="afterSaleModal" title="请选择申请售后的商品">
      <div>
        <Table
          border
          :columns="afterSaleColumns"
          :data="afterSaleArr"
          @on-row-click="afterSaleSelect"
        >
        </Table>
      </div>
      <div slot="footer"></div>
    </Modal>
    <Modal
      v-model="cancelAvail"
      title="请选择取消订单原因"
      @on-ok="sureCancel"
      @on-cancel="cancelAvail = false"
    >
      <RadioGroup
        v-model="cancelParams.reason"
        vertical
        type="button"
        button-style="solid"
      >
        <Radio :label="item.reason" v-for="item in cancelReason" :key="item.id">
          {{ item.reason }}
        </Radio>
      </RadioGroup>
    </Modal>

    <ContractSign
      :contractList="contractList"
      ref="contractSignRef"
      :signStatus="signStatus"
      @signCompleted="() => getList()"
    />

    <!-- 上传支付凭证 -->
    <el-dialog
      title="上传支付凭证"
      :visible.sync="dialogVisible"
      width="30%"
      @close="handleClose"
    >
      <el-form
        :model="formData"
        :rules="rules"
        ref="formRef"
        label-width="auto"
        label-position="top"
      >
        <el-form-item label="付款方式" prop="paymentMethod">
          <el-select
            v-model="formData.paymentMethod"
            placeholder="请选择付款方式"
            clearable
          >
            <el-option
              v-for="item in paymentMethodData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-row :gutter="20" v-if="formData.paymentMethod == 1">
          <el-col :span="12">
            <el-form-item label="所属银行" prop="settlementBankBranchName">
              <el-input v-model="formData.settlementBankBranchName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="对公帐户" prop="settlementBankAccountNum">
              <el-input v-model="formData.settlementBankAccountNum" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item
          label="支付凭证"
          prop="paymentVoucherUrl"
          v-if="formData.paymentMethod == 1"
        >
          <div class="file-upload-container">
            <!-- <Upload
              accept=".jpg,.jpeg,.png,.pdf"
              multiple
              type="drag"
              :action="action"
              :headers="accessToken"
              :on-success="handleSuccess"
              :before-upload="handleBeforeUpload"
              :on-preview="handlePreview"
              :on-remove="handleFileRemove"
              :on-error="handleError"
            >
              <div style="padding: 20px 0">
                <Icon
                  type="ios-cloud-upload"
                  size="52"
                  style="color: #3399ff"
                ></Icon>
                <p class="text">将文件拖到此处,或<em>点击上传</em></p>
              </div>
            </Upload> -->
            <el-upload
              class="upload-demo"
              drag
              :action="action"
              :headers="accessToken"
              multiple
              :on-success="handleSuccess"
              :before-upload="handleBeforeUpload"
              :on-remove="handleFileRemove"
              :on-error="handleError"
              accept=".jpg,.jpeg,.png,.pdf"
              :file-list="fileListData"
              :on-preview="handlePreview"
              :limit="1"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <div class="el-upload__tip" slot="tip">
                只能上传jpg、jepg、png、pdf文件，且不超过100M
              </div>
            </el-upload>
          </div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="submit">确 定</el-button>
      </span>
    </el-dialog>

    <FilePreview :url="fileUrl" />
    <ImagePreview :url="imageUrl" />
  </div>
</template>

<script>
import {
  getOrderList,
  sureReceived,
  cancelOrder,
  delOrder,
  detail,
  uploadPaymentVoucher,
} from "@/api/order";
import { afterSaleReason } from "@/api/member";
import { orderStatusList } from "../enumeration.js";
import { genContract } from "@/api/contract";
import ContractSign from "@/components/ContractSign.vue";
import FilePreview from "@/components/FilePreview.vue";
import ImagePreview from "@/components/ImagePreview.vue";
import { Loading } from "element-ui";
import { commonUrl } from "@/plugins/request.js";
import storage from "@/plugins/storage";
export default {
  name: "MyOrder",
  components: {
    ContractSign,
    FilePreview,
    ImagePreview,
  },
  props: {
    homePage: {
      // 判断是否个人中心首页展示内容
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      orderList: [], // 订单列表
      params: {
        // 请求参数
        pageNumber: 1,
        pageSize: 10,
        // orderStatus: 'ALL',
        keywords: "",
        tag: "ALL",
      },
      cancelParams: {
        // 取消售后参数
        orderSn: "",
        reason: "",
      },
      // 状态数组
      orderStatusList,
      changeWay: ["全部订单", "待付款", "待收货", "已完成"], // 订单状态
      total: 0, // 数据总数
      spinShow: false, // 加载状态
      afterSaleModal: false, // 选择售后商品模态框
      afterSaleColumns: [
        // 售后商品表头
        { title: "商品名称", key: "name" },
        { title: "价格", key: "goodsPrice" },
      ],
      afterSaleArr: [], // 售后商品列表
      cancelAvail: false, // 取消订单modal控制
      cancelReason: [], // 取消订单原因

      contractList: [], // 合同列表
      signStatus: "", // 签署状态

      // 上传支付凭证
      dialogVisible: false,
      action: commonUrl + "/common/common/upload/file", // 上传地址
      accessToken: {}, // 验证token
      formData: {
        paymentMethod: null, // 支付方式
        paymentVoucherUrl: [], // 支付凭证
        settlementBankAccountNum: null, // 对公账户
        settlementBankBranchName: null, // 所属银行
      },
      rules: {
        paymentMethod: [
          { required: true, message: "请选择支付方式", trigger: "change" },
        ],
        paymentVoucherUrl: [
          { required: true, message: "请上传支付凭证", trigger: "change" },
        ],
      },
      formRef: null,
      fileListData: [],
      fileUrl: "",
      imageUrl: "",
      paymentMethodData: [{ id: 1, name: "线下付款" }], // 支付方式列表
    };
  },
  mounted() {
    this.accessToken.accessToken = storage.getItem("accessToken");
    if (this.homePage) this.params.pageSize = 5;
    this.getList();
  },
  methods: {
    // 上传支付凭证
    submit() {
      this.$refs.formRef.validate(async (valid) => {
        if (valid) {
          let params = {};
          // 全款
          params.orderSn = this.orderSn;
          params.paymentVoucherUrl = this.formData.paymentVoucherUrl
            .map((item) => item.response.result)
            .join(",");

          console.log(params, "params");

          const res = await uploadPaymentVoucher(params);

          if (res.success) {
            this.$message.success("上传成功");
            this.getList();
            this.handleClose();
          }
        }
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.fileListData = [];
      this.formData.paymentVoucherUrl = [];
      this.$refs.formRef.resetFields();
    },
    async uploadVoucher(sn, storeId) {
      await this.bankDetail(storeId);
      this.orderSn = sn;
      this.dialogVisible = true;
      this.paymentType = 1;
    },
    // 银行信息
    bankDetail(storeId) {
      detail(storeId).then((res) => {
        if (res.success) {
          this.formData.settlementBankAccountNum =
            res.result.settlementBankAccountNum;
          this.formData.settlementBankBranchName =
            res.result.settlementBankBranchName;
        }
      });
    },
    handleError(file, fileList) {
      console.log(file, "file");
      console.log(fileList, "fileList");
      this.$message.error(fileList.message);
    },
    handleSuccess(response, file, fileList) {
      console.log(fileList, "fileList");

      this.fileListData = fileList;
      this.formData.paymentVoucherUrl = fileList;
    },

    handleFileRemove(file, fileList) {
      // uploadForm.value.contractInfoList = fileList
      console.log(file, "file");
      console.log(fileList, "fileList");
      // this.fileListData = [];
      this.formData.paymentVoucherUrl = fileList;
      this.fileListData = fileList;
    },
    handlePreview(file) {
      console.log(file, "--------------");
      const fileName = file.name;
      const lowerCaseFileName = fileName.toLowerCase();
      const targetUrl = file.response.result;
      if (lowerCaseFileName.endsWith(".pdf")) {
        this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      } else {
        this.imageUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      }
    },
    handleBeforeUpload(file) {
      console.log(file, "---file");
      const type = file.type;
      const isOver50MB = file.size > 50 * 1024 * 1024;
      if (
        ["image/jpg", "image/jpeg", "image/png", "application/pdf"].includes(
          type
        )
      ) {
        if (isOver50MB) {
          this.$message.error("文件大小不能超过50M");
          return false;
        }
      } else {
        this.$message.error("文件格式错误");
        return false;
      }
    },
    // 退款状态枚举
    refundPriceList(status) {
      switch (status) {
        case "ALL_REFUND":
          return "全部退款";
        case "PART_REFUND":
          return "部分退款";
        case "NO_REFUND":
          return "";
        case "REFUNDING":
          return "退款中";
        default:
          return "";
      }
    },
    goodsDetail(skuId, goodsId) {
      // 跳转商品详情
      let routeUrl = this.$router.resolve({
        path: "/goodsDetail",
        query: { skuId, goodsId },
      });
      window.open(routeUrl.href, "_blank");
    },
    // 切换订单状态
    change(index) {
      switch (index) {
        case 0:
          this.params.tag = "ALL";
          break;
        case 1:
          this.params.tag = "WAIT_PAY";
          break;
        case 2:
          this.params.tag = "WAIT_ROG";
          break;
        case 3:
          this.params.tag = "COMPLETE";
          break;
      }
      this.getList();
    },
    // 跳转店铺首页
    shopPage(id) {
      let routeUrl = this.$router.resolve({
        path: "/Merchant",
        query: { id: id },
      });
      window.open(routeUrl.href, "_blank");
    },
    orderDetail(sn) {
      // 跳转订单详情
      this.$router.push({ name: "OrderDetail", query: { sn } });
    },
    async signContrart(sn, type) {
      const loading = Loading.service({
        lock: true,
        text: "合同生成中...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 查看或签署
      this.signStatus = type;

      try {
        // 获取订单信息以获取商品ID
        const currentOrder = this.orderList.find(order => order.sn === sn);
        if (!currentOrder || !currentOrder.orderItems || currentOrder.orderItems.length === 0) {
          loading.close();
          this.$Message.error("无法获取订单商品信息");
          return;
        }

        // 直接生成合同，不传入templateId，让后端自动匹配模板
        const goodsId = currentOrder.orderItems[0].goodsId;
        const contractParams = {
          contractBizNo: sn,
          bigBizNo: sn,
          goodId: goodsId.toString(), // 保持为String类型，避免精度丢失
        };

        const genRes = await genContract(contractParams);
        if (genRes.success) {
          this.contractList = [{
            name: genRes.result.name,
            url: genRes.result.url,
            contractId: genRes.result.contractId,
            forceReadingSecond: genRes.result.forceReadingSecond || 0,
          }];
          loading.close();
          this.$refs.contractSignRef.handleOpen();
        } else {
          loading.close();
          this.$Message.error("合同生成失败");
        }
      } catch (error) {
        loading.close();
        this.$Message.error("合同生成失败");
      }
    },
    received(sn) {
      // 确认收货
      sureReceived(sn).then((res) => {
        if (res.success) {
          this.$Message.success("确认收货成功");
          this.getList();
        }
      });
    },
    goPay(sn) {
      // 去支付
      this.$router.push({
        path: "/payment",
        query: { orderType: "ORDER", sn },
      });
    },
    applyAfterSale(goodsItem) {
      // 申请售后
      let arr = [];
      goodsItem.forEach((e) => {
        if (
          e.afterSaleStatus === "NOT_APPLIED" ||
          e.afterSaleStatus === "PART_AFTER_SALE"
        ) {
          arr.push(e);
        }
      });
      if (arr.length === 1) {
        this.$router.push({ name: "ApplyAfterSale", query: { sn: arr[0].sn } });
      } else {
        this.afterSaleArr = arr;
        this.afterSaleModal = true;
      }
    },
    // 申请售后
    afterSaleSelect(item) {
      this.$router.push({ name: "ApplyAfterSale", query: { sn: item.sn } });
    },
    comment(sn, goodsIndex) {
      // 评价
      this.$router.push({
        path: "/home/<USER>",
        query: { sn, index: goodsIndex },
      });
    },
    complain(sn, goodsIndex) {
      // 投诉
      this.$router.push({ name: "Complain", query: { sn, index: goodsIndex } });
    },
    delOrder(sn) {
      // 删除订单
      this.$Modal.confirm({
        title: "删除订单",
        content: "<p>确认删除当前订单吗？</p>",
        onOk: () => {
          delOrder(sn).then((res) => {
            if (res.success) {
              this.$Message.success("删除成功");
              this.getList();
            }
          });
        },
        onCancel: () => {},
      });
    },
    getList() {
      // 获取订单列表
      this.spinShow = true;
      let params = JSON.parse(JSON.stringify(this.params));
      if (params.orderStatus === "ALL") {
        delete params.orderStatus;
      }
      getOrderList(params).then((res) => {
        this.spinShow = false;
        if (res.success) {
          this.orderList = res.result.records;
          this.total = res.result.total;
        }
      });
    },
    changePageNum(val) {
      // 修改页码
      this.params.pageNumber = val;
      this.getList();
    },
    changePageSize(val) {
      // 修改页数
      this.params.pageNumber = 1;
      this.params.pageSize = val;
      this.getList();
    },
    handleCancelOrder(sn) {
      // 取消订单
      this.cancelParams.orderSn = sn;
      afterSaleReason("CANCEL").then((res) => {
        if (res.success) {
          this.cancelReason = res.result;
          this.cancelAvail = true;
          this.cancelParams.reason = this.cancelReason[0].reason;
        }
      });
    },
    sureCancel() {
      // 确定取消
      cancelOrder(this.cancelParams).then((res) => {
        if (res.success) {
          this.$Message.success("取消订单成功");
          this.getList();
          this.cancelAvail = false;
        }
      });
    },
    filterOrderStatus(status) {
      // 获取订单状态中文
      const ob = this.orderStatusList.filter((e) => {
        return e.status === status;
      });
      return ob && ob[0] ? ob[0].name : status;
    },
  },
};
</script>

<style scoped lang="scss">
.wrapper {
  margin-bottom: 40px;
}
.box {
  overflow: hidden;
}
.page-size {
  margin: 15px 0px;
  text-align: right;
}
/** 订单列表 */
.order-list {
  border: 1px solid #ddd;
  border-radius: 3px;
  margin-bottom: 10px;

  &:hover {
    .del-btn {
      visibility: visible;
    }
  }
  .del-btn {
    visibility: hidden;
  }

  .order-header {
    display: flex;
    align-items: center;
    padding: 10px;
    justify-content: space-between;
    border-bottom: 1px solid #ddd;
    > div:nth-child(1) > div:nth-child(2) {
      font-size: 12px;
      color: #999;
      margin-top: 3px;
    }
  }
  .order-body {
    display: flex;
    justify-content: space-between;
    color: #999;
    padding: 10px;

    .goods-list > div {
      width: 500px;
      display: flex;
      margin-bottom: 10px;
      img {
        width: 60px;
        height: 60px;
        margin-right: 10px;
      }
      > div {
        flex: 1;
      }
    }

    > div:nth-child(2) {
      width: 150px;
      text-align: center;
      span {
        color: #438cde;
        cursor: pointer;
        &:hover {
          color: $theme_color;
        }
      }
      .ivu-icon {
        color: #ff8f23;
        cursor: pointer;
        &:hover {
          color: $theme_color;
        }
      }
    }

    > div:nth-child(3) {
      width: 100px;
      .ivu-btn {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
