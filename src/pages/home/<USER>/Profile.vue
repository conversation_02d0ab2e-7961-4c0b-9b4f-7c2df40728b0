<template>
  <div class="wrapper">
    <card _Title="个人信息" />
    <Form :model="formItem" :rules="rules" ref="form" :label-width="80">
      <FormItem label="头像">
        <Avatar v-if="formItem.face" :src="formItem.face" size="80" />
        <Avatar v-else icon="ios-person" size="80" />
        <Upload
          :show-upload-list="false"
          :on-success="handleSuccess"
          :format="['jpg', 'jpeg', 'png']"
          :action="action"
          :headers="accessToken"
        >
          <Button class="mt_10">上传头像</Button>
        </Upload>
      </FormItem>
      <FormItem label="昵称" prop="nickName">
        <Input
          class="wrapper-user-name"
          style="width: 400px"
          v-model="formItem.nickName"
          placeholder=""
        />
      </FormItem>

      <!-- <FormItem label="生日">
        <DatePicker type="date" placeholder="选择您的生日" v-model="formItem.birthday"></DatePicker>
      </FormItem>
      <FormItem label="性别">
        <RadioGroup v-model="formItem.sex" type="button" button-style="solid">
          <Radio :label="1">男</Radio>
          <Radio :label="0">女</Radio>
        </RadioGroup>
      </FormItem> -->
      <FormItem>
        <Button type="primary" @click="save">确认修改</Button>
      </FormItem>
    </Form>
    <template v-if="this.userInfo.status == 4">
      <card _Title="企业信息" />
      <div class="wrapper-company-info">
        <!-- <h4>基础信息</h4> -->
        <div class="item" v-if="form.companyName">
          <span class="item-label">企业名称：</span>
          <span class="item-value">{{ form.companyName }}</span>
        </div>
        <div class="item" v-if="form.companyAddress">
          <span class="item-label">公司地址：</span>
          <span class="item-value">{{ form.companyAddress }}</span>
        </div>
        <div class="item" v-if="form.employeeNum">
          <span class="item-label">员工人数：</span>
          <span class="item-value">{{ form.employeeNum }} 人</span>
        </div>
        <div class="item" v-if="form.registeredCapital">
          <span class="item-label">注册资金：</span>
          <span class="item-value">{{ form.registeredCapital }} 万元</span>
        </div>
        <div class="item" v-if="form.linkName">
          <span class="item-label">联系人姓名：</span>
          <span class="item-value">{{ form.linkName }}</span>
        </div>
        <div class="item" v-if="form.linkPhone">
          <span class="item-label">联系人电话：</span>
          <span class="item-value">{{ form.linkPhone }}</span>
        </div>
        <div class="item" v-if="form.companyEmail">
          <span class="item-label">电子邮箱：</span>
          <span class="item-value">{{ form.companyEmail }}</span>
        </div>
        <!-- <h4>营业执照信息</h4> -->
        <div class="item" v-if="form.licenseNum">
          <span class="item-label">营业执照：</span>
          <span class="item-value">{{ form.licenseNum }}</span>
        </div>
        <!-- <div class="item" v-if="form.scope">
          <span class="item-label">法定经营范围：</span>
          <span class="item-value">{{ form.scope }}</span>
        </div> -->
        <div class="item" v-if="form.licencePhoto">
          <span class="item-label">营业执照电子版：</span>
          <div
            class="img-list"
            v-for="(item, index) in form.licencePhoto"
            :key="index"
          >
            <img :src="item" width="100" alt="" />
            <div class="cover">
              <Icon
                type="ios-eye-outline"
                @click.native="handleView(item)"
              ></Icon>
            </div>
          </div>
        </div>
        <!-- <h4>法人信息</h4> -->
        <div class="item" v-if="form.legalName">
          <span class="item-label">法人姓名：</span>
          <span class="item-value">{{ form.legalName }}</span>
        </div>
        <div class="item" v-if="form.legalId">
          <span class="item-label">法人证件号：</span>
          <span class="item-value">{{ form.legalId }}</span>
        </div>
        <div class="item" v-if="form.legalPhoto">
          <span class="item-label">法人证件电子版：</span>
          <div
            class="img-list"
            v-for="(item, index) in form.legalPhoto"
            :key="index"
          >
            <img :src="item" width="100" alt="" />
            <div class="cover">
              <Icon
                type="ios-eye-outline"
                @click.native="handleView(item)"
              ></Icon>
            </div>
          </div>
        </div>
      </div>
    </template>

    <Modal title="图片预览" v-model="visible">
      <img :src="previewPicture" v-if="visible" style="width: 100%" />
    </Modal>
  </div>
</template>

<script>
import { editMemberInfo } from "@/api/account.js";
import { commonUrl } from "@/plugins/request.js";
import storage from "@/plugins/storage.js";
import { getCompanyAndBank as getCompanyAndBankApi } from "@/api/shopentry";
export default {
  name: "Profile",
  data() {
    return {
      rules: {
        // 验证规则
        nickName: [
          { required: true, message: "用户昵称不能为空" },
          { max: 51, message: "用户昵称不能超过50个字符" },
        ],
      },
      formItem: {}, // 表单数据
      action: commonUrl + "/common/common/upload/file", // 上传接口
      accessToken: {}, // 验证token
      form: {},
      previewPicture: "", // 预览图片url
      visible: false, // 预览图片
    };
  },
  computed: {
    userInfo() {
      // 用户信息
      if (storage.getItem("userInfo")) {
        return JSON.parse(storage.getItem("userInfo"));
      } else {
        return {};
      }
    },
  },
  mounted() {
    this.formItem = JSON.parse(storage.getItem("userInfo"));
    this.accessToken.accessToken = storage.getItem("accessToken");
    this.getCompanyAndBank();
  },
  methods: {
    save() {
      // 保存
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {
            // birthday: this.$options.filters.unixToDate(this.formItem.birthday / 1000, 'yyyy-MM-dd'),
            face: this.formItem.face,
            nickName: this.formItem.nickName,
            // sex: this.formItem.sex
          };
          editMemberInfo(params).then((res) => {
            if (res.success) {
              this.$Message.success("修改个人资料成功");
              storage.setItem("userInfo", res.result);
              this.$router.go(0);
            }
          });
        }
      });
    },
    handleSuccess(res, file) {
      // 上传成功
      this.$set(this.formItem, "face", res.result);
    },

    // 主动查询更新公司实名信息
    async getCompanyAndBank() {
      const res = await getCompanyAndBankApi();

      if (res.success) {
        this.form = {
          ...res.result,
          companyAddress: res.result.regLocation || "",
          legalPhoto: res.result.legalPhoto.split(","),
          licencePhoto: res.result.licencePhoto.split(","),
        };
      }
    },
    // 图片查看
    handleView(item) {
      this.previewPicture = item;
      this.visible = true;
    },
  },
};
</script>

<style scoped lang="scss">
h4 {
  margin-bottom: 10px;
  padding: 0 10px;
  border: 1px solid #ddd;
  background-color: #f8f8f8;
  font-weight: bold;
  color: #333;
  font-size: 14px;
  line-height: 40px;
  text-align: left;
}
.img-list {
  display: inline-block;
  margin: 10px;
  width: 100px;
  position: relative;
  .cover {
    display: none;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.6);
    width: inherit;
    height: inherit;
    align-items: center;
    justify-content: space-around;
    i {
      color: #fff;
      font-size: 30px;
      cursor: pointer;
    }
  }
  &:hover .cover {
    display: flex;
  }
}

.wrapper-company-info {
  .item {
    margin-bottom: 10px;
    line-height: 40px;
    .item-label {
      display: inline-block;
      font-size: 14px;
      color: #666;
      width: 120px;
      text-align: right;
      margin-right: 10px;
    }
    .item-value {
      font-size: 16px;
      color: #333;
    }
  }
}
</style>
