<template>
  <div class="order-detail" v-if="order.order">
    <card _Title="订单详情" :_Size="16"></card>
    <Card
      class="mb_10"
      v-if="
        order.allowOperationVO.pay ||
        order.allowOperationVO.rog ||
        order.allowOperationVO.cancel
      "
    >
      <!-- <Button
        type="success"
        @click="goPay(order.order.sn)"
        size="small"
        v-if="order.allowOperationVO.pay"
        >去支付</Button
      > -->
      <!-- <Button
        type="info"
        @click="received(order.order.sn)"
        size="small"
        v-if="order.allowOperationVO.rog"
        >确认收货</Button
      > -->
      <Button
        type="error"
        @click="handleCancelOrder(order.order.sn)"
        v-if="order.allowOperationVO.cancel"
        size="small"
        >取消订单</Button
      >
      <Button
        v-if="
          order.allowOperationVO.showLogistics ||
          orderPackage.length > 0 ||
          logistics
        "
        type="info"
        @click="logisticsList()"
        size="small"
        >查看物流</Button
      >
    </Card>
    <p class="verificationCode" v-if="order.order.verificationCode">
      核验码：<span>{{ order.order.verificationCode }}</span>
    </p>
    <div class="order-card">
      <p class="global_color fontsize_18">{{ order.orderStatusValue }}</p>
      <p>订单号：{{ order.order.sn }}</p>
      <div style="color: #999" class="operation-time">
        操作时间：{{ order.order.updateTime || order.order.createTime }}
      </div>
      <Steps
        class="progress"
        :current="progressList.length"
        direction="vertical"
      >
        <Step
          :title="progress.message"
          :content="progress.createTime"
          v-for="(progress, index) in progressList"
          :key="index"
        ></Step>
      </Steps>
    </div>
    <div
      class="order-card"
      v-if="
        order.order.deliveryMethod === 'LOGISTICS' &&
        order.order.orderType !== 'VIRTUAL'
      "
    >
      <h3>收货人信息</h3>
      <p>收货人：{{ order.order.consigneeName }}</p>
      <p>手机号码：{{ order.order.consigneeMobile | secrecyMobile }}</p>
      <p>
        收货地址：{{ order.order.consigneeAddressPath | unitAddress }}
        {{ order.order.consigneeDetail }}
      </p>
    </div>
    <div
      class="order-card"
      v-if="order.order.deliveryMethod === 'SELF_PICK_UP'"
    >
      <h3>自提点信息</h3>
      <p>自提点名称：{{ order.order.storeAddressPath }}</p>
      <p>联系方式：{{ order.order.storeAddressMobile }}</p>
    </div>
    <div class="order-card">
      <h3>付款信息</h3>
      <p>支付方式：{{ order.paymentMethodValue }}</p>
      <p>付款状态：{{ order.payStatusValue }}</p>
    </div>
    <div
      class="order-card"
      v-if="
        !order.order.verificationCode && order.order.orderType !== 'VIRTUAL'
      "
    >
      <h3>配送信息</h3>
      <p>配送方式：{{ order.deliveryMethodValue }}</p>
      <p v-if="order.order.deliveryMethod === 'LOGISTICS'">
        配送状态：{{ order.deliverStatusValue }}
      </p>
      <p v-if="logistics">
        物流信息：{{ logistics.shipper || "暂无物流信息" }}
      </p>
      <p v-if="logistics">
        物流单号：{{ logistics.logisticCode || "暂无物流单号" }}
      </p>
      <div class="div-express-log" v-if="logistics">
        <div class="express-log">
          <p>订单日志：</p>
          <div v-for="(item, index) in logistics.traces" :key="index">
            <span class="time">{{ item.AcceptTime }}</span>
            <span class="detail">{{ item.AcceptStation }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="order-card" v-if="order.order.payStatus === 'PAID'">
      <h3 class="mb_10">发票信息</h3>
      <template v-if="order.order.needReceipt && order.receipts.length">
        <!-- <p>发票抬头：{{ order.receipt.receiptTitle }}</p>
        <p>发票内容：{{ order.receipt.receiptContent }}</p>
        <p v-if="order.receipt.taxpayerId">
          纳税人识别号：{{ order.receipt.taxpayerId }}
        </p> -->
        <Table border :columns="receiptsColumns" :data="order.receipts">
          <template slot="fileUrlSlot" slot-scope="{ row }">
            <span
              v-if="row.fileUrl"
              class="hover-pointer-color"
              @click="previewVoucher(row)"
              >查看</span
            >
            <span v-else>未开票</span>
          </template>
        </Table>
        <!-- order.receipt 变成list了 -->
      </template>
      <div v-else style="color: #999; margin-left: 5px">未开发票</div>
    </div>
    <!-- 结算单 -->
    <div class="order-card">
      <h3 class="mb_10">结算单</h3>
      <Table border :columns="mainColumns" :data="mainData">
        <template slot="actionSlot" slot-scope="{ row }">
          <span
            class="hover-pointer-color mr_10"
            @click="handleMainSettlement(row)"
            >结算单</span
          >
        </template>
      </Table>
    </div>
    <!-- 订单商品 -->
    <div class="order-card">
      <div class="goods">
        <h3 class="mb_10 hover-pointer-color">
          <span @click="shopPage(order.order.storeId)">{{
            order.order.storeName
          }}</span>
        </h3>
        <!-- <table>
        <thead>
          <tr>
            <th width="28%">商品</th>
            <th width="14%">货号</th>
            <th width="8%">单价</th>
            <th width="5%">数量</th>
            <th width="9%">退款状态</th>
            <th width="10%">实际退款金额</th>
            <th width="9%">小计</th>
            <th width="7%">商家确认</th>
            <th width="10%">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(goods, goodsIndex) in order.orderItems" :key="goodsIndex">
            <td>
              <img
                @click="goodsDetail(goods.skuId, goods.goodsId)"
                :src="goods.image"
                alt=""
              />
              <div>
                <p
                  @click="goodsDetail(goods.skuId, goods.goodsId)"
                  class="hover-color"
                >
                  {{ goods.goodsName }}
                </p>
              </div>
            </td>
            <td>{{ goods.id }}</td>
            <td>{{ goods.goodsPrice | unitPrice("￥") }}</td>
            <td>{{ goods.num }}</td>
            <td>{{ refundPriceList(goods.isRefund) }}</td>
            <td>{{ goods.refundPrice | unitPrice("￥") }}</td>
            <td>{{ (goods.goodsPrice * goods.num) | unitPrice("￥") }}</td>
            <td>{{ confirmStatus(goods.changeConfirmStatus) }}</td>
            <td> -->
        <!-- <Button
                v-if="
                  goods.afterSaleStatus.includes('NOT_APPLIED') ||
                  goods.afterSaleStatus.includes('PART_AFTER_SALE')
                "
                @click="applyAfterSale(goods.sn)"
                type="info"
                size="small"
                class="mb_5"
                >申请售后</Button
              >
              <Button
                v-if="goods.commentStatus == 'UNFINISHED'"
                @click="comment(order.order.sn, goodsIndex)"
                size="small"
                type="success"
                class="fontsize_12 mb_5"
                >评价</Button
              > -->
        <!-- <Button
                v-if="goods.complainStatus == 'NO_APPLY'"
                @click="complain(order.order.sn, goodsIndex)"
                type="warning"
                class="fontsize_12"
                size="small"
                >投诉</Button
              > -->
        <!-- <Button
                v-if="
                  goods.deliverNumber == goods.num &&
                  goods.changeConfirmStatus != 'CONFIRM'
                "
                type="warning"
                class="fontsize_12 mb_5"
                size="small"
                @click="adjustReceived(goods)"
                >数量不符</Button
              >
              <Button
                v-if="
                  goods.deliverNumber == goods.num &&
                  goods.changeConfirmStatus != 'CONFIRM'
                "
                type="success"
                class="fontsize_12 mb_5"
                size="small"
                @click="confirmNum(goods)"
                >确认数量</Button
              >
              <Button
                type="success"
                class="fontsize_12 mb_5"
                size="small"
                @click="handleSubSettlement(goods)"
                >子结算单</Button
              >
            </td>
          </tr>
        </tbody>
      </table> -->
        <el-table :data="order.orderItems" border>
          <el-table-column
            label="商品"
            prop="image"
            minWidth="180px"
            align="center"
          >
            <template slot-scope="{ row }">
              <div style="display: flex">
                <img
                  @click="goodsDetail(row.skuId, row.goodsId)"
                  :src="row.image"
                  alt=""
                />
                <p
                  @click="goodsDetail(row.skuId, row.goodsId)"
                  class="hover-color"
                >
                  {{ row.goodsName }}
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="子订单编号"
            prop="sn"
            minWidth="250px"
            align="center"
          ></el-table-column>
          <el-table-column
            label="单价"
            prop="goodsPrice"
            align="center"
            width="140px"
          >
            <template slot-scope="{ row }">
              <span>{{ row.goodsPrice | unitPrice("￥") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="数量"
            prop="num"
            align="center"
          ></el-table-column>
          <el-table-column label="退款状态" prop="isRefund" align="center">
            <template slot-scope="{ row }">
              <span>{{ refundPriceList(row.isRefund) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="实际退款金额"
            prop="refundPrice"
            minWidth="120px"
            align="center"
          >
            <template slot-scope="{ row }">
              <span>{{ row.refundPrice | unitPrice("￥") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="小计" prop="" align="center" width="140px">
            <template slot-scope="{ row }">
              <span>{{ (row.goodsPrice * row.num) | unitPrice("￥") }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="已付金额"
            prop="paidAmount"
            align="center"
            width="140px"
          >
            <template slot-scope="{ row }">
              <span>{{ row.paidAmount | unitPrice("￥") }}</span>
            </template>
          </el-table-column>
          <el-table-column label="待确认凭证" align="center" width="100px">
            <template slot-scope="{ row }">
              <span
                class="hover-pointer-color"
                @click="previewPaymentVoucher(row, 'PAYMENT_UN_CONFIRM')"
                >查看</span
              >
            </template>
          </el-table-column>
          <el-table-column label="已拒绝凭证" align="center" width="100px">
            <template slot-scope="{ row }">
              <span
                class="hover-pointer-color"
                @click="previewPaymentVoucher(row, 'PAYMENT_REFUSE')"
                >查看</span
              >
            </template>
          </el-table-column>
          <el-table-column label="已确认凭证" align="center" width="100px">
            <template slot-scope="{ row }">
              <span
                class="hover-pointer-color"
                @click="previewPaymentVoucher(row, 'PAYMENT_CONFIRM')"
                >查看</span
              >
            </template>
          </el-table-column>
          <el-table-column label="商家确认" prop="changeConfirmStatus">
            <template slot-scope="{ row }">
              <span>{{ confirmStatus(row.changeConfirmStatus) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            prop="action"
            minWidth="100px"
            fixed="right"
            align="center"
          >
            <template slot-scope="{ row }">
              <div>
                <Button
                  v-if="
                    row.deliverNumber == row.num &&
                    row.changeConfirmStatus != 'UN_CONFIRM' &&
                    row.changeConfirmStatus != 'CONFIRM'
                  "
                  type="warning"
                  class="fontsize_12 mb_5"
                  size="small"
                  @click="adjustReceived(row)"
                  >申请售后</Button
                >
              </div>
              <div>
                <Button
                  v-if="
                    row.deliverNumber == row.num &&
                    row.changeConfirmStatus != 'UN_CONFIRM' &&
                    row.changeConfirmStatus != 'CONFIRM'
                  "
                  type="success"
                  class="fontsize_12 mb_5"
                  size="small"
                  @click="confirmNum(row)"
                  >确认收货</Button
                >
              </div>
              <div>
                <Button
                  type="success"
                  class="fontsize_12 mb_5"
                  size="small"
                  @click="handleSubSettlement(row)"
                  >子结算单</Button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- 订单价格 -->
        <div class="order-price">
          <div>
            <span>商品件数：</span><span>{{ order.order.goodsNum }}件</span>
          </div>
          <div>
            <span>商品总价：</span
            ><span>{{ order.order.goodsPrice | unitPrice("￥") }}</span
            ><br />
          </div>
          <div v-if="order.order.orderType !== 'VIRTUAL'">
            <span>运费：</span
            ><span>+{{ order.order.freightPrice | unitPrice("￥") }}</span
            ><br />
          </div>
          <!-- <div v-if="order.order.priceDetailDTO.couponPrice">
            <span>优惠券：</span
            ><span
              >-{{
                order.order.priceDetailDTO.couponPrice || 0 | unitPrice("￥")
              }}</span
            >
          </div>
          <div v-if="order.order.discountPrice">
            <span>活动优惠：</span
            ><span>-{{ order.order.discountPrice | unitPrice("￥") }}</span>
          </div>
          <div v-if="order.order.priceDetailDTO.updatePrice">
            <span>修改价格：</span
            ><span>{{
              order.order.priceDetailDTO.updatePrice | unitPrice("￥")
            }}</span>
          </div> -->
          <div>
            <span>应付金额：</span>
            <span class="actrual-price">{{
              order.order.flowPrice | unitPrice("￥")
            }}</span>
          </div>
        </div>
      </div>
    </div>
    <Modal
      v-model="cancelAvail"
      title="请选择取消订单原因"
      @on-ok="sureCancel"
      @on-cancel="cancelAvail = false"
    >
      <RadioGroup
        v-model="cancelParams.reason"
        vertical
        type="button"
        button-style="solid"
      >
        <Radio :label="item.reason" v-for="item in cancelReason" :key="item.id">
          {{ item.reason }}
        </Radio>
      </RadioGroup>
    </Modal>

    <!--查询物流-->
    <Modal v-model="logisticsModal" width="40">
      <p slot="header"><span>查询物流</span></p>
      <div class="layui-layer-wrap">
        <dl>
          <dt>订单号：</dt>
          <dd>
            <div class="text-box">{{ order.order.sn }}</div>
          </dd>
        </dl>
      </div>
      <div
        v-if="orderPackage.length > 0"
        v-for="(packageItem, packageIndex) in orderPackage"
        :key="packageIndex"
      >
        <div class="layui-layer-wrap">
          <dl>
            <dt>物流公司：</dt>
            <dd>
              <div class="text-box">{{ packageItem.logisticsName }}</div>
            </dd>
          </dl>
          <dl>
            <dt>快递单号：</dt>
            <dd>
              <div nctype="ordersSn" class="text-box">
                {{ packageItem.logisticsNo }}
              </div>
            </dd>
          </dl>
          <div class="div-express-log">
            <ul class="express-log express-log-name">
              <li
                v-for="(item, index) in packageItem.orderPackageItemList"
                :key="index"
              >
                <span class="time" style="width: 50%"
                  ><span>商品名称：</span
                  ><span>{{ item.goodsName }}</span></span
                >
                <span class="time" style="width: 30%"
                  ><span>发货时间：</span
                  ><span>{{ item.logisticsTime }}</span></span
                >
                <span class="time" style="width: 20%"
                  ><span>发货数量：</span
                  ><span>{{ item.deliverNumber }}</span></span
                >
              </li>
            </ul>
            <div class="div-express-log" style="overflow: hidden">
              <ul
                class="express-log"
                v-if="packageItem.traces && packageItem.traces.traces"
              >
                <li
                  v-for="(item, index) in packageItem.traces.traces"
                  :key="index"
                >
                  <span class="time">{{
                    item.AcceptTime || item.acceptTime
                  }}</span>
                  <span class="detail">{{
                    item.AcceptStation || item.remark
                  }}</span>
                </li>
              </ul>
              <ul class="express-log" v-else>
                <li>暂无物流信息</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      <div v-if="orderPackage.length == 0 && logistics">
        <div class="layui-layer-wrap">
          <dl>
            <dt>物流公司：</dt>
            <dd>
              <div class="text-box">{{ logistics.shipper }}</div>
            </dd>
          </dl>
          <dl>
            <dt>快递单号：</dt>
            <dd>
              <div nctype="ordersSn" class="text-box">
                {{ logistics.logisticCode }}
              </div>
            </dd>
          </dl>
          <div class="div-express-log">
            <ul class="express-log" v-if="logistics && logistics.traces">
              <li v-for="(item, index) in logistics.traces" :key="index">
                <span class="time">{{ item.AcceptTime }}</span>
                <span class="detail">{{ item.AcceptStation }}</span>
              </li>
            </ul>
            <ul class="express-log" v-else>
              <li>暂无物流信息</li>
            </ul>
          </div>
        </div>
      </div>
      <div slot="footer" style="text-align: right">
        <Button @click="logisticsModal = false">取消</Button>
      </div>
    </Modal>

    <el-dialog
      title="到货数量"
      :visible.sync="receiptVisible"
      width="50%"
      @close="handleReceiptClose"
    >
      <el-form
        :model="receiptFormData"
        :rules="receiptRules"
        ref="receiptFormRef"
        label-width="auto"
        label-position="left"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="收货数量" prop="num">
              <el-input
                v-model="receiptFormData.num"
                type="number"
                :min="1"
                placeholder="请输入收货数量"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调整价格" prop="flowPrice">
              <el-input
                v-model="receiptFormData.flowPrice"
                type="number"
                placeholder="请输入调整价格"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="描述" prop="changeDesc">
              <el-input
                v-model="receiptFormData.changeDesc"
                type="textarea"
                placeholder="请输入描述"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="上传凭证" prop="changeUrl">
              <el-upload
                class="upload-demo"
                drag
                :action="action"
                :headers="accessToken"
                multiple
                :on-success="handleSuccess"
                :before-upload="handleBeforeUpload"
                :on-remove="handleFileRemove"
                :on-error="handleError"
                accept=".jpg,.jpeg,.png,.pdf"
                :file-list="fileListData"
                :on-preview="handlePreview"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  只能上传jpg、jepg、png、pdf文件，且不超过100M
                </div>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleReceiptClose">取 消</el-button>
        <el-button type="primary" @click="handleReceiptSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <FilePreview :url="fileUrl" />
    <ImagePreview :url="imageUrl" />

    <!-- 多文件回显 -->
    <MultifilePlayback
      :fileListData="MultiFileListData"
      @closePreview="closePreview"
      :title="MultifilePlaybackTitle"
    />
  </div>
</template>
<script>
import {
  orderDetail,
  getTraces,
  sureReceived,
  cancelOrder,
  getPackage,
  changeOrderItem,
  changeConfirmStatus as changeConfirmStatusApi,
  queryListByParams,
  getPaymentVoucherApi,
} from "@/api/order.js";
import { afterSaleReason } from "@/api/member";
import { commonUrl } from "@/plugins/request.js";
import storage from "@/plugins/storage";
import FilePreview from "@/components/FilePreview.vue";
import ImagePreview from "@/components/ImagePreview.vue";
import MultifilePlayback from "@/components/MultifilePlayback.vue";
export default {
  name: "order-detail",
  components: { FilePreview, ImagePreview, MultifilePlayback },
  data() {
    return {
      action: commonUrl + "/common/common/upload/file", // 上传地址
      accessToken: {}, // 验证token
      order: {}, // 订单详情数据
      progressList: [], // 订单流程
      logistics: "", // 物流数据
      cancelParams: {
        // 取消售后参数
        orderSn: "",
        reason: "",
      },
      cancelAvail: false, // 取消订单modal控制
      cancelReason: [], // 取消订单原因
      orderPackage: [],
      packageTraceList: [],
      logisticsModal: false,

      receiptVisible: false, // 确认收货modal控制
      receiptFormRef: null, // 确认收货表单ref
      receiptFormData: {
        num: "", // 数量
        flowPrice: "", // 调整的价格
        orderItemSn: "", // 子订单商品id
        changeDesc: "", // 描述
        changeUrl: "", // 凭证
      },
      receiptRules: {
        num: [{ required: true, message: "请输入收货数量", trigger: "blur" }],
        flowPrice: [
          { required: true, message: "请输入调整的价格", trigger: "blur" },
        ],
        changeDesc: [
          { required: true, message: "请输入描述", trigger: "blur" },
        ],
        changeUrl: [{ required: true, message: "请上传附件", trigger: "blur" }],
      },
      fileListData: [], // 上传凭证
      fileUrl: "", // pdf文件预览地址
      imageUrl: "", // 图片预览地址

      MultiFileListData: [], // 多文件回显
      MultifilePlaybackTitle: "支付凭证", // 多文件回显标题

      // 发票
      receiptsColumns: [
        {
          title: "子订单号",
          key: "orderItemSn",
          minWidth: 100,
        },
        {
          title: "发票抬头",
          key: "receiptTitle",
          minWidth: 100,
        },
        {
          title: "发票内容",
          key: "receiptContent",
          minWidth: 100,
        },
        {
          title: "纳税人识别号",
          key: "taxpayerId",
          minWidth: 100,
        },
        {
          title: "发票文件",
          key: "fileUrl",
          width: 100,
          slot: "fileUrlSlot",
        },
      ],

      // 结算单
      mainData: [],
      mainColumns: [
        // {
        //   title: "会员id",
        //   key: "memberId",
        //   minWidth: 200,
        //   tooltip: true,
        // },
        {
          title: "订单编号",
          key: "orderSn",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "企业名称",
          key: "companyName",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "店铺名称",
          key: "storeName",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "商品名称",
          key: "goodsName",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "主合同编号",
          key: "mainContractNo",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "结算单",
          key: "action",
          minWidth: 160,
          slot: "actionSlot",
          fixed: "right",
          align: "center",
        },
      ],
    };
  },
  methods: {
    // 支付凭证
    async previewPaymentVoucher(row, paymentStatus) {
      const res = await getPaymentVoucherApi(row.sn, paymentStatus);
      if (res.success) {
        let fileUrls = res.result
          .map((item) => item.paymentVoucherUrl)
          .join(",");
        this.handleFilePreview(fileUrls, paymentStatus);
      }
    },
    // 文件列表预览
    handleFilePreview(urls, paymentStatus) {
      if (urls) {
        // 将多个 URL 拆分成数组
        const fileUrls = urls.split(",").filter((url) => url.trim() != "");
        this.MultiFileListData = fileUrls.map((url, index) => {
          const suffix = this.$options.filters.formatFileSuffix(url);

          let fileName = "";
          if (fileUrls.length > 1) {
            fileName = `${this.MultifilePlaybackTitle}${index + 1}${suffix}`;
          } else {
            fileName = `${this.MultifilePlaybackTitle}${suffix}`;
          }

          return {
            name: fileName, // 文件名
            url, // 文件 URL
            uid: -(index + 1), // 唯一标识，负数表示是回显的文件，每个文件有不同的 uid
            status: "done", // 文件状态为已完成
            response: { result: url }, // 模拟上传成功后的响应数据
          };
        });
      } else {
        let msg =
          paymentStatus == "PAYMENT_CONFIRM"
            ? "已确认"
            : paymentStatus == "PAYMENT_UN_CONFIRM"
            ? "待确认"
            : "已拒绝";
        this.$message.error(`该订单没有${msg}的支付凭证`);
        this.MultiFileListData = [];
      }
    },
    // 发票
    previewVoucher(row) {
      console.log(row.fileUrl, "--row.fileUrl");
      // const targetUrl = row.fileUrl;
      // if (targetUrl.endsWith(".pdf")) {
      //   this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      // } else {
      //   this.imageUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      // }
      // 处理文件回显
      if (row.fileUrl) {
        // 将多个 URL 拆分成数组
        const fileUrls = row.fileUrl
          .split(",")
          .filter((url) => url.trim() != "");
        console.log(fileUrls, "fileUrls");
        this.MultiFileListData = fileUrls.map((url, index) => {
          const suffix = this.$options.filters.formatFileSuffix(url);

          let fileName = "";
          if (fileUrls.length > 1) {
            fileName = `${this.MultifilePlaybackTitle}${index + 1}${suffix}`;
          } else {
            fileName = `${this.MultifilePlaybackTitle}${suffix}`;
          }

          return {
            name: fileName, // 文件名
            url, // 文件 URL
            uid: -(index + 1), // 唯一标识，负数表示是回显的文件，每个文件有不同的 uid
            status: "done", // 文件状态为已完成
            response: { result: url }, // 模拟上传成功后的响应数据
          };
        });
      } else {
        this.MultiFileListData = [];
      }
    },
    // 关闭预览,清空 MultiFileListData
    closePreview(data) {
      if (!data) {
        this.MultiFileListData = [];
      }
    },
    // 主结算单
    handleMainSettlement(row) {
      this.fileUrl = "";
      const targetUrl = row.pdfUrl;
      this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
    },
    // 子结算单
    handleSubSettlement(row) {
      console.log(row.sn, "--row");
      let params = {
        pageNumber: 1,
        pageSize: 1000,
        orderItemSn: row.sn,
      };
      queryListByParams(params).then((res) => {
        if (res.success && res.result && res.result.length) {
          // this.mainData = res.result;
          console.log(res, "--row.sn");
          this.fileUrl = "";
          const targetUrl = res.result[0].pdfUrl;
          this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
        } else {
          this.$message.error("未查询到结算单");
        }
      });
    },
    handleError(file, fileList) {
      console.log(file, "file");
      console.log(fileList, "fileList");
      this.$message.error(fileList.message);
    },
    handleSuccess(response, file, fileList) {
      console.log(fileList, "fileList");

      this.fileListData = fileList;
      this.receiptFormData.changeUrl = fileList;
    },

    handleFileRemove(file, fileList) {
      // uploadForm.value.contractInfoList = fileList
      console.log(file, "file");
      console.log(fileList, "fileList");
      // this.fileListData = [];
      this.receiptFormData.changeUrl = fileList;
      this.fileListData = fileList;
    },
    handlePreview(file) {
      console.log(file, "--------------");
      const fileName = file.name;
      const lowerCaseFileName = fileName.toLowerCase();
      const targetUrl = file.response.result;
      if (lowerCaseFileName.endsWith(".pdf")) {
        this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      } else {
        this.imageUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      }
    },
    handleBeforeUpload(file) {
      console.log(file, "---file");
      const type = file.type;
      const isOver50MB = file.size > 50 * 1024 * 1024;
      if (
        ["image/jpg", "image/jpeg", "image/png", "application/pdf"].includes(
          type
        )
      ) {
        if (isOver50MB) {
          this.$message.error("文件大小不能超过50M");
          return false;
        }
      } else {
        this.$message.error("文件格式错误");
        return false;
      }
    },
    confirmNum(order) {
      this.$confirm("请确认到货数量，该操作不可逆。是否继续？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          let orderItemSn = order.sn;
          let changeConfirmStatus = "CONFIRM";
          const res = await changeConfirmStatusApi(
            orderItemSn,
            changeConfirmStatus
          );

          if (res.success) {
            this.$message.success("操作成功");
            this.getDetail();
          }
        })
        .catch(() => {});
    },
    confirmStatus(changeConfirmStatus) {
      const statusMap = {
        CONFIRM: "已确认",
        REFUSE: "已拒绝",
        UN_CONFIRM: "未确认",
      };
      // 使用对象的属性访问来获取对应状态的描述，若不存在则返回默认值 '--'
      return statusMap[changeConfirmStatus] || "--";
    },
    handleReceiptClose() {
      this.receiptVisible = false;
      this.receiptFormData.changeUrl = [];
      this.fileListData = [];

      this.$refs.receiptFormRef.resetFields();
    },
    adjustReceived(order) {
      // 确认收货数量
      this.receiptVisible = true;
      this.receiptFormData.orderItemSn = order.sn;
    },
    // 确认收货数量
    handleReceiptSubmit() {
      this.$refs.receiptFormRef.validate(async (valid) => {
        if (valid) {
          if (this.fileListData.length > 0) {
            this.receiptFormData.changeUrl = this.fileListData
              .map((item) => item.response.result)
              .join(",");
          }

          let params = {
            ...this.receiptFormData,
          };

          const res = await changeOrderItem(params);
          if (res.success) {
            this.$message.success("操作成功");
            this.getDetail();
            this.handleReceiptClose();
          }
        }
      });
    },
    // 退款状态枚举
    refundPriceList(status) {
      switch (status) {
        case "ALL_REFUND":
          return "全部退款";
        case "PART_REFUND":
          return "部分退款";
        case "NO_REFUND":
          return "未退款";
        case "REFUNDING":
          return "退款中";
        default:
          return "未退款";
      }
    },
    goodsDetail(skuId, goodsId) {
      // 跳转商品详情
      let routeUrl = this.$router.resolve({
        path: "/goodsDetail",
        query: { skuId, goodsId },
      });
      window.open(routeUrl.href, "_blank");
    },
    // 跳转店铺首页
    shopPage(id) {
      let routeUrl = this.$router.resolve({
        path: "/Merchant",
        query: { id: id },
      });
      window.open(routeUrl.href, "_blank");
    },
    getDetail() {
      // 获取订单详情
      orderDetail(this.$route.query.sn).then((res) => {
        if (res.success) {
          this.order = res.result;
          this.progressList = res.result.orderLogs;
          if (this.order.order.deliveryMethod === "LOGISTICS") {
            this.getOrderPackage(this.order.order.sn);
            this.traces();
          }

          this.getQueryListByParams();
        }
      });
    },
    getOrderPackage(sn) {
      getPackage(sn).then((res) => {
        if (res.success) {
          this.orderPackage = res.result;
        }
      });
    },
    traces() {
      // 物流信息
      getTraces(this.$route.query.sn).then((res) => {
        if (res.success) {
          this.logistics = res.result;
        }
      });
    },
    logisticsList() {
      this.logisticsModal = true;
      this.packageTraceList = this.orderPackage;
      // getTracesList(this.order.order.sn).then((res) => {
      //   if (res.success && res.result != null) {
      //     this.packageTraceList = res.result;
      //   }
      // });
    },
    received(sn) {
      // 确认收货
      sureReceived(sn).then((res) => {
        if (res.success) {
          this.$Message.success("确认收货成功");
          this.getDetail();
        }
      });
    },
    goPay(sn) {
      // 去支付
      this.$router.push({
        path: "/payment",
        query: { orderType: "ORDER", sn },
      });
    },
    applyAfterSale(sn) {
      // 申请售后
      this.$router.push({ name: "ApplyAfterSale", query: { sn: sn } });
    },
    comment(sn, goodsIndex) {
      // 评价
      this.$router.push({
        path: "/home/<USER>",
        query: { sn, index: goodsIndex },
      });
    },
    complain(sn, goodsIndex) {
      // 投诉
      this.$router.push({ name: "Complain", query: { sn, index: goodsIndex } });
    },
    handleCancelOrder(sn) {
      // 取消订单
      this.cancelParams.orderSn = sn;
      afterSaleReason("CANCEL").then((res) => {
        if (res.success) {
          this.cancelReason = res.result;
          this.cancelAvail = true;
          this.cancelParams.reason = this.cancelReason[0].reason;
        }
      });
    },
    sureCancel() {
      // 确定取消
      cancelOrder(this.cancelParams).then((res) => {
        if (res.success) {
          this.$Message.success("取消订单成功");
          this.getDetail();
          this.cancelAvail = false;
        }
      });
    },
    // 主结算单
    getQueryListByParams() {
      let params = {
        pageNumber: 1,
        pageSize: 1000,
        orderSn: this.$route.query.sn,
      };
      queryListByParams(params).then((res) => {
        if (res.success) {
          this.mainData = res.result;
        }
      });
    },
  },
  mounted() {
    this.accessToken.accessToken = storage.getItem("accessToken");
    this.getDetail();
  },
};
</script>
<style lang="scss" scoped>
.mb_10 {
  Button:nth-of-type(2) {
    margin-left: 10px;
  }
}

.mb_5 {
  margin-bottom: 5px;
}
.order-card {
  padding: 10px;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-bottom: 1px solid #e8eaec;
  position: relative;
  .global_color {
    color: $theme_color;
  }
  p {
    color: #999;
    margin: 3px;
    margin-left: 5px;
  }
  h3 {
    font-weight: normal;
    font-size: 16px;
  }
  .operation-time {
    position: absolute;
    right: 20px;
    top: 20px;
  }
}
/** 店铺名称 */
.shop-name {
  margin: 15px 0;
  span {
    color: #438cde;
    cursor: pointer;
    &:hover {
      color: $theme_color;
    }
  }
  .ivu-icon {
    color: #ff8f23;
    cursor: pointer;
    &:hover {
      color: $theme_color;
    }
  }
}
/** 商品列表 */
table {
  border: 1px solid #ddd;
  color: #999;
  border-collapse: collapse;
  width: 100%;
  tr {
    border-top: 1px solid #ddd;
  }
  thead > tr {
    height: 40px;
    background: #eee;
  }
  td {
    padding: 5px;
    text-align: center;
    &:first-child {
      text-align: left;
      display: flex;
      img {
        width: 70px;
        height: 70px;
        margin-right: 10px;
        margin-left: 10px;
        cursor: pointer;
      }
    }
    &:last-child {
      color: $theme_color;
    }
  }
}
/** 订单价格 */
.order-price {
  text-align: right;
  margin-top: 30px;
  font-size: 16px;
  color: #999;
  > div > span:nth-child(2) {
    width: 130px;
    text-align: right;
    display: inline-block;
    margin-top: 10px;
  }
  .actrual-price {
    color: $theme_color;
    font-weight: bold;
    font-size: 20px;
  }
}
.verificationCode {
  font-size: 20px;
  margin-bottom: 20px;
  color: rgb(65, 63, 63);
  font-weight: bold;
  text-align: center;
  span {
    color: $theme_color;
  }
}
/** 订单进度条 */
.progress {
  margin: 15px 0;
}

.layui-layer-wrap {
  dl {
    border-top: solid 1px #f5f5f5;
    margin-top: -1px;
    overflow: hidden;

    dt {
      font-size: 14px;
      line-height: 28px;
      display: inline-block;
      padding: 8px 1% 8px 0;
      color: #999;
    }

    dd {
      font-size: 14px;
      line-height: 28px;
      display: inline-block;
      padding: 8px 0 8px 8px;
      border-left: solid 1px #f5f5f5;

      .text-box {
        line-height: 40px;
        color: #333;
        word-break: break-all;
      }
    }
  }
}

.layui-layer-wrap > .div-express-log {
  max-height: 300px;
}
/deep/ .layui-layer-wrap > .div-express-log::-webkit-scrollbar {
  width: 1px;
  height: 5px;
}
/deep/ .layui-layer-wrap > .div-express-log::-webkit-scrollbar-thumb {
  border-radius: 1em;
  background-color: rgba(50, 50, 50, 0.3);
}
/deep/ .layui-layer-wrap > .div-express-log::-webkit-scrollbar-track {
  border-radius: 1em;
  background-color: rgba(50, 50, 50, 0.1);
}

.div-express-log {
  border: solid 1px #e7e7e7;
  background: #fafafa;
  overflow-y: auto;
  overflow-x: auto;
}

.express-log {
  /*margin: 5px -10px 5px 5px;*/
  padding: 10px;
  list-style-type: none;

  .time {
    width: 30%;
    display: inline-block;
    float: left;
  }

  .detail {
    width: 60%;
    margin-left: 30px;
    display: inline-block;
  }

  li {
    line-height: 30px;
  }
}

.express-log-name {
  li {
    display: flex;
    span {
      display: flex;
    }
  }
}

.layui-layer-wrap {
  dl {
    border-top: solid 1px #f5f5f5;
    margin-top: -1px;
    overflow: hidden;

    dt {
      font-size: 14px;
      line-height: 28px;
      display: inline-block;
      padding: 8px 1% 8px 0;
      color: #999;
    }

    dd {
      font-size: 14px;
      line-height: 28px;
      display: inline-block;
      padding: 8px 0 8px 8px;
      border-left: solid 1px #f5f5f5;

      .text-box {
        line-height: 40px;
        color: #333;
        word-break: break-all;
      }
    }
  }
}
// 表格头样式
::v-deep .is-leaf.el-table__cell {
  background-color: #f8f8f9;
  color: #333;
  font-size: 12px;
  padding: 8px 0;
}
</style>
