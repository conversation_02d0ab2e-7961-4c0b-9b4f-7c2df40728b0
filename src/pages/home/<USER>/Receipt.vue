<template>
  <div class="search">
    <!-- <card _Title="我的发票" :_Size="16" /> -->

    <!-- 搜索 筛选 -->
    <div class="mb_24">
      <div class="searchList">
        <Input
          class="mr_20"
          enter-button
          v-model="searchForm.orderSn"
          @on-search="getDataList"
          placeholder="请输入订单编号搜索"
        />
        <Input
          class="mr_20"
          enter-button
          v-model="searchForm.storeName"
          @on-search="getDataList"
          placeholder="请输入店铺名称搜索"
        />
        <Button @click="handleSearch" type="primary" class="search-btn mr_20"
          >搜索</Button
        >
        <Button @click="handleReset" class="search-btn">重置</Button>
      </div>
    </div>
    <Table
      :loading="loading"
      border
      :columns="columns"
      :data="data"
      ref="table"
    >
      <template slot-scope="{ row }" slot="pdfUrlSlot">
        <div class="preview-btn" v-if="row.receiptStatus === 1">
          <span
            class="mr_10 hover-pointer-color"
            @click="previewBtn(row, 'preview')"
            >查看</span
          >
          <!-- <span class="hover-pointer-color" @click="previewBtn(row, 'download')"
            >下载</span
          > -->
        </div>
        <span v-else>--</span>
      </template>
    </Table>
    <Row type="flex" justify="end" class="mt_10">
      <Page
        :current="searchForm.pageNumber"
        :total="total"
        :page-size="searchForm.pageSize"
        @on-change="changePage"
        @on-page-size-change="changePageSize"
        :page-size-opts="[10, 20, 50]"
        size="small"
        show-total
        show-elevator
        show-sizer
      ></Page>
    </Row>

    <FilePreview :url="fileUrl" />
    <ImagePreview :url="imageUrl" />

    <!-- 多文件回显 -->
    <MultifilePlayback
      :fileListData="MultiFileListData"
      @closePreview="closePreview"
      :title="MultifilePlaybackTitle"
    />
  </div>
</template>

<script>
import FilePreview from "@/components/FilePreview.vue";
import ImagePreview from "@/components/ImagePreview.vue";
import MultifilePlayback from "@/components/MultifilePlayback.vue";
import { receiptApi } from "@/api/receipt";

export default {
  name: "SettlementDoc",
  components: {
    FilePreview,
    ImagePreview,
    MultifilePlayback,
  },
  data() {
    return {
      loading: true, // 表单加载状态
      searchForm: {
        // 搜索框初始化对象
        pageNumber: 1, // 当前页数
        pageSize: 10, // 页面大小
        // sort: "", // 默认排序字段
        // order: "", // 默认排序方式
        // startDate: "", // 起始时间
        // endDate: "", // 终止时间
        // orderSn: "",
        // buyerName: "",
        // orderStatus: "",
        // orderType: "NORMAL",
      },
      selectDate: null,
      columns: [
        {
          title: "订单编号",
          key: "orderSn",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "子订单编号",
          key: "orderItemSn",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "店铺名称",
          key: "storeName",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "发票抬头",
          key: "receiptTitle",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "纳税人识别号",
          key: "taxpayerId",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "发票内容",
          key: "receiptContent",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "发票金额",
          key: "receiptPrice",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "状态",
          key: "receiptStatus",
          minWidth: 200,
          render: (h, params) => {
            if (params.row.receiptStatus == 1) {
              return h("div", [
                h("tag", { props: { color: "green" } }, "已开发票"),
              ]);
            } else {
              return h("div", [
                h("tag", { props: { color: "success" } }, "开票中"),
              ]);
            }
          },
        },
        {
          title: "操作",
          key: "pdfUrl",
          minWidth: 120,
          slot: "pdfUrlSlot",
          fixed: "right",
          align: "center",
        },
      ],
      data: [], // 表单数据
      total: 0, // 表单数据总数

      fileUrl: "",
      imageUrl: "",
      MultifilePlaybackTitle: "发票凭证", // 多文件回显标题
      MultiFileListData: [], // 多文件回显数据
    };
  },
  methods: {
    // 关闭预览,清空 MultiFileListData
    closePreview(data) {
      if (!data) {
        this.MultiFileListData = [];
      }
    },
    // 预览文件
    previewBtn(v, type) {
      let urls = v.fileUrl;
      if (!urls) return this.$message.error("文件不存在");

      // 文件后缀名
      if (type == "preview") {
        const fileUrls = urls.split(",").filter((url) => url.trim() != "");
        this.MultiFileListData = fileUrls.map((url, index) => {
          const suffix = this.$options.filters.formatFileSuffix(url);

          let fileName = "";
          if (fileUrls.length > 1) {
            fileName = `${this.MultifilePlaybackTitle}${index + 1}${suffix}`;
          } else {
            fileName = `${this.MultifilePlaybackTitle}${suffix}`;
          }

          return {
            name: fileName, // 文件名
            url, // 文件 URL
            uid: -(index + 1), // 唯一标识，负数表示是回显的文件，每个文件有不同的 uid
            status: "done", // 文件状态为已完成
            response: { result: url }, // 模拟上传成功后的响应数据
          };
        });
      }
    },
    // 初始化数据
    init() {
      this.getDataList();
    },
    // 清理搜索表单中的空值
    cleanSearchForm() {
      const fields = ["orderSn", "storeName"];
      fields.forEach((field) => {
        if (!this.searchForm[field]) {
          delete this.searchForm[field];
        }
      });
    },
    // 改变页码
    changePage(v) {
      this.searchForm.pageNumber = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 改变页数
    changePageSize(v) {
      this.searchForm.pageSize = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 搜索订单
    handleSearch() {
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 重置
    handleReset() {
      this.searchForm = {
        pageNumber: 1,
        pageSize: 10,
        orderSn: "",
        storeName: "",
      };
      // 重新加载数据
      this.cleanSearchForm();
      this.getDataList();
    },
    // 获取表格数据
    getDataList() {
      this.loading = true;
      receiptApi(this.searchForm).then((res) => {
        this.loading = false;
        if (res.success) {
          this.data = res.result.records;
          this.total = res.result.total;
        }
      });
    },
  },
  mounted() {
    this.init();
  },
  // 页面缓存处理，从该页面离开时，修改KeepAlive为false，保证进入该页面是刷新
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false;
    next();
  },
};
</script>
<style lang="scss" scoped>
.searchList {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
