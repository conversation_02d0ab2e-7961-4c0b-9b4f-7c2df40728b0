<template>
  <div class="sign-up">
    <div style="height: 50px"></div>
    <div class="logo-box">
      <img width="150" :src="$store.state.logoImg" @click="$router.push('/')" />
      <div>&nbsp;&nbsp;</div>
      <div>注册</div>

      <div class="login-btn" v-if="currentStep == 0">
        已有账号？<a @click="$router.push('login')">立即登录</a>
      </div>
    </div>
    <div class="login-container">
      <div class="step">
        <Steps :current="currentStep">
          <Step title="个人注册"></Step>
          <Step title="开通企业"></Step>
          <Step title="企业实名"></Step>
          <Step title="企业实名认证"></Step>
          <Step title="完成"></Step>
        </Steps>
      </div>
      <!-- 个人注册 -->
      <div class="form-container" v-if="currentStep == 0">
        <Form
          ref="formRegist"
          :model="formRegist"
          :rules="ruleInline"
          label-position="top"
        >
          <FormItem prop="username">
            <i-input
              type="text"
              v-model="formRegist.username"
              clearable
              placeholder="用户名"
            >
              <Icon type="md-person" slot="prepend"></Icon>
            </i-input>
          </FormItem>
          <FormItem prop="password">
            <i-input
              type="password"
              v-model="formRegist.password"
              clearable
              placeholder="请输入大于6位密码"
            >
              <Icon type="md-lock" slot="prepend"> </Icon>
            </i-input>
          </FormItem>
          <FormItem prop="mobilePhone">
            <i-input
              type="text"
              v-model="formRegist.mobilePhone"
              clearable
              placeholder="手机号"
            >
              <Icon type="md-phone-portrait" slot="prepend"></Icon>
            </i-input>
          </FormItem>
          <FormItem prop="code">
            <i-input
              type="text"
              v-model="formRegist.code"
              clearable
              placeholder="手机验证码"
            >
              <Icon
                type="ios-text-outline"
                style="font-weight: bold"
                slot="prepend"
              />
              <Button slot="append" @click="sendCode">{{ codeMsg }}</Button>
            </i-input>
          </FormItem>
          <FormItem>
            <Button type="error" size="large" @click="handleRegist" long
              >注册</Button
            >
          </FormItem>
          <FormItem
            ><span class="color999 ml_20"
              >点击注册，表示您同意《<router-link
                to="/article?id=1371992704333905920"
                target="_blank"
                >商城用户协议</router-link
              >》</span
            ></FormItem
          >
        </Form>
      </div>
      <!-- 企业认证 -->
      <div class="form-container" v-if="currentStep == 1">
        <el-form
          :model="formData"
          :rules="formRules"
          label-position="top"
          label-width="auto"
          ref="formDataRef"
        >
          <el-form-item label="营业执照" prop="businessLicence">
            <el-upload
              v-loading="isLoadingLicence"
              class="avatar-uploader"
              :action="action"
              :headers="accessToken"
              :limit="1"
              :on-success="
                (response, file, fileList) =>
                  handleSuccess(response, file, fileList, 'businessLicence')
              "
              :show-file-list="false"
              accept="image/*"
              :before-upload="(file) => beforeUpload(file, 'businessLicence')"
              :file-list="fileListLicence"
              :disabled="isDisabledLicence"
              :on-error="
                (err, file, fileList) =>
                  handleError(err, file, fileList, 'businessLicence')
              "
            >
              <div class="icon" v-if="imageUrlLicense">
                <img :src="imageUrlLicense" class="avatar" />
                <Icon
                  @click.stop="handleRemove('businessLicence')"
                  class="del-btn"
                  type="md-close"
                  size="28"
                  color="red"
                />
              </div>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="企业名称" prop="corpName">
            <el-input
              v-model="formData.corpName"
              placeholder="请输入企业名称"
            ></el-input>
          </el-form-item>
          <el-form-item label="统一信用代码" prop="businessLicenceNumber">
            <el-input
              v-model="formData.businessLicenceNumber"
              placeholder="请输入统一信用代码"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="法人姓名" prop="corporationName">
            <el-input
              v-model="formData.corporationName"
              placeholder="请输入法人姓名"
            ></el-input>
          </el-form-item> -->
          <el-form-item label="法人身份证人像面" prop="personFront">
            <el-upload
              v-loading="isLoadingFront"
              class="avatar-uploader"
              :action="action"
              :headers="accessToken"
              :limit="1"
              :on-success="
                (response, file, fileList) =>
                  handleSuccess(response, file, fileList, 'personFront')
              "
              :show-file-list="false"
              accept="image/*"
              :before-upload="(file) => beforeUpload(file, 'personFront')"
              :file-list="fileListFront"
              :disabled="isDisabledFront"
              :on-error="
                (err, file, fileList) =>
                  handleError(err, file, fileList, 'personFront')
              "
            >
              <div class="icon" v-if="imageUrlFront">
                <img :src="imageUrlFront" class="avatar" />
                <Icon
                  @click.stop="handleRemove('personFront')"
                  class="del-btn"
                  type="md-close"
                  size="28"
                  color="red"
                />
              </div>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="法人身份证国徽面" prop="personBack">
            <el-upload
              v-loading="isLoadingBack"
              class="avatar-uploader"
              :action="action"
              :headers="accessToken"
              :limit="1"
              :on-success="
                (response, file, fileList) =>
                  handleSuccess(response, file, fileList, 'personBack')
              "
              :show-file-list="false"
              accept="image/*"
              :before-upload="(file) => beforeUpload(file, 'personBack')"
              :file-list="fileListBack"
              :disabled="isDisabledBack"
              :on-error="
                (err, file, fileList) =>
                  handleError(err, file, fileList, 'personBack')
              "
            >
              <div class="icon" v-if="imageUrlBack">
                <img :src="imageUrlBack" class="avatar" />
                <Icon
                  @click.stop="handleRemove('personBack')"
                  class="del-btn"
                  type="md-close"
                  size="28"
                  color="red"
                />
              </div>
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
            </el-upload>
          </el-form-item>
          <el-form-item label="法人姓名" prop="name">
            <el-input
              v-model="formData.name"
              placeholder="请输入法人姓名"
            ></el-input>
          </el-form-item>
          <el-form-item label="法人身份证号" prop="number">
            <el-input
              v-model="formData.number"
              placeholder="请输入法人身份证号"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <Button
              :loading="loading"
              type="error"
              size="large"
              @click="handleConfirm2"
              long
              >确认</Button
            >
          </el-form-item>
        </el-form>
      </div>
      <!-- 企业回款账户信息 -->
      <div class="form-container" v-if="currentStep == 2">
        <el-form
          :model="collectionFormData"
          :rules="collectionFormRules"
          ref="collectionFormRef"
          label-width="auto"
          label-position="top"
          class="form2"
        >
          <!-- <el-form-item label="银行开户名" label-position="left"> -->
          <div class="fontsize_14 mb_10">
            <span>银行开户名：</span>
            <span>{{ collectionFormData.bankCardName }}</span>
          </div>
          <!-- </el-form-item> -->
          <el-form-item label="开户银行" prop="bankName">
            <el-input
              v-model="collectionFormData.bankName"
              placeholder="请输入开户银行"
              @change="handleInputChange"
            ></el-input>
          </el-form-item>
          <el-form-item label="开户所在地" prop="bankAreaCode">
            <el-cascader
              v-model="collectionFormData.bankAreaCode"
              :options="cascaderOptions"
              @expand-change="handleCascaderChange"
              @change="handleCascaderChange2"
              placeholder="请选择开户所在地"
              :props="cascaderProps"
              @blur="handleBlur"
            ></el-cascader>
          </el-form-item>
          <el-form-item label="选择支行" prop="braBankName">
            <!-- <el-input
              v-model="collectionFormData.braBankName"
              placeholder="请选择支行"
            ></el-input> -->
            <el-select
              v-model="collectionFormData.braBankName"
              placeholder="请选择支行"
            >
              <el-option
                v-for="item in braBankOptions"
                :key="item.id"
                :label="item.name"
                :value="item.name"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="银行账号" prop="bankCard">
            <el-input
              v-model="collectionFormData.bankCard"
              placeholder="请输入银行账号"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <Button
              :loading="loading"
              type="error"
              size="large"
              @click="handleConfirm3"
              long
              >确认</Button
            >
          </el-form-item>
        </el-form>
      </div>
      <!-- 企业回款 -->
      <div class="form-container" v-if="currentStep == 3">
        <el-form
          :model="accountFormData"
          label-width="auto"
          label-position="left"
          ref="accountFormRef"
          :rules="accountFormRules"
        >
          <el-form-item label="回款金额" prop="transactionAmount">
            <el-input
              v-model="accountFormData.transactionAmount"
              placeholder="请输入回款金额"
            ></el-input>
          </el-form-item>
          <div class="button-group">
            <Button
              :loading="loading"
              size="large"
              long
              @click="() => currentStep--"
              >上一步</Button
            >
            <div style="width: 40px;"></div>
            <Button
              :loading="loading"
              type="error"
              size="large"
              @click="handleConfirm4"
              long
              >确认提交</Button
            >
          </div>
        </el-form>
      </div>
      <!-- 完成 -->
      <div class="form-container" v-if="currentStep == 4">
        <el-result icon="success">
          <template slot="title">
            <div class="result-content">恭喜您！注册成功</div>
          </template>
          <template slot="subTitle">
            <div class="result-subTitle result-content">
              用户您好，已成功为您发放CA证书，该证书存储在上上签平台
            </div>
          </template>
          <template slot="extra">
            <div class="result-extra">请点击下方按钮前往首页</div>
            <el-button
              type="primary"
              size="medium"
              round
              @click="() => $router.push('/')"
              >去首页</el-button
            >
          </template>
        </el-result>
      </div>

      <!-- 拼图验证码 -->
      <!-- <Verify
        ref="verify"
        class="verify-con"
        :verifyType="verifyType"
        @change="verifyChange"
      ></Verify> -->
    </div>
    <div class="foot">
      <Row type="flex" justify="space-around" class="help">
        <router-link to="/article" class="item" target="_blank"
          >帮助</router-link
        >
        <router-link
          to="/article?id=1371779927900160000"
          class="item"
          target="_blank"
          >隐私</router-link
        >
        <router-link
          to="/article?id=1371992704333905920"
          class="item"
          target="_blank"
          >条款</router-link
        >
      </Row>
      <Row type="flex" justify="center" class="copyright">
        Copyright © {{ year }} - Present
        <a
          href="https://www.jingruiit.com/"
          target="_blank"
          style="margin: 0 5px"
          >{{ config.title }}</a
        >
        版权所有
      </Row>
    </div>
  </div>
</template>

<script>
import * as RegExp from "@/plugins/RegExp.js";
import { md5 } from "@/plugins/md5.js";
import * as apiLogin from "@/api/login.js";
import {
  sendSms,
  getRegion as getRegionApi,
  searchBank,
} from "@/api/common.js";
import Verify from "@/components/verify";
import storage from "@/plugins/storage.js";
import { commonUrl } from "@/plugins/request.js";
import {
  recognizeBusinessLicense,
  revognizeIdentity,
  openCompany,
  queryEntAuth,
  commitEntAuthInfoByType,
  verifyEntAuthInfoByType,
} from "@/api/signUp.js";

export default {
  name: "SignUp",
  components: { Verify },
  data() {
    const savedStep = localStorage.getItem("currentStep");
    return {
      action: commonUrl + "/common/oss/endpoint/put-file-attach", // 上传地址
      accessToken: {}, // 验证token
      config: require("@/config"),
      year: new Date().getFullYear(),
      formRegist: {
        // 注册表单
        mobilePhone: "",
        code: "",
        username: "",
        password: "",
      },
      ruleInline: {
        // 验证规则
        username: [{ required: true, message: "请输入用户名" }],
        password: [
          { required: true, message: "请输入密码" },
          { type: "string", min: 6, message: "密码不能少于6位" },
        ],
        mobilePhone: [
          { required: true, message: "请输入手机号码" },
          {
            pattern: RegExp.mobile,
            trigger: "blur",
            message: "请输入正确的手机号",
          },
        ],
        code: [{ required: true, message: "请输入手机验证码" }],
      },
      verifyStatus: false, // 是否验证通过
      verifyType: "REGISTER", // 验证状态
      codeMsg: "发送验证码", // 提示文字
      interval: "", // 定时器
      time: 60, // 倒计时
      // currentStep: savedStep !== null ? parseInt(savedStep) : 0,
      currentStep: 0,

      // 企业认证
      formData: {
        businessLicence: "", // 营业执照
        corpName: "",
        businessLicenceNumber: "",
        corporationName: "",
        personFront: "",
        name: "",
        number: "",
        personBack: "",

        // 法人信息
        sex: "", // 性别
        nation: "", // 民族
        birth: "", // 出生日期
        address: "", // 户籍
        issue: "", // 签发机关
        validFrom: "", // 有效期
        validTo: "", // 失效日期
      },
      formRules: {
        businessLicence: [
          { required: true, message: "请上传营业执照", trigger: "change" },
        ],
        corpName: [
          { required: true, message: "请输入企业名称", trigger: "change" },
        ],
        businessLicenceNumber: [
          { required: true, message: "请输入统一信用代码", trigger: "change" },
        ],
        corporationName: [
          { required: true, message: "请输入法人姓名", trigger: "change" },
        ],
        personFront: [
          {
            required: true,
            message: "请上传法人身份证人像面",
            trigger: "change",
          },
        ],
        personBack: [
          {
            required: true,
            message: "请上传法人身份证国徽面",
            trigger: "change",
          },
        ],
        name: [
          { required: true, message: "请输入法人姓名", trigger: "change" },
        ],
        number: [
          { required: true, message: "请输入法人身份证号", trigger: "change" },
        ],
      },
      // 营业执照
      attachIdLicence: "",
      fileListLicence: [],
      isLoadingLicence: false,
      imageUrlLicense: "",
      isDisabledLicence: false,

      // 法人身份证人像面
      attachIdFront: "",
      fileListFront: [],
      isLoadingFront: false,
      imageUrlFront: "",
      isDisabledFront: false,

      // 法人身份证国徽面
      attachIdBack: "",
      fileListBack: [],
      isLoadingBack: false,
      imageUrlBack: "",
      isDisabledBack: false,

      loading: false, // 提交按钮loading

      // 企业回款账户
      collectionFormData: {
        bankAreaCode: [], // 地区
        bankCard: "", // 银行卡号
        bankCardName: "", // 开户名
        bankName: "", // 开户银行
        braBankName: "", // 开户行
        id: "",
      },
      braBankOptions: [],
      collectionFormRules: {
        bankName: [
          { required: true, message: "请输入开户银行", trigger: "change" },
        ],
        bankAreaCode: [
          { required: true, message: "请选择开户所在地", trigger: "change" },
        ],
        bankCard: [
          { required: true, message: "请输入银行卡号", trigger: "change" },
        ],
        braBankName: [
          { required: true, message: "请输入支行名称", trigger: "change" },
        ],
      },
      // currentStepChild: localStorage.getItem("currentStepChild") || false, // 企业回款显示
      cascaderData: [],
      cascaderOptions: [],
      cascaderProps: {
        value: "value",
        label: "label",
        children: "children",
      },

      // 企业回款
      accountFormData: {
        transactionAmount: "", // 回款金额
        id: "",
        bankCard: "", // 银行卡号
      },
      accountFormRules: {
        transactionAmount: [
          { required: true, message: "请输入回款金额", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    // 监听 currentStep 的变化
    currentStep(newValue) {
      // 当 currentStep 变化时，将新值存到本地缓存
      localStorage.setItem("currentStep", newValue);
      if (newValue == 2) {
        this.getQueryEntAuth();

        // if (!this.currentStepChild) {
        // }
        // this.getRegion();
      }
      if (newValue == 3) {
        this.getQueryEntAuth();
      }
    },
  },
  methods: {
    handleConfirm2() {
      this.$refs.formDataRef.validate((valid) => {
        if (valid) {
          this.loading = true;
          let params = {
            legalPersonFlag: 1, // 法人标识 1：法人
            corpName: this.formData.corpName, // 企业名称
            businessLicenceNumber: this.formData.businessLicenceNumber, // 统一信用代码
            corporationName: this.formData.name, // 法人姓名
            corporationIdCardNumber: this.formData.number, // 法人身份证号
            corporationFaceAttachId: this.attachIdFront, // 法人身份证人像面
            corporationBackAttachId: this.attachIdBack, // 法人身份证国徽面
            businessLicenceAttachId: this.attachIdLicence, // 营业执照

            // 法人信息
            sex: this.formData.sex == "男" ? "0" : "1", // 性别
            nation: this.formData.nation, // 民族
            birth: this.formData.birth, // 出生日期
            address: this.formData.address, // 户籍
            issue: this.formData.issue, // 签发机关
            validFrom: this.formData.validFrom, // 有效期
            validTo: this.formData.validTo, // 失效日期
          };

          openCompany(params)
            .then((res) => {
              if (res.success) {
                this.loading = false;
                this.collectionFormData.id = res.result.id;
                this.collectionFormData.bankCardName = res.result.corpName;
                localStorage.setItem("collectionFormDataId", res.result.id);
                apiLogin.getMemberMsg().then((resMember) => {
                  if (resMember.success) {
                    storage.setItem("userInfo", resMember.result);
                    this.currentStep = resMember.result.status;

                    // 主动查询更新公司实名信息
                    this.getQueryEntAuth();
                  }
                });
              }
            })
            .catch((err) => {
              this.loading = false;
            });
        }
      });
    },
    handleConfirm3() {
      this.$refs.collectionFormRef.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          let params = {
            ...this.collectionFormData,
            bankAreaCode: this.collectionFormData.bankAreaCode.join(","),
          };
          console.log(params, "collectionFormRef");

          try {
            const res = await commitEntAuthInfoByType(params);

            if (res.success) {
              this.loading = false;
              // this.currentStepChild = true;
              // localStorage.setItem("currentStepChild", this.currentStepChild);
              apiLogin.getMemberMsg().then((resMember) => {
                if (resMember.success) {
                  storage.setItem("userInfo", resMember.result);
                  this.currentStep = resMember.result.status;
                }
              });
            }
          } catch (error) {
            this.loading = false;
          }
        }
      });
    },
    handleConfirm4() {
      this.$refs.accountFormRef.validate(async (valid) => {
        if (valid) {
          this.loading = true;
          let params = {
            ...this.accountFormData,
            id: this.collectionFormData.id,
            bankCard: this.collectionFormData.bankCard,
          };

          try {
            const res = await verifyEntAuthInfoByType(params);

            // console.log(res, "----res--");

            if (res.success) {
              this.loading = false;
              apiLogin.getMemberMsg().then((resMember) => {
                if (resMember.success) {
                  storage.setItem("userInfo", resMember.result);
                  this.currentStep = resMember.result.status;
                }
              });
            }
          } catch (error) {
            this.loading = false;
          }
        }
      });
    },
    async handleCascaderChange(value) {
      await this.getRegion();

      const selectedOption = this.cascaderOptions.find(
        (option) => option.value == value[0]
      );

      if (selectedOption) {
        this.getSecondLevelData(selectedOption.id).then((children) => {
          selectedOption.children = children;
        });
      }
    },
    async handleInputChange(value) {
      this.collectionFormData.braBankName = "";
      this.collectionFormData.bankName = value;
      await this.getSearchBank(value, this.collectionFormData.bankAreaCode);
    },
    handleBlur(value) {
      // this.collectionFormData.bankAreaCode = "";
    },
    async handleCascaderChange2(value) {
      this.collectionFormData.braBankName = "";
      this.collectionFormData.bankAreaCode = value;
      await this.getSearchBank(this.collectionFormData.bankName, value);
    },
    // 选择支行
    async getSearchBank(bank, city) {
      // 检查输入参数是否有效
      if (!bank || !Array.isArray(city) || city.length < 2) {
        console.error("输入参数无效，bank 或 city 不符合要求");
        return;
      }

      // 查找一级地区选项
      const selectedOption = this.cascaderOptions.find(
        (option) => option.value === city[0]
      );

      if (!selectedOption) {
        console.error("未找到对应的一级地区选项");
        return;
      }

      // 确保二级地区数据已加载
      if (!selectedOption.children.length) {
        try {
          const children = await this.getSecondLevelData(selectedOption.id);
          selectedOption.children = children;
        } catch (error) {
          console.error("获取二级地区数据时出错:", error);
          return;
        }
      }

      // 查找二级地区选项
      const selectedOptionChild = selectedOption.children.find(
        (option) => option.value === city[1]
      );

      if (!selectedOptionChild) {
        console.error("未找到对应的二级地区选项");
        return;
      }

      // 构建请求参数
      const params = {
        bank,
        city: selectedOptionChild.label.replace(/(市|城区|郊县|自治州)/g, ""),
      };

      try {
        // 发起异步请求
        const res = await searchBank(params);
        // 可在此处添加处理响应结果的逻辑
        if (res.success) {
          this.braBankOptions = res.result;
          // this.collectionFormData.braBankName = "";
        }
      } catch (error) {
        console.error("查询银行时出错:", error);
      }
    },
    // 获取地区数据 0为顶级
    async getRegion() {
      try {
        const res = await getRegionApi(0);
        // console.log(res.result, "----res--");
        if (!res.success || !Array.isArray(res.result)) {
          console.error("获取第一级地区数据失败");
          return;
        }
        // 将接口返回的第一级数据转换为 el-cascader 需要的格式
        this.cascaderOptions = res.result.map((item) => ({
          value: item.adCode,
          label: item.name,
          // originalData: item,
          children: [], // 子级数据初始为空
          id: item.id,
        }));
      } catch (error) {
        console.error("获取第一级地区数据时出错:", error);
      }
    },
    // 获取第二级地区数据
    async getSecondLevelData(parentId) {
      try {
        const res = await getRegionApi(parentId);
        if (!res.success || !Array.isArray(res.result)) {
          console.error("获取第二级地区数据失败");
          return [];
        }
        // 将接口返回的第二级数据转换为 el-cascader 需要的格式
        return res.result.map((item) => ({
          value: item.adCode,
          label: item.name,
          // originalData: item,
          // children: [],
        }));
      } catch (error) {
        console.error("获取第二级地区数据时出错:", error);
        return [];
      }
    },
    // 主动查询更新公司实名信息
    async getQueryEntAuth() {
      const id =
        this.collectionFormData.id ||
        localStorage.getItem("collectionFormDataId") ||
        "";
      let params = { id };
      const res = params.id ? await queryEntAuth(params) : await queryEntAuth();

      if (res.success && res.result) {
        this.collectionFormData.id = res.result.id;
        this.collectionFormData.bankCardName = res.result.corpName;
        this.collectionFormData.bankName = res.result.bankName;
        this.collectionFormData.braBankName = res.result.braBankName;
        this.collectionFormData.bankAreaCode =
          res.result.bankAreaCode.split(",");

        this.collectionFormData.bankCard = res.result.bankCard;

        if (this.currentStep == 2) {
          await this.handleCascaderChange(this.collectionFormData.bankAreaCode);

          await this.getSearchBank(
            this.collectionFormData.bankName,
            this.collectionFormData.bankAreaCode
          );
        }

        localStorage.setItem("collectionFormDataId", res.result.id);
      }
    },

    handleSuccess(res, file, fileList, type) {
      if (type === "businessLicence") {
        // 上传成功后，获取图片链接
        this.formData.businessLicence = res.result.link;
        this.imageUrlLicense = res.result.link;
        this.isDisabledLicence = true;
        this.isLoadingLicence = false;
        this.fileListLicence = fileList;

        this.attachIdLicence = res.result.attachId;

        // 调用接口识别营业执照
        let params = {
          picUrl: res.result.link, // 图片链接
        };
        recognizeBusinessLicense(params)
          .then((res) => {
            if (res.success) {
              this.formData.corpName = res.result.name; // 企业名称
              this.formData.businessLicenceNumber =
                res.result.registrationNumber; // 统一信用代码
              this.formData.corporationName = res.result.legalRepresentative; // 法人姓名
            }
          })
          .catch((err) => {
            this.handleRemove(type);
          });
      } else {
        if (type === "personFront") {
          // 上传成功后，获取图片链接
          this.formData.personFront = res.result.link;
          this.imageUrlFront = res.result.link;
          this.isDisabledFront = true;
          this.isLoadingFront = false;
          this.fileListFront = fileList;
          this.attachIdFront = res.result.attachId;
          // 调用接口识别身份证
          let params = {
            picUrl: this.imageUrlFront, // 图片链接
          };
          revognizeIdentity(params)
            .then((res) => {
              if (res.success) {
                this.formData.name = res.result.name; // 法人姓名
                this.formData.number = res.result.number; // 法人身份证号
                this.formData.sex = res.result.sex; // 性别
                this.formData.nation = res.result.ethnicity; // 民族
                this.formData.birth = res.result.birth; // 出生日期
                this.formData.address = res.result.address; // 户籍
              }
            })
            .catch((err) => {
              this.handleRemove(type);
            });
        } else {
          // 上传成功后，获取图片链接
          this.formData.personBack = res.result.link;
          this.imageUrlBack = res.result.link;
          this.isDisabledBack = true;
          this.isLoadingBack = false;
          this.fileListBack = fileList;
          this.attachIdBack = res.result.attachId;
          // 调用接口识别身份证
          let params = {
            picUrl: this.imageUrlBack, // 图片链接
          };
          revognizeIdentity(params)
            .then((res) => {
              if (res.success) {
                // this.formData.name = res.result.name; // 法人姓名
                // this.formData.number = res.result.number; // 法人身份证号
                this.formData.issue = res.result.issue;
                this.formData.validFrom = res.result.valid_from;
                this.formData.validTo = res.result.valid_to;
              }
            })
            .catch((err) => {
              this.handleRemove(type);
            });
        }
      }
    },
    handleError(err, file, fileList, type) {
      if (type === "businessLicence") {
        this.imageUrlLicense = "";
        this.isLoadingLicence = false;
        this.isDisabledLicence = false;
        this.fileListLicence = [];
        this.attachIdLicence = "";
        this.formData.corpName = "";
        this.formData.businessLicenceNumber = "";
        this.formData.corporationName = "";
        this.formData.businessLicence = "";
      } else if (type === "personFront") {
        this.imageUrlFront = "";
        this.isDisabledFront = false;
        this.isLoadingFront = false;
        this.fileListFront = [];
        this.attachIdFront = "";
        this.formData.name = "";
        this.formData.number = "";
        this.formData.personFront = "";

        this.formData.sex = ""; // 性别
        this.formData.nation = ""; // 民族
        this.formData.birth = ""; // 出生日期
        this.formData.address = ""; // 户籍
      } else if (type === "personBack") {
        this.imageUrlBack = "";
        this.isLoadingBack = false;
        this.isDisabledBack = false;
        this.fileListBack = [];
        this.attachIdBack = "";
        this.formData.personBack = "";

        this.formData.issue = ""; // 签发机关
        this.formData.validFrom = ""; // 有效期
        this.formData.validTo = ""; // 失效日期
      }
      this.$message.error("上传失败");
    },
    beforeUpload(file, type) {
      if (type === "businessLicence") {
        this.isLoadingLicence = true;
      } else if (type === "personFront") {
        this.isLoadingFront = true;
      } else if (type === "personBack") {
        this.isLoadingBack = true;
      }

      const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];
      const isAllowedType = allowedTypes.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 10;

      if (!isAllowedType) {
        this.$message.error("上传图片只能是 JPG JPEG PNG 格式!");
      }
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 10MB!");
      }
      return isAllowedType && isLt2M;
    },
    handleRemove(type) {
      if (type === "businessLicence") {
        this.imageUrlLicense = "";
        this.fileListLicence = [];
        this.isDisabledLicence = false;
        this.formData.corpName = "";
        this.formData.businessLicenceNumber = "";
        this.formData.corporationName = "";
        this.formData.businessLicence = "";
        this.attachIdLicence = "";
      } else if (type === "personFront") {
        this.imageUrlFront = "";
        this.fileListFront = [];
        this.isDisabledFront = false;
        this.formData.name = "";
        this.formData.number = "";
        this.formData.personFront = "";
        this.attachIdFront = "";

        this.formData.sex = ""; // 性别
        this.formData.nation = ""; // 民族
        this.formData.birth = ""; // 出生日期
        this.formData.address = ""; // 户籍
      } else if (type === "personBack") {
        this.formData.personBack = "";
        this.imageUrlBack = "";
        this.fileListBack = [];
        this.isDisabledBack = false;
        this.attachIdBack = "";

        this.formData.issue = ""; // 签发机关
        this.formData.validFrom = ""; // 有效期
        this.formData.validTo = ""; // 失效日期
      }
    },
    // 注册
    handleRegist() {
      this.$refs.formRegist.validate((valid) => {
        if (valid) {
          let data = JSON.parse(JSON.stringify(this.formRegist));
          data.password = md5(data.password);
          apiLogin.regist(data).then((res) => {
            if (res.success) {
              this.$Message.success("注册成功!");
              // this.$router.push("login");
              storage.setItem("accessToken", res.result.accessToken);
              storage.setItem("refreshToken", res.result.refreshToken);

              this.accessToken.accessToken = res.result.accessToken;

              apiLogin.getMemberMsg().then((resMember) => {
                if (resMember.success) {
                  storage.setItem("userInfo", resMember.result);
                  this.currentStep = resMember.result.status;
                }
              });
            } else {
              this.$Message.warning(res.message);
            }
          });
        } else {
        }
      });
    },
    // 发送短信验证码
    sendCode() {
      if (this.time === 60) {
        if (this.formRegist.mobilePhone === "") {
          this.$Message.warning("请先填写手机号");
          return;
        }
        // if (!this.verifyStatus) {
        //   this.$Message.warning('请先完成安全验证');
        //   return;
        // }
        let params = {
          mobile: this.formRegist.mobilePhone,
          verificationEnums: "REGISTER",
        };
        sendSms(params).then((res) => {
          if (res.success) {
            this.$Message.success("验证码发送成功");
            let that = this;
            this.interval = setInterval(() => {
              that.time--;
              if (that.time === 0) {
                that.time = 60;
                that.codeMsg = "重新发送";
                that.verifyStatus = false;
                clearInterval(that.interval);
              } else {
                that.codeMsg = that.time;
              }
            }, 1000);
          } else {
            this.$Message.warning(res.message);
          }
        });
      }
    },
    // 图片验证码成功回调
    verifyChange(con) {
      if (!con.status) return;
      this.$refs.verify.show = false;
      this.verifyStatus = true;
    },
    // 打开图片验证码
    verifyBtnClick() {
      if (!this.verifyStatus) {
        this.$refs.verify.init();
      }
    },
  },
  computed: {
    userInfo() {
      // 用户信息
      if (storage.getItem("userInfo")) {
        return JSON.parse(storage.getItem("userInfo"));
      } else {
        return {};
      }
    },
  },
  mounted() {
    this.$refs.formRegist && this.$refs.formRegist.resetFields();
    document.querySelector(".sign-up").style.height = window.innerHeight + "px";

    // console.log(this.userInfo, "userinfo");
    if (this.userInfo.status) {
      this.currentStep = this.userInfo.status;
    } else {
      this.currentStep = 0;
    }

    // // 企业回款、打款
    // if (this.currentStep == 2) {
    //   this.getQueryEntAuth();
    // }
    // // 企业回款账户
    // if (this.currentStep == 2 && !this.currentStepChild) {
    //   this.getRegion();
    // }
  },

  destroyed() {
    localStorage.removeItem("currentStep");
    localStorage.removeItem("collectionFormDataId");
    // localStorage.removeItem("currentStepChild");
  },
};
</script>
<style scoped lang="scss">
.sign-up {
  background-color: #ffff;
}
.logo-box {
  width: 800px;
  height: 80px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  position: relative;
  img {
    width: 150px;
    cursor: pointer;
  }
  div {
    font-size: 20px;
    margin-top: 10px;
  }

  .login-btn {
    position: absolute;
    right: 0px;
    bottom: 0px;
    font-size: 14px;
  }
}

.login-container {
  border-top: 2px solid $theme_color;
  position: relative;
  margin: 0 auto;
  width: 900px;
  // background-color: rgba(0, 0, 0, 0.01);
  background-color: #fff;
  padding: 20px 50px;
  overflow-y: auto;
  // height: 600px;
  margin-bottom: 50px;

  box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.1);

  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */

  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }

  .step {
    margin-bottom: 20px;
  }

  .form-container {
    // width: 500px;
    margin: 0 auto;
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .button-group {
      display: flex;
    }
  }
}

.verify-con {
  position: absolute;
  left: 140px;
  top: 80px;
  z-index: 10;
}

.other-login {
  margin: 0 auto;
  .ivu-icon {
    font-size: 24px;
  }
}
.regist {
  display: flex;
  justify-content: flex-end;
  margin-top: -10px;
  span {
    margin-left: 10px;
    &:hover {
      cursor: pointer;
      color: $theme_color;
    }
  }
}
.foot {
  // position: fixed;
  // bottom: 4vh;
  width: 368px;
  margin: 0 auto;
  padding: 20px 10px;
  // left: calc(50% - 184px);
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  .help {
    margin: 0 auto;
    margin-bottom: 1vh;
    width: 60%;
    .item {
      color: rgba(0, 0, 0, 0.45);
    }
    :hover {
      color: rgba(0, 0, 0, 0.65);
    }
  }
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 300px;
  height: 178px;
  display: block;
}

.icon {
  position: relative;
  .del-btn {
    position: absolute;
    top: 0px;
    right: 0px;
    z-index: 10;
  }
}

.region {
  ::v-deep .ivu-input {
    height: 40px;
  }
}

.form2 {
  ::v-deep .el-input {
    width: 300px;
  }
}

.result-content {
  font-size: 26px;
  color: #333;
  font-weight: 700;
}
.result-subTitle {
  font-size: 18px;
}
.result-extra {
  margin-bottom: 10px;
  color: #999;
}

::v-deep .el-result__extra {
  margin-top: 10px;
}
</style>
