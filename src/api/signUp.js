import request, { Method, commonUrl } from "@/plugins/request.js";

/**
 * 营业执照识别
 */
export function recognizeBusinessLicense(params) {
  return request({
    url: `${commonUrl}/common/businessLicense/recognizeBusinessLicense`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
/**
 * 身份证识别
 */
export function revognizeIdentity(params) {
  return request({
    url: `${commonUrl}/common/businessLicense/revognizeIdentity`,
    method: Method.POST,
    needToken: true,
    params,
  });
}
/**
 * 身份证识别
 */
export function openCompany(params) {
  return request({
    url: `${commonUrl}/common/customerInfo/openCompany`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
/**
 * 主动查询更新公司实名信息
 */
export function queryEntAuth(params) {
  return request({
    url: `${commonUrl}/common/customerInfo/queryEntAuth`,
    method: Method.GET,
    needToken: true,
    params,
  });
}
/**
 * 提交实名企业认证资料
 */
export function commitEntAuthInfoByType(params) {
  return request({
    url: `${commonUrl}/common/customerInfo/commitEntAuthInfoByType?type=${3}`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
/**
 * 验真实名认证资料
 */
export function verifyEntAuthInfoByType(params) {
  return request({
    url: `${commonUrl}/common/customerInfo/verifyEntAuthInfoByType?type=${3}`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
