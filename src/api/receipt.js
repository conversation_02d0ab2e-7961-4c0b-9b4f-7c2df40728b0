import request, { Method } from "@/plugins/request.js";

// 询价单
export function receiptApi(params) {
  return request({
    url: `/buyer/trade/receipt`,
    method: Method.GET,
    needToken: true,
    params: params,
  });
}
// 待开票子订单列表
export function pendingInvoicingPage(params) {
  return request({
    url: `/buyer/order/order/pendingInvoicingPage`,
    method: Method.GET,
    needToken: true,
    params: params,
  });
}
// 申请开票
export function applyReceipt(params) {
  return request({
    url: `/buyer/trade/receipt/applyReceipt`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
