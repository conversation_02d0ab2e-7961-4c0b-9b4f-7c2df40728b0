import request, { Method } from "@/plugins/request.js";

// 询价单
export function submit(params) {
  return request({
    url: `/buyer/commodity/priceInquiry/submit`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
}

// 询价列表
export function getPriceInquiryList(params) {
  return request({
    url: `/buyer/commodity/priceInquiry/page`,
    method: Method.POST,
    needToken: true,
    data: params,
    // headers: {
    //   // "Content-Type": "application/json",
    // },
  });
}
// 报价详情
export function priceQuoteDetail(params) {
  return request({
    url: `/buyer/commodity/priceInquiry/priceQuoteDetail`,
    method: Method.GET,
    needToken: true,
    params: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
// 确认报价
export function confirmQuote(params) {
  return request({
    url: `/buyer/commodity/priceInquiry/confirmQuote`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
