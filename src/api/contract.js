import request, { Method } from "@/plugins/request.js";

// 获取合同模板
export function getContractTemplate(params) {
  return request({
    url: `/buyer/contract/templateCustomer/getContractTemplate`,
    method: Method.GET,
    needToken: true,
    params: params,
    // headers: {
    //   "Content-Type": "application/json",
    // },
  });
}

// 获取合同
export function genContract(params) {
  return request({
    url: `/buyer/contract/contractCustomer/genContract`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
// 使用当前用户进行签署
export function sign(params) {
  return request({
    url: `/buyer/contract/contractCustomer/sign`,
    method: Method.POST,
    needToken: true,
    data: params,
    headers: {
      "Content-Type": "application/json",
    },
  });
}
