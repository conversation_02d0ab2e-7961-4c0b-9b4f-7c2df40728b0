import {
  getRequest,
  postRequest,
  putRequest,
  postRequestWithNoForm,
  postRequestX,
} from "@/libs/axios";

// 合同列表分页
export const page = (params) => {
  return postRequest(`/contract/contractlist/getContractDetails`, params);
};
// 获取合同详情
export const getDetails = (params) => {
  return getRequest(`/contract/web-back/contractlist/getDetails`, params);
};
// 获取合同模板分页
export const contractTemplateList = (params) => {
  return postRequest(`/contract/contractTemplate/list`, params);
};
// 新增或修改
export const submit = (params) => {
  return postRequestWithNoForm(`/contract/contractTemplate/submit`, params);
};

// 获取合同模板
export const getContractTemplate = (params) => {
  return getRequest(`/contract/templateCustomer/getContractTemplate`, params);
};
// 获取合同
export const genContract = (params) => {
  return postRequestWithNoForm(`/contract/contractCustomer/genContract`, params);
};
// 使用当前用户进行签署
export const sign = (params) => {
  return postRequestWithNoForm(`/contract/contractCustomer/sign`, params);
};

// ==================== 店铺端合同模板相关接口 ====================

// 根据商品ID获取可用的合同模板列表
export const getAvailableTemplatesForGoods = (goodsId) => {
  return getRequest(`/contract/template/available-by-goods/${goodsId}`);
};

// 根据分类路径获取可用的合同模板列表
export const getAvailableTemplatesByCategoryPath = (categoryPath) => {
  return getRequest(`/contract/template/available-by-category?categoryPath=${categoryPath}`);
};

// 查询商品绑定的合同模板ID
export const getGoodsBoundTemplateId = (goodsId) => {
  return getRequest(`/contract/template/bound-template/${goodsId}`);
};
