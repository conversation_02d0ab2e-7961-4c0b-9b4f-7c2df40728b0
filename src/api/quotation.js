import {
  getRequest,
  postRequest,
  putRequest,
  postRequestWithNoForm,
} from "@/libs/axios";

// 待报价分页

export const page = (params) => {
  return postRequest(`/commodity/priceInquiry/page`, params);
};
// 报价商品下拉框
export const pulldownList = (goodsName = "") => {
  return getRequest(`/goods/goods/pulldownList?name=${goodsName}`);
};
// 卖家已报价分页
export const quotePage = (params) => {
  return postRequest(`/commodity/priceInquiry/quotePage`, params);
};
// 卖家针对询价记录报价
export const quota = (params) => {
  return postRequestWithNoForm(`/commodity/priceQuote/quota`, params);
};
