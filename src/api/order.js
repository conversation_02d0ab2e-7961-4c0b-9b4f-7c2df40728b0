import request, {
  Method
} from '@/plugins/request.js';

// 查询会员订单列表
export function getOrderList (params) {
  return request({
    url: `/buyer/order/order`,
    method: Method.GET,
    needToken: true,
    params
  });
}

/**
 * 订单明细
 * @param {orderSn} 订单编号
 */
export function orderDetail (orderSn) {
  return request({
    url: `/buyer/order/order/${orderSn}`,
    method: Method.GET,
    needToken: true
  });
}

/**
 * 取消订单
 * @param {orderSn} 订单编号
 * @param reason 取消订单原因
 */
export function cancelOrder (params) {
  return request({
    url: `/buyer/order/order/${params.orderSn}/cancel`,
    method: Method.POST,
    needToken: true,
    params
  });
}

/**
 * 删除订单
 * @param {orderSn} 订单编号
 */
export function delOrder (orderSn) {
  return request({
    url: `/buyer/order/order/${orderSn}`,
    method: Method.DELETE,
    needToken: true
  });
}

/**
 * 确认收货
 * @param {orderSn} 订单编号
 */
export function sureReceived (orderSn) {
  return request({
    url: `/buyer/order/order/${orderSn}/receiving`,
    method: Method.POST,
    needToken: true
  });
}
/**
 * 买家调整确认收货数量
 */
export function changeOrderItem (params) {
  return request({
    url: `/buyer/order/order/changeOrderItem`,
    method: Method.POST,
    needToken: true,
    params
  });
}

/**
 * 查询物流
 * @param {orderSn} 订单编号
 */
export function getTraces (orderSn) {
  return request({
    url: `/buyer/order/order/getTraces/${orderSn}`,
    method: Method.POST,
    needToken: true
  });
}

/**
 * 评价列表
 *
 */
export function evolutionList (params) {
  return request({
    url: `/buyer/member/evaluation`,
    method: Method.GET,
    needToken: true,
    params
  });
}

// 添加交易投诉对话
export function communication (params) {
  return request({
    url: `/buyer/order/complain/communication`,
    method: Method.POST,
    needToken: true,
    params
  });
}

// 退换货服务 提交物流
export function afterSaleDelivery (params) {
  return request({
    url: `/buyer/order/afterSale/delivery/${params.afterSaleSn}`,
    method: Method.POST,
    needToken: true,
    params
  });
}
// 获取退货可选物流公司
export function getLogisticsCompany () {
  return request({
    url: `/buyer/other/logistics`,
    method: Method.GET,
    needToken: true,
    params: { pageNumber: 1, pageSize: 200, disabled: 'OPEN' }
  });
}

//查询包裹列表
export const getPackage = (sn) => {
  return request({
    url: `/buyer/order/order/getPackage/${sn}`,
    method: Method.GET,
    needToken: true,
  })
}

//查询物流
export const getTracesList = (sn, params) => {
  return request({
    url: `/buyer/order/order/getTracesList/${sn}`,
    method: Method.GET,
    needToken: true,
  })
};
//上传支付凭证
export const uploadPaymentVoucher = (params) => {
  return request({
    url: `/buyer/order/order/uploadPaymentVoucher`,
    method: Method.POST,
    needToken: true,
    params,
  })
};
//上传子订单一次支付凭证
export const uploadFirstPaymentVoucherUrl = (params) => {
  return request({
    url: `/buyer/order/order/uploadFirstPaymentVoucherUrl`,
    method: Method.POST,
    needToken: true,
    params,
  })
};
// 支付凭证确认
export const orderItemPayment = (data, orderItemSn) => {
  return request({
    url: `/buyer/order/order/orderItemPayment/${orderItemSn}`,
    method: Method.POST,
    needToken: true,
    data,
    headers: {
      "Content-Type": "application/json",
    },
  })
};
//上传子订单尾款支付凭证
export const uploadBalancePaymentVoucherUrl = (params) => {
  return request({
    url: `/buyer/order/order/uploadBalancePaymentVoucherUrl`,
    method: Method.POST,
    needToken: true,
    params,
  })
};
export const orderItemSettlement = (params) => {
  return request({
    url: `/buyer/order/orderItemSettlement`,
    method: Method.GET,
    needToken: true,
    params,
  })
};
export const changeConfirmStatus = (orderItemSn, changeConfirmStatus) => {
  return request({
    url: `/buyer/order/order/${orderItemSn}/${changeConfirmStatus}`,
    method: Method.POST,
    needToken: true,
  })
};
// 通过id获取店铺信息
export const detail = (id) => {
  return request({
    url: `/buyer/store/store/get/detail/${id}`,
    method: Method.GET,
    needToken: true,
  })
};
// 根据订单sn或子订单sn查询
export const queryListByParams = (params) => {
  return request({
    url: `/buyer/order/orderItemSettlement/queryListByParams`,
    method: Method.GET,
    needToken: true,
    params,
  })
};
// 获取子订单应付金额等
export const payableByItemSn = (orderItemSn) => {
  return request({
    url: `/buyer/order/order/payableByItemSn/${orderItemSn}`,
    method: Method.GET,
    needToken: true,
  })
};
// 根据订单号查询
export const getByOrderSn = (params) => {
  return request({
    url: `/buyer/commodity/priceInquiry/getByOrderSn`,
    method: Method.GET,
    needToken: true,
    params,
  })
};
// 查询产品和额度
export const getGoodsAndQuota = () => {
  return request({
    url: `/common/remoteSystem/getGoodsAndQuota`,
    method: Method.GET,
    needToken: true,
  })
};
// 根据子订单编号查询待确认或已确认的支付凭证记录
export const getPaymentVoucherApi = (itemSn, paymentStatus) => {
  return request({
    url: `/buyer/order-item/pay-records/list/${itemSn}/${paymentStatus}`,
    method: Method.GET,
    needToken: true,
  });
}
