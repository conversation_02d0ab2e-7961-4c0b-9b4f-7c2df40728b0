// 统一请求路径前缀在libs/axios.js中修改
import { getRequest, postRequest, putRequest ,postRequestWithNoForm } from "@/libs/axios";

import { baseUrl } from "@/libs/axios.js";

// 下载待发货的订单列表
export const verificationCode = verificationCode => {
  return getRequest(`/order/order/getOrderByVerificationCode/${verificationCode}`);
};

// 下载待发货的订单列表
export const downLoadDeliverExcel = params => {
  return getRequest(`/order/order/downLoadDeliverExcel`, params, 'blob');
};
// 导出待发货订单
export const queryExportOrder = params => {
  return getRequest(`/order/order/queryExportOrder`, params);
};

//获取电子面单
export const getOrderFaceSheet= (orderSn,params) =>{
  return postRequest(`/order/order/${orderSn}/createElectronicsFaceSheet`,params)
}

// 上传待发货的订单列表
export const uploadDeliverExcel = params => {
  return postRequestWithNoForm(`/order/order/batchDeliver`, params );
};

// 获取普通订单列表
export const getOrderList = params => {
  return getRequest(`/order/order`, params);
};

//  导出订单列表
export const exportOrder = (params) => {
  return getRequest(`/order/order/queryExportOrder`, params,'blob')
};

// 获取普通订单详细信息
export const getOrderDetail = sn => {
  return getRequest(`/order/order/${sn}`);
};

// 调整订单金额
export const modifyOrderPrice = (sn, params) => {
  return putRequest(`/order/order/update/${sn}/price`, params);
};

// 修改订单备注
export const modifyOrderRemark = (sn, params) => {
  return putRequest(`/order/order/${sn}/sellerRemark`, params);
};

// 取消订单
export const cancelOrder = (sn, params) => {
  return postRequest(`/order/order/${sn}/cancel`, params);
};

// 修改收货地址
export const editOrderConsignee = (sn, params) => {
  return postRequest(`/order/order/update/${sn}/consignee`, params);
};
//获取投诉列表
export const getComplainPage = params => {
  return getRequest(`/order/complain`, params);
};

//获取投诉详情
export const getComplainDetail = id => {
  return getRequest(`/order/complain/${id}`);
};

//添加交易投诉对话
export const addOrderComplaint = params => {
  return postRequest(`/order/complain/communication/`, params);
};

//添加交易投诉对话
export const appeal = params => {
  return putRequest(`/order/complain/appeal`, params);
};

//获取订单日志
export const getOrderLog = (sn, params) => {
  return getRequest(`/orderLog/${sn}`, params);
};

// 订单发货
export const orderDelivery = (sn, params) => {
  return postRequest(`/order/order/${sn}/delivery`, params);
};

// 订单发货
export const orderShunFengDelivery = (sn) => {
  return postRequest(`/order/order/${sn}/shunfeng/delivery`);
};

// 获取商家选中的物流公司
export const getLogisticsChecked = () => {
  return getRequest(`/other/logistics/getChecked`);
};

// 订单核验
export const orderTake = (sn, verificationCode) => {
  return putRequest(`/order/order/take/${sn}/${verificationCode}`);
};

// 售后服务单
export const afterSaleOrderPage = params => {
  return getRequest(`/order/afterSale/page`, params);
};

// 售后服务单详情
export const afterSaleOrderDetail = sn => {
  return getRequest(`/order/afterSale/${sn}`);
};

// 商家审核
export const afterSaleSellerReview = (sn, params) => {
  return putRequest(`/order/afterSale/review/${sn}`, params);
};

// 商家确认收货
export const afterSaleSellerConfirm = (sn, params) => {
  return putRequest(`/order/afterSale/confirm/${sn}`, params);
};

// 商家换货业务发货
export const afterSaleSellerDelivery = (sn, params) => {
  return postRequest(`/order/afterSale/${sn}/delivery`, params);
};
//查询物流
export const getTraces = (sn, params) => {
  return getRequest(`/order/order/getTraces/${sn}`, params);
};
//售后单查询物流
export const getSellerDeliveryTraces = (sn, params) => {
  return getRequest(`/order/afterSale/getSellerDeliveryTraces/${sn}`, params);
};
//售后单查询物流
export const getAfterSaleTraces = (sn, params) => {
  return getRequest(`/order/afterSale/getDeliveryTraces/${sn}`, params);
};
//获取发票列表
export const getReceiptPage = params => {
  return getRequest(`/trade/receipt`, params);
};

//获取发票列表
export const invoicing = id => {
  return postRequest(`/trade/receipt/${id}/invoicing`);
};

//查询包裹列表
export const getPackage = (orderSn) => {
  return getRequest(`/order/order/getPackage/${orderSn}`);
}

//分包裹发货
export const partDelivery = (orderSn,params) => {
  return postRequest(`/order/order/${orderSn}/partDelivery`,params,{
    "Content-type": "application/json"
  })
}
// 子订单发货
export const orderItemDelivery = (orderItemSn,params) => {
  return postRequest(`/order/order/${orderItemSn}/orderItemDelivery`,params,{
    "Content-type": "application/json"
  })
}

//查询物流
export const getTracesList = (sn) => {
  return getRequest(`/order/order/getTracesList/${sn}`);
}
//确认支付凭证
export const confirmPaymentVoucher = (params) => {
  return getRequest(`/order/order/confirmPaymentVoucher`, params);
}
//确认子订单一次支付凭证
export const confirmFirstPaymentVoucher = (params) => {
  return getRequest(`/order/order/confirmFirstPaymentVoucher`, params);
}
//确认子订单尾款支付凭证
export const confirmBalancePaymentVoucher = (params) => {
  return getRequest(`/order/order/confirmBalancePaymentVoucher`, params);
}
//卖家 确认/拒绝 买家调整的数量
export const changeConfirmStatus = (orderItemSn, changeConfirmStatus) => {
  return postRequest(`/order/order/${orderItemSn}/${changeConfirmStatus}`);
}
//根据订单sn或子订单sn查询
export const queryListByParams = (params) => {
  return getRequest(`/order/orderItemSettlement/queryListByParams`, params);
}
//查询可发货订单
export const deliveredOrderItem = (params) => {
  return getRequest(`/order/order/deliveredOrderItem`, params);
}
// 发票分页获取
export const receipt = (params) => {
  return getRequest(`/trade/receipt`, params);
}
// 发票分页获取
export const uploadFileUrl = (id, fileUrl) => {
  return postRequest(`/trade/receipt/${id}/uploadFileUrl`, fileUrl);
}
// 根据子订单编号查询待确认或已确认的支付凭证记录
export const getPaymentVoucherApi = (itemSn, paymentStatus) => {
  return getRequest(`order-item/pay-records/list/${itemSn}/${paymentStatus}`);
}
// 确认或拒绝支付记录
export const updatePayRecordsApi = (params) => {
  return getRequest(`/order/order/updatePayRecords`, params);
}
