<template>
  <div class="search">
    <Card>
      <!-- 操作按钮 -->
      <div class="export">
        <Button class="mr_10 orange-btn-1" @click="handleRefresh">
          <Icon type="md-refresh" />
          刷新商品
        </Button>
        <!-- <Button class="mr_10 orange-btn-2" @click="handleBatchConfig">
          批量配置
        </Button> -->

      </div>

      <!-- 左右布局 -->
      <Row :gutter="16">
        <!-- 左侧：商品列表 -->
        <Col span="8">
          <Card title="商品列表" :padding="0" :bordered="false">
            <!-- 商品搜索 -->
            <div class="goods-search" style="padding: 16px; border-bottom: 1px solid #e8eaec;">
              <Form
                ref="goodsSearchForm"
                :model="goodsSearchForm"
                inline
                :label-width="80"
              >
                <Form-item label="商品名称">
                  <Input
                    v-model="goodsSearchForm.goodsName"
                    placeholder="请输入商品名称"
                    style="width: 200px"
                    clearable
                    @on-enter="handleGoodsSearch"
                  />
                </Form-item>
                <Form-item label="商品编号">
                  <Input
                    v-model="goodsSearchForm.sn"
                    placeholder="请输入商品编号"
                    style="width: 200px"
                    clearable
                    @on-enter="handleGoodsSearch"
                  />
                </Form-item>
                <Form-item>
                  <Button @click="handleGoodsSearch" type="primary" size="small">搜索</Button>
                  <Button @click="handleGoodsReset" size="small" style="margin-left: 8px">重置</Button>
                </Form-item>
              </Form>
            </div>

            <!-- 商品列表 -->
            <div class="goods-list-container">
              <div
                v-for="goods in goodsList"
                :key="goods.id"
                class="goods-item"
                :class="{ 'goods-item-selected': selectedGoods && selectedGoods.id === goods.id }"
                @click="handleSelectGoods(goods)"
              >
                <div class="goods-info">
                  <div class="goods-image">
                    <img :src="goods.thumbnail || '/default-goods.png'" :alt="goods.goodsName" />
                  </div>
                  <div class="goods-details">
                    <div class="goods-name" :title="goods.goodsName">{{ goods.goodsName }}</div>
                    <div class="goods-sn">编号：{{ goods.id }}</div>
                    <div class="goods-price">价格：¥{{ goods.price }}</div>
                  </div>
                  <div class="goods-status">
                    <el-tag
                      :type="goods.marketEnable === 'UPPER' ? '' : 'danger'"
                      :class="goods.marketEnable === 'UPPER' ? 'status-upper' : ''"
                      size="mini"
                    >
                      {{ goods.marketEnable === 'UPPER' ? '上架' : '下架' }}
                    </el-tag>
                    <el-tag v-if="isGoodsConfigured(goods.id)" type="success" size="mini" style="margin-top: 4px">
                      已配置
                    </el-tag>
                  </div>
                </div>
              </div>

              <!-- 商品列表分页 -->
              <div class="goods-pagination" style="padding: 16px; text-align: center; border-top: 1px solid #e8eaec;">
                <Page
                  :current="goodsSearchForm.pageNumber"
                  :total="goodsTotal"
                  :page-size="goodsSearchForm.pageSize"
                  @on-change="changeGoodsPage"
                  @on-page-size-change="changeGoodsPageSize"
                  :page-size-opts="[10, 20, 50]"
                  size="small"
                  show-total
                  show-elevator
                  show-sizer
                ></Page>
              </div>
            </div>
          </Card>
        </Col>

        <!-- 右侧：配置表单 -->
        <Col span="16">
          <Card title="合同模板配置" :bordered="false">
            <div v-if="selectedGoods" class="config-container">
              <!-- 商品信息展示 -->
              <div class="goods-info-display">
                <h4>商品信息</h4>
                <Row :gutter="16">
                  <Col span="12">
                    <p><strong>商品名称：</strong>{{ selectedGoods.goodsName }}</p>
                    <p><strong>商品编号：</strong>{{ selectedGoods.sn }}</p>
                  </Col>
                  <Col span="12">
                    <p><strong>商品ID：</strong>{{ selectedGoods.id }}</p>
                    <p><strong>配置状态：</strong>
                      <el-tag v-if="hasAnyConfig()" type="success" size="mini">已配置</el-tag>
                      <Tag v-else color="default">未配置</Tag>
                    </p>
                  </Col>
                </Row>
              </div>

              <!-- 合同类型配置卡片 -->
              <div class="contract-type-configs">
                <Row :gutter="16">
                  <!-- 现货合同配置 -->
                  <Col span="12">
                    <Card class="contract-type-card spot-card">
                      <div slot="title" class="card-title">
                        <Icon type="ios-cube" />
                        现货合同模板
                      </div>
                      <div slot="extra">
                        <el-tag v-if="spotConfig" type="success" size="mini">已配置</el-tag>
                        <el-tag v-else type="info" size="mini">未配置</el-tag>
                      </div>

                      <Form
                        ref="spotConfigForm"
                        :model="spotConfigForm"
                        :rules="configFormRules"
                        :label-width="80"
                      >
                        <Form-item label="合同模板" prop="templateId">
                          <Select v-model="spotConfigForm.templateId" filterable clearable placeholder="请选择现货合同模板">
                            <Option
                              v-for="template in templateOptions"
                              :key="template.templateId"
                              :value="template.templateId"
                            >
                              {{ template.templateName }}
                            </Option>
                          </Select>
                        </Form-item>

                        <Form-item label="优先级" prop="priority">
                          <InputNumber
                            v-model="spotConfigForm.priority"
                            :min="0"
                            :max="999"
                            placeholder="数值越大优先级越高"
                            style="width: 100%"
                          />
                        </Form-item>

                        <Form-item label="状态" prop="status">
                          <Select v-model="spotConfigForm.status">
                            <Option :value="1">启用</Option>
                            <Option :value="0">禁用</Option>
                          </Select>
                        </Form-item>

                        <Form-item label="备注">
                          <Input
                            v-model="spotConfigForm.remark"
                            type="textarea"
                            :rows="2"
                            placeholder="请输入备注信息"
                          />
                        </Form-item>

                        <Form-item>
                          <Button class="save-config-btn" @click="handleSaveSpotConfig" :loading="spotSaveLoading" size="small">
                            保存
                          </Button>
                          <Button @click="handleClearSpotConfig" size="small" style="margin-left: 8px">
                            清空
                          </Button>
                          <Button
                            v-if="spotConfig && spotConfig.id"
                            type="error"
                            @click="handleDeleteSpotConfig"
                            size="small"
                            style="margin-left: 8px"
                          >
                            删除
                          </Button>
                        </Form-item>
                      </Form>
                    </Card>
                  </Col>

                  <!-- 期货合同配置 -->
                  <Col span="12">
                    <Card class="contract-type-card futures-card">
                      <div slot="title" class="card-title">
                        <Icon type="ios-trending-up" />
                        期货合同模板
                      </div>
                      <div slot="extra">
                        <el-tag v-if="futuresConfig" type="success" size="mini">已配置</el-tag>
                        <el-tag v-else type="info" size="mini">未配置</el-tag>
                      </div>

                      <Form
                        ref="futuresConfigForm"
                        :model="futuresConfigForm"
                        :rules="configFormRules"
                        :label-width="80"
                      >
                        <Form-item label="合同模板" prop="templateId">
                          <Select v-model="futuresConfigForm.templateId" filterable clearable placeholder="请选择期货合同模板">
                            <Option
                              v-for="template in templateOptions"
                              :key="template.templateId"
                              :value="template.templateId"
                            >
                              {{ template.templateName }}
                            </Option>
                          </Select>
                        </Form-item>

                        <Form-item label="优先级" prop="priority">
                          <InputNumber
                            v-model="futuresConfigForm.priority"
                            :min="0"
                            :max="999"
                            placeholder="数值越大优先级越高"
                            style="width: 100%"
                          />
                        </Form-item>

                        <Form-item label="状态" prop="status">
                          <Select v-model="futuresConfigForm.status">
                            <Option :value="1">启用</Option>
                            <Option :value="0">禁用</Option>
                          </Select>
                        </Form-item>

                        <Form-item label="备注">
                          <Input
                            v-model="futuresConfigForm.remark"
                            type="textarea"
                            :rows="2"
                            placeholder="请输入备注信息"
                          />
                        </Form-item>

                        <Form-item>
                          <Button class="save-config-btn" @click="handleSaveFuturesConfig" :loading="futuresSaveLoading" size="small">
                            保存
                          </Button>
                          <Button @click="handleClearFuturesConfig" size="small" style="margin-left: 8px">
                            清空
                          </Button>
                          <Button
                            v-if="futuresConfig && futuresConfig.id"
                            type="error"
                            @click="handleDeleteFuturesConfig"
                            size="small"
                            style="margin-left: 8px"
                          >
                            删除
                          </Button>
                        </Form-item>
                      </Form>
                    </Card>
                  </Col>
                </Row>
              </div>
            </div>
            <div v-else class="no-selection">
              <p>请在左侧选择一个商品来配置合同模板</p>
            </div>
          </Card>
        </Col>
      </Row>
    </Card>

    <!-- 批量配置对话框 -->
    <Modal
      v-model="batchDialogVisible"
      title="批量商品配置"
      width="800"
      :mask-closable="false"
      @on-cancel="handleBatchDialogClose"
    >
      <Form
        ref="batchForm"
        :model="batchForm"
        :rules="batchFormRules"
        :label-width="100"
      >
        <Form-item label="合同模板" prop="templateId">
          <Select v-model="batchForm.templateId" filterable>
            <Option
              v-for="template in templateOptions"
              :key="template.templateId"
              :value="template.templateId"
            >
              {{ template.templateName }}
            </Option>
          </Select>
        </Form-item>

        <Form-item label="商品ID列表" prop="goodsIds">
          <Input
            v-model="batchForm.goodsIds"
            type="textarea"
            :rows="5"
            placeholder="请输入商品ID，多个用逗号分隔，例如：goods001,goods002,goods003"
          />
        </Form-item>

        <Form-item label="优先级" prop="priority">
          <InputNumber
            v-model="batchForm.priority"
            :min="0"
            :max="999"
            placeholder="数值越大优先级越高"
          />
        </Form-item>
      </Form>

      <div slot="footer">
        <Button @click="handleBatchDialogClose">取消</Button>
        <Button type="primary" @click="handleBatchSubmit" :loading="batchSubmitLoading">
          确定
        </Button>
      </div>
    </Modal>

    <!-- 测试匹配对话框 - 已删除 -->
    <!-- <Modal
      v-model="testDialogVisible"
      title="测试模板匹配"
      width="600"
      :mask-closable="false"
      @on-cancel="handleTestDialogClose"
    >
      <Form
        ref="testForm"
        :model="testForm"
        :label-width="100"
      >
        <Form-item label="商品ID" prop="goodsId">
          <Input
            v-model="testForm.goodsId"
            placeholder="请输入要测试的商品ID"
          />
        </Form-item>

        <Button @click="handleTestSubmit" type="primary" :loading="testLoading">
          测试匹配
        </Button>
      </Form>

      <div v-if="testResult" class="test-result">
        <h4>匹配结果：</h4>
        <pre>{{ testResult }}</pre>
      </div>

      <div slot="footer">
        <Button @click="handleTestDialogClose">关闭</Button>
      </div>
    </Modal> -->


  </div>
</template>

<style scoped>
/* 商品列表容器 */
.goods-list-container {
  max-height: 600px;
  overflow-y: auto;
}

/* 商品项样式 */
.goods-item {
  padding: 12px;
  border-bottom: 1px solid #e8eaec;
  cursor: pointer;
  transition: all 0.3s ease;
}

.goods-item:hover {
  background-color: #f8f9fa;
}

.goods-item-selected {
  background-color: #e6f7ff;
  border-left: 3px solid #409eff;
}

.goods-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.goods-image {
  width: 60px;
  height: 60px;
  margin-right: 12px;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e8eaec;
}

.goods-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.goods-details {
  flex: 1;
  min-width: 0;
}

.goods-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.goods-sn {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.goods-price {
  font-size: 12px;
  color: #f56c6c;
  font-weight: 500;
  margin-bottom: 4px;
}

.goods-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

/* 配置容器 */
.config-container {
  padding: 16px 0;
}

.goods-info-display {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e8eaec;
}

.goods-info-display h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.goods-info-display p {
  margin: 0 0 8px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}

.goods-info-display p:last-child {
  margin-bottom: 0;
}

/* 合同类型配置卡片 */
.contract-type-configs {
  margin-top: 20px;
}

.contract-type-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.contract-type-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.spot-card {
  border-left: 4px solid #52c41a;
}

.futures-card {
  border-left: 4px solid #1890ff;
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.card-title i {
  margin-right: 8px;
  font-size: 16px;
}

.spot-card .card-title {
  color: #52c41a;
}

.futures-card .card-title {
  color: #1890ff;
}

.no-selection {
  text-align: center;
  padding: 60px 20px;
  color: #999;
  font-size: 14px;
}

/* 搜索表单样式 */
.goods-search .ivu-form-item {
  margin-bottom: 8px;
}

/* 滚动条样式 */
.goods-list-container::-webkit-scrollbar {
  width: 6px;
}

.goods-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.goods-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.goods-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .goods-image {
    width: 50px;
    height: 50px;
  }

  .goods-name {
    font-size: 13px;
  }

  .goods-sn,
  .goods-price {
    font-size: 11px;
  }
}
</style>

<script>
import {
  getGoodsConfigPage,
  saveGoodsConfig,
  deleteGoodsConfig,
  contractTemplateList,
  batchConfigGoods
  // getBestMatchTemplate // 已删除
} from "@/api/contract.js";
import { getGoodsListData } from "@/api/goods.js";

export default {
  name: "goodsTemplateConfig",
  data() {
    return {
      loading: false,

      // 商品列表相关
      goodsList: [],
      goodsTotal: 0,
      goodsLoading: false,
      goodsSearchForm: {
        pageNumber: 1,
        pageSize: 10,
        goodsName: "",
        sn: "",
        marketEnable: ""
      },
      selectedGoods: null,

      // 配置相关
      configuredGoods: new Map(), // 存储已配置的商品信息
      spotConfig: null, // 现货配置
      futuresConfig: null, // 期货配置
      spotSaveLoading: false,
      futuresSaveLoading: false,

      // 现货配置表单
      spotConfigForm: {
        goodsId: "",
        templateId: "",
        contractType: 1, // 现货
        priority: 40,
        status: 1,
        remark: ""
      },

      // 期货配置表单
      futuresConfigForm: {
        goodsId: "",
        templateId: "",
        contractType: 2, // 期货
        priority: 40,
        status: 1,
        remark: ""
      },

      configFormRules: {
        templateId: [
          { required: true, message: "请选择合同模板", trigger: "change" }
        ],
        priority: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === null || value === undefined || value === '') {
                callback(new Error('请输入优先级'));
              } else if (typeof value !== 'number' || isNaN(value)) {
                callback(new Error('优先级必须是数字'));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        status: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === null || value === undefined || value === '') {
                callback(new Error('请选择状态'));
              } else {
                callback();
              }
            },
            trigger: "change"
          }
        ]
      },



      // 批量配置相关
      batchDialogVisible: false,
      batchSubmitLoading: false,
      batchForm: {
        templateId: "",
        goodsIds: "",
        priority: 40 // 商品批量配置默认优先级为40
      },
      batchFormRules: {
        templateId: [
          { required: true, message: "请选择合同模板", trigger: "change" }
        ],
        goodsIds: [
          { required: true, message: "请输入商品ID列表", trigger: "blur" }
        ],
        priority: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === null || value === undefined || value === '') {
                callback(new Error('请输入优先级'));
              } else if (typeof value !== 'number' || isNaN(value)) {
                callback(new Error('优先级必须是数字'));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ]
      },

      // 测试匹配相关 - 已删除
      // testDialogVisible: false,
      // testLoading: false,
      // testForm: {
      //   goodsId: ""
      // },
      // testResult: "",

      // 模板选项
      templateOptions: []
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getGoodsList();
      this.getTemplateOptions();
      this.loadConfiguredGoods();
    },

    // 获取商品列表
    async getGoodsList() {
      this.goodsLoading = true;
      try {
        const res = await getGoodsListData(this.goodsSearchForm);
        if (res.success) {
          this.goodsList = res.result.records || [];
          this.goodsTotal = res.result.total || 0;
        } else {
          this.$Message.error(res.message || "获取商品列表失败");
        }
      } catch (error) {
        this.$Message.error("获取商品列表失败");
        console.error("获取商品列表失败", error);
      } finally {
        this.goodsLoading = false;
      }
    },

    // 加载已配置的商品
    async loadConfiguredGoods() {
      try {
        const res = await getGoodsConfigPage({ pageNumber: 1, pageSize: 1000 });
        if (res.success) {
          this.configuredGoods.clear();
          (res.result.records || []).forEach(config => {
            const key = `${config.goodsId}_${config.contractType}`;
            this.configuredGoods.set(key, config);
          });
        }
      } catch (error) {
        console.error("加载已配置商品失败", error);
      }
    },

    // 获取模板选项
    async getTemplateOptions() {
      try {
        const res = await contractTemplateList({ pageNumber: 1, pageSize: 100 });
        if (res.success) {
          this.templateOptions = res.result.records || [];
        }
      } catch (error) {
        console.error("获取模板列表失败", error);
      }
    },

    // 商品搜索
    handleGoodsSearch() {
      this.goodsSearchForm.pageNumber = 1;
      this.getGoodsList();
    },

    // 商品搜索重置
    handleGoodsReset() {
      // 手动清空所有搜索条件
      this.goodsSearchForm = {
        pageNumber: 1,
        pageSize: 10,
        goodsName: "",
        sn: "",
        marketEnable: ""
      };

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.goodsSearchForm) {
          this.$refs.goodsSearchForm.resetFields();
        }
      });

      // 重新查询
      this.getGoodsList();
    },

    // 商品分页
    changeGoodsPage(page) {
      this.goodsSearchForm.pageNumber = page;
      this.getGoodsList();
    },

    changeGoodsPageSize(pageSize) {
      this.goodsSearchForm.pageSize = pageSize;
      this.goodsSearchForm.pageNumber = 1;
      this.getGoodsList();
    },

    // 选择商品
    handleSelectGoods(goods) {
      console.log('选择商品:', goods.goodsName, 'ID:', goods.id);
      this.selectedGoods = goods;
      this.loadGoodsConfigs(goods.id);
    },

    // 检查商品是否已配置（任一类型）
    isGoodsConfigured(goodsId) {
      return this.configuredGoods.has(`${goodsId}_1`) || this.configuredGoods.has(`${goodsId}_2`);
    },

    // 检查是否有任何配置
    hasAnyConfig() {
      return this.spotConfig || this.futuresConfig;
    },

    // 加载商品配置（现货和期货）
    async loadGoodsConfigs(goodsId) {
      // 加载现货配置
      const spotKey = `${goodsId}_1`;
      const spotConfig = this.configuredGoods.get(spotKey);
      if (spotConfig) {
        this.spotConfig = spotConfig;
        this.spotConfigForm = {
          id: spotConfig.id,
          goodsId: spotConfig.goodsId,
          templateId: spotConfig.templateId,
          contractType: 1,
          priority: Number(spotConfig.priority) || 40,
          status: Number(spotConfig.status),
          remark: spotConfig.remark || ""
        };
      } else {
        this.spotConfig = null;
        this.spotConfigForm = {
          goodsId: goodsId,
          templateId: "",
          contractType: 1,
          priority: 40,
          status: 1,
          remark: ""
        };
      }

      // 加载期货配置
      const futuresKey = `${goodsId}_2`;
      const futuresConfig = this.configuredGoods.get(futuresKey);
      if (futuresConfig) {
        this.futuresConfig = futuresConfig;
        this.futuresConfigForm = {
          id: futuresConfig.id,
          goodsId: futuresConfig.goodsId,
          templateId: futuresConfig.templateId,
          contractType: 2,
          priority: Number(futuresConfig.priority) || 40,
          status: Number(futuresConfig.status),
          remark: futuresConfig.remark || ""
        };
      } else {
        this.futuresConfig = null;
        this.futuresConfigForm = {
          goodsId: goodsId,
          templateId: "",
          contractType: 2,
          priority: 40,
          status: 1,
          remark: ""
        };
      }

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.spotConfigForm) {
          this.$refs.spotConfigForm.clearValidate();
        }
        if (this.$refs.futuresConfigForm) {
          this.$refs.futuresConfigForm.clearValidate();
        }
      });
    },

    // 保存现货配置
    async handleSaveSpotConfig() {
      if (!this.selectedGoods) {
        this.$Message.error("请先选择商品");
        return;
      }

      try {
        await this.$refs.spotConfigForm.validate();
      } catch (error) {
        this.$Message.error("请检查现货配置表单输入");
        return;
      }

      this.spotSaveLoading = true;
      try {
        const formData = {
          ...this.spotConfigForm,
          goodsId: this.selectedGoods.id,
          goodsName: this.selectedGoods.goodsName
        };

        const res = await saveGoodsConfig(formData);
        if (res.success) {
          this.$Message.success("现货配置保存成功");
          // 更新已配置商品列表
          const key = `${this.selectedGoods.id}_1`;
          this.configuredGoods.set(key, {
            ...formData,
            id: res.result.id || this.spotConfig?.id
          });
          // 重新加载配置
          this.loadGoodsConfigs(this.selectedGoods.id);
        } else {
          this.$Message.error(res.message || "现货配置保存失败");
        }
      } catch (error) {
        this.$Message.error("现货配置保存失败");
        console.error("保存现货配置失败", error);
      } finally {
        this.spotSaveLoading = false;
      }
    },

    // 保存期货配置
    async handleSaveFuturesConfig() {
      if (!this.selectedGoods) {
        this.$Message.error("请先选择商品");
        return;
      }

      try {
        await this.$refs.futuresConfigForm.validate();
      } catch (error) {
        this.$Message.error("请检查期货配置表单输入");
        return;
      }

      this.futuresSaveLoading = true;
      try {
        const formData = {
          ...this.futuresConfigForm,
          goodsId: this.selectedGoods.id,
          goodsName: this.selectedGoods.goodsName
        };

        const res = await saveGoodsConfig(formData);
        if (res.success) {
          this.$Message.success("期货配置保存成功");
          // 更新已配置商品列表
          const key = `${this.selectedGoods.id}_2`;
          this.configuredGoods.set(key, {
            ...formData,
            id: res.result.id || this.futuresConfig?.id
          });
          // 重新加载配置
          this.loadGoodsConfigs(this.selectedGoods.id);
        } else {
          this.$Message.error(res.message || "期货配置保存失败");
        }
      } catch (error) {
        this.$Message.error("期货配置保存失败");
        console.error("保存期货配置失败", error);
      } finally {
        this.futuresSaveLoading = false;
      }
    },

    // 清空现货配置
    handleClearSpotConfig() {
      if (this.selectedGoods) {
        this.spotConfigForm = {
          goodsId: this.selectedGoods.id,
          templateId: "",
          contractType: 1,
          priority: 40,
          status: 1,
          remark: ""
        };
        this.spotConfig = null;

        // 清除表单验证状态
        this.$nextTick(() => {
          if (this.$refs.spotConfigForm) {
            this.$refs.spotConfigForm.clearValidate();
          }
        });
      }
    },

    // 清空期货配置
    handleClearFuturesConfig() {
      if (this.selectedGoods) {
        this.futuresConfigForm = {
          goodsId: this.selectedGoods.id,
          templateId: "",
          contractType: 2,
          priority: 40,
          status: 1,
          remark: ""
        };
        this.futuresConfig = null;

        // 清除表单验证状态
        this.$nextTick(() => {
          if (this.$refs.futuresConfigForm) {
            this.$refs.futuresConfigForm.clearValidate();
          }
        });
      }
    },

    // 删除现货配置
    async handleDeleteSpotConfig() {
      if (!this.spotConfig || !this.spotConfig.id) {
        this.$Message.error("没有可删除的现货配置");
        return;
      }

      this.$Modal.confirm({
        title: "确认删除",
        content: `确定要删除商品"${this.selectedGoods.goodsName}"的现货合同模板配置吗？`,
        onOk: async () => {
          try {
            const res = await deleteGoodsConfig(this.spotConfig.id);
            if (res.success) {
              this.$Message.success("现货配置删除成功");
              // 从已配置商品列表中移除
              const key = `${this.selectedGoods.id}_1`;
              this.configuredGoods.delete(key);
              // 清空表单
              this.handleClearSpotConfig();
            } else {
              this.$Message.error(res.message || "现货配置删除失败");
            }
          } catch (error) {
            this.$Message.error("现货配置删除失败");
            console.error("删除现货配置失败", error);
          }
        }
      });
    },

    // 删除期货配置
    async handleDeleteFuturesConfig() {
      if (!this.futuresConfig || !this.futuresConfig.id) {
        this.$Message.error("没有可删除的期货配置");
        return;
      }

      this.$Modal.confirm({
        title: "确认删除",
        content: `确定要删除商品"${this.selectedGoods.goodsName}"的期货合同模板配置吗？`,
        onOk: async () => {
          try {
            const res = await deleteGoodsConfig(this.futuresConfig.id);
            if (res.success) {
              this.$Message.success("期货配置删除成功");
              // 从已配置商品列表中移除
              const key = `${this.selectedGoods.id}_2`;
              this.configuredGoods.delete(key);
              // 清空表单
              this.handleClearFuturesConfig();
            } else {
              this.$Message.error(res.message || "期货配置删除失败");
            }
          } catch (error) {
            this.$Message.error("期货配置删除失败");
            console.error("删除期货配置失败", error);
          }
        }
      });
    },

    // 刷新
    handleRefresh() {
      this.init();
    },



    // 批量配置
    handleBatchConfig() {
      this.batchForm = {
        templateId: "",
        goodsIds: "",
        priority: 40 // 商品批量配置默认优先级为40
      };
      this.batchDialogVisible = true;
    },

    // 测试匹配 - 已删除
    // handleTestMatch() {
    //   this.testForm = {
    //     goodsId: ""
    //   };
    //   this.testResult = "";
    //   this.testDialogVisible = true;
    // },



    // 批量配置对话框关闭
    handleBatchDialogClose() {
      this.batchDialogVisible = false;
      this.$refs.batchForm.resetFields();
    },

    // 批量配置提交
    handleBatchSubmit() {
      this.$refs.batchForm.validate(async (valid) => {
        if (valid) {
          this.batchSubmitLoading = true;
          try {
            const goodsIds = this.batchForm.goodsIds
              .split(',')
              .map(id => id.trim())
              .filter(id => id);

            const submitData = {
              templateId: this.batchForm.templateId,
              targetIds: goodsIds,
              priority: this.batchForm.priority
            };

            const res = await batchConfigGoods(submitData);
            if (res.success) {
              this.$Message.success("批量配置成功");
              this.handleBatchDialogClose();
              this.loadConfiguredGoods(); // 重新加载已配置商品
            } else {
              this.$Message.error(res.message || "批量配置失败");
            }
          } catch (error) {
            this.$Message.error("批量配置失败");
          } finally {
            this.batchSubmitLoading = false;
          }
        }
      });
    },

    // 测试对话框关闭 - 已删除
    // handleTestDialogClose() {
    //   this.testDialogVisible = false;
    // },

    // 测试提交 - 已删除
    // async handleTestSubmit() {
    //   if (!this.testForm.goodsId) {
    //     this.$Message.error("请输入商品ID");
    //     return;
    //   }

    //   this.testLoading = true;
    //   try {
    //     const res = await getBestMatchTemplate(this.testForm.goodsId);
    //     if (res.success) {
    //       this.testResult = JSON.stringify(res.result, null, 2);
    //     } else {
    //       this.testResult = `测试失败: ${res.message}`;
    //     }
    //   } catch (error) {
    //     this.testResult = `测试失败: ${error.message}`;
    //   } finally {
    //     this.testLoading = false;
    //   }
    // }
  }
};
</script>

<style scoped>
.search {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.export {
  margin-bottom: 20px;
}

.mr_10 {
  margin-right: 10px;
}

.mt_10 {
  margin-top: 10px;
}

/* 橙色渐变按钮样式 */
.orange-btn-1 {
  background-color: #ff6600 !important;
  border-color: #ff6600 !important;
  color: white !important;
}

.orange-btn-1:hover {
  background-color: #e55a00 !important;
  border-color: #e55a00 !important;
  color: white !important;
}

.orange-btn-2 {
  background-color: #ff8533 !important;
  border-color: #ff8533 !important;
  color: white !important;
}

.orange-btn-2:hover {
  background-color: #ff7519 !important;
  border-color: #ff7519 !important;
  color: white !important;
}

.orange-btn-3 {
  background-color: #ffa366 !important;
  border-color: #ffa366 !important;
  color: white !important;
}

.orange-btn-3:hover {
  background-color: #ff9952 !important;
  border-color: #ff9952 !important;
  color: white !important;
}

/* 保存配置按钮 - 浅绿色 */
.save-config-btn {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
  color: white !important;
}

.save-config-btn:hover {
  background-color: #5daf34 !important;
  border-color: #5daf34 !important;
  color: white !important;
}

/* 上架状态标签 - 浅蓝背景深蓝字体 */
.status-upper {
  background-color: #f0f8ff !important;
  border-color: #91d5ff !important;
  color: #1890ff !important;
}

/* 测试结果样式 - 已删除 */
/* .test-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #f8f8f9;
  border: 1px solid #dcdee2;
  border-radius: 4px;
}

.test-result pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
} */
</style>
