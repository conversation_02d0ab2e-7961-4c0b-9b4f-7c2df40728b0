<template>
  <div class="search">
    <Card>
      <!-- 操作按钮 -->
      <div class="export">
        <Button class="mr_10 orange-btn-1" @click="handleRefresh">
          <Icon type="md-refresh" />
          刷新分类
        </Button>
        <!-- <Button class="mr_10 orange-btn-2" @click="handleBatchConfig">
          批量配置
        </Button> -->
        <Button class="mr_10 orange-btn-2" @click="expandAll">
          展开全部
        </Button>
        <Button class="mr_10 orange-btn-3" @click="collapseAll">
          收起全部
        </Button>
      </div>

      <!-- 分类树状图 -->
      <Row :gutter="16">
        <Col span="8">
          <Card title="商品分类" :bordered="false">
            <div class="tree-container">
              <el-tree
                ref="categoryTree"
                :data="categoryTreeData"
                :props="treeProps"
                node-key="id"
                :default-expand-all="false"
                :expand-on-click-node="false"
                :highlight-current="true"
                @node-click="handleNodeClick"
                class="category-tree"
              >
                <template slot-scope="{ node, data }">
                  <div class="tree-node-content" :class="`tree-node-level-${data.level}`">
                    <span class="category-name">{{ data.name }}</span>
                    <el-tag v-if="isConfigured(data.id)" type="success" size="mini" class="config-tag">已配置</el-tag>
                  </div>
                </template>
              </el-tree>
            </div>
          </Card>
        </Col>
        <Col span="16">
          <Card title="合同模板配置" :bordered="false">
            <div v-if="selectedCategory" class="config-container">
              <!-- 分类信息展示 -->
              <div class="category-info-display">
                <h4>分类信息</h4>
                <Row :gutter="16">
                  <Col span="12">
                    <p><strong>分类名称：</strong>{{ selectedCategory.name }}</p>
                    <p><strong>分类ID：</strong>{{ selectedCategory.id }}</p>
                  </Col>
                  <Col span="12">
                    <p><strong>分类层级：</strong>{{ selectedCategory.level + 1 }}级</p>
                    <p><strong>配置状态：</strong>
                      <el-tag v-if="hasAnyConfig()" type="success" size="mini">已配置</el-tag>
                      <Tag v-else color="default">未配置</Tag>
                    </p>
                  </Col>
                </Row>
              </div>

              <!-- 合同模板配置 -->
              <div class="contract-template-configs">
                <Card class="template-config-card" :bordered="false">
                  <div slot="title" class="card-title">
                    <Icon type="md-document" class="title-icon" />
                    合同模板配置
                  </div>

                  <!-- 已绑定的模板列表 -->
                  <div v-if="boundTemplates.length > 0" class="bound-templates">
                    <h5>已绑定的合同模板：</h5>
                    <div class="template-list">
                      <div v-for="template in boundTemplates" :key="template.id" class="template-item">
                        <div class="template-info">
                          <span class="template-name">{{ getTemplateName(template.templateId) }}</span>
                          <span class="template-priority">优先级: {{ template.priority }}</span>
                          <span class="template-status" :class="template.status === 1 ? 'status-enabled' : 'status-disabled'">
                            {{ template.status === 1 ? '启用' : '禁用' }}
                          </span>
                        </div>
                        <div class="template-actions">
                          <Button
                            :type="template.status === 1 ? 'warning' : 'success'"
                            size="small"
                            @click="handleToggleStatus(template)"
                            :loading="template.statusLoading"
                            class="mr_8"
                          >
                            {{ template.status === 1 ? '禁用' : '启用' }}
                          </Button>
                          <Button
                            type="error"
                            size="small"
                            @click="handleDeleteTemplate(template.id)"
                            :loading="deleteLoading"
                          >
                            删除
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 添加新模板配置 -->
                  <div class="add-template-config">
                    <h5>{{ boundTemplates.length > 0 ? '添加新的合同模板：' : '配置合同模板：' }}</h5>
                    <Form ref="templateForm" :model="templateForm" :rules="configFormRules" :label-width="100">
                      <Row :gutter="16">
                        <Col span="12">
                          <Form-item label="合同模板" prop="templateId">
                            <Select v-model="templateForm.templateId" filterable clearable placeholder="请选择合同模板">
                              <Option v-for="template in availableTemplates" :key="template.templateId" :value="template.templateId">
                                {{ template.templateName }}
                              </Option>
                            </Select>
                          </Form-item>
                        </Col>
                        <Col span="12">
                          <Form-item label="优先级" prop="priority">
                            <InputNumber
                              v-model="templateForm.priority"
                              :min="0"
                              :max="999"
                              placeholder="数值越大优先级越高"
                              style="width: 100%"
                            />
                          </Form-item>
                        </Col>
                      </Row>
                      <Row :gutter="16">
                        <Col span="12">
                          <Form-item label="状态" prop="status">
                            <Select v-model="templateForm.status">
                              <Option :value="1">启用</Option>
                              <Option :value="0">禁用</Option>
                            </Select>
                          </Form-item>
                        </Col>
                        <Col span="12">
                          <Form-item>
                            <Button type="primary" @click="handleAddTemplate" :loading="saveLoading" class="orange-btn-1">
                              <Icon type="md-add" />
                              添加模板配置
                            </Button>
                            <Button @click="handleClearForm" style="margin-left: 8px">
                              清空
                            </Button>
                          </Form-item>
                        </Col>
                      </Row>
                      <Row>
                        <Col span="24">
                          <Form-item label="备注">
                            <Input v-model="templateForm.remark" type="textarea" :rows="2" placeholder="请输入备注信息" />
                          </Form-item>
                        </Col>
                      </Row>
                    </Form>
                  </div>
                </Card>
              </div>
            </div>
            <div v-else class="no-selection">
              <p>请在左侧选择一个分类来配置合同模板</p>
            </div>
          </Card>
        </Col>
      </Row>
    </Card>



    <!-- 批量配置对话框 -->
    <Modal
      v-model="batchDialogVisible"
      title="批量分类配置"
      width="800"
      :mask-closable="false"
      @on-cancel="handleBatchDialogClose"
    >
      <Form
        ref="batchForm"
        :model="batchForm"
        :rules="batchFormRules"
        :label-width="100"
      >
        <Form-item label="合同模板" prop="templateId">
          <Select v-model="batchForm.templateId" filterable>
            <Option 
              v-for="template in templateOptions" 
              :key="template.templateId" 
              :value="template.templateId"
            >
              {{ template.templateName }}
            </Option>
          </Select>
        </Form-item>

        <Form-item label="分类ID列表" prop="categoryIds">
          <Input 
            v-model="batchForm.categoryIds" 
            type="textarea" 
            :rows="5"
            placeholder="请输入分类ID，多个用逗号分隔，例如：cat001,cat002,cat003"
          />
        </Form-item>

        <Form-item label="优先级" prop="priority">
          <InputNumber 
            v-model="batchForm.priority" 
            :min="0" 
            :max="999"
            placeholder="数值越大优先级越高"
          />
        </Form-item>
      </Form>

      <div slot="footer">
        <Button @click="handleBatchDialogClose">取消</Button>
        <Button type="primary" @click="handleBatchSubmit" :loading="batchSubmitLoading">
          确定
        </Button>
      </div>
    </Modal>
  </div>
</template>

<script>
import {
  getCategoryConfigPage,
  saveCategoryConfig,
  deleteCategoryConfig,
  contractTemplateList,
  batchConfigCategory
} from "@/api/contract.js";
import { getCategoryTree } from "@/api/goods.js";

export default {
  name: "categoryTemplateConfig",
  data() {
    return {
      loading: false,
      saveLoading: false,

      // 分类树相关
      categoryTreeData: [],
      selectedCategory: null,
      boundTemplates: [], // 已绑定的模板列表
      configuredCategories: new Map(), // 存储所有已配置的分类信息
      deleteLoading: false,
      treeProps: {
        children: 'children',
        label: 'name'
      },

      // 模板配置表单
      templateForm: {
        categoryId: "",
        categoryPath: "",
        templateId: "",
        priority: 0,
        status: 1,
        remark: ""
      },

      configFormRules: {
        templateId: [
          { required: true, message: "请选择合同模板", trigger: "change" }
        ],
        priority: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === null || value === undefined || value === '') {
                callback(new Error('请输入优先级'));
              } else if (typeof value !== 'number' || isNaN(value)) {
                callback(new Error('优先级必须是数字'));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ],
        status: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === null || value === undefined || value === '') {
                callback(new Error('请选择状态'));
              } else {
                callback();
              }
            },
            trigger: "change"
          }
        ]
      },


      // 批量配置相关
      batchDialogVisible: false,
      batchSubmitLoading: false,
      batchForm: {
        templateId: "",
        categoryIds: "",
        priority: 0
      },
      batchFormRules: {
        templateId: [
          { required: true, message: "请选择合同模板", trigger: "change" }
        ],
        categoryIds: [
          { required: true, message: "请输入分类ID列表", trigger: "blur" }
        ],
        priority: [
          {
            required: true,
            validator: (rule, value, callback) => {
              if (value === null || value === undefined || value === '') {
                callback(new Error('请输入优先级'));
              } else if (typeof value !== 'number' || isNaN(value)) {
                callback(new Error('优先级必须是数字'));
              } else {
                callback();
              }
            },
            trigger: "blur"
          }
        ]
      },

      // 模板选项
      templateOptions: []
    };
  },
  computed: {
    // 获取可用的模板选项（排除已绑定的）
    availableTemplates() {
      const boundTemplateIds = this.boundTemplates.map(t => t.templateId);
      return this.templateOptions.filter(t => !boundTemplateIds.includes(t.templateId));
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getCategoryTree();
      this.getTemplateOptions();
      this.loadAllConfiguredCategories();
    },

    // 获取分类树
    async getCategoryTree() {
      this.loading = true;
      try {
        const res = await getCategoryTree();
        if (res.success) {
          this.categoryTreeData = this.formatTreeData(res.result || []);
        } else {
          this.$Message.error(res.message || "获取分类树失败");
        }
      } catch (error) {
        this.$Message.error("获取分类树失败");
        console.error("获取分类树失败", error);
      } finally {
        this.loading = false;
      }
    },

    // 格式化树数据
    formatTreeData(data, seenIds = new Set()) {
      if (!data || !Array.isArray(data)) {
        return [];
      }

      return data
        .filter(item => {
          // 过滤掉空ID或无效项
          if (!item || !item.id || item.id.toString().trim() === '') {
            console.warn('发现无效的分类项:', item);
            return false;
          }

          const idStr = item.id.toString();
          // 检查重复ID
          if (seenIds.has(idStr)) {
            console.warn('发现重复的分类ID:', idStr, item);
            return false;
          }

          seenIds.add(idStr);
          return true;
        })
        .map(item => {
          const node = {
            id: item.id.toString(), // 确保ID是字符串
            name: item.name || '未命名分类',
            level: item.level || 0,
            parentId: item.parentId || '',
            categoryPath: item.categoryPath || "",
            children: item.children ? this.formatTreeData(item.children, seenIds) : []
          };

          return node;
        });
    },



    // 检查指定分类是否已配置
    isConfigured(categoryId) {
      return this.configuredCategories.has(categoryId);
    },

    // 检查当前选中分类是否有任何配置
    hasAnyConfig() {
      return this.boundTemplates.length > 0;
    },

    // 处理节点点击
    handleNodeClick(data, node, component) {
      console.log('选择分类:', data.name, '层级:', data.level);
      this.selectedCategory = data;
      this.loadCategoryTemplates(data.id);
      this.resetTemplateForm(data);
    },

    // 获取模板选项
    async getTemplateOptions() {
      try {
        const res = await contractTemplateList({ pageNumber: 1, pageSize: 100 });
        if (res.success) {
          this.templateOptions = res.result.records || [];
        }
      } catch (error) {
        console.error("获取模板列表失败", error);
      }
    },

    // 加载所有已配置的分类
    async loadAllConfiguredCategories() {
      try {
        const res = await getCategoryConfigPage({ pageNumber: 1, pageSize: 1000 });
        if (res.success) {
          const configs = res.result.records || [];
          this.configuredCategories.clear();
          configs.forEach(config => {
            this.configuredCategories.set(config.categoryId, true);
          });
        }
      } catch (error) {
        console.error("获取已配置分类失败", error);
      }
    },

    // 加载分类绑定的模板列表
    async loadCategoryTemplates(categoryId) {
      try {
        const res = await getCategoryConfigPage({
          pageNumber: 1,
          pageSize: 100,
          categoryId: categoryId
        });
        if (res.success) {
          this.boundTemplates = (res.result.records || []).map(template => ({
            ...template,
            statusLoading: false // 为每个模板添加状态切换的加载状态
          }));
        } else {
          this.boundTemplates = [];
        }
      } catch (error) {
        console.error("获取分类模板配置失败", error);
        this.boundTemplates = [];
      }
    },

    // 重置模板表单
    resetTemplateForm(categoryData) {
      const defaultPriority = this.getDefaultPriorityByLevel(categoryData?.level);
      this.templateForm = {
        categoryId: categoryData?.id || "",
        categoryPath: categoryData?.categoryPath || "",
        templateId: "",
        priority: defaultPriority,
        status: 1,
        remark: ""
      };

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.templateForm) {
          this.$refs.templateForm.clearValidate();
        }
      });
    },

    // 根据分类层级获取默认优先级
    getDefaultPriorityByLevel(level) {
      switch (level) {
        case 0: // 一级分类
          return 10;
        case 1: // 二级分类
          return 20;
        case 2: // 三级分类
          return 30;
        default:
          return 0;
      }
    },

    // 清空配置表单
    clearConfigForm() {
      // 根据当前选中分类的层级设置默认优先级
      const defaultPriority = this.selectedCategory ?
        this.getDefaultPriorityByLevel(this.selectedCategory.level) : 0;

      this.configForm = {
        categoryId: this.selectedCategory?.id || "",
        categoryPath: this.selectedCategory?.categoryPath || "",
        templateId: "",
        contractType: 1, // 默认现货
        priority: defaultPriority,
        status: 1,
        remark: ""
      };
      this.currentConfig = null;

      // 清除表单验证状态
      this.$nextTick(() => {
        if (this.$refs.configForm) {
          this.$refs.configForm.clearValidate();
        }
      });
    },

    // 刷新
    handleRefresh() {
      this.init();
      this.$Message.success("刷新成功");
    },

    // 展开全部
    expandAll() {
      console.log('开始展开全部操作...');
      if (this.$refs.categoryTree) {
        // 获取所有节点
        const allNodes = this.$refs.categoryTree.store.root.childNodes;
        this.expandAllNodes(allNodes);
        this.$Message.success('已展开所有节点');
      } else {
        this.$Message.error('树组件未找到');
      }
    },

    // 收起全部
    collapseAll() {
      console.log('开始收起全部操作...');
      if (this.$refs.categoryTree) {
        // 获取所有节点
        const allNodes = this.$refs.categoryTree.store.root.childNodes;
        this.collapseAllNodes(allNodes);
        this.$Message.success('已收起所有节点');
      } else {
        this.$Message.error('树组件未找到');
      }
    },

    // 递归展开所有节点
    expandAllNodes(nodes) {
      nodes.forEach(node => {
        if (node.childNodes && node.childNodes.length > 0) {
          node.expanded = true;
          this.expandAllNodes(node.childNodes);
        }
      });
    },

    // 递归收起所有节点
    collapseAllNodes(nodes) {
      nodes.forEach(node => {
        if (node.childNodes && node.childNodes.length > 0) {
          node.expanded = false;
          this.collapseAllNodes(node.childNodes);
        }
      });
    },



    // 添加模板配置
    async handleAddTemplate() {
      if (!this.selectedCategory) {
        this.$Message.error("请先选择分类");
        return;
      }

      try {
        await this.$refs.templateForm.validate();
      } catch (error) {
        this.$Message.error("请检查表单输入");
        return;
      }

      // 检查是否已经绑定了相同的模板
      const existingTemplate = this.boundTemplates.find(t => t.templateId === this.templateForm.templateId);
      if (existingTemplate) {
        this.$Message.error("该模板已经绑定到此分类，请选择其他模板");
        return;
      }

      this.saveLoading = true;
      try {
        const formData = {
          ...this.templateForm,
          categoryId: this.selectedCategory.id,
          categoryPath: this.selectedCategory.categoryPath || ""
        };

        const res = await saveCategoryConfig(formData);
        if (res.success) {
          this.$Message.success("模板配置添加成功");
          // 更新配置状态
          this.configuredCategories.set(this.selectedCategory.id, true);
          // 重新加载模板列表
          this.loadCategoryTemplates(this.selectedCategory.id);
          // 清空表单
          this.handleClearForm();
          // 刷新树显示
          this.getCategoryTree();
        } else {
          this.$Message.error(res.message || "模板配置添加失败");
        }
      } catch (error) {
        this.$Message.error("模板配置添加失败");
        console.error("添加模板配置失败", error);
      } finally {
        this.saveLoading = false;
      }
    },

    // 删除模板配置
    async handleDeleteTemplate(configId) {
      this.$Modal.confirm({
        title: "确认删除",
        content: `确定要删除这个合同模板配置吗？`,
        onOk: async () => {
          this.deleteLoading = true;
          try {
            const res = await deleteCategoryConfig(configId);
            if (res.success) {
              this.$Message.success("模板配置删除成功");
              // 重新加载模板列表
              await this.loadCategoryTemplates(this.selectedCategory.id);
              // 检查是否还有配置，如果没有则更新状态
              if (this.boundTemplates.length === 0) {
                this.configuredCategories.delete(this.selectedCategory.id);
              }
              // 刷新树显示
              this.getCategoryTree();
            } else {
              this.$Message.error(res.message || "模板配置删除失败");
            }
          } catch (error) {
            this.$Message.error("模板配置删除失败");
            console.error("删除模板配置失败", error);
          } finally {
            this.deleteLoading = false;
          }
        }
      });
    },

    // 切换模板配置状态（启用/禁用）
    async handleToggleStatus(template) {
      const newStatus = template.status === 1 ? 0 : 1;
      const statusText = newStatus === 1 ? '启用' : '禁用';

      this.$Modal.confirm({
        title: "确认操作",
        content: `确定要${statusText}这个合同模板配置吗？`,
        onOk: async () => {
          // 设置当前模板的加载状态
          this.$set(template, 'statusLoading', true);

          try {
            // 构建更新数据，包含完整的配置信息
            const updateData = {
              id: template.id,
              categoryId: template.categoryId,
              categoryPath: template.categoryPath,
              templateId: template.templateId,
              priority: template.priority,
              status: newStatus,
              remark: template.remark
            };

            const res = await saveCategoryConfig(updateData);
            if (res.success) {
              this.$Message.success(`模板配置${statusText}成功`);
              // 更新本地状态
              template.status = newStatus;
            } else {
              this.$Message.error(res.message || `模板配置${statusText}失败`);
            }
          } catch (error) {
            this.$Message.error(`模板配置${statusText}失败`);
            console.error(`${statusText}模板配置失败`, error);
          } finally {
            // 清除加载状态
            this.$set(template, 'statusLoading', false);
          }
        }
      });
    },

    // 清空表单
    handleClearForm() {
      if (this.selectedCategory) {
        this.resetTemplateForm(this.selectedCategory);
      }
    },

    // 获取模板名称
    getTemplateName(templateId) {
      const template = this.templateOptions.find(t => t.templateId === templateId);
      return template ? template.templateName : templateId;
    },





    // 批量配置
    handleBatchConfig() {
      this.batchForm = {
        templateId: "",
        categoryIds: "",
        priority: 0
      };
      this.batchDialogVisible = true;
    },



    // 批量配置对话框关闭
    handleBatchDialogClose() {
      this.batchDialogVisible = false;
      this.$refs.batchForm.resetFields();
    },

    // 批量配置提交
    handleBatchSubmit() {
      this.$refs.batchForm.validate(async (valid) => {
        if (valid) {
          this.batchSubmitLoading = true;
          try {
            const categoryIds = this.batchForm.categoryIds
              .split(',')
              .map(id => id.trim())
              .filter(id => id);

            const submitData = {
              templateId: this.batchForm.templateId,
              targetIds: categoryIds,
              priority: this.batchForm.priority
            };

            const res = await batchConfigCategory(submitData);
            if (res.success) {
              this.$Message.success("批量配置成功");
              this.handleBatchDialogClose();
              // 刷新数据
              this.loadAllConfiguredCategories();
              this.getCategoryTree();
              if (this.selectedCategory) {
                this.loadCategoryTemplates(this.selectedCategory.id);
              }
            } else {
              this.$Message.error(res.message || "批量配置失败");
            }
          } catch (error) {
            this.$Message.error("批量配置失败");
          } finally {
            this.batchSubmitLoading = false;
          }
        }
      });
    }
  }
};
</script>

<style scoped>
.search {
  padding: 20px;
}

.export {
  margin-bottom: 20px;
}

.mr_10 {
  margin-right: 10px;
}

.mr_8 {
  margin-right: 8px;
}

.mt_10 {
  margin-top: 10px;
}

/* 橙色渐变按钮样式 */
.orange-btn-1 {
  background-color: #ff6600 !important;
  border-color: #ff6600 !important;
  color: white !important;
}

.orange-btn-1:hover {
  background-color: #e55a00 !important;
  border-color: #e55a00 !important;
  color: white !important;
}

.orange-btn-2 {
  background-color: #ff8533 !important;
  border-color: #ff8533 !important;
  color: white !important;
}

.orange-btn-2:hover {
  background-color: #ff7519 !important;
  border-color: #ff7519 !important;
  color: white !important;
}

.orange-btn-3 {
  background-color: #ffa366 !important;
  border-color: #ffa366 !important;
  color: white !important;
}

.orange-btn-3:hover {
  background-color: #ff9952 !important;
  border-color: #ff9952 !important;
  color: white !important;
}

.orange-btn-4 {
  background-color: #ffc299 !important;
  border-color: #ffc299 !important;
  color: white !important;
}

.orange-btn-4:hover {
  background-color: #ffb885 !important;
  border-color: #ffb885 !important;
  color: white !important;
}

/* 保存配置按钮 - 浅绿色 */
.save-config-btn {
  background-color: #67c23a !important;
  border-color: #67c23a !important;
  color: white !important;
}

.save-config-btn:hover {
  background-color: #5daf34 !important;
  border-color: #5daf34 !important;
  color: white !important;
}



.tree-container {
  max-height: 600px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 12px;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 配置容器 */
.config-container {
  padding: 16px 0;
}

.category-info-display {
  background-color: #f8f9fa;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e8eaec;
}

.category-info-display h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.category-info-display p {
  margin: 0 0 8px 0;
  font-size: 14px;
  line-height: 1.5;
  color: #666;
}

.category-info-display p:last-child {
  margin-bottom: 0;
}

/* 合同模板配置 */
.contract-template-configs {
  margin-top: 20px;
}

.template-config-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.template-config-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.card-title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333;
}

.title-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #ff6600;
}

/* 已绑定模板列表 */
.bound-templates {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e8eaec;
}

.bound-templates h5 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.template-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.template-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e8eaec;
  transition: all 0.3s ease;
}

.template-item:hover {
  border-color: #ff6600;
  box-shadow: 0 2px 8px rgba(255, 102, 0, 0.1);
}

.template-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.template-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.template-name {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.template-priority {
  color: #666;
  font-size: 12px;
  background-color: #f0f0f0;
  padding: 2px 8px;
  border-radius: 4px;
}

.template-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.status-enabled {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-disabled {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffb3b3;
}

/* 添加模板配置 */
.add-template-config {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8eaec;
}

.add-template-config h5 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.form-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.no-selection {
  text-align: center;
  padding: 60px 20px;
  color: #999;
}

.no-selection p {
  font-size: 14px;
}

/* el-tree 样式优化 */
.category-tree {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.tree-node-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: calc(100% - 16px);
  padding: 6px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  margin: 2px 8px;
  box-sizing: border-box;
}

.category-name {
  flex: 1;
  font-size: 14px;
  color: #333;
  font-weight: 400;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.level-badge {
  font-size: 11px;
  color: #999;
  margin-left: 8px;
  font-weight: normal;
}

.config-tag {
  margin-left: auto;
  flex-shrink: 0;
}

/* 不同层级的样式 - 直接应用到 tree-node-content */
.tree-node-level-0 {
  /* 一级分类 - 浅蓝色 */
  background: #f0f8ff !important;
  color: #1a1a1a !important;
  font-weight: 500 !important;
  font-size: 15px !important;
  border: 1px solid #e3f2fd !important;
}

.tree-node-level-0:hover {
  background: #e3f2fd !important;
  border-color: #bbdefb !important;
}

.tree-node-level-1 {
  /* 二级分类 - 浅绿色 */
  background: #f9fff9 !important;
  color: #333333 !important;
  font-weight: 450 !important;
  font-size: 14px !important;
  border: 1px solid #e8f5e8 !important;
}

.tree-node-level-1:hover {
  background: #f1f8e9 !important;
  border-color: #c8e6c9 !important;
}

.tree-node-level-2 {
  /* 三级分类 - 浅黄色 */
  background: #fffef7 !important;
  color: #444444 !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  border: 1px solid #fff8e1 !important;
}

.tree-node-level-2:hover {
  background: #fff3e0 !important;
  border-color: #ffcc02 !important;
}

.tree-node-level-3,
.tree-node-level-4,
.tree-node-level-5 {
  /* 四级及以下分类 - 浅灰色 */
  background: #fafafa !important;
  color: #666666 !important;
  font-weight: 400 !important;
  font-size: 13px !important;
  border: 1px solid #f0f0f0 !important;
}

.tree-node-level-3:hover,
.tree-node-level-4:hover,
.tree-node-level-5:hover {
  background: #f5f5f5 !important;
  border-color: #e0e0e0 !important;
}

/* 滚动条样式优化 */
.tree-container::-webkit-scrollbar {
  width: 8px;
}

.tree-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.tree-container::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, #c1c1c1 0%, #a8a8a8 100%);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.tree-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, #a8a8a8 0%, #8f8f8f 100%);
}

/* el-tree 基础样式重置 */
.category-tree >>> .el-tree-node {
  position: relative;
}

.category-tree >>> .el-tree-node__content {
  padding-left: 0 !important;
  background: transparent !important;
  border: none !important;
  height: auto !important;
  line-height: normal !important;
}

.category-tree >>> .el-tree-node__content:hover {
  background: transparent !important;
}

/* 层级缩进 - 通过 el-tree-node__content 的 padding 实现 */
.category-tree >>> .el-tree-node__children .el-tree-node__content {
  padding-left: 20px !important;
}

.category-tree >>> .el-tree-node__children .el-tree-node__children .el-tree-node__content {
  padding-left: 40px !important;
}

.category-tree >>> .el-tree-node__children .el-tree-node__children .el-tree-node__children .el-tree-node__content {
  padding-left: 60px !important;
}

.category-tree >>> .el-tree-node__children .el-tree-node__children .el-tree-node__children .el-tree-node__children .el-tree-node__content {
  padding-left: 80px !important;
}

/* 确保每个节点内容区域独立 */
.category-tree >>> .el-tree-node__children {
  background: transparent !important;
}

.category-tree >>> .el-tree-node {
  background: transparent !important;
}

/* el-tree 展开图标样式 */
.category-tree >>> .el-tree-node__expand-icon {
  color: #666;
  font-size: 14px;
  transition: all 0.3s ease;
}

.category-tree >>> .el-tree-node__expand-icon:hover {
  color: #409eff;
}

.category-tree >>> .el-tree-node__expand-icon.expanded {
  transform: rotate(90deg);
}
</style>
