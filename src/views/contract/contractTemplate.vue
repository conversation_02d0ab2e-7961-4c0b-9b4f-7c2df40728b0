<template>
  <div class="search">
    <Card>
      <Form
        ref="searchForm"
        :model="searchForm"
        inline
        :label-width="70"
        class="search-form"
      >
        <Form-item label="合同编号" prop="orderSn">
          <Input
            type="text"
            v-model="searchForm.orderSn"
            clearable
            placeholder="请输入合同编号"
            style="width: 160px"
          />
        </Form-item>
        <Form-item label="合同名称" prop="buyerName">
          <Input
            type="text"
            v-model="searchForm.buyerName"
            clearable
            placeholder="请输入合同名称"
            style="width: 160px"
          />
        </Form-item>
        <Form-item label="合同状态" prop="orderStatus">
          <Select
            v-model="searchForm.orderStatus"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <Option value="1">待签署</Option>
            <Option value="2">已签署</Option>
            <Option value="3">已作废</Option>
          </Select>
        </Form-item>
        <Button @click="handleSearch" type="primary" class="search-btn"
          >搜索</Button
        >
        <Button @click="handleReset" class="search-btn">重置</Button>
      </Form>
      <div class="export">
        <Button type="success" icon="ios-add" class="mr_10" @click="handleAdd"
          >新增</Button
        >
      </div>
      <Table
        :loading="loading"
        border
        :columns="columns"
        :data="data"
        ref="table"
      >
        <template slot="actionSlot" slot-scope="{ row }">
          <span
            class="mr_10"
            style="color: #409eff; cursor: pointer"
            @click="handlePreview(row)"
          >
            预览
          </span>
          <span
            class="mr_10"
            style="color: #409eff; cursor: pointer"
            @click="handleEdit(row)"
          >
            编辑
          </span>
          <span
            class="mr_10"
            style="color: #409eff; cursor: pointer"
            @click="handleEnable(row)"
          >
            启用
          </span>
          <span
            style="color: #409eff; cursor: pointer"
            @click="handleDisable(row)"
          >
            禁用
          </span>
        </template>
      </Table>
      <Row type="flex" justify="end" class="mt_10">
        <Page
          :current="searchForm.pageNumber"
          :total="total"
          :page-size="searchForm.pageSize"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
          :page-size-opts="[10, 20, 50]"
          size="small"
          show-total
          show-elevator
          show-sizer
        ></Page>
      </Row>
    </Card>

    <!-- 新增/修改 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="1000px"
      append-to-body
      @close="handleDialogClose"
    >
      <div class="container">
        <el-form
          :model="dialogForm"
          :rules="dialogFormRules"
          ref="dialogFormRef"
          label-width="auto"
          class="demo-ruleForm"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="合同模板" prop="templateName">
                <el-upload
                  class="upload-demo"
                  :action="uploadFileUrl"
                  multiple
                  :limit="3"
                  :file-list="fileList"
                >
                  <el-button size="small" type="primary">点击上传</el-button>
                  <div slot="tip" class="el-upload__tip">
                    只能上传jpg/png文件，且不超过500kb
                  </div>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="模板标题" prop="templateName">
                <el-input
                  v-model="dialogForm.templateName"
                  placeholder="请输入模板标题"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="强制阅读时间(秒)" prop="forceReadingSecond">
                <el-input
                  v-model="dialogForm.forceReadingSecond"
                  placeholder="请输入强制阅读时间(秒)"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否需要手写签名" prop="needCustomizeWrite">
                <el-select
                  v-model="dialogForm.needCustomizeWrite"
                  style="width: 100%"
                  placeholder="请选择是否需要手写签名"
                >
                  <el-option label="是" value="1"></el-option>
                  <el-option label="否" value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="签署校验类型" prop="templateName">
                <el-input v-model="dialogForm.verifyType"></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item label="签署人数" prop="signerNum">
                <el-input
                  v-model="dialogForm.signerNum"
                  placeholder="请输入签署人数"
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item label="需要平台默认签署" prop="templateName">
                <el-select v-model="dialogForm.needPlatSign">
                  <el-option label="是" value="1"></el-option>
                  <el-option label="否" value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
          </el-row>
        </el-form>
        <el-row :gutter="20">
          <el-col :span="3">
            <span>签署关键字配置</span>
          </el-col>
          <el-col :span="21">
            <el-table border :data="dialogTableData">
              <el-table-column align="center" width="80">
                <template slot="header" slot-scope="scope">
                  <!-- 绑定点击事件 -->
                  <Icon
                    style="cursor: pointer"
                    color="#007dff"
                    size="36"
                    type="md-add-circle"
                    @click="addRow"
                  />
                </template>
                <template slot-scope="scope">
                  <div
                    @mouseenter="handleMouseEnter(scope.row)"
                    @mouseleave="handleMouseLeave(scope.row)"
                    style="cursor: pointer"
                  >
                    <span v-if="!scope.row.showDelete">{{
                      scope.$index + 1
                    }}</span>
                    <!-- 鼠标移入显示删除图标 -->
                    <Icon
                      v-else
                      @click="deleteRow(scope.$index)"
                      color="red"
                      size="36"
                      type="md-trash"
                    />
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="签署关键字" align="center">
                <template slot-scope="scope">
                  <!-- 可输入表单 -->
                  <el-input v-model="scope.row.keywords" />
                </template>
              </el-table-column>
              <el-table-column label="关键字对齐方式" align="center">
                <template slot-scope="scope">
                  <!-- 可输入表单 -->
                  <el-input v-model="scope.row.signAlign" />
                </template>
              </el-table-column>
              <el-table-column label="关键字类型" align="center">
                <template slot-scope="scope">
                  <!-- 可输入表单 -->
                  <el-select v-model="scope.row.signType">
                    <el-option label="签名" value="1"></el-option>
                    <el-option label="印章" value="2"></el-option>
                    <el-option label="日期" value="3"></el-option>
                  </el-select>
                </template>
              </el-table-column>
              <el-table-column label="签署人顺序" align="center">
                <template slot-scope="scope">
                  <!-- 可输入表单 -->
                  <el-input v-model="scope.row.signNo" />
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleDialogClose">取 消</el-button>
        <el-button type="primary" @click="handleDialogSubmit">确 定</el-button>
      </span>
    </el-dialog>

    <FilePreview :url="fileUrl" />
  </div>
</template>

<script>
import { contractTemplateList, submit } from "@/api/contract.js";
import FilePreview from "@/components/FilePreview.vue";
import { uploadFile } from "@/libs/axios";
export default {
  name: "contractTemplate",
  components: {
    FilePreview,
  },
  data() {
    return {
      accessToken: {}, // 验证token
      uploadFileUrl: uploadFile, // 上传路径
      fileUrl: "",
      loading: true, // 表单加载状态
      searchForm: {
        // 搜索框初始化对象
        pageNumber: 1, // 当前页数
        pageSize: 10, // 页面大小
        // sort: "", // 默认排序字段
        // order: "", // 默认排序方式
        // startDate: "", // 起始时间
        // endDate: "", // 终止时间
        // orderSn: "",
        // buyerName: "",
        // orderStatus: "",
        // orderType: "NORMAL",
      },
      selectDate: null,
      columns: [
        {
          title: "模板id",
          minWidth: 200,
          key: "templateId",
          tooltip: true,
        },
        {
          title: "模板标题",
          key: "templateName",
          minWidth: 120,
        },
        {
          title: "供应商",
          key: "apiSupplier",
          minWidth: 120,
        },
        {
          title: "模板类型",
          key: "templateCategory",
          minWidth: 120,
        },
        {
          title: "模板状态",
          key: "templateStatus",
          minWidth: 120,
        },
        {
          title: "合同生成方式",
          key: "contractGenType",
          minWidth: 130,
          tooltip: true,
          render: (h, params) => {
            if (params.row.contractGenType == "1") {
              return h("div", {}, "合同模板");
            } else if (params.row.contractGenType == "2") {
              return h("div", {}, "报表引擎");
            } else {
              return h("div", {}, params.row.contractGenType);
            }
          },
        },
        {
          title: "是否需要手签",
          key: "needCustomizeWrite",
          minWidth: 130,
          tooltip: true,
          render: (h, params) => {
            if (params.row.needCustomizeWrite == "0") {
              return h("div", {}, "不需要");
            } else if (params.row.needCustomizeWrite == "1") {
              return h("div", {}, "需要");
            } else {
              return h("div", {}, params.row.needCustomizeWrite);
            }
          },
        },
        {
          title: "强制阅读秒数",
          key: "forceReadingSecond",
          minWidth: 100,
          tooltip: true,
        },
        {
          title: "操作人",
          key: "operateName",
          minWidth: 100,
          tooltip: true,
        },
        {
          title: "签署人数",
          key: "signerNum",
          minWidth: 170,
        },
        {
          title: "操作时间",
          key: "operateTime",
          minWidth: 170,
        },
        {
          title: "状态",
          key: "status",
          minWidth: 170,
        },
        {
          title: "操作",
          key: "action",
          align: "center",
          minWidth: 200,
          slot: "actionSlot",
          fixed: "right",
        },
      ],
      data: [], // 表单数据
      total: 0, // 表单数据总数

      dialogVisible: false, // 新增/修改对话框是否显示
      dialogTitle: "", // 对话框标题
      dialogForm: {
        templateName: "", // 模板标题
        forceReadingSecond: "", // 强制阅读秒数
        needCustomizeWrite: "", // 是否需要手写签名
        // verifyType: "0", // 签署校验类型
        signerNum: "", // 签署人数
        // needPlatSign: "", // 需要平台方签署
        // signConfigList: [], // 签署关键字配置
      }, // 对话框表单数据
      dialogFormRules: {}, // 对话框表单验证规则
      dialogTableData: [], // 对话框表格数据
      fileList: [], // 上传文件列表
    };
  },
  methods: {
    // 删除行
    deleteRow(index) {
      this.dialogTableData.splice(index, 1);
    },
    // 鼠标移入显示当前行的删除图标
    handleMouseEnter(row) {
      row.showDelete = true;
    },
    // 鼠标移出隐藏当前行的删除图标
    handleMouseLeave(row) {
      row.showDelete = false;
    },
    // 添加新行
    addRow() {
      this.dialogTableData.push({
        keywords: "",
        signAlign: "",
        signType: "",
        signNo: "",
        showDelete: false,
      });
    },
    handleDialogClose() {
      this.dialogVisible = false;
      this.dialogTitle = "";
      this.dialogTableData = [];
      this.$refs.dialogFormRef.resetFields();
    },
    handleDialogSubmit() {
      this.$refs.dialogFormRef.validate(async (valid) => {
        if (valid) {
          let params = {
            ...this.dialogForm,
            signConfigList: this.dialogTableData,
            contractGenType: 2,
          };
          const res = await submit(params);

          console.log(res, 'res')
        }
      });
    },
    handlePreview(row) {
      const targetUrl = row.fileUrl;
      this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
    },
    handleAdd(row) {
      console.log(row, "新增");
      this.dialogVisible = true;
      this.dialogTitle = "新 增";
    },
    handleEdit(row) {
      console.log(row, "编辑");
      this.dialogVisible = true;
      this.dialogTitle = "编 辑";
      this.dialogForm = { ...row };
      this.dialogTableData = row.signConfigList.map((item) => ({
        keywords: item.keywords,
        signAlign: item.signAlign,
        signType: item.signType,
        signNo: item.signNo,
        showDelete: false,
      }));
    },
    handleEnable(row) {
      console.log(row, "启用");
    },
    handleDisable(row) {
      console.log(row, "禁用");
    },
    // 初始化数据
    init() {
      this.accessToken = {
        accessToken: this.getStore("accessToken")
      };
      this.getDataList();
    },
    // 改变页码
    changePage(v) {
      this.searchForm.pageNumber = v;
      this.getDataList();
    },
    // 改变页数
    changePageSize(v) {
      this.searchForm.pageSize = v;
      this.getDataList();
    },
    // 搜索订单
    handleSearch() {
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.getDataList();
    },
    // 重置
    handleReset() {
      this.searchForm = {};
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.selectDate = null;
      this.searchForm.startDate = "";
      this.searchForm.endDate = "";
      (this.searchForm.orderType = "NORMAL"),
        // 重新加载数据
        this.getDataList();
    },
    // 获取表格数据
    getDataList() {
      this.loading = true;
      contractTemplateList(this.searchForm).then((res) => {
        this.loading = false;
        if (res.success) {
          this.data = res.result.records;
          this.total = res.result.total;
        }
      });
    },
  },
  mounted() {
    this.init();
  },
  // 页面缓存处理，从该页面离开时，修改KeepAlive为false，保证进入该页面是刷新
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false;
    next();
  },
};
</script>
<style lang="scss">
// 建议引入通用样式 可删除下面样式代码
@import "@/styles/table-common.scss";
.export {
  margin: 10px 20px 10px 0;
}
</style>
