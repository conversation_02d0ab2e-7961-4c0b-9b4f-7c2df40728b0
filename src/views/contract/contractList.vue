<template>
  <div class="search">
    <Card>
      <Form
        ref="searchForm"
        :model="searchForm"
        inline
        :label-width="70"
        class="search-form"
      >
        <Form-item label="合同编号" prop="contractNo">
          <Input
            type="text"
            v-model="searchForm.contractNo"
            clearable
            placeholder="请输入合同编号"
            style="width: 160px"
          />
        </Form-item>
        <Form-item label="合同名称" prop="contractTitle">
          <Input
            type="text"
            v-model="searchForm.contractTitle"
            clearable
            placeholder="请输入合同名称"
            style="width: 160px"
          />
        </Form-item>
        <Form-item label="合同状态" prop="status">
          <Select
            v-model="searchForm.status"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <Option :value="1">待签署</Option>
            <Option :value="2">已撤销</Option>
            <Option :value="3">已签署</Option>
            <Option :value="4">已失效</Option>
            <Option :value="5">已完成</Option>
            <Option :value="6">签署中</Option>
          </Select>
        </Form-item>
        <Button @click="handleSearch" type="primary" class="search-btn"
          >搜索</Button
        >
        <!-- <Button @click="handleReset" class="search-btn">重置</Button> -->
      </Form>
      <div class="export"></div>
      <Table
        :loading="loading"
        border
        :columns="columns"
        :data="data"
        ref="table"
      >
        <template slot="actionSlot" slot-scope="{ row }">
          <span
            class="mr_10"
            style="color: #409eff; cursor: pointer"
            @click="handleView(row)"
            v-if="false"
          >
            详情
          </span>
          <span
            style="color: #409eff; cursor: pointer"
            @click="handlePreview(row)"
          >
            预览
          </span>
        </template>
      </Table>
      <Row type="flex" justify="end" class="mt_10">
        <Page
          :current="searchForm.pageNumber"
          :total="total"
          :page-size="searchForm.pageSize"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
          :page-size-opts="[10, 20, 50]"
          size="small"
          show-total
          show-elevator
          show-sizer
        ></Page>
      </Row>
    </Card>

    <FilePreview :url="fileUrl" />
  </div>
</template>

<script>
import { page } from "@/api/contract.js";
import FilePreview from "@/components/FilePreview.vue";

export default {
  name: "contractList",
  components: {
    FilePreview,
  },
  data() {
    return {
      loading: true, // 表单加载状态
      searchForm: {
        // 搜索框初始化对象
        pageNumber: 1, // 当前页数
        pageSize: 10, // 页面大小
        // sort: "", // 默认排序字段
        // order: "", // 默认排序方式
        // startDate: "", // 起始时间
        // endDate: "", // 终止时间
        // orderSn: "",
        // buyerName: "",
        // orderStatus: "",
        // orderType: "NORMAL",
      },
      selectDate: null,
      columns: [
        {
          title: "合同编号",
          key: "contractNo",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "合同名称",
          key: "contractTitle",
          minWidth: 120,
        },
        {
          title: "开始时间",
          key: "createTime",
          minWidth: 170,
        },
        {
          title: "结束时间",
          key: "signDeadLine",
          minWidth: 170,
        },
        {
          title: "合同状态",
          key: "status",
          minWidth: 170,
          render: (h, params) => {
            if (params.row.status == "1") {
              return h("div", [
                h("tag", { props: { color: "volcano" } }, "待签署"),
              ]);
            } else if (params.row.status == "2") {
              return h("div", [h("tag", { props: { color: "lime" } }, "已撤销")]);
            } else if (params.row.status == "3") {
              return h("div", [h("tag", { props: { color: "blue" } }, "已签署")]);
            } else if (params.row.status == "4") {
              return h("div", [h("tag", { props: { color: "volcano" } }, "已失效")]);
            } else if (params.row.status == "5") {
              return h("div", [h("tag", { props: { color: "green" } }, "已完成")]);
            } else if (params.row.status == "6") {
              return h("div", [h("tag", { props: { color: "orange" } }, "签署中")]);
            } else {
              return h("div", {}, params.row.status);
            }
          },
        },
        {
          title: "操作",
          key: "action",
          align: "center",
          minWidth: 100,
          slot: "actionSlot",
          fixed: "right",
        },
      ],
      data: [], // 表单数据
      total: 0, // 表单数据总数
      orderCode: "", // 订单核验码
      fileUrl: "",
    };
  },
  methods: {
    handlePreview(row) {
      const targetUrl = row.fileUrl;
      this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
    },
    handleView(row) {
      console.log(row);
      // this.$router.push({
      //   name: "contract-list-detail",
      // });
    },
    // 初始化数据
    init() {
      this.getDataList();
    },
    // 清理搜索表单中的空值
    cleanSearchForm() {
      const fields = ["contractNo", "contractTitle", "status"];
      fields.forEach((field) => {
        const value = this.searchForm[field];
        // 删除空值、undefined、null、空字符串
        if (!value || value === 'undefined' || value === 'null' || value === '' || value === undefined) {
          delete this.searchForm[field];
        }
      });
    },
    // 改变页码
    changePage(v) {
      this.searchForm.pageNumber = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 改变页数
    changePageSize(v) {
      this.searchForm.pageSize = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 搜索订单
    handleSearch() {
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 重置
    handleReset() {
      this.searchForm = {
        pageNumber: 1,
        pageSize: 10,
        contractNo: "",
        contractTitle: "",
        status: "",
      };
      // 重新加载数据
      this.cleanSearchForm();
      this.getDataList();
    },
    // 获取表格数据
    getDataList() {
      this.loading = true;
      page({ ...this.searchForm }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.data = res.result.records;
          this.total = res.result.total;
        }
      });
    },
  },
  mounted() {
    this.init();
  },
  // 页面缓存处理，从该页面离开时，修改KeepAlive为false，保证进入该页面是刷新
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false;
    next();
  },
};
</script>
<style lang="scss">
// 建议引入通用样式 可删除下面样式代码
@import "@/styles/table-common.scss";
.export {
  margin: 10px 20px 10px 0;
}
</style>
