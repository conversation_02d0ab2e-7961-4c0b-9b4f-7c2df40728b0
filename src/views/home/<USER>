.card {
  margin: 10px 10px 20px;
  padding: 0 20px 20px;
  background: #fff;
  border: 1px solid #e7e7e7;

  border-radius: 0.4em;
}

h4 {
  margin: 20px 0;
  font-size: 18px;
}
/deep/ .ivu-icon {
  margin-right: 10px;
}
.rate-box {
  flex: 3.5;
  margin-top: 50px;
  display: flex;
  justify-content: flex-end;
  > div {
    padding: 0 20px;

    // border-left: 1px solid #ededed;
    > h5 {

      text-align: center;
      margin: 10px 0;
    }
  }
}
.bold {
  font-size: 18px;
}
.shop-box {
  flex: 3;
  display: flex;
  font-size: 12px;
  margin-top: 50px;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-left: 20px;
  > .box-item {
    width: 100%;
    display: flex;
    align-items: center;
    margin: 10px 0;
    justify-content: space-between;
  }
  > .title {
    font-size: 30px;
  }
}
.shop {
  align-items: center;
  justify-content: flex-start;
}
.notice-title {
  margin: 10px 0;
  font-weight: bold;
}
.detail-list {
  display: flex;
  flex-wrap: wrap;
}
.detail-title {
  position: absolute;
  left: 0;
  top: -15px;
  opacity: 0.3;
  color: #999;
  font-size: 21px;
  text-decoration: initial;
  transition: 0.35s;
}

.detail-item {
  cursor: pointer;
  transition: 0.35s;
  position: relative;
  font-weight: bold;
  width: 286px;
  display: flex;
  /deep/ span {
    color: $theme_color;
    font-size: 18px;
  }
  align-items: center;
  justify-content: center;
  padding: 20px;
  background: #eee;
  border-radius: 0.4em;
  margin: 10px;
  > div {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin: 0 20px;
  }
}
.detail-item:hover {
  box-shadow: 3px 5px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-4px);
  > .detail-title {
    opacity: 1;

    top: 5px;
  }
}

.shop-logo {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 3px solid #ededed;
  box-sizing: border-box;
}
.count-item,
.todo-item {
  height: 84px;
  display: flex;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  color: #fff;
  border-radius: 0.4em;
  flex: 1;
  font-weight: bold;
  margin-right: 20px;
}

.card {
  box-shadow: 1px 3px 12px rgba($color: #e7e7e7, $alpha: 0.3);
  border-radius: 0.4em;
  box-shadow: 1px 3px 12px rgba($color: #e7e7e7, $alpha: 0.3);
}

.box-left {
  flex: 8;
}
.box-right {
  flex: 2;
  margin-left: 20px;
}

.count-item:nth-of-type(1) {
  background-image: linear-gradient(109.6deg, rgba($color: #ff9a76, $alpha: 0.6) 11.2%, #ff9a76 100.2%);

  box-shadow: 1px 3px 12px rgba($color: #ff9a76, $alpha: 0.3);
}
.count-item:nth-of-type(2) {
  background-image: linear-gradient(109.6deg, rgba($color: #4e89ae, $alpha: 0.6) 11.2%, #4e89ae 100.2%);

  box-shadow: 1px 3px 12px rgba($color: #4e89ae, $alpha: 0.3);
}
.count-item:nth-of-type(3) {
  background-image: linear-gradient(109.6deg, rgba($color: #679b9b, $alpha: 0.6) 11.2%, #679b9b 100.2%);

  box-shadow: 1px 3px 12px rgba($color: #679b9b, $alpha: 0.3);
}
.count-item:nth-of-type(4) {
  background-image: linear-gradient(109.6deg, rgba($color: #637373, $alpha: 0.6) 11.2%, #637373 100.2%);

  box-shadow: 1px 3px 12px rgba($color: #637373, $alpha: 0.3);
}
