<template>
  <div class="search">
    <Card>
      <Form
        ref="searchForm"
        :model="searchForm"
        inline
        :label-width="80"
        class="search-form"
      >
        <Form-item label="订单编号" prop="orderSn">
          <Input
            type="text"
            v-model="searchForm.orderSn"
            clearable
            placeholder="请输入订单编号"
            style="width: 200px"
          />
        </Form-item>
        <Form-item label="子订单编号" prop="orderItemSn">
          <Input
            type="text"
            v-model="searchForm.orderItemSn"
            clearable
            placeholder="请输入订单编号"
            style="width: 200px"
          />
        </Form-item>
        <Form-item label="状态" prop="receiptStatus">
          <Select
            v-model="searchForm.receiptStatus"
            placeholder="请选择"
            clearable
            style="width: 200px"
          >
            <Option :value="0">未开票</Option>
            <Option :value="1">已开票</Option>
          </Select>
        </Form-item>
        <Button @click="handleSearch" type="primary" class="search-btn"
          >搜索</Button
        >
        <Button @click="handleReset" class="search-btn">重置</Button>
      </Form>
      <Table
        class="mt_10"
        :loading="loading"
        border
        :columns="columns"
        :data="data"
        ref="table"
      >
        <!-- 订单详情格式化 -->
        <template slot="orderSlot" slot-scope="scope">
          <a
            @click="
              $router.push({
                name: 'order-detail',
                query: { sn: scope.row.orderSn },
              })
            "
            >{{ scope.row.orderSn }}</a
          >
        </template>
      </Table>
      <Row type="flex" justify="end" class="mt_10">
        <Page
          :current="searchForm.pageNumber"
          :total="total"
          :page-size="searchForm.pageSize"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
          :page-size-opts="[10, 20, 50]"
          size="small"
          show-total
          show-elevator
          show-sizer
        ></Page>
      </Row>
    </Card>

    <!-- 开票 -->
    <el-dialog
      title="上传发票"
      :visible.sync="dialogVisible"
      width="50%"
      append-to-body
      @close="handleClose"
    >
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="发票上传" prop="fileUrl">
          <el-upload
            class="upload-demo"
            multiple
            drag
            :action="uploadFile"
            :headers="accessToken"
            accept=".pdf,.jpg,.png,.jpeg"
            :limit="9"
            :on-success="handleSuccess"
            :on-remove="handleRemove"
            :on-preview="handlePreview"
            :file-list="fileListData"
            :before-upload="handleBeforeUpload"
          >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">
              将文件拖到此处，或<em>点击上传</em>
            </div>
            <div class="el-upload__tip" slot="tip">
              请上传.pdf,.jpg,.png,.jpeg文件，大小不超过10M
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleSubmit('form')">
          确 定
        </el-button>
      </span>
    </el-dialog>

    <FilePreview :url="fileUrl" />
    <ImagePreview :url="imageUrl" />

    <!-- 多文件回显 -->
    <MultifilePlayback
      :fileListData="MultifileListData"
      @closePreview="closePreview"
      :title="MultifilePlaybackTitle"
    />
  </div>
</template>

<script>
import * as API_Order from "@/api/order";
import { uploadFile } from "@/libs/axios";
import FilePreview from "@/components/FilePreview.vue";
import ImagePreview from "@/components/ImagePreview.vue";
import MultifilePlayback from "@/components/MultifilePlayback.vue";

export default {
  name: "receipt",
  components: {
    FilePreview,
    ImagePreview,
    MultifilePlayback,
  },
  data() {
    return {
      uploadFile,
      loading: true, // 表单加载状态
      searchForm: {
        // 搜索框初始化对象
        pageNumber: 1, // 当前页数
        pageSize: 10, // 页面大小
      },
      columns: [
        {
          title: "订单号",
          key: "orderSn",
          minWidth: 160,
          slot: "orderSlot",
        },
        {
          title: "子订单号",
          key: "orderItemSn",
          minWidth: 160,
        },

        {
          title: "发票抬头",
          key: "receiptTitle",
          minWidth: 120,
          tooltip: true,
          render: (h, params) => {
            return h("div", params.row.receiptTitle || "暂未填写");
          },
        },
        {
          title: "纳税人识别号",
          key: "taxpayerId",
          minWidth: 100,
          tooltip: true,
          render: (h, params) => {
            return h("div", params.row.taxpayerId || "暂未填写");
          },
        },
        {
          title: "发票内容",
          key: "receiptContent",
          minWidth: 90,
          tooltip: true,
          render: (h, params) => {
            return h("div", params.row.receiptContent || "暂未填写");
          },
        },
        {
          title: "发票金额",
          key: "billPrice",
          width: 150,
          render: (h, params) => {
            return h("priceColorScheme", {
              props: { value: params.row.receiptPrice, color: this.$mainColor },
            });
          },
        },
        {
          title: "发票状态",
          key: "receiptStatus",
          width: 100,
          tooltip: true,
          render: (h, params) => {
            if (params.row.receiptStatus === 0) {
              return h("div", [
                h("tag", { props: { color: "volcano" } }, "未开票"),
              ]);
            } else {
              return h("div", [
                h("tag", { props: { color: "green" } }, "已开票"),
              ]);
            }
          },
        },
        {
          title: "订单状态",
          key: "orderStatus",
          width: 100,
          render: (h, params) => {
            if (params.row.orderStatus == "UNPAID") {
              return h("div", [
                h("tag", { props: { color: "magenta" } }, "未付款"),
              ]);
            } else if (params.row.orderStatus == "PAID") {
              return h("div", [
                h("tag", { props: { color: "blue" } }, "已付款"),
              ]);
            } else if (params.row.orderStatus == "UNDELIVERED") {
              return h("div", [
                h("tag", { props: { color: "geekblue" } }, "待发货"),
              ]);
            } else if (params.row.orderStatus == "STAY_PICKED_UP") {
              return h("div", [
                h("tag", { props: { color: "geekblue" } }, "待自提"),
              ]);
            } else if (params.row.orderStatus == "PARTS_DELIVERED") {
              return h("div", [
                h("tag", { props: { color: "cyan" } }, "部分发货"),
              ]);
            } else if (params.row.orderStatus == "DELIVERED") {
              return h("div", [
                h("tag", { props: { color: "cyan" } }, "已发货"),
              ]);
            } else if (params.row.orderStatus == "COMPLETED") {
              return h("div", [
                h("tag", { props: { color: "green" } }, "已完成"),
              ]);
            } else if (params.row.orderStatus == "TAKE") {
              return h("div", [
                h("tag", { props: { color: "volcano" } }, "待核验"),
              ]);
            } else if (params.row.orderStatus == "CANCELLED") {
              return h("div", [
                h("tag", { props: { color: "red" } }, "已取消"),
              ]);
            } else if (params.row.orderStatus == "PAYING") {
              return h("div", [
                h("tag", { props: { color: "red" } }, "待确认"),
              ]);
            } else if (params.row.orderStatus == "IN_PROGRESS") {
              return h("div", [
                h("tag", { props: { color: "red" } }, "交易中"),
              ]);
            }
          },
        },

        {
          title: "操作",
          key: "action",
          align: "center",
          width: 120,
          render: (h, params) => {
            let canInvoicing = "";
            let detail = "";
            if (params.row.canInvoicing) {
              canInvoicing = h(
                "Button",
                {
                  props: {
                    size: "small",
                    type: "success",
                  },
                  style: {
                    marginRight: "5px",
                  },
                  on: {
                    click: () => {
                      this.dialogInvoicing(params.row.id);
                    },
                  },
                },
                "开票"
              );
            }
            if (params.row.fileUrl) {
              detail = h(
                "Button",
                {
                  props: {
                    size: "small",
                    type: "info",
                  },
                  style: {
                    marginRight: "5px",
                  },
                  on: {
                    click: () => {
                      this.previewBill(params.row);
                    },
                  },
                },
                "查看"
              );
            }
            if (detail || canInvoicing) {
              return h("div", [detail, canInvoicing]);
            } else {
              return h("div", "--");
            }
          },
        },
      ],
      data: [], // 表单数据
      total: 0, // 表单数据总数

      accessToken: {}, // 验证token
      dialogVisible: false, // 上传发票弹窗
      form: {
        id: "", // 订单id
        fileUrl: "", // 发票
      },
      rules: {
        fileUrl: [{ required: true, message: "请上传发票", trigger: "blur" }],
      },
      fileUrl: "",
      imageUrl: "",
      fileListData: [],
      MultifilePlaybackTitle: "发票凭证", // 多文件回显标题
      MultifileListData: [], // 多文件回显数据
    };
  },
  methods: {
    // 初始化数据
    init() {
      this.getData();
    },
    // 改变页码
    changePage(v) {
      this.searchForm.pageNumber = v;
      this.getData();
    },
    // 改变页数
    changePageSize(v) {
      this.searchForm.pageSize = v;
      this.getData();
    },
    // 搜索
    handleSearch() {
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.getData();
    },
    // 重置搜索条件
    handleReset() {
      this.searchForm = {};
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.getData();
    },
    // 时间段从新赋值
    selectDateRange(v) {
      if (v) {
        this.searchForm.startDate = v[0];
        this.searchForm.endDate = v[1];
      }
    },
    // 获取数据
    getData() {
      this.loading = true;
      API_Order.receipt(this.searchForm).then((res) => {
        this.loading = false;
        if (res.success) {
          this.data = res.result.records;
          this.total = res.result.total;
        }
      });
      this.total = this.data.length;
      this.loading = false;
    },
    //开发票
    invoicing(params) {
      this.$Modal.confirm({
        title: "确认开票",
        content: "您确认已经开具发票 ?",
        loading: true,
        onOk: () => {
          API_Order.invoicing(params.id).then((res) => {
            if (res.success) {
              this.$Message.success("开票成功");
            }
            this.$Modal.remove();
            this.getData();
          });
        },
      });
    },
    previewBill(row) {
      // 处理文件回显
      if (row.fileUrl) {
        // 将多个 URL 拆分成数组
        const fileUrls = row.fileUrl
          .split(",")
          .filter((url) => url.trim() != "");
        console.log(fileUrls, "fileUrls");
        this.MultifileListData = fileUrls.map((url, index) => {
          const suffix = this.$options.filters.formatFileSuffix(url);

          let fileName = "";
          if (fileUrls.length > 1) {
            fileName = `${this.MultifilePlaybackTitle}${index + 1}${suffix}`;
          } else {
            fileName = `${this.MultifilePlaybackTitle}${suffix}`;
          }

          return {
            name: fileName, // 文件名
            url, // 文件 URL
            uid: -(index + 1), // 唯一标识，负数表示是回显的文件，每个文件有不同的 uid
            status: "done", // 文件状态为已完成
            response: { result: url }, // 模拟上传成功后的响应数据
          };
        });
      } else {
        this.MultifileListData = [];
      }
    },
    // 关闭预览,清空fileListData
    closePreview(data) {
      if (!data) {
        this.MultifileListData = [];
      }
    },
    // 开发票
    dialogInvoicing(id) {
      this.dialogVisible = true;
      this.form.id = id;
    },
    handleClose() {
      this.form = {
        id: "", // 订单id
        fileUrl: "", // 发票
      };
      this.fileListData = [];
      this.$refs.form.resetFields();
      this.dialogVisible = false;
    },
    handleBeforeUpload(file) {
      console.log(file, "---file");
      const type = file.type;
      const isOver50MB = file.size > 50 * 1024 * 1024;
      if (
        ["image/jpg", "image/jpeg", "image/png", "application/pdf"].includes(
          type
        )
      ) {
        if (isOver50MB) {
          this.$message.error("文件大小不能超过50M");
          return false;
        }
      } else {
        this.$message.error("文件格式错误");
        return false;
      }
    },
    handlePreview(file) {
      console.log(file, "--------------");
      const fileName = file.name;
      const lowerCaseFileName = fileName.toLowerCase();
      const targetUrl = file.response.result;
      if (lowerCaseFileName.endsWith(".pdf")) {
        this.fileUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      } else {
        this.imageUrl = targetUrl + "?time=" + new Date().getMilliseconds();
      }
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.fileListData = fileList;
      this.form.fileUrl = fileList;
    },
    handleSuccess(response, file, fileList) {
      console.log(response);
      this.fileListData = fileList;
      this.form.fileUrl = fileList;
    },
    handleSubmit(name) {
      this.$refs[name].validate(async (valid) => {
        if (valid) {
          let params = {
            fileUrl: this.form.fileUrl
              .map((item) => item.response.result)
              .join(","),
          };

          console.log(params, "params");

          const res = await API_Order.uploadFileUrl(this.form.id, params);

          if (res.success) {
            this.$Message.success("上传成功");
            this.handleClose();
            this.getData();
          }
        }
      });
    },
  },
  mounted() {
    this.accessToken = {
      accessToken: this.getStore("accessToken"),
    };
    this.init();
  },
};
</script>
<style lang="scss">
// 建议引入通用样式 可删除下面样式代码
@import "@/styles/table-common.scss";
</style>
