
export const modelData = [
  {
    type: 'hotAdvert',
    name: '热门广告',
    icon: 'md-image',
    showName: '',
    options: {
      list: [{
          img: require('@/assets/nav/decorate1.png'),
          url: '',
          size: '1200*自定义'
        },
        {
          img: require('@/assets/nav/1.jpg'),
          url: '',
          size: '230*190'
        },
        {
          img: require('@/assets/nav/1.jpg'),
          url: '',
          size: '230*190'
        },
        {
          img: require('@/assets/nav/1.jpg'),
          url: '',
          size: '230*190'
        },
        {
          img: require('@/assets/nav/1.jpg'),
          url: '',
          size: '230*190'
        },
        {
          img: require('@/assets/nav/1.jpg'),
          url: '',
          size: '230*190'
        }
      ],
    },
  },
  {
    type: 'seckill',
    name: '促销活动',
    icon: 'md-image',
    showName: '',
    options: {
      list: [{
          time: 6,
          goodsList: [{
              img: require('@/assets/nav/1.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafads123213a',
              url: ''
            },
            {
              img: require('@/assets/nav/2.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafadsa',
              url: ''
            },
            {
              img: require('@/assets/nav/3.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafadsa',
              url: ''
            },
            {
              img: require('@/assets/nav/4.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafadsa',
              url: ''
            },
            {
              img: require('@/assets/nav/5.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafadsa',
              url: ''
            },
            {
              img: require('@/assets/nav/1.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafadsa',
              url: ''
            },
            {
              img: require('@/assets/nav/2.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafadsa',
              url: ''
            },
            {
              img: require('@/assets/nav/3.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafadsa',
              url: ''
            },
            {
              img: require('@/assets/nav/4.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafadsa',
              url: ''
            },
            {
              img: require('@/assets/nav/5.jpg'),
              price: 20,
              originalPrice: 30,
              name: '阿迪达斯三叶草asdasdafadsa',
              url: ''
            },
          ]
        },
        {
          time: 8,
          goodsList: [{
              img: require('@/assets/nav/1.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/2.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/3.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/4.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/5.jpg'),
              url: ''
            },
          ]
        },
        {
          time: 10,
          goodsList: [{
              img: require('@/assets/nav/1.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/2.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/3.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/4.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/5.jpg'),
              url: ''
            },
          ]
        },
        {
          time: 12,
          goodsList: [{
              img: require('@/assets/nav/1.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/2.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/3.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/4.jpg'),
              url: ''
            },
            {
              img: require('@/assets/nav/5.jpg'),
              url: ''
            },
          ]
        },
        {
          time: 14,
          goodsList: []
        },
        {
          time: 16,
          goodsList: []
        },
        {
          time: 18,
          goodsList: []
        }

      ]
    },
  },
  {
    type: 'discountAdvert',
    name: '折扣广告',
    icon: 'md-image',
    options: {
      bgImg: {
        img: require('@/assets/nav/decorate.png'),
        url: '',
        size: "1300*586"
      },
      classification: [{
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, {
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, {
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, {
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, {
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, {
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, {
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, {
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, {
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, {
        img: require('@/assets/nav/decorate2.jpeg'),
        url: '',
        size: '190*210'
      }, ],
      brandList: [{
        img: require('@/assets/nav/decorate11.jpeg'),
        url: '',
        size: '240*105'
      }, {
        img: require('@/assets/nav/decorate11.jpeg'),
        url: '',
        size: '240*105'
      }, {
        img: require('@/assets/nav/decorate11.jpeg'),
        url: '',
        size: '240*105'
      }, {
        img: require('@/assets/nav/decorate11.jpeg'),
        url: '',
        size: '240*105'
      }, ]
    },
  },
  {
    type: 'recommend',
    name: '好货推荐',
    icon: 'md-image',
    options: {
      contentLeft: {
        title: '发现好货',
        secondTitle: '更多好货',
        bgColor: '#449dae',
        url: '',
        list: [{
            img: require('@/assets/nav/decorate3.jpeg'),
            name: '阿迪达斯三叶草',
            describe: '也许是每一款经典系列都应该有一个独特的故事吧',
            url: '',
            size: '160*160'
          },
          {
            img: require('@/assets/nav/decorate4.jpeg'),
            name: '360行车记录',
            describe: '夜行 监控 电子狗 蓝牙',
            url: '',
            size: '80*80'
          },
          {
            img: require('@/assets/nav/decorate4.jpeg'),
            name: '360行车记录',
            describe: '夜行 监控 电子狗 蓝牙',
            url: '',
            size: '80*80'
          },
          {
            img: require('@/assets/nav/decorate4.jpeg'),
            name: '360行车记录',
            describe: '夜行 监控 电子狗 蓝牙',
            url: '',
            size: '80*80'
          },
          {
            img: require('@/assets/nav/decorate4.jpeg'),
            name: '360行车记录',
            describe: '夜行 监控 电子狗 蓝牙',
            url: '',
            size: '80*80'
          },
          {
            img: require('@/assets/nav/decorate4.jpeg'),
            name: '360行车记录',
            describe: '夜行 监控 电子狗 蓝牙',
            url: '',
            size: '80*80'
          },
          {
            img: require('@/assets/nav/decorate4.jpeg'),
            name: '360行车记录',
            describe: '夜行 监控 电子狗 蓝牙',
            url: '',
            size: '80*80'
          },
        ]
      },
      contentRight: {
        title: '特色推荐',
        secondTitle: '更多特色推荐',
        bgColor: '#a25684',
        url: '',
        list: [{
            img: require('@/assets/nav/decorate5.jpeg'),
            name: '好心情喝出来',
            describe: '遇见懂你的饮品',
            url: '',
            size: '100*100'
          },
          {
            img: require('@/assets/nav/decorate5.jpeg'),
            name: '好心情喝出来',
            describe: '遇见懂你的饮品',
            url: '',
            size: '100*100'
          },
          {
            img: require('@/assets/nav/decorate5.jpeg'),
            name: '好心情喝出来',
            describe: '遇见懂你的饮品',
            url: '',
            size: '100*100'
          },
          {
            img: require('@/assets/nav/decorate5.jpeg'),
            name: '好心情喝出来',
            describe: '遇见懂你的饮品',
            url: '',
            size: '100*100'
          },
        ]
      }
    },
  },
  {
    type: 'newGoodsSort',
    name: '新品排行',
    icon: 'md-image',
    options: {
      left: {
        title: '特卖',
        secondTitle: "更多特卖",
        bgColor: '#c43d7e',
        url: '',
        list: [{
            name: '新年心愿单',
            describe: '满269减50,满999减100',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "160*160"
          },
          {
            name: 'Ms.Maggie 冬季时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
          {
            name: 'Ms.Maggie 冬季时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
          {
            name: 'Ms.Maggie 冬季时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
          {
            name: '阿迪达斯 领跑时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
        ],
      },
      middle: {
        title: '新品',
        secondTitle: "更多新品",
        bgColor: '#e66a07',
        url: '',
        list: [{
            name: '阿迪达斯 领跑时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
          {
            name: '阿迪达斯 领跑时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
          {
            name: '阿迪达斯 领跑时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
          {
            name: '阿迪达斯 领跑时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
          {
            name: '阿迪达斯 领跑时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
          {
            name: '阿迪达斯 领跑时尚',
            describe: '满269减50',
            img: require('@/assets/nav/decorate6.jpeg'),
            url: '',
            size: "90*90"
          },
        ]
      },
      right: {
        title: '排行榜',
        secondTitle: "精品风向标",
        bgColor: '#b62323',
        url: '',
        list: [{
            name: '小米红米3s手机壳保护套红米3高配版指纹男女款潮版磨砂硬壳防摔 收藏截图 送大礼包',
            price: 14.9,
            img: require('@/assets/nav/decorate7.jpeg'),
            url: ''
          },
          {
            name: '小米红米3s手机壳保护套红米3高配版指纹男女款潮版磨砂硬壳防摔 收藏截图 送大礼包',
            price: 14.9,
            img: require('@/assets/nav/decorate7.jpeg'),
            url: ''
          },
          {
            name: '小米红米3s手机壳保护套红米3高配版指纹男女款潮版磨砂硬壳防摔 收藏截图 送大礼包',
            price: 14.9,
            img: require('@/assets/nav/decorate7.jpeg'),
            url: ''
          },
          {
            name: '小米红米3s手机壳保护套红米3高配版指纹男女款潮版磨砂硬壳防摔 收藏截图 送大礼包',
            price: 14.9,
            img: require('@/assets/nav/decorate7.jpeg'),
            url: ''
          },
          {
            name: '小米红米3s手机壳保护套红米3高配版指纹男女款潮版磨砂硬壳防摔 收藏截图 送大礼包',
            price: 14.9,
            img: require('@/assets/nav/decorate7.jpeg'),
            url: ''
          },
          {
            name: '小米红米3s手机壳保护套红米3高配版指纹男女款潮版磨砂硬壳防摔 收藏截图 送大礼包',
            price: 14.9,
            img: require('@/assets/nav/decorate7.jpeg'),
            url: ''
          },
        ]
      }
    },
  },
  {
    type: 'firstAdvert',
    name: '首页广告',
    icon: 'md-image',
    options: {
      list: [{
          name: '生鲜',
          describe: "年货带回家 满199减60",
          img: require('@/assets/nav/decorate8.png'),
          url: '',
          fromColor: '#e89621',
          toColor: "#f5c568",
          size: '170*170'
        },
        {
          name: '众筹',
          describe: "年货带回家",
          img: require('@/assets/nav/decorate9.png'),
          url: '',
          fromColor: "#325bb4",
          toColor: '#4c9afe',
          size: '170*170'
        },
        {
          name: '生鲜',
          describe: "年货带回家 满199减60",
          img: require('@/assets/nav/decorate8.png'),
          url: '',
          fromColor: "#1c9daf",
          toColor: '#40cda7',
          size: '170*170'
        },
        {
          name: '众筹',
          describe: "备孕有孕检测仪",
          img: require('@/assets/nav/decorate9.png'),
          url: '',
          fromColor: "#d13837",
          toColor: '#df6d4f',
          size: '170*170'
        },
        {
          name: '生鲜',
          describe: "年货带回家 满199减60",
          img: require('@/assets/nav/decorate8.png'),
          url: '',
          fromColor: "#ca4283",
          toColor: '#eb75cf',
          size: '170*170'
        },
        {
          name: '众筹',
          describe: "备孕有孕检测仪",
          img: require('@/assets/nav/decorate9.png'),
          url: '',
          fromColor: "#5d40c1",
          toColor: '#8c5fdb',
          size: '170*170'
        },
      ],
    },
  },
  {
    type: 'bannerAdvert',
    name: '横幅广告',
    icon: 'md-image',
    options: {
      img: '',
      url: '',
      size: '1200*自定义'
    },
  },
  {
    type: 'notEnough',
    name: '还没逛够',
    icon: 'md-image',
    options: {
      list: [
        [{
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
        ],
        [{
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
        ],
        [{
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
        ],
        [{
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
          {
            img: require('@/assets/nav/decorate10.jpeg'),
            name: 'Apple/苹果 13 英寸：MacBook Pro Multi-Touch Bar 和 Touch ID 2.9GHz 处理器 512GB 存储容量',
            price: 6666,
            url: ''
          },
        ],

      ],
      navList: [{
          title: '精选',
          desc: '猜你喜欢'
        },
        {
          title: '智能先锋',
          desc: '大电器城'
        },
        {
          title: '居家优品',
          desc: '品质生活'
        },
        {
          title: '超市百货',
          desc: '百货生鲜'
        },
      ]
    },
  },
]
