<template>
  <div class="search">
    <Card>
      <Form
        ref="searchForm"
        :model="searchForm"
        inline
        :label-width="80"
        class="search-form"
      >
        <Form-item label="订单编号" prop="orderSn">
          <Input
            type="text"
            v-model="searchForm.orderSn"
            clearable
            placeholder="请输入订单编号"
            style="width: 240px"
          />
        </Form-item>
        <Form-item label="子订单编号" prop="orderItemSn">
          <Input
            type="text"
            v-model="searchForm.orderItemSn"
            clearable
            placeholder="请输入子订单编号"
            style="width: 240px"
          />
        </Form-item>
        <Form-item label="企业名称" prop="corpName">
          <Input
            type="text"
            v-model="searchForm.corpName"
            clearable
            placeholder="请输入企业名称"
            style="width: 240px"
          />
        </Form-item>
        <Form-item label="状态" prop="paymentStatus">
          <Select
            v-model="searchForm.paymentStatus"
            clearable
            placeholder="请选择"
            style="width: 240px"
          >
            <Option value="PAYMENT_UN_CONFIRM">待确认</Option>
            <Option value="PAYMENT_CONFIRM">已确认</Option>
            <Option value="PAYMENT_REFUSE">已拒绝</Option>
          </Select>
        </Form-item>
        <Button @click="handleSearch" type="primary" class="search-btn"
          >搜索</Button
        >
        <Button @click="handleReset" class="search-btn">重置</Button>
      </Form>
      <div class="export"></div>
      <Table
        :loading="loading"
        border
        :columns="columns"
        :data="data"
        ref="table"
      >
        <template slot="orderSlot" slot-scope="scope">
          <a
            @click="
              $router.push({
                name: 'order-detail',
                query: { sn: scope.row.orderSn },
              })
            "
            >{{ scope.row.orderSn }}</a
          >
        </template>
        <template slot-scope="{ row }" slot="pdfUrlSlot">
          <div class="preview-btn">
            <span class="mr_10 hover-pointer-color" @click="detail(row)"
              >查看</span
            >
            <span v-if="row.paymentStatus === 'PAYMENT_UN_CONFIRM'">
              <span
                class="mr_10 hover-pointer-color"
                @click="confirmPay(row, 'PAYMENT_CONFIRM')"
                >确认</span
              >
              <span
                class="hover-pointer-color_2"
                @click="confirmPay(row, 'PAYMENT_REFUSE')"
                >拒绝</span
              >
            </span>
          </div>
        </template>
      </Table>
      <Row type="flex" justify="end" class="mt_10">
        <Page
          :current="searchForm.pageNumber"
          :total="total"
          :page-size="searchForm.pageSize"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
          :page-size-opts="[10, 20, 50]"
          size="small"
          show-total
          show-elevator
          show-sizer
        ></Page>
      </Row>
    </Card>

    <!-- 多文件回显 -->
    <MultifilePlayback
      :fileListData="fileListData"
      @closePreview="closePreview"
      :title="MultifilePlaybackTitle"
    />

    <!-- 确认支付 -->
    <el-dialog
      :visible.sync="confirmVoucherVisible"
      append-to-body
      width="400px"
      title="确认凭证"
      @close="handleVoucherVisibleClose"
    >
      <div>
        <el-form
          ref="confirmVoucherForm"
          :model="confirmVoucherForm"
          label-position="left"
          label-width="auto"
          :rules="confirmVoucherValidate"
        >
          <el-form-item label="订单金额" prop="orderPrice">
            <el-input v-model="confirmVoucherForm.orderPrice" size="large">
              <span slot="append">元</span>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" style="text-align: right">
        <Button @click="handleVoucherVisibleClose" class="mr_20">关闭</Button>
        <Button :loading="loading" type="primary" @click="confirmVoucher"
          >确认</Button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import MultifilePlayback from "@/components/MultifilePlayback.vue";
import * as API_Shop from "@/api/shops";

export default {
  name: "contractList",
  components: {
    MultifilePlayback,
  },
  data() {
    return {
      confirmVoucherVisible: false, // 确认凭证
      confirmVoucherForm: {
        orderPrice: 0,
      },
      confirmVoucherData: {}, // 确认凭证信息
      //验证要确认凭证的订单金额
      confirmVoucherValidate: {
        orderPrice: [
          {
            required: true,
            message: "请输入大于 0 的合法金额，最多保留两位小数",
          },
          {
            pattern: /^([1-9]\d*|0\.\d[1-9]|[1-9]\d*\.\d{1,2}|0\.[1-9]\d?)$/,
            message: "请输入大于 0 的合法金额，最多保留两位小数",
            trigger: "change",
          },
        ],
      },
      loading: true, // 表单加载状态
      searchForm: {
        // 搜索框初始化对象
        pageNumber: 1, // 当前页数
        pageSize: 10, // 页面大小
      },

      fileListData: [], // 多文件回显
      MultifilePlaybackTitle: "支付凭证", // 多文件回显标题

      columns: [
        {
          title: "订单编号",
          key: "orderSn",
          width: 240,
          tooltip: true,
          slot: "orderSlot",
        },
        {
          title: "子订单编号",
          key: "orderItemSn",
          width: 240,
          tooltip: true,
        },
        {
          title: "企业名称",
          key: "corpName",
          minWidth: 200,
          tooltip: true,
        },
        // {
        //   title: "店铺名称",
        //   key: "storeName",
        //   width: 240,
        //   tooltip: true,
        // },
        {
          title: "商品名称",
          key: "goodsName",
          width: 240,
          tooltip: true,
        },
        {
          title: "应付金额(元)",
          key: "payableAmount",
          width: 160,
          tooltip: true,
        },
        {
          title: "实付金额(元)",
          key: "paidAmount",
          width: 160,
          tooltip: true,
        },
        {
          title: "状态",
          width: 120,
          key: "paymentStatus",
          render: (h, params) => {
            if (params.row.paymentStatus == "PAYMENT_UN_CONFIRM") {
              return h("div", [
                h("tag", { props: { color: "magenta" } }, "待确认"),
              ]);
            } else if (params.row.paymentStatus == "PAYMENT_CONFIRM") {
              return h("div", [
                h("tag", { props: { color: "blue" } }, "已确认"),
              ]);
            } else if (params.row.paymentStatus == "PAYMENT_REFUSE") {
              return h("div", [
                h("tag", { props: { color: "red" } }, "已拒绝"),
              ]);
            } else {
              return h("div", {}, params.row.paymentStatus);
            }
          },
        },
        // {
        //   title: "支付凭证",
        //   width: 120,
        //   slot: "paySlot",
        //   align: "center",
        // },
        {
          title: "操作人",
          key: "operateMemberName",
          width: 100,
          align: "center",
        },
        {
          title: "操作时间",
          key: "operateTime",
          width: 160,
          align: "center",
        },
        {
          title: "操作",
          key: "pdfUrl",
          width: 160,
          slot: "pdfUrlSlot",
          fixed: "right",
          align: "center",
        },
      ],
      data: [], // 表单数据
      total: 0, // 表单数据总数
    };
  },
  methods: {
    // 关闭确认支付弹窗
    handleVoucherVisibleClose() {
      this.confirmVoucherVisible = false;
      this.$refs.confirmVoucherForm.resetFields();
    },
    // 确认支付凭证金额
    async confirmVoucher() {
      this.$refs.confirmVoucherForm.validate(async (valid) => {
        if (valid) {
          const loading = this.$loading({
            lock: true,
            text: "请稍等...",
            spinner: "el-icon-loading",
            background: "rgba(0, 0, 0, 0.7)",
          });

          this.loading = true;
          let params = {
            ...this.confirmVoucherData,
            amount: this.confirmVoucherForm.orderPrice,
          };

          try {
            await this.confirmOrRefuseVoucher(params, loading);
            this.loading = false;
          } catch (error) {
            this.loading = false;
            loading.close();
          }
        }
      });
    },
    // 确认或拒绝支付凭证接口方法
    async confirmOrRefuseVoucher(params, loading) {
      const res = await API_Shop.updatePayRecords(params);

      if (res.success) {
        this.loading = false;
        await this.getDataList(loading);
        this.$message.success("操作成功");
        this.handleVoucherVisibleClose();
      } else {
        this.loading = false;
        loading.close();
      }
    },
    // 跳转到支付详情页面
    detail(row) {
      this.$router.push({
        name: "payment-list-detail",
        query: {
          id: row.id,
        },
      });
    },
    // 确认或拒绝支付凭证
    async confirmPay(row, paymentStatus) {
      let params = {
        orderItemSn: row.orderItemSn,
        paymentStatus,
        amount: "-1",
        payRecordsId: row.id,
      };
      if (paymentStatus == "PAYMENT_CONFIRM") {
        this.confirmVoucherVisible = true;
        this.confirmVoucherData = params;
      } else {
        this.$confirm("是否拒绝本次支付凭证？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(async () => {
            const loading = this.$loading({
              lock: true,
              text: "请稍等...",
              spinner: "el-icon-loading",
              background: "rgba(0, 0, 0, 0.7)",
            });
            await this.confirmOrRefuseVoucher(params, loading);
          })
          .catch(() => {});
      }
    },
    previewVoucher(row) {
      if (row.paymentVoucherList?.length) {
        // 将多个 URL 拆分成数组
        const fileUrls = row.paymentVoucherList.map(
          (item) => item.paymentVoucherUrl
        );
        this.fileListData = fileUrls.map((url, index) => {
          const suffix = this.$options.filters.formatFileSuffix(url);

          let fileName = "";
          if (fileUrls.length > 1) {
            fileName = `${this.MultifilePlaybackTitle}${index + 1}${suffix}`;
          } else {
            fileName = `${this.MultifilePlaybackTitle}${suffix}`;
          }

          return {
            name: fileName, // 文件名
            url, // 文件 URL
            uid: -(index + 1), // 唯一标识，负数表示是回显的文件，每个文件有不同的 uid
            status: "done", // 文件状态为已完成
            response: { result: url }, // 模拟上传成功后的响应数据
          };
        });
      } else {
        this.fileListData = [];
        this.$message.error("该订单买家没有上传支付凭证");
      }
    },
    // 关闭预览,清空fileListData
    closePreview(data) {
      if (!data) {
        this.fileListData = [];
      }
    },
    // 初始化数据
    init() {
      this.getDataList();
    },
    // 清理搜索表单中的空值
    cleanSearchForm() {
      const fields = ["orderSn", "orderItemSn", "corpName", "paymentStatus"];
      fields.forEach((field) => {
        if (!this.searchForm[field]) {
          delete this.searchForm[field];
        }
      });
    },
    // 改变页码
    changePage(v) {
      this.searchForm.pageNumber = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 改变页数
    changePageSize(v) {
      this.searchForm.pageSize = v;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 搜索订单
    handleSearch() {
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 重置
    handleReset() {
      this.searchForm = {
        pageNumber: 1,
        pageSize: 10,
        orderSn: "",
        orderItemSn: "",
        corpName: "",
        paymentStatus: "",
      };
      // 重新加载数据
      this.cleanSearchForm();
      this.getDataList();
    },
    // 获取表格数据
    getDataList(loading) {
      this.loading = true;
      API_Shop.pageByParams(this.searchForm).then((res) => {
        this.loading = false;
        if (res.success) {
          this.data = res.result.records.map((item) => {
            return {
              ...item,
              paidAmount: item.paidAmount ? item.paidAmount : "--",
              payableAmount: item.payableAmount ? item.payableAmount : 0,
              operateMemberName: item.operateMemberName
                ? item.operateMemberName
                : "--",
              operateTime: item.operateTime ? item.operateTime : "--",
            };
          });
          this.total = res.result.total;

          // 确认凭证的loading
          loading && loading.close();
        }
      });
    },
  },
  mounted() {
    this.init();
  },
  // 页面缓存处理，从该页面离开时，修改KeepAlive为false，保证进入该页面是刷新
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false;
    next();
  },
};
</script>
<style lang="scss">
// 建议引入通用样式 可删除下面样式代码
@import "@/styles/table-common.scss";
.export {
  margin: 10px 20px 10px 0;
}
</style>
