<template>
  <div class="payment-detail">
    <!-- 页面头部 -->
    <div class="page-header">
      <!-- <Button @click="goBack" icon="ios-arrow-back" type="text" size="large">
        返回
      </Button> -->
      <h2>支付详情</h2>
    </div>

    <Card>
      <!-- 基本信息 -->
      <div class="detail-section">
        <h3 class="section-title">基本信息</h3>
        <Row :gutter="24">
          <Col span="12">
            <div class="info-item">
              <span class="label">订单编号：</span>
              <span class="value">
                <a
                  @click="
                    $router.push({
                      name: 'order-detail',
                      query: { sn: paymentDetail.orderSn },
                    })
                  "
                >
                  {{ paymentDetail.orderSn }}
                </a>
              </span>
            </div>
          </Col>
          <Col span="12">
            <div class="info-item">
              <span class="label">子订单编号：</span>
              <span class="value">{{ paymentDetail.orderItemSn }}</span>
            </div>
          </Col>
          <Col span="12">
            <div class="info-item">
              <span class="label">企业名称：</span>
              <span class="value">{{ paymentDetail.corpName }}</span>
            </div>
          </Col>
          <Col span="12">
            <div class="info-item">
              <span class="label">商品名称：</span>
              <span class="value">{{ paymentDetail.goodsName }}</span>
            </div>
          </Col>
        </Row>
      </div>
    </Card>
    <Card class="mt_10">
      <!-- 支付信息 -->
      <div class="detail-section">
        <h3 class="section-title">支付信息</h3>
        <Row :gutter="24">
          <Col span="12">
            <div class="info-item">
              <span class="label">订单金额：</span>
              <span class="value amount">
                ¥ {{ paymentDetail.orderItemAmount }}</span
              >

              <span v-if="paymentDetail.priceDetailDTO?.freightPrice"
                >（含运费：¥{{
                  paymentDetail.priceDetailDTO?.freightPrice
                }}）</span
              >
            </div>
          </Col>
          <Col span="12">
            <div class="info-item">
              <span class="label">已付金额：</span>
              <span class="value amount"
                >¥ {{ paymentDetail.paidAmount || "--" }}</span
              >
            </div>
          </Col>
          <Col span="12">
            <div class="info-item">
              <span class="label">支付状态：</span>
              <Tag :color="getStatusColor(paymentDetail.paymentStatus)">
                {{ getStatusText(paymentDetail.paymentStatus) }}
              </Tag>
            </div>
          </Col>
          <Col span="12">
            <div class="info-item">
              <span class="label">支付方式：</span>
              <span class="value">{{
                getPaymentMethodText(paymentDetail.paymentMethod)
              }}</span>
            </div>
          </Col>
        </Row>
      </div>
    </Card>
    <Card class="mt_10">
      <!-- 支付凭证 -->
      <div class="detail-section">
        <h3 class="section-title">支付凭证</h3>
        <div class="voucher-section">
          <div
            v-if="
              paymentDetail.paymentVoucherUrlList &&
              paymentDetail.paymentVoucherUrlList.length > 0
            "
          >
            <div class="voucher-list">
              <div
                v-for="(
                  voucherUrl, index
                ) in paymentDetail.paymentVoucherUrlList"
                :key="index"
                class="voucher-item"
              >
                <div class="voucher-info">
                  <Icon type="ios-document" size="20" />
                  <span>支付凭证{{ index + 1 }}</span>
                </div>
                <div class="voucher-actions">
                  <Button
                    type="primary"
                    size="small"
                    @click="previewVoucher(voucherUrl)"
                  >
                    查看
                  </Button>
                  <Button
                    type="default"
                    size="small"
                    @click="downloadVoucher(voucherUrl, `支付凭证${index + 1}`)"
                  >
                    下载
                  </Button>
                </div>
              </div>
            </div>
            <div class="voucher-preview-btn">
              <Button type="dashed" @click="previewAllVouchers">
                <Icon type="ios-eye" />
                预览所有凭证
              </Button>
            </div>
          </div>
          <div v-else class="no-voucher">
            <Icon type="ios-information-circle" size="24" color="#c5c8ce" />
            <span>暂无支付凭证</span>
          </div>
        </div>
      </div>
    </Card>
    <Card class="mt_10">
      <!-- 操作记录 -->
      <div class="detail-section">
        <h3 class="section-title">操作记录</h3>
        <Row :gutter="24">
          <Col span="12">
            <div class="info-item">
              <span class="label">操作人：</span>
              <span class="value">{{
                paymentDetail.operateMemberName || "--"
              }}</span>
            </div>
          </Col>
          <Col span="12">
            <div class="info-item">
              <span class="label">操作时间：</span>
              <span class="value">{{ paymentDetail.operateTime || "--" }}</span>
            </div>
          </Col>
        </Row>
      </div>
    </Card>

    <!-- 多文件预览组件 -->
    <MultifilePlayback
      :fileListData="fileListData"
      @closePreview="closePreview"
      :title="'支付凭证'"
    />
  </div>
</template>

<script>
import MultifilePlayback from "@/components/MultifilePlayback.vue";
import * as API_Shop from "@/api/shops";

export default {
  name: "PaymentListDetail",
  components: {
    MultifilePlayback,
  },
  data() {
    return {
      // 多文件预览数据
      fileListData: [],

      // 支付详情静态数据
      paymentDetail: {},
    };
  },
  methods: {
    // 返回列表页
    goBack() {
      this.$router.go(-1);
    },

    // 获取支付状态颜色
    getStatusColor(status) {
      const colorMap = {
        PAYMENT_UN_CONFIRM: "orange",
        PAYMENT_CONFIRM: "green",
        PAYMENT_REFUSE: "red",
      };
      return colorMap[status] || "default";
    },

    // 获取支付状态文本
    getStatusText(status) {
      const textMap = {
        PAYMENT_UN_CONFIRM: "待确认",
        PAYMENT_CONFIRM: "已确认",
        PAYMENT_REFUSE: "已拒绝",
      };
      return textMap[status] || status;
    },
    // 获取支付方式文本
    getPaymentMethodText(status) {
      const textMap = {
        PAYMENT_VOUCHER: "线下支付",
        FINANCING: "融资支付",
      };
      return textMap[status] || "--";
    },

    // 获取时间轴颜色
    getTimelineColor(type) {
      const colorMap = {
        create: "blue",
        upload: "green",
        pending: "orange",
        confirm: "green",
        refuse: "red",
      };
      return colorMap[type] || "blue";
    },

    // 预览单个凭证
    previewVoucher(url) {
      this.fileListData = [
        {
          name: "支付凭证.jpg",
          url: url,
          uid: -1,
          status: "done",
          response: { result: url },
        },
      ];
    },

    // 预览所有凭证
    previewAllVouchers() {
      if (
        this.paymentDetail.paymentVoucherUrlList &&
        this.paymentDetail.paymentVoucherUrlList.length > 0
      ) {
        this.fileListData = this.paymentDetail.paymentVoucherUrlList.map(
          (voucherUrl, index) => ({
            name: `支付凭证${index + 1}.jpg`,
            url: voucherUrl,
            uid: -(index + 1),
            status: "done",
            response: { result: voucherUrl },
          })
        );
      }
    },

    // 下载凭证
    downloadVoucher(url, fileName) {
      const link = document.createElement("a");
      link.href = url;
      link.download = fileName;
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    // 关闭预览
    closePreview() {
      this.fileListData = [];
    },

    // 详情数据
    async getDetail(id) {
      const res = await API_Shop.detail(id);
      if (res.success) {
        this.paymentDetail = res.result;
      }
    },
    // 初始化数据
    init() {
      // 获取路由参数
      const { id } = this.$route.query;

      if (id) {
        // 这里后续可以根据ID调用API获取真实数据
        this.getDetail(id);
      }
    },
  },

  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.payment-detail {
  // padding: 20px;
  background-color: #f5f5f5;
  // min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  background: white;
  padding: 16px 0;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h2 {
    margin: 0 0 0 16px;
    color: #333;
    font-size: 20px;
    font-weight: 600;
  }
}

.detail-section {
  margin-bottom: 32px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;

  .label {
    font-weight: 500;
    color: #666;
    min-width: 80px;
    margin-right: 8px;
  }

  .value {
    color: #333;

    &.amount {
      font-weight: 600;
      color: #f56c6c;
      font-size: 16px;
    }
  }
}

.voucher-section {
  .voucher-list {
    margin-bottom: 16px;
  }

  .voucher-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #fafafa;
    border-radius: 6px;
    margin-bottom: 8px;

    .voucher-info {
      display: flex;
      align-items: center;

      span {
        margin-left: 8px;
        color: #333;
      }
    }

    .voucher-actions {
      display: flex;
      gap: 8px;
    }
  }

  .voucher-preview-btn {
    text-align: center;
    padding: 16px 0;
    border-top: 1px dashed #e8e8e8;
  }

  .no-voucher {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 0;
    color: #999;

    span {
      margin-top: 8px;
    }
  }
}

.timeline-content {
  .timeline-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
  }

  .timeline-desc {
    color: #666;
    margin-bottom: 8px;
  }

  .timeline-time {
    color: #999;
    font-size: 12px;
    margin-bottom: 4px;
  }

  .timeline-operator {
    color: #999;
    font-size: 12px;
  }
}

.action-section {
  text-align: center;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;

  .ivu-btn {
    margin: 0 8px;
    min-width: 100px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .payment-detail {
    padding: 10px;
  }

  .page-header {
    padding: 12px 16px;

    h2 {
      font-size: 18px;
    }
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;

    .label {
      min-width: auto;
      margin-bottom: 4px;
    }
  }

  .voucher-item {
    flex-direction: column;
    align-items: flex-start;

    .voucher-actions {
      margin-top: 8px;
      width: 100%;
      justify-content: flex-end;
    }
  }
}
</style>
