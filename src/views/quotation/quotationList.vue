<template>
  <div class="search">
    <Card>
      <Form
        ref="searchForm"
        :model="searchForm"
        inline
        :label-width="70"
        class="search-form"
      >
        <Form-item label="商品名称" prop="commodityListName">
          <Input
            type="text"
            v-model="searchForm.commodityListName"
            clearable
            placeholder="请输入商品名称"
            style="width: 160px"
          />
        </Form-item>
        <Form-item label="询价企业" prop="corpName">
          <Input
            type="text"
            v-model="searchForm.corpName"
            clearable
            placeholder="请输入询价企业"
            style="width: 160px"
          />
        </Form-item>
        <Form-item label="状态" prop="status">
          <Select
            v-model="searchForm.status"
            placeholder="请选择"
            clearable
            style="width: 160px"
          >
            <!-- <Option value="0">待报价</Option> -->
            <Option value="1">已报价</Option>
            <Option value="2">已确认</Option>
          </Select>
        </Form-item>
        <Button @click="handleSearch" type="primary" class="search-btn"
          >搜索</Button
        >
        <Button @click="handleReset" class="search-btn">重置</Button>
      </Form>
      <div class="export"></div>
      <Table
        :loading="loading"
        border
        :columns="columns"
        :data="data"
        ref="table"
      >
        <template slot="quotaSlot" slot-scope="{ row }">
          <div
            style="color: #409eff; cursor: pointer"
            @click="handleQuotaDetail(row)"
          >
            报价详情
          </div>
        </template>
      </Table>
      <Row type="flex" justify="end" class="mt_10">
        <Page
          :current="searchForm.pageNumber"
          :total="total"
          :page-size="searchForm.pageSize"
          @on-change="changePage"
          @on-page-size-change="changePageSize"
          :page-size-opts="[10, 20, 50]"
          size="small"
          show-total
          show-elevator
          show-sizer
        ></Page>
      </Row>
    </Card>

    <!-- 报价详情 -->
    <el-dialog
      title="报价详情"
      append-to-body
      :visible.sync="visible"
      width="60%"
      class=""
      @close="handleClose"
    >
      <el-table :data="commodityPriceQuoteList" border style="width: 100%">
        <el-table-column
          prop="createTime"
          label="报价时间"
          minWidth="170px"
        ></el-table-column>
        <el-table-column
          prop="supplierName"
          label="供应商名称"
          minWidth="360px"
        ></el-table-column>
        <el-table-column
          prop="commodityListName"
          label="商品名称"
          minWidth="180px"
        ></el-table-column>
        <el-table-column
          prop="commoditySpecName"
          label="规格型号"
          minWidth="80px"
        ></el-table-column>
        <el-table-column
          prop="deliveryQuantity"
          label="交货数量"
          minWidth="80px"
        ></el-table-column>
        <el-table-column
          prop="supplierQuotationAmount"
          label="报价金额（元）"
          minWidth="120px"
        ></el-table-column>
        <el-table-column
          prop="settlementModel"
          label="结算方式"
          minWidth="140px"
        >
          <template slot-scope="scope">
            <span>{{
              getLabelByValue(
                settlementModelDict,
                scope.row.settlementModel,
                "settlementModel",
                scope.row.earnestMoneyRatio
              ) || "--"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="deliveryModel" label="配送方式" minWidth="140px">
          <template slot-scope="scope">
            <span>{{
              getLabelByValue(
                deliveryModelDict,
                scope.row.deliveryModel,
                "deliveryModel",
              ) || "--"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="deliveryDate"
          label="交货时间"
          minWidth="140px"
        ></el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleClose">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { quotePage } from "@/api/quotation";
export default {
  name: "quotationList",
  data() {
    return {
      loading: true, // 表单加载状态
      searchForm: {
        // 搜索框初始化对象
        pageNumber: 1, // 当前页数
        pageSize: 10, // 页面大小
        commodityListName: "", // 商品名称
        corpName: "", // 询价企业
        status: "", // 状态
      },
      selectDate: null,
      columns: [
        {
          title: "询价时间",
          key: "createTime",
          minWidth: 160,
          tooltip: true,
        },
        {
          title: "商品名称",
          key: "commodityListName",
          minWidth: 200,
          tooltip: true,
        },
        {
          title: "询价企业",
          key: "customerCorpName",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "期望购买数量",
          key: "purchaseQuantity",
          minWidth: 120,
          tooltip: true,
        },
        {
          title: "交货方式",
          key: "tradeModel",
          minWidth: 100,
          render: (h, params) => {
            if (params.row.tradeModel == "1") {
              return h("div", {}, "现货交易");
            } else if (params.row.tradeModel == "2") {
              return h("div", {}, "期货交易");
            } else if (params.row.tradeModel == "3") {
              return h("div", {}, "混合交易");
            } else {
              return h("div", {}, params.row.tradeModel);
            }
          },
        },
        {
          title: "交货日期",
          key: "deliveryDate",
          minWidth: 100,
          tooltip: true,
        },
        {
          title: "交货地点",
          key: "deliveryPlace",
          minWidth: 240,
          tooltip: true,
        },
        {
          title: "报价",
          key: "quota",
          minWidth: 120,
          tooltip: true,
          slot: "quotaSlot",
        },
        {
          title: "状态",
          key: "status",
          minWidth: 100,
          render: (h, params) => {
            if (params.row.status == "0") {
              return h("div", [
                h("tag", { props: { color: "magenta" } }, "待报价"),
              ]);
            } else if (params.row.status == "1") {
              return h("div", [
                h("tag", { props: { color: "cyan" } }, "已报价"),
              ]);
            } else if (params.row.status == "2") {
              return h("div", [
                h("tag", { props: { color: "green" } }, "已确认"),
              ]);
            } else {
              return h("div", {}, params.row.status);
            }
          },
        },
      ],
      data: [], // 表单数据
      total: 0, // 表单数据总数
      commodityPriceQuoteList: [], // 报价详情
      visible: false, // 报价详情
      deliveryModelDict: [
        {
          dictKey: 1,
          dictValue: "买方自提",
        },
        {
          dictKey: 2,
          dictValue: "供方承运",
        },
      ], // 交货方式字典
      settlementModelDict: [
        {
          dictKey: 1,
          dictValue: "定金发货",
        },
        {
          dictKey: 2,
          dictValue: "全额付款",
        },
        {
          dictKey: 3,
          dictValue: "分期付款",
        },
      ], // 结算方式字典
    };
  },
  methods: {
    /**
     * 根据 value 获取对应的 label
     * @param {Array} dict - 字典数据数组
     * @param {any} value - 要查找的 value
     * @returns {string} - 对应的 label，如果未找到则返回空字符串
     */
    getLabelByValue(dict, value, name, earnestMoneyRatio) {
      const item = dict.find((item) => item.dictKey == value);

      if (name == "settlementModel" && value == 1) {
        return item ? `${item.dictValue}（${earnestMoneyRatio}%）` : "";
      } else {
        return item ? item.dictValue : "";
      }
    },
    handleClose() {
      this.visible = false;
      this.commodityPriceQuoteList = [];
    },
    // 报价详情
    handleQuotaDetail(row) {
      this.commodityPriceQuoteList = row.commodityPriceQuoteList;
      this.visible = true;
    },
    // 初始化数据
    init() {
      this.cleanSearchForm();
      this.getDataList();
    },
    // 清理搜索表单中的空值
    cleanSearchForm() {
      const fields = ["commodityListName", "corpName", "status"];
      fields.forEach((field) => {
        if (!this.searchForm[field]) {
          delete this.searchForm[field];
        }
      });
    },

    changePage(v) {
      this.searchForm.pageNumber = v;
      this.cleanSearchForm();
      this.getDataList();
    },

    // 改变页数
    changePageSize(v) {
      this.searchForm.pageSize = v;
      this.cleanSearchForm();
      this.getDataList();
    },

    // 搜索订单
    handleSearch() {
      this.searchForm.pageNumber = 1;
      this.searchForm.pageSize = 10;
      this.cleanSearchForm();
      this.getDataList();
    },
    // 重置
    handleReset() {
      this.searchForm = {
        pageNumber: 1,
        pageSize: 10,
        commodityListName: "",
        corpName: "",
        status: "",
      };
      this.handleSearch();
    },
    // 获取表格数据
    getDataList() {
      this.loading = true;
      quotePage({ ...this.searchForm }).then((res) => {
        this.loading = false;
        if (res.success) {
          this.data = res.result.records;
          this.total = res.result.total;
        }
      });
    },
  },
  mounted() {
    this.init();
  },
  // 页面缓存处理，从该页面离开时，修改KeepAlive为false，保证进入该页面是刷新
  beforeRouteLeave(to, from, next) {
    from.meta.keepAlive = false;
    next();
  },
};
</script>
<style lang="scss">
// 建议引入通用样式 可删除下面样式代码
@import "@/styles/table-common.scss";
.export {
  margin: 10px 20px 10px 0;
}
</style>
