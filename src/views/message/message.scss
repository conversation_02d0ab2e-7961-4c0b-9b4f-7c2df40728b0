.message-main-con{
  min-height: calc(100vh - 120px);
}
.message {
    &-main-con {
        position: absolute;
        left: 0px;
        top: 0px;
        right: 0px;
        bottom: 0px;
    }
    &-mainlist-con {
        position: absolute;
        left: 0;
        top: 10px;
        width: 300px;
        bottom: 0;
        padding: 10px 0;
        div {
            padding: 10px;
            margin: 0 20px;
            border-bottom: 1px dashed #d2d3d2;
            &:last-child {
                border: none;
            }
            .mes-wrap {
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .message-count-badge-outer {
                margin-left: 5px;
            }
            .message-count-badge {
                background: #d2d3d2;
            }
            .message-count-badge-red {
                background: #ed3f14;
            }
            .mes-type-btn-text {
                margin-left: 10px;
            }
        }
    }
    &-content-con {
        position: absolute;
        top: 10px;
        right: 10px;
        bottom: 10px;
        left: 300px;
        background: white;
        border-radius: 3px;
        box-shadow: 2px 2px 10px 2px rgba(0, 0, 0, .1);
        overflow: auto;
        .message-title-list-con {
            width: 100%;
            height: 100%;
        }
        .message-content-top-bar {
            height: 40px;
            width: 100%;
            background: white;
            position: absolute;
            left: 0;
            top: 0;
            border-bottom: 1px solid #dededb;
            .mes-back-btn-con {
                position: absolute;
                width: 70px;
                height: 30px;
                left: 0;
                top: 5px;
            }
            .mes-title {
                position: absolute;
                top: 0;
                right: 70px;
                bottom: 0;
                left: 70px;
                line-height: 40px;
                padding: 0 30px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                text-align: center;
            }
        }
        .mes-time-con {
            position: absolute;
            width: 100%;
            top: 40px;
            left: 0;
            padding: 20px 0;
            text-align: center;
            font-size: 14px;
            color: #b7b7b5;
        }
        .message-content-body {
            position: absolute;
            top: 90px;
            right: 0;
            bottom: 0;
            left: 0;
            overflow: auto;
            .message-content {
                padding: 10px 20px;
            }
        }
    }
}

.page-fix {
    position: fixed;
    right: 2vw;
    bottom: 3vh;
}

.back-message-list-enter,
    .back-message-list-leave-to {
        opacity: 0;
    }

    .back-message-list-enter-active,
    .back-message-list-leave-active {
        transition: all .5s;
    }

    .back-message-list-enter-to,
    .back-message-list-leave {
        opacity: 1;
    }

    .view-message-enter,
    .view-message-leave-to {
        opacity: 0;
    }

    .view-message-enter-active,
    .view-message-leave-active {
        transition: all .5s;
    }

    .view-message-enter-to,
    .view-message-leave {
        opacity: 1;
    }

    .mes-current-type-btn-enter,
    .mes-current-type-btn-leave-to {
        opacity: 0;
        width: 0;
    }

    .mes-current-type-btn-enter-active,
    .mes-current-type-btn-leave-active {
        transition: all .3s;
    }

    .mes-current-type-btn-enter-to,
    .mes-current-type-btn-leave {
        opacity: 1;
        width: 12px;
    }

