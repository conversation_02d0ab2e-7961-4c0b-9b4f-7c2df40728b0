# LILISHOP-UI

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Run your tests
```
npm run test
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
详情点击 [https://cli.vuejs.org/zn/config/](https://cli.vuejs.org/zn/config/).

####  login.vue页面，测试时不走权限，直接return  318行
####  Main.vue 页面，241行，修改，避免报错

#### main-parts  头部，左侧所有内容

#### decoration  楼层装修模块
     
