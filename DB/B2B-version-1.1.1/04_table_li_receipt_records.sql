
-- 开票记录表
DROP TABLE IF EXISTS `li_receipt_records`;
CREATE TABLE `li_receipt_records` (
`id` bigint NOT NULL COMMENT 'ID',
`create_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '创建者',
`create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
`delete_flag` bit(1) DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
`update_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '更新者',
`update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
`receipt_id` varchar(255) DEFAULT NULL COMMENT '发票id',
`file_url` varchar(255) DEFAULT NULL,
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='开票记录表';
