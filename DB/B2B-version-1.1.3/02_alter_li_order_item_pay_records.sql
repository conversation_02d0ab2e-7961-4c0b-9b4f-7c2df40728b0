-- 子订单付款记录新增字段
ALTER TABLE `li_order_item_pay_records`
ADD COLUMN `back_paid_sn` varchar(50) NULL COMMENT '付款通知回调的唯一编号' AFTER `payment_method`,
ADD COLUMN `operate_member_id` varchar(50) NULL COMMENT '操作确认人' AFTER `back_paid_sn`,
ADD COLUMN `operate_member_name` varchar(255) NULL COMMENT '操作人名称' AFTER `operate_member_id`,
ADD COLUMN `operate_time` datetime(6) NULL COMMENT '操作时间' AFTER `operate_member_name`;
ADD COLUMN `payable_amount` decimal(20, 2) NULL COMMENT '应付金额' AFTER `payment_status`;