
-- 新增字段
ALTER TABLE `li_order_item`
ADD COLUMN `apply_invoicing_status` varchar(50) NULL COMMENT '申请开票状态' AFTER `item_payment_method`;

-- 刷历史数据脚本
-- 1. 首先将所有订单项的开票状态设置为未申请（如果为空的话）
UPDATE li_order_item
SET apply_invoicing_status = 'NOT_APPLIED'
WHERE apply_invoicing_status IS NULL;

-- 2. 更新已申请发票但未开票的订单项状态
UPDATE li_order_item
SET apply_invoicing_status = 'APPLIED'
WHERE sn IN (
    SELECT order_item_sn FROM li_receipt
    WHERE receipt_status = 0  -- 未开具发票状态
);

-- 3. 更新已开票的订单项状态
UPDATE li_order_item
SET apply_invoicing_status = 'INVOICED'
WHERE sn IN (
    SELECT order_item_sn FROM li_receipt
    WHERE receipt_status = 1  -- 已开具发票状态
);