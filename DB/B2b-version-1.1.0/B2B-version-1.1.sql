-- 子订表结构新增字段
ALTER TABLE `li_order_item`
    ADD COLUMN `change_url` varchar(255) NULL COMMENT '调整附件' AFTER `change_desc`,
    ADD COLUMN `earnest_money_ratio` decimal(6, 2) NULL COMMENT '定金比例 如20% 值为20' AFTER `change_url`,
    ADD COLUMN `first_payment_voucher_url` varchar(255) NULL COMMENT '一次凭证url 如果是定金即为定金凭证url, 如果是全款即为全款支付凭证' AFTER `earnest_money_ratio`,
    ADD COLUMN `balance_payment_voucher_url` varchar(255) NULL COMMENT '尾款凭证url' AFTER `first_payment_voucher_url`,
    ADD COLUMN `item_payment_status` varchar(50) NULL COMMENT '子订单支付状态' AFTER `balance_payment_voucher_url`;
-- 询价表新增字段
ALTER TABLE `jrzh_commodity_price_inquiry`
    ADD COLUMN `earnest_money_ratio` decimal(5, 2) NULL DEFAULT NULL COMMENT '定金比例 如20% 传入20' AFTER `quote_supplier_store_ids`;

-- 报价表新增字段
ALTER TABLE `jrzh_commodity_price_quote`
    ADD COLUMN `earnest_money_ratio` decimal(6, 2) NULL COMMENT '定金比例 如20% 传入20' AFTER `commodity_spec_id`;

-- 结算单新增字段
ALTER TABLE `li_order_item_settlement`
    ADD COLUMN `settlement_sourc` varchar(20) NULL COMMENT '结算单来源' AFTER `pdf_url`;

ALTER TABLE `li_order`
    MODIFY COLUMN `payment_voucher_url` text CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL COMMENT '支付凭证url' AFTER `settlement_model`;