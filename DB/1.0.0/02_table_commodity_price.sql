
-- 询价表
CREATE TABLE `jrzh_commodity_price_inquiry`
(
    `id`                       bigint NOT NULL COMMENT '主键id',
    `create_by`                varchar(255)                                                  DEFAULT NULL COMMENT '创建人',
    `create_time`              datetime(6) DEFAULT NULL COMMENT '创建时间',
    `update_by`                varchar(255)                                                  DEFAULT NULL COMMENT '更新人',
    `update_time`              datetime(6) DEFAULT NULL COMMENT '更新时间',
    `status`                   int                                                           DEFAULT '0' COMMENT '询价状态 0-询价中 1-已报价 2-已确认报价',
    `delete_flag`              bit(1)                                                        DEFAULT b'0' COMMENT '删除标志 true/false 删除/未删除',
    `commodity_list_name`      varchar(255)                                                  DEFAULT NULL COMMENT '商品名称',
    `commodity_spec`           varchar(50)                                                   DEFAULT NULL COMMENT '规格型号',
    `commodity_list_id`        bigint                                                        DEFAULT NULL COMMENT '商品id',
    `customer_id`              varchar(20)                                                   DEFAULT NULL COMMENT '融资用户（询价用户）',
    `purchase_quantity`        int                                                           DEFAULT NULL COMMENT '期望购买数量',
    `delivery_date`            date                                                          DEFAULT NULL COMMENT '交货日期',
    `trade_model`              tinyint                                                       DEFAULT NULL COMMENT '交货方式 1-现货交易 2-期货交易 3-混合交易',
    `delivery_place`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '交货地点',
    `price_quote_end_date`     date                                                          DEFAULT NULL COMMENT '报价截止时间',
    `delivery_model`           tinyint                                                       DEFAULT NULL COMMENT '配送方式 1-买方自提 2-供方承运',
    `detailed_sescription`     varchar(255)                                                  DEFAULT NULL COMMENT '询价详情描述',
    `attch_ids`                varchar(255)                                                  DEFAULT NULL COMMENT '附件id',
    `settlement_model`         tinyint                                                       DEFAULT NULL COMMENT '结算方式 1-定金发货 2-全额付款 3-分期付款',
    `contact_name`             varchar(50)                                                   DEFAULT NULL COMMENT '联系人姓名',
    `contact_phone`            varchar(20)                                                   DEFAULT NULL COMMENT '手机号',
    `quote_supplier_store_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '已报价供应商ids',
    PRIMARY KEY (`id`),
    KEY                        `idx_commodity_list` (`commodity_list_id`) USING BTREE,
    KEY                        `idx_customer` (`customer_id`) USING BTREE
) ENGINE=InnoDB COMMENT='商品询价表';

-- 报价表
CREATE TABLE `jrzh_commodity_price_quote`
(
    `id`                         bigint NOT NULL COMMENT '主键id',
    `create_by`                  varchar(255)                                                 DEFAULT NULL COMMENT '创建人',
    `create_time`                datetime                                                     DEFAULT NULL COMMENT '创建时间',
    `update_by`                  varchar(255)                                                 DEFAULT NULL COMMENT '更新人',
    `update_time`                datetime                                                     DEFAULT NULL COMMENT '更新时间',
    `status`                     int                                                          DEFAULT '0' COMMENT '报价状态 1-已报价 2-已确认报价',
    `delete_flag`                bit(1)                                                       DEFAULT b'0' COMMENT '删除标志 true/false 删除/未删除',
    `commodity_price_inquiry_id` bigint                                                       DEFAULT NULL COMMENT '报价记录id',
    `supplier_id`                varchar(20)                                                  DEFAULT NULL COMMENT '店铺id',
    `supplier_name`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商机构名称',
    `supplier_quotation_amount`  decimal(18, 2)                                               DEFAULT NULL COMMENT '供应商报价金额',
    `delivery_quantity`          int                                                          DEFAULT NULL COMMENT '商品交货数量',
    `delivery_date`              date                                                         DEFAULT NULL COMMENT '交货时间',
    `delivery_model`             tinyint                                                      DEFAULT NULL COMMENT '配送方式 1-买方自提 2-供方承运',
    `settlement_model`           tinyint                                                      DEFAULT NULL COMMENT '结算方式 1-定金发货 2-全额付款 3-分期付款',
    `commodity_list_id`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '供应商商品id，对应li_goods表的id',
    `commodity_spec_id`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品规格id，对应li_goods_sku表的id',
    PRIMARY KEY (`id`),
    KEY                          `idx_inquiry_id` (`commodity_price_inquiry_id`) USING BTREE
) ENGINE=InnoDB COMMENT='供应商报价表';

-- 配置表新增
INSERT INTO `lilishop`.`li_setting`(`id`, `create_by`, `create_time`, `delete_flag`, `update_by`, `update_time`,
                                    `setting_value`)
VALUES ('PDF_TEMPLATE', 'admin', '2025-05-08 17:59:37.000000', NULL, 'admin', '2025-05-08 17:59:47.000000',
        '{\"settlementTemplate\":\"https://test.jingruiit.com:9002/supplychain/upload/20250508/8ac869299a28661b20d64a177e9ac65e.docx\"}');
INSERT INTO `lilishop`.`li_setting`(`id`, `create_by`, `create_time`, `delete_flag`, `update_by`, `update_time`,
                                    `setting_value`)
VALUES ('MINIO', 'admin', '2025-04-17 09:34:41.000000', b'0', 'admin', '2025-04-17 08:54:01.000000',
        '{\r\n    \"m_endpoint\": \"https://test.jingruiit.com:9002\",\r\n    \"m_accessKey\": \"jrzh\",\r\n    \"m_secretKey\": \"jrzh@2022\",\r\n    \"m_bucketName\": \"supplychain\",\r\n    \"m_frontUrl\": \"https://test.jingruiit.com:9002\",\r\n    \"type\": \"MINIO\"\r\n}');

-- 订单结算单表结构新增
CREATE TABLE `li_order_item_settlement`
(
    `id`                    bigint NOT NULL COMMENT 'ID',
    `create_by`             varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '创建者',
    `create_time`           datetime(6) DEFAULT NULL COMMENT '创建时间',
    `delete_flag`           bit(1)                                                        DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
    `update_by`             varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL COMMENT '更新者',
    `update_time`           datetime(6) DEFAULT NULL COMMENT '更新时间',
    `member_id`             bigint                                                        DEFAULT NULL COMMENT '会员id',
    `store_id`              bigint                                                        DEFAULT NULL COMMENT '店铺id',
    `order_sn`              varchar(50)                                                   DEFAULT NULL COMMENT '订单sn',
    `order_item_sn`         varchar(50)                                                   DEFAULT NULL COMMENT '子订单sn',
    `main_contract_no`      varchar(50)                                                   DEFAULT NULL COMMENT '主合同编号',
    `goods_name`            varchar(50)                                                   DEFAULT NULL COMMENT '商品名',
    `simple_specs`          varchar(50)                                                   DEFAULT NULL COMMENT '规格名称',
    `order_num`             int                                                           DEFAULT NULL COMMENT '主订单数量',
    `goods_unit_price`      decimal(18, 2)                                                DEFAULT NULL COMMENT '商品单价',
    `deliver_number`        decimal(18, 2)                                                DEFAULT NULL COMMENT '发货数量',
    `take_delivery_number`  decimal(18, 2)                                                DEFAULT NULL COMMENT '收货数量',
    `settlement_number`     decimal(18, 2)                                                DEFAULT NULL COMMENT '结算数量同收货数量',
    `settlement_unit_price` decimal(18, 2)                                                DEFAULT NULL COMMENT '结算单价',
    `total_amount`          decimal(18, 2)                                                DEFAULT NULL COMMENT '总价 = 结算单价 * 收货数量',
    `deduction_amount`      decimal(18, 2)                                                DEFAULT NULL COMMENT '扣款金额',
    `payable_amount`        decimal(18, 2)                                                DEFAULT NULL COMMENT '应付金额 = 总价 - 扣款金额',
    `settlement_desc`       varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '结算说明',
    `pdf_url`               varchar(255)                                                  DEFAULT NULL COMMENT '结算单pdf',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='订单结算单';