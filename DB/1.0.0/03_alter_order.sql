
-- 原订单表新增字段
ALTER TABLE `li_order`
    MODIFY COLUMN `order_promotion_type` varchar (255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '订单促销类型' AFTER `receivable_no`,
    MODIFY COLUMN `verification_code` varchar (255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '提货码' AFTER `order_promotion_type`,
    MODIFY COLUMN `store_address_path` varchar (255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '自提点地址' AFTER `verification_code`,
    MODIFY COLUMN `store_address_center` varchar (255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '自提点地址经纬度' AFTER `store_address_path`,
    MODIFY COLUMN `store_address_mobile` varchar (255) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci NULL DEFAULT NULL COMMENT '自提点电话' AFTER `store_address_center`,
    ADD COLUMN `goods_name` varchar (255) NULL COMMENT '商品名称' AFTER `store_address_mobile`,
    ADD COLUMN `simple_specs` varchar (255) NULL COMMENT '规格型号' AFTER `goods_name`,
    ADD COLUMN `settlement_model` varchar (255) NULL COMMENT '结算方式' AFTER `simple_specs`,
    ADD COLUMN `payment_voucher_url` varchar (255) NULL COMMENT '支付凭证url' AFTER `settlement_model`;

-- 子订单表新增字段
ALTER TABLE `li_order_item`
    ADD COLUMN `simple_specs` varchar(255) NULL COMMENT '规格名称' AFTER `refund_price`,
ADD COLUMN `store_id` varchar(255) NULL COMMENT '店铺ID' AFTER `simple_specs`,
ADD COLUMN `store_name` varchar(255) NULL COMMENT '店铺名称' AFTER `store_id`,
ADD COLUMN `delivery_date` date NULL COMMENT '交货时间' AFTER `store_name`,
ADD COLUMN `delivery_model` varchar(20) NULL COMMENT '配送方式' AFTER `delivery_date`,
ADD COLUMN `settlement_model` varchar(50) NULL COMMENT '结算方式' AFTER `delivery_model`,
ADD COLUMN `change_deliver_number` decimal(18, 2) NULL COMMENT '调整后的收货数' AFTER `settlement_model`,
ADD COLUMN `change_flow_price` decimal(18, 2) NULL COMMENT '调整后实际金额' AFTER `change_deliver_number`,
ADD COLUMN `change_confirm_status` varchar(20) NULL COMMENT '调整收货数后状态' AFTER `change_flow_price`;


ALTER TABLE `li_order_item`
    ADD COLUMN `change_desc` varchar(255) NULL COMMENT '调整描述' AFTER `change_confirm_status`;