ALTER TABLE `jrzh_attach`
DROP
COLUMN `tenant_id`,
DROP
COLUMN `create_user`,
DROP
COLUMN `create_dept`,
DROP
COLUMN `create_time`,
DROP
COLUMN `update_user`,
DROP
COLUMN `update_time`,
DROP
COLUMN `is_deleted`,
MODIFY COLUMN `status` int(11) NULL DEFAULT NULL COMMENT '状态' AFTER `attach_size`,
ADD COLUMN `create_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者' AFTER `status`,
ADD COLUMN `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间' AFTER `create_by`,
ADD COLUMN `delete_flag` bit(1) NULL DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除' AFTER `create_time`,
ADD COLUMN `update_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者' AFTER `delete_flag`,
ADD COLUMN `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间' AFTER `update_by`;
ALTER TABLE `li_customer_info`
DROP
PRIMARY KEY;