DROP TABLE IF EXISTS `jrzh_contract_sign_config`;
CREATE TABLE `jrzh_contract_sign_config`
(
    `id`          bigint(20) NOT NULL COMMENT '主键id',
    `keywords`    varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '签署关键字',
    `sign_align`  varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '对齐方式',
    `sign_no`     int(11) NULL DEFAULT NULL COMMENT '签署人顺序编号',
    `status`      tinyint(4) NULL DEFAULT 1 COMMENT '状态',
    `is_deleted`  tinyint(4) NULL DEFAULT 0 COMMENT '删除标记（1：已删除，0：未删除）',
    `tenant_id`   varchar(12) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
    `template_id` bigint(20) NULL DEFAULT NULL COMMENT '模板id',
    `sign_type`   int(11) NULL DEFAULT NULL COMMENT '签署类型 1、签名 2、印章',
    `create_by`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `create_time` datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
    `delete_flag` bit(1) NULL DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
    `update_by`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
    `update_time` datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '合同模板签署配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jrzh_contract_template_config
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_contract_template_config`;
CREATE TABLE `jrzh_contract_template_config`
(
    `id`                          bigint(20) NOT NULL COMMENT '主键id',
    `status`                      int(11) NOT NULL DEFAULT 1 COMMENT '状态',
    `template_id`                 varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '模板id',
    `bean_clazz_path`             varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '数据源（多数据源以逗号分割）',
    `template_fields_json`        longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'json合同字段',
    `base_info`                   varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '合同基础信息 多选：逗号分割 1：发起人个人信息 2：资方信息 3 产品信息',
    `template_fields_config_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'json全限名类字段json',
    `goods_type`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '产品类型',
    `iz_built`                    tinyint(4) NULL DEFAULT 0 COMMENT '是否内置数据源',
    `sign_node`                   varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '签署节点',
    `create_by`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `create_time`                 date NULL DEFAULT NULL COMMENT '创建时间',
    `delete_flag`                 bit(1) NULL DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
    `update_by`                   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
    `update_time`                 date NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同模板方案表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jrzh_contract_sign_seal
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_contract_sign_seal`;
CREATE TABLE `jrzh_contract_sign_seal`
(
    `id`            bigint(20) NOT NULL COMMENT '主键id',
    `category`      tinyint(4) NULL DEFAULT NULL COMMENT '类别（签名：0，印章：1）',
    `attach_id`     bigint(20) NULL DEFAULT NULL COMMENT '签名/印章附件id',
    `generate_link` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '生成的签名/印章url',
    `status`        tinyint(4) NULL DEFAULT 1 COMMENT '状态',
    `is_deleted`    tinyint(4) NULL DEFAULT 0 COMMENT '删除标记（1：已删除，0：未删除）',
    `create_by`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `create_time`   date NULL DEFAULT NULL COMMENT '创建时间',
    `delete_flag`   bit(1) NULL DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
    `update_by`     varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
    `update_time`   date NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '合同签章/印章配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jrzh_contract_config
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_contract_config`;
CREATE TABLE `jrzh_contract_config`
(
    `id`          bigint(20) NOT NULL COMMENT '主键id',
    `sign_way`    tinyint(4) NULL DEFAULT 0 COMMENT '签署方式（0：手动签，1：自动签）',
    `img_type`    tinyint(4) NULL DEFAULT NULL COMMENT '签名/印章图片类型（0：上传，1：生成）',
    `user_id`     bigint(20) NULL DEFAULT NULL COMMENT '账户id',
    `sign_id`     bigint(20) NULL DEFAULT NULL COMMENT '签名配置id',
    `seal_id`     bigint(20) NULL DEFAULT NULL COMMENT '印章配置id',
    `status`      tinyint(4) NULL DEFAULT 1 COMMENT '状态',
    `create_by`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `create_time` date NULL DEFAULT NULL COMMENT '创建时间',
    `delete_flag` bit(1) NULL DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
    `update_by`   varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
    `update_time` date NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '合同配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jrzh_contract_template_fields_config
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_contract_template_fields_config`;
CREATE TABLE `jrzh_contract_template_fields_config`
(
    `id`                 bigint(20) NOT NULL COMMENT '主键id',
    `status`             int(11) NOT NULL DEFAULT 1 COMMENT '状态',
    `template_config_id` bigint(20) NOT NULL COMMENT '模板方案id',
    `parentId`           bigint(20) NULL DEFAULT NULL COMMENT '父级id',
    `ssq_field_name`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '上上签业务字段名',
    `field_name`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '属性名',
    `parent_field_name`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '父级属性名',
    `iz_bind`            tinyint(4) NULL DEFAULT 0 COMMENT '合同绑定标识;0 未绑定 1绑定',
    `iz_front_field`     tinyint(4) NULL DEFAULT NULL COMMENT '是否为前端字段',
    `clazz_name`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字段映射类',
    `config_id`          bigint(20) NULL DEFAULT NULL COMMENT '配置id',
    `config_json`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '配置json',
    `create_by`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `create_time`        date NULL DEFAULT NULL COMMENT '创建时间',
    `delete_flag`        bit(1) NULL DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
    `update_by`          varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
    `update_time`        date NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同模板字段表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jrzh_attach
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_attach`;
CREATE TABLE `jrzh_attach`
(
    `id`            bigint(20) NOT NULL COMMENT '主键',
    `tenant_id`     varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户ID',
    `link`          varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件地址',
    `domain`        varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件域名',
    `name`          varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件名称',
    `original_name` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件原名',
    `extension`     varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附件拓展名',
    `attach_size`   bigint(20) NULL DEFAULT NULL COMMENT '附件大小',
    `create_user`   bigint(20) NULL DEFAULT NULL COMMENT '创建人',
    `create_dept`   bigint(20) NULL DEFAULT NULL COMMENT '创建部门',
    `create_time`   datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `update_user`   bigint(20) NULL DEFAULT NULL COMMENT '修改人',
    `update_time`   datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `status`        int(11) NULL DEFAULT NULL COMMENT '状态',
    `is_deleted`    int(11) NULL DEFAULT NULL COMMENT '是否已删除',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jrzh_contract
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_contract`;
CREATE TABLE `jrzh_contract`
(
    `id`                   bigint(20) NOT NULL,
    `contract_title`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '合同标题',
    `contract_id`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '合同编号',
    `sign_node`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '签署节点',
    `file_url`             varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件路径',
    `senders`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '发送人',
    `contract_template_id` bigint(20) NULL DEFAULT NULL COMMENT '合同模板id',
    `contract_biz_no`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合同业务号 用于查询本次业务的所有相关合同',
    `process_type`         int(11) NOT NULL DEFAULT 0 COMMENT '流程类型',
    `goods_id`             bigint(20) NULL DEFAULT NULL COMMENT '产品id',
    `good_name`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '产品名称',
    `good_type`            int(11) NOT NULL DEFAULT 0 COMMENT '产品类型',
    `capital_id`           bigint(20) NULL DEFAULT NULL COMMENT '资金方id',
    `create_dept`          bigint(20) NULL DEFAULT NULL COMMENT '部门',
    `status`               int(11) NOT NULL DEFAULT 0 COMMENT '状态',
    `finance_apply_id`     bigint(20) NULL DEFAULT NULL COMMENT '放款申请id',
    `finish_time`          datetime(0) NULL DEFAULT NULL COMMENT '签约时间',
    `send_time`            datetime(0) NULL DEFAULT NULL COMMENT '发送时间',
    `big_biz_no`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大流程水流号',
    `contract_no`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '合同编号',
    `sign_keyword_json`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签署列表关键字json',
    `sign_dead_line`       datetime(0) NULL DEFAULT NULL COMMENT '截止时间',
    `signed_url`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '完成后的合同url',
    `contract_var`         text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '合同生成变量',
    `resended`             tinyint(4) NULL DEFAULT 0 COMMENT '合同已重新发送 0非 1是',
    `create_by`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `create_time`          date NULL DEFAULT NULL COMMENT '创建时间',
    `delete_flag`          bit(1) NULL DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
    `update_by`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
    `update_time`          date NULL DEFAULT NULL COMMENT '更新时间',
    `seller_id`            bigint(20) NULL DEFAULT NULL COMMENT '卖方id',
    `buyer_id`             bigint(20) NULL DEFAULT NULL COMMENT '买方id',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX                  `contract_id`(`contract_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jrzh_contract_template
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_contract_template`;
CREATE TABLE `jrzh_contract_template`
(
    `id`                   bigint(20) NOT NULL,
    `template_id`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板id',
    `template_name`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板标题',
    `template_category`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板类型',
    `remarks`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
    `doc_ids`              varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文档id',
    `sign_role`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '签约角色',
    `sign_require`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '签约要求',
    `sign_method`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '签署方式',
    `template_status`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '模板状态',
    `operate_name`         varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '操作人',
    `operate_time`         datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
    `system_deleted`       int(11) NULL DEFAULT 0 COMMENT '系统删除',
    `attach_id`            bigint(20) NULL DEFAULT NULL,
    `status`               int(11) NOT NULL DEFAULT 0,
    `tenant_id`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户id',
    `expire_day`           int(11) NULL DEFAULT 3 COMMENT '过期时间',
    `force_reading_second` int(11) NULL DEFAULT NULL COMMENT '强制阅读秒数',
    `verify_type`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '签署校验类型 1 验证码 2 人脸',
    `api_supplier`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '接口供应商',
    `contract_gen_type`    int(11) NULL DEFAULT 1 COMMENT '合同生成方式 1合同模板 2自定义合同模板',
    `signer_num`           int(11) NULL DEFAULT 1 COMMENT '签署人数量',
    `template_url`         varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '合同模板文件url',
    `template_word_url`    varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '自定义模板word类型url',
    `sign_keyword_json`    text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '签署列表关键字json',
    `need_customize_write` tinyint(4) NULL DEFAULT 0 COMMENT '是否需要手写签名 0不需要 1需要',
    `sign_node`            varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '签署节点',
    `sub_contract_no`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '关联子合同编号',
    `need_plat_sign`       tinyint(4) NULL DEFAULT 0 COMMENT '需要平台签署 0不需要 1需要',
    `create_by`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `create_time`          datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `delete_flag`          bit(1) NULL DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
    `update_by`            varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
    `update_time`          datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同模板表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for jrzh_contract_operator
-- ----------------------------
DROP TABLE IF EXISTS `jrzh_contract_operator`;
CREATE TABLE `jrzh_contract_operator`
(
    `status`              int(11) NULL DEFAULT NULL COMMENT '状态;0未签1已签',
    `id`                  bigint(20) NULL DEFAULT NULL COMMENT 'id',
    `contract_id`         bigint(20) NULL DEFAULT NULL COMMENT '合同id',
    `signer_id`           bigint(20) NULL DEFAULT NULL COMMENT '合同签署人id',
    `user_type`           int(11) NULL DEFAULT NULL COMMENT '用户类型',
    `signer_name`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '签署人',
    `signer_company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '签署企业名称',
    `contract_no`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '合同编号',
    `template_name`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '合同名称',
    `sorted`              int(11) NULL DEFAULT NULL COMMENT '签署顺序',
    `create_by`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者',
    `create_time`         datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
    `delete_flag`         bit(1) NULL DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
    `update_by`           varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新者',
    `update_time`         datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    INDEX                 `contract_id`(`contract_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '合同签署表' ROW_FORMAT = Dynamic;


ALTER TABLE `li_member`
    ADD COLUMN `status` bigint NULL DEFAULT NULL COMMENT '企业实名开通状态' AFTER `total_point`;


DROP TABLE IF EXISTS `li_huaweiocrresult`;
CREATE TABLE `li_huaweiocrresult`
(
    `id`             bigint NOT NULL COMMENT '唯一标识;ID',
    `create_by`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建人;创建人',
    `delete_flag`    bit(1) NULL DEFAULT NULL COMMENT '创建部门',
    `create_time`    datetime(6) NULL DEFAULT NULL COMMENT '创建时间',
    `update_by`      bigint NULL DEFAULT NULL COMMENT '更新人',
    `update_time`    datetime(6) NULL DEFAULT NULL COMMENT '更新时间',
    `is_deleted`     int NULL DEFAULT NULL COMMENT '是否已删除;是否删除 0-未删除 1-已删除',
    `status`         int NULL DEFAULT NULL COMMENT '状态',
    `attach_id`      bigint NULL DEFAULT -1 COMMENT '附加id',
    `ocr_type`       int NULL DEFAULT NULL COMMENT '识别类型 1身份证 2营业执照',
    `hash_val`       bigint NULL DEFAULT NULL COMMENT '图片hash',
    `proof`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片地址',
    `ocr_result`     longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '识别结果',
    `result_success` int NULL DEFAULT NULL COMMENT '识别是否成功 0 失败 1成功',
    `fail_reason`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '失败原因',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '华为云orc识别附件' ROW_FORMAT = DYNAMIC;


DROP TABLE IF EXISTS `li_customer_info`;
CREATE TABLE `li_customer_info`
(
    `id`                         bigint NULL DEFAULT NULL COMMENT '主键',
    `customer_id`                bigint NULL DEFAULT NULL COMMENT 'li_member表id',
    `corp_name`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '企业名称',
    `business_licence_number`    varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '工商注册号',
    `regist_pic_attch_id`        varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '工商号图片id',
    `corporation_name`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '法人姓名',
    `corporation_id_card_number` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '法人证件号',
    `corporation_sex`            int NULL DEFAULT 2 COMMENT '法人性别;0 男 1 女  2 不展示',
    `corporation_country`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '法人国家',
    `corporation_nation`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '法人名族',
    `corporation_valid_time`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '法人证件有效期',
    `corporation_address`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '法人居住地',
    `mobile`                     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '法人手机号',
    `leagal_no_attach_id`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '法人证件号附件id',
    `operator_name`              varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经办人姓名',
    `operator_idcard`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经办人证件号',
    `operator_sex`               int NULL DEFAULT 2 COMMENT '经办人性别;0 男 1 女  2 不展示',
    `operator_country`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经办人国籍',
    `operator_nation`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经办人名族',
    `operator_valid_time`        varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经办人证件有效期',
    `operator_address`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经办人居住地',
    `operator_phone`             varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经办人手机号',
    `operator_attach_id`         varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经办人附件id',
    `corporation_id_type`        varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '法人证件类型-若证件类型为“0”、法人证件号码为身份证号码，证件类型为“1”、法人证件号码为护照号码',
    `corporation_face_attach_id` bigint NULL DEFAULT NULL COMMENT '法人证件正面附件id',
    `corporation_back_attach_id` bigint NULL DEFAULT NULL COMMENT '法人证件反面附件',
    `operator_face_attach_id`    bigint NULL DEFAULT NULL COMMENT '经办人证件正面id',
    `operator_back_attach_id`    bigint NULL DEFAULT NULL COMMENT '经办人证件反面附件id',
    `business_licence_attach_id` bigint NULL DEFAULT NULL COMMENT '公司经营证件附件id',
    `operation_status`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经营状况',
    `operation_from`             varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经营期限自',
    `operation_to`               varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '经营期限至',
    `corporation_email`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '法人邮箱',
    `update_time`                datetime NULL DEFAULT NULL COMMENT '更新时间',
    `create_time`                datetime NULL DEFAULT NULL COMMENT '创建时间',
    `status`                     int                                                           NOT NULL DEFAULT 1 COMMENT '状态 1开通中 2 已开通 3开通失败 ',
    `create_by`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '创建人',
    `update_by`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '更新人',
    `delete_flag`                bit(1) NULL DEFAULT b'0' COMMENT '创建部门',
    `is_deleted`                 int                                                           NOT NULL DEFAULT 0 COMMENT '是否删除;1删除 0 没有删除',
    `tenant_id`                  varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '000000' COMMENT '租户id',
    `company_id`                 bigint NULL DEFAULT NULL COMMENT '企业id',
    `auth_status`                int                                                           NOT NULL DEFAULT 1 COMMENT '实名认证状态（0：未认证； 1： 审核中 ；2：认证通过 ；3：认证不通过 ；4:意愿性认证中）',
    `legal_person_flag`          int NULL DEFAULT NULL COMMENT '是否为法人认证 1：是；0:否',
    `ent_auth_type`              int NULL DEFAULT NULL COMMENT '企业认证类型 2 融资企业 3 核心企业',
    `logo`                       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '企业logo',
    `business_licence_id`        bigint NULL DEFAULT -1 COMMENT '营业执照id',
    `start_sign`                 int NULL DEFAULT 1 COMMENT '经办人是否签署授权书 1 未签署 2 签署',
    `contract_no`                varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '合同编号',
    `business_info_id`           bigint NULL DEFAULT NULL COMMENT '工商信息表id',
    `enter_status`               int NULL DEFAULT 1 COMMENT '是否通过审批 1 未通过 2通过',
    `operator_id_type`           int NULL DEFAULT NULL COMMENT '经办人证件类型-若证件类型为“0”、经办人证件号码为身份证号码，证件类型为“1”、经办人号码为护照号码\', ',
  `auth_type` int NULL DEFAULT NULL COMMENT '
    实名方式
    1待提交实名
    2档案柜实名
    ',
      `task_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '
    异步任务id
    ',
      `bank_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '
    对公银行卡号
    ',
      `bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '
    银行名称
    ',
      `bank_area_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '
    银行区划编号
    ',
      `bra_bank_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '
    银行支行
    ',
      `process` int NULL DEFAULT NULL COMMENT '
    实名进度
    ',
      `api_supplier` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '
    api供应商
    ',
      `base` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '
    省份拼音简写
    ',
      `user_task_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '
    用户异步注册任务id
    ',
      PRIMARY KEY (`corporation_country`) USING BTREE,
      UNIQUE INDEX `business_licence_number`(`business_licence_number` ASC, `tenant_id` ASC) USING BTREE,
      INDEX `customer_id`(`customer_id` ASC) USING BTREE
    ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '
    企业基本认证信息
    ' ROW_FORMAT = DYNAMIC;
