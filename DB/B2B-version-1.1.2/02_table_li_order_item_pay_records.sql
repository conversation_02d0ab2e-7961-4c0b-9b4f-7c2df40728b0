CREATE TABLE `li_order_item_pay_records` (
`id` bigint NOT NULL COMMENT 'ID',
`create_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '创建者',
`create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
`delete_flag` bit(1) DEFAULT NULL COMMENT '删除标志 true/false 删除/未删除',
`update_by` varchar(255) CHARACTER SET utf8mb3 COLLATE utf8mb3_bin DEFAULT NULL COMMENT '更新者',
`update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
`order_item_sn` varchar(255) DEFAULT NULL COMMENT '子订单编号',
`payment_voucher_url` varchar(255) DEFAULT NULL COMMENT '店铺id',
`store_id` varchar(50) DEFAULT NULL COMMENT '店铺id',
`member_id` varchar(50) DEFAULT NULL COMMENT '买方id',
`payment_status` varchar(20) DEFAULT NULL COMMENT '支付确认状态',
`paid_amount` decimal(20,2) DEFAULT NULL COMMENT '支付金额',
`parent_id` bigint DEFAULT NULL COMMENT '支付记录id',
`type` varchar(30) DEFAULT NULL COMMENT '数据类型',
`payment_method` varchar(30) DEFAULT NULL COMMENT '支付方式',
PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='子订单付款记录';