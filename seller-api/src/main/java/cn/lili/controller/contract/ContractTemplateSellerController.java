package cn.lili.controller.contract;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_contract.contract_api.entity.ContractTemplate;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractTemplateConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 店铺端合同模板相关接口
 *
 * <AUTHOR>
 * @since 2024-06-25
 */
@RestController
@RequestMapping("/store/contract/template")
@Api(tags = "店铺端合同模板接口")
@RequiredArgsConstructor
@Slf4j
public class ContractTemplateSellerController {

    private final IContractTemplateConfigService contractTemplateConfigService;

    @ApiOperation("根据商品ID获取可用的合同模板列表")
    @GetMapping("/available-by-goods/{goodsId}")
    public ResultMessage<List<ContractTemplate>> getAvailableTemplatesForGoods(
            @ApiParam(value = "商品ID", required = true) @PathVariable String goodsId) {

        log.info("店铺端查询商品可用合同模板，商品ID：{}", goodsId);
        List<ContractTemplate> templates = contractTemplateConfigService.getAvailableTemplatesForGoods(goodsId);
        return ResultUtil.data(templates);
    }

    @ApiOperation("根据分类路径获取可用的合同模板列表")
    @GetMapping("/available-by-category")
    public ResultMessage<List<ContractTemplate>> getAvailableTemplatesByCategoryPath(
            @ApiParam(value = "分类路径，格式如：1,2,3", required = true) @RequestParam String categoryPath) {

        log.info("店铺端查询分类可用合同模板，分类路径：{}", categoryPath);
        List<ContractTemplate> templates = contractTemplateConfigService.getAvailableTemplatesByCategoryPath(categoryPath);
        return ResultUtil.data(templates);
    }

    @ApiOperation("查询商品绑定的合同模板ID")
    @GetMapping("/bound-template/{goodsId}")
    public ResultMessage<String> getGoodsBoundTemplateId(
            @ApiParam(value = "商品ID", required = true) @PathVariable String goodsId) {

        log.info("店铺端查询商品绑定的合同模板，商品ID：{}", goodsId);
        String templateId = contractTemplateConfigService.getGoodsBoundTemplateId(goodsId);
        return ResultUtil.data(templateId);
    }
}
