/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON> All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package cn.lili.controller.contract;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.lili.common.enums.ResultUtil;
import cn.lili.common.exception.ServiceException;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.jrzh_bases.Func;
import cn.lili.modules.jrzh_bases.SmsCode;
import cn.lili.modules.jrzh_contract.contract_api.dto.ContractSignParam;
import cn.lili.modules.jrzh_contract.contract_api.vo.ContractSignVO;
import cn.lili.modules.jrzh_contract.contract_biz.service.IContractService;
import cn.lili.modules.jrzh_contract.customer_ct.dto.ContractReturnData;
import cn.lili.modules.jrzh_contract.customer_ct.dto.CustomerContractDataParamsDTO;
import cn.lili.modules.jrzh_contract.customer_ct.service.IContractCustomerService;
import cn.lili.modules.member.service.MemberService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

/**
 * 客户合同 控制器
 *
 * <AUTHOR>
 * @since 2022-03-31
 */
@RestController
@AllArgsConstructor
@RequestMapping("/store/contract/contractCustomer")
@Api(value = "店铺生成签署合同")
public class ContractStoreFrontController {

    private final IContractCustomerService contractCustomerService;
//    private final IContractSignService contractSignService;
//    private final ICustomerAuthService customerAuthService;
//    private final RedisLockClient redisLockClient;
    private MemberService memberService;
    private RedissonClient redisson;


    /**
     * 生成合同
     * @param paramsDTO
     * @return
     */
    @PostMapping("/genContract")
    public ResultMessage<ContractReturnData> generateContractWithBusiness(@RequestBody CustomerContractDataParamsDTO paramsDTO) {
//        TODO获取会员id，后续需要改成企业或其他用户id
//        Member member = memberService.getUserInfo();
//        Long userId= Long.valueOf(member.getId());
        AuthUser user = UserContext.getCurrentUser();
        Long userId= Long.valueOf(user.getId());
        if (ObjectUtil.isEmpty(userId)) {
            throw new ServiceException("用户登录过期");
        }
        //查合同
        String key = "supply:chan:contract:generator:" + userId + paramsDTO.getTemplateId();
        ContractReturnData contractReturnData;
        RLock lock = redisson.getLock(user.getId() + key);
        lock.lock();
        try {
            //设置需要生成的合同
            contractReturnData = contractCustomerService.generateContractWithBusiness(paramsDTO);
        } finally {
            lock.unlock();
        }
        return ResultUtil.data(contractReturnData);
    }

    /**
     * 使用当前用户进行签署
     *
     * @return
     */
    @PostMapping("/sign")
    public ResultMessage skipToSign(@RequestBody ContractSignVO contractSignVO) {
        SmsCode code = BeanUtil.copyProperties(contractSignVO.getCode(), SmsCode.class);
        //当前用户签署信息
        ContractSignParam signParam = ContractSignParam.builder()
                .contractIds(contractSignVO.getContractId())
                .code(code)
                .customizeWriteBase64(contractSignVO.getCustomizeWriteBase64())
                .verifyType(contractSignVO.getVerifyType())
                .autoLock(contractSignVO.getAutoLock())
                .build();
        contractCustomerService.signByCurrentUser(signParam);
        return ResultUtil.data("");
    }


    private IContractService contractService;
    @GetMapping("/test")
    public ResultMessage test(@RequestParam String contractId){
        contractService.signedUpdateContractUrl(Func.toStrList(contractId));
        return ResultUtil.data("");
    }

    /**
     * 下载合同
     *
     * @param contractId
     * @return
     */
//    @GetMapping("downLoadUrl")
//    public R downLoadContract(@RequestParam String contractId) {
//        return R.data(contractCustomerService.downLoadContract(contractId));
//    }

    /**
     * 签署注册合同
     *
     * @param contractId 合同id
     */
//    @GetMapping("/signRegistContract")
//    public R signRegistContract(@RequestParam String contractId,@RequestParam String returnUrl) {
//       return R.data(customerAuthService.signRegisterContract(contractId,returnUrl));
//    }
}
