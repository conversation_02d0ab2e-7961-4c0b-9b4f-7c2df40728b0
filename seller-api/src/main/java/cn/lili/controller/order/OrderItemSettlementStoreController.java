package cn.lili.controller.order;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.utils.StringUtils;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.order.order.entity.dto.OrderItemSettlementParams;
import cn.lili.modules.order.order.entity.enums.SettlementSourcEnum;
import cn.lili.modules.order.order.entity.vo.OrderItemSettlementVo;
import cn.lili.modules.order.order.service.OrderItemSettlementService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025年05月07日19:16
 */
@RestController
@Api(tags = "卖家端,结算单")
@RequestMapping("/store/order/orderItemSettlement")
public class OrderItemSettlementStoreController {

    @Autowired
    private OrderItemSettlementService orderItemSettlementService;

    /**
     * 卖家端结算单分页
     * @param params
     * @return
     */
    @GetMapping
    public ResultMessage<IPage<OrderItemSettlementVo>> queryByParams(OrderItemSettlementParams params) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        params.setStoreId(currentUser.getStoreId());
        params.setSettlementSourcEnum(SettlementSourcEnum.ORDER_ITEM);
        return ResultUtil.data(orderItemSettlementService.queryByParams(params));
    }

    /**
     * 根据订单sn或子订单sn查询
     * @param params
     * @return
     */
    @GetMapping("/queryListByParams")
    public ResultMessage<List<OrderItemSettlementVo>> queryListByParams(OrderItemSettlementParams params) {
        AuthUser currentUser = Objects.requireNonNull(UserContext.getCurrentUser());
        params.setStoreId(currentUser.getStoreId());
        if (StringUtils.isNotEmpty(params.getOrderSn())) {
            params.setSettlementSourcEnum(SettlementSourcEnum.ORDER);
        }
        if (StringUtils.isNotEmpty(params.getOrderItemSn())) {
            params.setSettlementSourcEnum(SettlementSourcEnum.ORDER_ITEM);
        }
        return ResultUtil.data(orderItemSettlementService.queryListByParams(params));
    }

}
