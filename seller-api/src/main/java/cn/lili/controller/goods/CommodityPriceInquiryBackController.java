package cn.lili.controller.goods;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.security.AuthUser;
import cn.lili.common.security.context.UserContext;
import cn.lili.common.vo.PageVO;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.goods.entity.dto.CommodityPriceInquiryQueryDto;
import cn.lili.modules.goods.entity.vos.CommodityPriceInquiryVo;
import cn.lili.modules.goods.service.ICommodityPriceInquiryService;
import cn.lili.modules.goods.service.ICommodityPriceQuoteService;
import cn.lili.modules.store.entity.vos.StoreVO;
import cn.lili.modules.store.service.StoreService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 询价平台端功能
 * <AUTHOR>
 * @date 2025/3/5
 */
@RestController
@AllArgsConstructor
@RequestMapping("/store/commodity/priceInquiry")
public class CommodityPriceInquiryBackController {

    private final ICommodityPriceInquiryService commodityPriceInquiryService;
    private final ICommodityPriceQuoteService commodityPriceQuoteService;
    private final StoreService storeService;

    /**
     * 待报价分页
     * 询价记录，查询询价中的数据
     */
    @PostMapping("/page")
    public ResultMessage<IPage<CommodityPriceInquiryVo>> page(PageVO query, CommodityPriceInquiryQueryDto dto) {
        StoreVO storeDetail = storeService.getStoreDetail();
        // 店铺id
        String storeId = storeDetail.getId();
        IPage<CommodityPriceInquiryVo> page = commodityPriceInquiryService.pageBySupplier(query, storeId, dto);
        return ResultUtil.data(page);
    }

    /**
     * 已报价分页
     */
    @PostMapping("/quotePage")
    public ResultMessage<IPage<CommodityPriceInquiryVo>> quotePage(PageVO query, CommodityPriceInquiryQueryDto dto) {
        // 获取当前登录店铺
        StoreVO storeDetail = storeService.getStoreDetail();
        String storeId = storeDetail.getId();
        IPage<CommodityPriceInquiryVo> page = commodityPriceQuoteService.quotePage(query, storeId, dto);
        return ResultUtil.data(page);
    }

}
