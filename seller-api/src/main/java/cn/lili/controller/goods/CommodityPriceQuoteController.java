package cn.lili.controller.goods;

import cn.lili.common.enums.ResultUtil;
import cn.lili.common.vo.ResultMessage;
import cn.lili.modules.goods.entity.dto.CommodityPriceQuoteDto;
import cn.lili.modules.goods.service.ICommodityPriceQuoteService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 报价
 * <AUTHOR>
 * @date 2025/3/6
 */
@RestController
@AllArgsConstructor
@RequestMapping("/store/commodity/priceQuote")
public class CommodityPriceQuoteController {

    private final ICommodityPriceQuoteService commodityPriceQuoteService;

    /**
     * 针对询价记录进行报价
     * @param dtos
     * @return
     */
    @PostMapping("/quota")
    public ResultMessage<Boolean> quota(@RequestBody List<CommodityPriceQuoteDto> dtos) {
        return ResultUtil.data(commodityPriceQuoteService.quota(dtos));
    }

}
